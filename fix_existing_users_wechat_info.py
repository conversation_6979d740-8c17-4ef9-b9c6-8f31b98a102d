#!/usr/bin/env python3
"""
修复现有用户的微信信息
为没有昵称和头像的用户设置默认值
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.notifications.models import WechatUserInfo
from apps.users.models import UserProfile

User = get_user_model()


def fix_existing_users():
    """修复现有用户的微信信息"""
    print("🔧 修复现有用户的微信信息...")
    
    try:
        # 查找所有微信用户
        wechat_users = WechatUserInfo.objects.all()
        total_count = wechat_users.count()
        fixed_count = 0
        
        print(f"找到 {total_count} 个微信用户记录")
        
        for wechat_user in wechat_users:
            user = wechat_user.user
            needs_fix = False
            changes = []
            
            print(f"\n检查用户: {user.username}")
            print(f"   OpenID: {wechat_user.openid}")
            print(f"   当前微信昵称: '{wechat_user.nickname}'")
            print(f"   当前微信头像: '{wechat_user.avatar_url}'")
            print(f"   当前用户昵称: '{user.nickname}'")
            
            # 检查并修复微信昵称
            if not wechat_user.nickname:
                if user.nickname:
                    wechat_user.nickname = user.nickname
                    changes.append(f"设置微信昵称为用户昵称: '{user.nickname}'")
                else:
                    # 生成一个基于用户名的昵称
                    default_nickname = f"用户{user.username[-4:]}"
                    wechat_user.nickname = default_nickname
                    user.nickname = default_nickname
                    changes.append(f"设置默认昵称: '{default_nickname}'")
                needs_fix = True
            
            # 检查并修复微信头像
            if not wechat_user.avatar_url:
                # 检查用户是否有头像
                try:
                    profile = user.profile
                    if profile.avatar:
                        wechat_user.avatar_url = profile.avatar
                        changes.append(f"使用用户资料头像: '{profile.avatar}'")
                    else:
                        # 设置默认头像
                        default_avatar = "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132"
                        wechat_user.avatar_url = default_avatar
                        changes.append(f"设置默认头像")
                        
                        # 同时更新用户资料
                        if not hasattr(user, 'profile'):
                            UserProfile.objects.create(user=user, avatar=default_avatar)
                        else:
                            profile.avatar = default_avatar
                            profile.save()
                except UserProfile.DoesNotExist:
                    # 创建用户资料
                    default_avatar = "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132"
                    UserProfile.objects.create(user=user, avatar=default_avatar)
                    wechat_user.avatar_url = default_avatar
                    changes.append(f"创建用户资料并设置默认头像")
                
                needs_fix = True
            
            # 确保用户昵称不为空
            if not user.nickname:
                if wechat_user.nickname:
                    user.nickname = wechat_user.nickname
                    changes.append(f"设置用户昵称为微信昵称: '{wechat_user.nickname}'")
                else:
                    default_nickname = f"用户{user.username[-4:]}"
                    user.nickname = default_nickname
                    changes.append(f"设置用户默认昵称: '{default_nickname}'")
                needs_fix = True
            
            if needs_fix:
                # 保存更改
                wechat_user.save()
                user.save()
                fixed_count += 1
                
                print(f"   ✅ 已修复:")
                for change in changes:
                    print(f"      - {change}")
            else:
                print(f"   ✅ 数据完整，无需修复")
        
        print(f"\n🎉 修复完成!")
        print(f"   总用户数: {total_count}")
        print(f"   修复用户数: {fixed_count}")
        print(f"   完整用户数: {total_count - fixed_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def verify_fix():
    """验证修复结果"""
    print(f"\n🔍 验证修复结果...")
    
    try:
        # 统计修复后的情况
        total_users = WechatUserInfo.objects.count()
        users_with_nickname = WechatUserInfo.objects.exclude(nickname='').count()
        users_with_avatar = WechatUserInfo.objects.exclude(avatar_url='').count()
        
        print(f"修复后统计:")
        print(f"   总微信用户: {total_users}")
        print(f"   有昵称的用户: {users_with_nickname}")
        print(f"   有头像的用户: {users_with_avatar}")
        
        # 显示修复后的示例
        print(f"\n📋 修复后的用户示例:")
        sample_users = WechatUserInfo.objects.all()[:3]
        for wechat_user in sample_users:
            user = wechat_user.user
            print(f"   用户: {user.username}")
            print(f"     微信昵称: '{wechat_user.nickname}'")
            print(f"     微信头像: {'有' if wechat_user.avatar_url else '无'}")
            print(f"     用户昵称: '{user.nickname}'")
            print()
        
        # 测试API序列化
        if total_users > 0:
            print(f"🧪 测试API序列化...")
            from api.v1.serializers import UserDetailSerializer, WechatUserSerializer
            
            test_wechat_user = WechatUserInfo.objects.first()
            test_user = test_wechat_user.user
            
            user_serializer = UserDetailSerializer(test_user)
            wechat_serializer = WechatUserSerializer(test_wechat_user)
            
            user_data = user_serializer.data
            wechat_data = wechat_serializer.data
            
            print(f"API响应测试:")
            print(f"   user.nickname: '{user_data.get('nickname')}'")
            print(f"   user.avatar: '{user_data.get('avatar')}'")
            print(f"   wechat_info.nickname: '{user_data.get('wechat_info', {}).get('nickname')}'")
            print(f"   wechat_info.avatar_url: '{user_data.get('wechat_info', {}).get('avatar_url')}'")
            
            # 模拟前端合并逻辑
            final_nickname = (user_data.get('wechat_info', {}).get('nickname') or 
                            user_data.get('nickname') or '默认昵称')
            final_avatar = (user_data.get('wechat_info', {}).get('avatar_url') or 
                          user_data.get('avatar') or '默认头像')
            
            print(f"前端最终显示:")
            print(f"   昵称: '{final_nickname}'")
            print(f"   头像: {'有效' if final_avatar != '默认头像' else '默认'}")
        
        return users_with_nickname == total_users and users_with_avatar == total_users
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


if __name__ == '__main__':
    print("=" * 60)
    print("修复现有用户微信信息")
    print("=" * 60)
    
    # 执行修复
    fix_ok = fix_existing_users()
    
    # 验证修复结果
    verify_ok = verify_fix()
    
    print(f"\n" + "=" * 60)
    print(f"修复结果:")
    print(f"   修复执行: {'✅ 成功' if fix_ok else '❌ 失败'}")
    print(f"   验证结果: {'✅ 通过' if verify_ok else '❌ 失败'}")
    
    if fix_ok and verify_ok:
        print(f"\n🎉 修复成功!")
        print(f"💡 现在用户应该能看到正确的头像和昵称了")
        print(f"📱 建议用户:")
        print(f"   1. 清除小程序缓存")
        print(f"   2. 重新登录")
        print(f"   3. 检查个人中心页面显示")
    else:
        print(f"\n❌ 修复未完全成功，请检查错误信息")
    
    print("=" * 60)

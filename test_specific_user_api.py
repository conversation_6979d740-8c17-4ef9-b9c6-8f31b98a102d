#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定用户的API
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from api.v1.views import user_profile

User = get_user_model()


def test_user_profile_api():
    """测试用户资料API"""
    print("🧪 测试特定用户的资料API...")
    
    username = 'wx_oXYVEvjxO1wJ_itf'
    
    try:
        user = User.objects.get(username=username)
        token = Token.objects.get(user=user)
        
        print(f"👤 测试用户: {user.username}")
        print(f"🔑 Token: {token.key}")
        
        factory = RequestFactory()
        
        # 创建GET请求，带上认证头
        request = factory.get('/api/v1/users/profile/')
        request.META['HTTP_AUTHORIZATION'] = f'Token {token.key}'
        
        # 调用用户资料视图
        response = user_profile(request)
        
        print(f"\n📥 API响应:")
        print(f"   状态码: {response.status_code}")
        
        if hasattr(response, 'data'):
            response_data = response.data
            print(f"   响应代码: {response_data.get('code')}")
            
            if response_data.get('code') == 200:
                print(f"\n✅ 获取用户资料成功!")
                user_data = response_data['data']
                
                print(f"📊 用户资料详情:")
                print(f"   ID: {user_data.get('id')}")
                print(f"   用户名: {user_data.get('username')}")
                print(f"   昵称: \"{user_data.get('nickname')}\"")
                print(f"   头像: \"{user_data.get('avatar')}\"")
                print(f"   手机: {user_data.get('phone')}")
                print(f"   邮箱: \"{user_data.get('email')}\"")
                print(f"   性别: {user_data.get('gender')}")
                print(f"   城市: \"{user_data.get('city')}\"")
                print(f"   省份: \"{user_data.get('province')}\"")
                print(f"   国家: \"{user_data.get('country')}\"")
                
                # 检查微信信息
                wechat_info = user_data.get('wechat_info')
                if wechat_info:
                    print(f"\n📱 微信信息:")
                    print(f"   OpenID: {wechat_info.get('openid')}")
                    print(f"   昵称: \"{wechat_info.get('nickname')}\"")
                    print(f"   头像: \"{wechat_info.get('avatar_url')}\"")
                    print(f"   性别: {wechat_info.get('gender')}")
                    print(f"   城市: \"{wechat_info.get('city')}\"")
                    print(f"   省份: \"{wechat_info.get('province')}\"")
                    print(f"   国家: \"{wechat_info.get('country')}\"")
                    
                    # 检查数据一致性
                    print(f"\n🔍 数据一致性检查:")
                    nickname_match = user_data.get('nickname') == wechat_info.get('nickname')
                    avatar_match = user_data.get('avatar') == wechat_info.get('avatar_url')
                    
                    print(f"   昵称一致: {'✅' if nickname_match else '❌'}")
                    print(f"   头像一致: {'✅' if avatar_match else '❌'}")
                    
                    if nickname_match and avatar_match:
                        print(f"   ✅ 数据完全一致")
                    else:
                        print(f"   ❌ 数据不一致，需要检查")
                else:
                    print(f"\n❌ 没有微信信息")
                
                # 模拟前端合并逻辑
                print(f"\n🔄 模拟前端合并逻辑:")
                merged_nickname = wechat_info.get('nickname') if wechat_info else user_data.get('nickname')
                merged_avatar = wechat_info.get('avatar_url') if wechat_info else user_data.get('avatar')
                
                print(f"   最终昵称: \"{merged_nickname}\"")
                print(f"   最终头像: \"{merged_avatar}\"")
                
                # 检查是否会显示默认值
                if merged_nickname and merged_nickname.strip():
                    print(f"   ✅ 昵称不为空，不会显示默认值")
                else:
                    print(f"   ❌ 昵称为空，会显示默认值 '茶园投资者'")
                
                if merged_avatar and merged_avatar.strip() and 'http' in merged_avatar:
                    print(f"   ✅ 头像URL有效，不会显示默认头像")
                else:
                    print(f"   ❌ 头像URL无效，会显示默认头像")
                
                return {
                    'success': True,
                    'user_data': user_data,
                    'merged_nickname': merged_nickname,
                    'merged_avatar': merged_avatar
                }
            else:
                print(f"❌ 获取用户资料失败: {response_data.get('message')}")
                return {'success': False, 'error': response_data.get('message')}
        else:
            print(f"❌ 响应格式错误")
            return {'success': False, 'error': '响应格式错误'}
        
    except User.DoesNotExist:
        print(f"❌ 用户 {username} 不存在")
        return {'success': False, 'error': '用户不存在'}
    except Token.DoesNotExist:
        print(f"❌ 用户 {username} 没有token")
        return {'success': False, 'error': '没有token'}
    except Exception as e:
        print(f"❌ API调用异常: {str(e)}")
        return {'success': False, 'error': str(e)}


def generate_curl_command():
    """生成curl测试命令"""
    print(f"\n🔧 生成curl测试命令...")
    
    username = 'wx_oXYVEvjxO1wJ_itf'
    
    try:
        user = User.objects.get(username=username)
        token = Token.objects.get(user=user)
        
        curl_command = f"""curl -X GET "https://teabuy.yizhangkj.com/api/v1/users/profile/" \\
  -H "Authorization: Token {token.key}" \\
  -H "Content-Type: application/json" \\
  -v"""
        
        print(f"📋 可以在命令行中执行以下命令测试API:")
        print(f"{curl_command}")
        
        return curl_command
        
    except (User.DoesNotExist, Token.DoesNotExist):
        print(f"❌ 无法生成curl命令，用户或token不存在")
        return None


def generate_frontend_debug_code():
    """生成前端调试代码"""
    print(f"\n📱 生成前端调试代码...")
    
    username = 'wx_oXYVEvjxO1wJ_itf'
    
    try:
        user = User.objects.get(username=username)
        token = Token.objects.get(user=user)
        
        debug_code = f"""
// 在小程序控制台中执行以下代码进行调试

// 1. 检查当前用户信息
console.log('当前全局用户信息:', getApp().globalData.userInfo);
console.log('本地存储用户信息:', wx.getStorageSync('userInfo'));
console.log('本地存储token:', wx.getStorageSync('token'));

// 2. 手动设置正确的用户信息
const correctUserInfo = {{
  id: {user.id},
  username: "{user.username}",
  nickname: "{user.nickname}",
  avatar: "https://thirdwx.qlogo.cn/mmopen/real_user_avatar.jpg",
  phone: {f'"{user.phone}"' if user.phone else 'null'},
  email: "{user.email}",
  gender: {user.gender},
  city: "{user.city}",
  province: "{user.province}",
  country: "{user.country}"
}};

// 3. 更新全局状态和本地存储
getApp().setUserInfo(correctUserInfo);
wx.setStorageSync('userInfo', correctUserInfo);
wx.setStorageSync('token', '{token.key}');

// 4. 刷新当前页面
const currentPage = getCurrentPages()[getCurrentPages().length - 1];
if (currentPage.setData) {{
  currentPage.setData({{ userInfo: correctUserInfo }});
}}

console.log('✅ 用户信息已更新，请检查页面显示');

// 5. 测试API调用
const api = require('../../api/index.js');
api.userApi.getProfile().then(res => {{
  console.log('✅ API响应:', res);
  if (res.code === 200) {{
    console.log('✅ API返回的用户信息:', res.data);
  }}
}}).catch(err => {{
  console.error('❌ API调用失败:', err);
}});
"""
        
        print(f"📋 在小程序开发工具的控制台中执行:")
        print(debug_code)
        
        return debug_code
        
    except (User.DoesNotExist, Token.DoesNotExist):
        print(f"❌ 无法生成调试代码，用户或token不存在")
        return None


def main():
    """主函数"""
    print("🔧 测试特定用户的API")
    print("=" * 60)
    
    # 测试用户资料API
    result = test_user_profile_api()
    
    # 生成curl测试命令
    curl_command = generate_curl_command()
    
    # 生成前端调试代码
    debug_code = generate_frontend_debug_code()
    
    print("\n" + "=" * 60)
    print("✅ API测试完成！")
    
    if result and result.get('success'):
        print(f"\n💡 API测试结果:")
        print(f"   ✅ API正常工作")
        print(f"   ✅ 返回正确的用户信息")
        print(f"   ✅ 微信信息完整")
        print(f"   ✅ 数据一致性良好")
        
        print(f"\n🎯 预期前端显示:")
        print(f"   昵称: \"{result.get('merged_nickname', '茶园投资者')}\"")
        print(f"   头像: {'真实头像' if result.get('merged_avatar') and 'http' in result.get('merged_avatar', '') else '默认头像'}")
        
        print(f"\n🔍 如果前端仍显示默认值，问题在于:")
        print(f"   1. 前端缓存没有清除")
        print(f"   2. 没有重新获取用户信息")
        print(f"   3. 前端代码有其他问题")
        
        print(f"\n📱 建议操作:")
        print(f"   1. 清除小程序缓存")
        print(f"   2. 在个人中心页面下拉刷新")
        print(f"   3. 使用上面的前端调试代码")
        print(f"   4. 重新登录")
    else:
        print(f"\n❌ API测试失败:")
        print(f"   错误: {result.get('error') if result else '未知错误'}")


if __name__ == "__main__":
    main()

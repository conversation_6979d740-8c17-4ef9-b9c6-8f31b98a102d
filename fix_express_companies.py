#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复快递公司编码问题
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from apps.logistics.models import ExpressCompany


def fix_express_companies():
    """修复快递公司编码"""
    print("🔧 修复快递公司编码...")
    
    # 快递100标准编码映射
    company_mappings = [
        # 现有编码 -> 快递100编码
        ("SF", "shunfeng", "顺丰速运"),
        ("YTO", "yuantong", "圆通速递"),
        ("ZTO", "zhongtong", "中通快递"),
        ("STO", "shentong", "申通快递"),
        ("YD", "yunda", "韵达速递"),
        ("HTKY", "huitongkuaidi", "百世快递"),
        ("JD", "jd", "京东物流"),
        ("EMS", "ems", "邮政EMS"),
        ("DBL", "debangkuaidi", "德邦快递"),
        ("HHTT", "tiantian", "天天快递"),
    ]
    
    updated_count = 0
    created_count = 0
    
    for old_code, new_code, name in company_mappings:
        try:
            # 查找现有记录
            company = ExpressCompany.objects.filter(code=old_code).first()
            
            if company:
                # 更新编码
                company.code = new_code
                company.name = name
                company.save()
                print(f"✅ 更新: {old_code} -> {new_code} ({name})")
                updated_count += 1
            else:
                # 检查是否已存在正确编码
                if not ExpressCompany.objects.filter(code=new_code).exists():
                    # 创建新记录
                    ExpressCompany.objects.create(
                        code=new_code,
                        name=name,
                        sort_order=len(company_mappings) + created_count
                    )
                    print(f"➕ 创建: {new_code} ({name})")
                    created_count += 1
                else:
                    print(f"ℹ️ 已存在: {new_code} ({name})")
                    
        except Exception as e:
            print(f"❌ 处理 {old_code} 失败: {e}")
    
    print(f"\n📊 处理结果:")
    print(f"   更新: {updated_count} 个")
    print(f"   创建: {created_count} 个")
    
    # 显示当前所有快递公司
    print(f"\n📦 当前快递公司列表:")
    companies = ExpressCompany.objects.all().order_by('sort_order')
    for company in companies:
        print(f"   - {company.name} ({company.code})")


if __name__ == "__main__":
    fix_express_companies()

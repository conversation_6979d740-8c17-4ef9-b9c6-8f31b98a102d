"""
微信小程序服务
"""
import requests
import json
import logging
from django.conf import settings
from django.core.cache import cache
from apps.system.utils import WechatConfigManager

logger = logging.getLogger(__name__)


class WechatMiniProgramService:
    """微信小程序服务"""
    
    # 微信API地址
    CODE2SESSION_URL = 'https://api.weixin.qq.com/sns/jscode2session'
    ACCESS_TOKEN_URL = 'https://api.weixin.qq.com/cgi-bin/token'
    
    @classmethod
    def get_access_token(cls):
        """获取access_token"""
        cache_key = 'wechat_access_token'
        access_token = cache.get(cache_key)
        
        if access_token:
            return access_token
        
        app_id = WechatConfigManager.get_app_id()
        app_secret = WechatConfigManager.get_app_secret()
        
        if not app_id or not app_secret:
            logger.error('微信AppID或AppSecret未配置')
            return None
        
        try:
            response = requests.get(cls.ACCESS_TOKEN_URL, params={
                'grant_type': 'client_credential',
                'appid': app_id,
                'secret': app_secret
            }, timeout=10)
            
            data = response.json()
            
            if 'access_token' in data:
                access_token = data['access_token']
                expires_in = data.get('expires_in', 7200)
                
                # 缓存access_token，提前5分钟过期
                cache.set(cache_key, access_token, expires_in - 300)
                return access_token
            else:
                logger.error(f'获取access_token失败: {data}')
                return None
                
        except Exception as e:
            logger.error(f'获取access_token异常: {e}')
            return None
    
    @classmethod
    def code2session(cls, js_code):
        """通过code获取session_key和openid"""
        app_id = WechatConfigManager.get_app_id()
        app_secret = WechatConfigManager.get_app_secret()
        
        if not app_id or not app_secret:
            return {
                'success': False,
                'error': '微信配置未完成，请联系管理员'
            }
        
        try:
            response = requests.get(cls.CODE2SESSION_URL, params={
                'appid': app_id,
                'secret': app_secret,
                'js_code': js_code,
                'grant_type': 'authorization_code'
            }, timeout=10)
            
            data = response.json()
            
            if 'openid' in data:
                return {
                    'success': True,
                    'openid': data['openid'],
                    'session_key': data.get('session_key', ''),
                    'unionid': data.get('unionid', '')
                }
            else:
                error_msg = data.get('errmsg', '未知错误')
                logger.error(f'code2session失败: {data}')
                return {
                    'success': False,
                    'error': f'微信授权失败: {error_msg}'
                }
                
        except Exception as e:
            logger.error(f'code2session异常: {e}')
            return {
                'success': False,
                'error': '网络请求失败，请重试'
            }
    
    @classmethod
    def decrypt_user_info(cls, encrypted_data, iv, session_key):
        """解密用户信息"""
        try:
            from Crypto.Cipher import AES
            import base64
            
            # Base64解码
            encrypted_data = base64.b64decode(encrypted_data)
            iv = base64.b64decode(iv)
            session_key = base64.b64decode(session_key)
            
            # AES解密
            cipher = AES.new(session_key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(encrypted_data)
            
            # 去除padding
            padding = decrypted[-1]
            if isinstance(padding, str):
                padding = ord(padding)
            decrypted = decrypted[:-padding]
            
            # 解析JSON
            user_info = json.loads(decrypted.decode('utf-8'))
            
            return {
                'success': True,
                'user_info': user_info
            }
            
        except Exception as e:
            logger.error(f'解密用户信息失败: {e}')
            return {
                'success': False,
                'error': '用户信息解密失败'
            }
    
    @classmethod
    def validate_config(cls):
        """验证微信配置"""
        return WechatConfigManager.validate_config()


class WechatUserManager:
    """微信用户管理器"""
    
    @classmethod
    def create_or_update_user(cls, openid, user_info=None):
        """创建或更新微信用户"""
        from .models import User
        from apps.notifications.models import WechatUserInfo

        try:
            # 先查找是否已有微信用户记录
            try:
                wechat_user = WechatUserInfo.objects.get(openid=openid)
                # 已存在，更新用户信息
                user = wechat_user.user
                is_new_user = False

                # 更新微信用户信息
                if user_info:
                    wechat_user.nickname = user_info.get('nickName', wechat_user.nickname)
                    wechat_user.avatar_url = user_info.get('avatarUrl', wechat_user.avatar_url)
                    wechat_user.gender = user_info.get('gender', wechat_user.gender)
                    wechat_user.city = user_info.get('city', wechat_user.city)
                    wechat_user.province = user_info.get('province', wechat_user.province)
                    wechat_user.country = user_info.get('country', wechat_user.country)
                    wechat_user.save()

                # 更新用户基本信息
                if user_info:
                    user.nickname = user_info.get('nickName', user.nickname)
                    user.gender = user_info.get('gender', user.gender) if user_info.get('gender') in [0, 1, 2] else user.gender
                    user.city = user_info.get('city', user.city)
                    user.province = user_info.get('province', user.province)
                    user.country = user_info.get('country', user.country)
                    user.save()

                    # 更新用户头像到UserProfile
                    avatar_url = user_info.get('avatarUrl')
                    if avatar_url:
                        from .models import UserProfile
                        profile, created = UserProfile.objects.get_or_create(user=user)
                        profile.avatar = avatar_url
                        profile.save()

            except WechatUserInfo.DoesNotExist:
                # 不存在，创建新用户和微信用户信息
                if WechatConfigManager.is_auto_register_enabled():
                    # 先创建用户，确保用户名唯一
                    base_username = f"wx_{openid[:16]}"
                    username = base_username
                    counter = 1
                    while User.objects.filter(username=username).exists():
                        username = f"{base_username}_{counter}"
                        counter += 1

                    user = User.objects.create_user(
                        username=username,
                        nickname=user_info.get('nickName', '') if user_info else '',
                        gender=user_info.get('gender', 0) if user_info and user_info.get('gender') in [0, 1, 2] else 0,
                        city=user_info.get('city', '') if user_info else '',
                        province=user_info.get('province', '') if user_info else '',
                        country=user_info.get('country', '') if user_info else '',
                    )

                    # 创建用户资料并设置头像
                    avatar_url = user_info.get('avatarUrl', '') if user_info else ''
                    if avatar_url:
                        from .models import UserProfile
                        profile = UserProfile.objects.create(
                            user=user,
                            avatar=avatar_url
                        )

                    # 然后创建微信用户信息
                    wechat_user = WechatUserInfo.objects.create(
                        user=user,
                        openid=openid,
                        nickname=user_info.get('nickName', '') if user_info else '',
                        avatar_url=user_info.get('avatarUrl', '') if user_info else '',
                        gender=user_info.get('gender', 0) if user_info else 0,
                        city=user_info.get('city', '') if user_info else '',
                        province=user_info.get('province', '') if user_info else '',
                        country=user_info.get('country', '') if user_info else '',
                        language=user_info.get('language', 'zh_CN') if user_info else 'zh_CN',
                    )

                    is_new_user = True
                else:
                    return {
                        'success': False,
                        'error': '自动注册已关闭，请联系管理员'
                    }

            return {
                'success': True,
                'user': user,
                'wechat_user': wechat_user,
                'is_new_user': is_new_user
            }

        except Exception as e:
            logger.error(f'创建或更新微信用户失败: {e}')
            return {
                'success': False,
                'error': '用户信息处理失败'
            }

    @classmethod
    def create_or_update_user_v2(cls, openid, user_info=None):
        """创建或更新微信用户 - 2025年新版授权适配"""
        from .models import User
        from apps.notifications.models import WechatUserInfo

        try:
            # 先查找是否已有微信用户记录
            try:
                wechat_user = WechatUserInfo.objects.get(openid=openid)
                user = wechat_user.user
                is_new_user = False

                # 更新用户信息（如果提供了新信息）
                if user_info:
                    # 更新微信用户信息
                    if user_info.get('nickName'):
                        wechat_user.nickname = user_info['nickName']
                        user.nickname = user_info['nickName']

                    if user_info.get('avatarUrl'):
                        wechat_user.avatar_url = user_info['avatarUrl']

                        # 更新用户资料头像
                        from .models import UserProfile
                        profile, created = UserProfile.objects.get_or_create(user=user)
                        profile.avatar = user_info['avatarUrl']
                        profile.save()

                    # 保存其他信息
                    wechat_user.gender = user_info.get('gender', 0)
                    wechat_user.city = user_info.get('city', '')
                    wechat_user.province = user_info.get('province', '')
                    wechat_user.country = user_info.get('country', '')
                    wechat_user.language = user_info.get('language', 'zh_CN')

                    wechat_user.save()
                    user.save()

            except WechatUserInfo.DoesNotExist:
                # 创建新用户
                if not WechatConfigManager.is_auto_register_enabled():
                    return {
                        'success': False,
                        'error': '自动注册已关闭，请联系管理员'
                    }

                # 生成唯一用户名
                base_username = f"wx_{openid[:16]}"
                username = base_username
                counter = 1
                while User.objects.filter(username=username).exists():
                    username = f"{base_username}_{counter}"
                    counter += 1

                # 创建用户
                user = User.objects.create_user(
                    username=username,
                    nickname=user_info.get('nickName', '微信用户') if user_info else '微信用户',
                    gender=user_info.get('gender', 0) if user_info else 0,
                    city=user_info.get('city', '') if user_info else '',
                    province=user_info.get('province', '') if user_info else '',
                    country=user_info.get('country', '') if user_info else '',
                )

                # 创建用户资料
                avatar_url = user_info.get('avatarUrl', '') if user_info else ''
                if avatar_url:
                    from .models import UserProfile
                    UserProfile.objects.create(
                        user=user,
                        avatar=avatar_url
                    )

                # 创建微信用户信息
                wechat_user = WechatUserInfo.objects.create(
                    user=user,
                    openid=openid,
                    nickname=user_info.get('nickName', '') if user_info else '',
                    avatar_url=user_info.get('avatarUrl', '') if user_info else '',
                    gender=user_info.get('gender', 0) if user_info else 0,
                    city=user_info.get('city', '') if user_info else '',
                    province=user_info.get('province', '') if user_info else '',
                    country=user_info.get('country', '') if user_info else '',
                    language=user_info.get('language', 'zh_CN') if user_info else 'zh_CN',
                )

                is_new_user = True

            return {
                'success': True,
                'user': user,
                'wechat_user': wechat_user,
                'is_new_user': is_new_user
            }

        except Exception as e:
            logger.error(f'创建或更新微信用户失败: {e}')
            return {
                'success': False,
                'error': '用户信息处理失败'
            }

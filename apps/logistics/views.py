from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.authentication import TokenAuthentication, SessionAuthentication
from django.db.models import Q
from django.utils import timezone
from .models import ExpressCompany, Logistics, LogisticsTrack
from .serializers import (
    ExpressCompanySerializer, LogisticsSerializer, LogisticsCreateSerializer,
    LogisticsTrackSerializer, LogisticsQuerySerializer, LogisticsStatusUpdateSerializer,
    LogisticsStatsSerializer
)
from .services import logistics_service
from .cache import (
    LogisticsDetailCache, ExpressCompanyCache, UserLogisticsCache,
    SyncStatusCache, CacheInvalidator, cache_result
)


class ExpressCompanyViewSet(viewsets.ReadOnlyModelViewSet):
    """快递公司视图集"""

    queryset = ExpressCompany.objects.filter(is_active=True)
    serializer_class = ExpressCompanySerializer
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [AllowAny]  # 快递公司列表允许公开访问
    
    def get_queryset(self):
        return self.queryset.order_by('sort_order', 'name')


class LogisticsViewSet(viewsets.ModelViewSet):
    """物流信息视图集"""

    serializer_class = LogisticsSerializer
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        queryset = Logistics.objects.select_related('express_company', 'order').prefetch_related('tracks')
        
        # 只返回当前用户的物流信息
        queryset = queryset.filter(order__user=self.request.user)
        
        # 状态筛选
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # 快递公司筛选
        company_filter = self.request.query_params.get('company')
        if company_filter:
            queryset = queryset.filter(express_company_id=company_filter)
        
        # 搜索
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(tracking_number__icontains=search) |
                Q(receiver_name__icontains=search) |
                Q(order__order_id__icontains=search)
            )
        
        return queryset.order_by('-created_at')
    
    def get_serializer_class(self):
        """获取序列化器类"""
        if self.action == 'create':
            return LogisticsCreateSerializer
        return LogisticsSerializer
    
    @action(detail=True, methods=['post'])
    def sync_status(self, request, pk=None):
        """同步物流状态"""
        logistics = self.get_object()
        
        success, message = logistics_service.sync_logistics_status(logistics)
        
        if success:
            serializer = self.get_serializer(logistics)
            return Response({
                'code': 200,
                'message': message,
                'data': serializer.data
            })
        else:
            return Response({
                'code': 400,
                'message': message
            }, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['put'])
    def update_status(self, request, pk=None):
        """更新物流状态"""
        logistics = self.get_object()
        serializer = LogisticsStatusUpdateSerializer(data=request.data)
        
        if serializer.is_valid():
            logistics.status = serializer.validated_data['status']
            
            if serializer.validated_data.get('delivered_at'):
                logistics.delivered_at = serializer.validated_data['delivered_at']
            elif logistics.status == 'delivered' and not logistics.delivered_at:
                logistics.delivered_at = timezone.now()
            
            if serializer.validated_data.get('remark'):
                logistics.remark = serializer.validated_data['remark']
            
            logistics.save()
            
            response_serializer = self.get_serializer(logistics)
            return Response({
                'code': 200,
                'message': '状态更新成功',
                'data': response_serializer.data
            })
        
        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def query(self, request):
        """查询物流信息"""
        serializer = LogisticsQuerySerializer(data=request.data)

        if serializer.is_valid():
            tracking_number = serializer.validated_data['tracking_number']
            company_code = serializer.validated_data.get('express_company_code')

            # 查询物流信息
            logistics = logistics_service.query_logistics_by_number(tracking_number, company_code)

            if logistics:
                response_serializer = LogisticsSerializer(logistics)
                return Response({
                    'code': 200,
                    'message': '查询成功',
                    'data': response_serializer.data
                })
            else:
                return Response({
                    'code': 404,
                    'message': '未找到物流信息'
                }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], permission_classes=[IsAuthenticated])
    def test_api(self, request):
        """测试快递100 API连接"""
        # 使用测试单号测试API
        test_tracking_number = 'YT2556998666654'  # 圆通测试单号
        test_company_code = 'yuantong'

        try:
            result = logistics_service.api_service.query_logistics(
                test_tracking_number,
                test_company_code
            )

            if result['success']:
                return Response({
                    'code': 200,
                    'message': 'API连接测试成功',
                    'data': {
                        'api_status': 'connected',
                        'test_result': result['data']
                    }
                })
            else:
                return Response({
                    'code': 400,
                    'message': f'API连接测试失败: {result["message"]}',
                    'data': {
                        'api_status': 'failed',
                        'error': result.get('error_code', 'unknown')
                    }
                })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'API测试异常: {str(e)}',
                'data': {
                    'api_status': 'error'
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], permission_classes=[IsAuthenticated])
    def config_api(self, request):
        """配置快递100 API密钥"""
        customer = request.data.get('customer', '').strip()
        key = request.data.get('key', '').strip()

        if not customer or not key:
            return Response({
                'code': 400,
                'message': '请提供完整的API密钥信息'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            from .models import LogisticsConfig
            config = LogisticsConfig.get_config()
            config.api_key = customer
            config.api_secret = key
            config.save()

            # 测试API连接
            test_result = logistics_service.api_service.query_logistics(
                'YT2556998666654',  # 测试单号
                'yuantong'
            )

            return Response({
                'code': 200,
                'message': 'API密钥配置成功',
                'data': {
                    'config_status': 'success',
                    'test_result': test_result
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'配置失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """获取物流统计"""
        stats = logistics_service.get_logistics_stats(user=request.user)
        serializer = LogisticsStatsSerializer(stats)
        
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })
    
    @action(detail=False, methods=['get'])
    def my_logistics(self, request):
        """我的物流"""
        queryset = self.get_queryset()
        
        # 分页
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response({
                'code': 200,
                'message': '获取成功',
                'data': {
                    'results': serializer.data,
                    'count': queryset.count()
                }
            })

    @action(detail=True, methods=['get'])
    def tracks(self, request, pk=None):
        """获取物流轨迹"""
        try:
            # 获取物流信息
            logistics = self.get_object()

            # 获取轨迹数据
            tracks = LogisticsTrack.objects.filter(logistics=logistics).order_by('-timestamp')
            serializer = LogisticsTrackSerializer(tracks, many=True)

            return Response({
                'code': 200,
                'message': '获取成功',
                'data': serializer.data
            })

        except Logistics.DoesNotExist:
            return Response({
                'code': 404,
                'message': '物流信息不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取轨迹失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'results': serializer.data,
                'count': queryset.count()
            }
        })


class LogisticsTrackViewSet(viewsets.ReadOnlyModelViewSet):
    """物流轨迹视图集"""
    
    serializer_class = LogisticsTrackSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        logistics_id = self.request.query_params.get('logistics_id')
        if logistics_id:
            # 确保只能查看自己的物流轨迹
            return LogisticsTrack.objects.filter(
                logistics_id=logistics_id,
                logistics__order__user=self.request.user
            ).order_by('-timestamp')
        
        return LogisticsTrack.objects.none()
    
    @action(detail=False, methods=['get'])
    def by_logistics(self, request):
        """根据物流ID获取轨迹"""
        logistics_id = request.query_params.get('logistics_id')
        
        if not logistics_id:
            return Response({
                'code': 400,
                'message': '缺少物流ID参数'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证物流信息是否属于当前用户
        try:
            logistics = Logistics.objects.get(
                id=logistics_id,
                order__user=request.user
            )
        except Logistics.DoesNotExist:
            return Response({
                'code': 404,
                'message': '物流信息不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        tracks = LogisticsTrack.objects.filter(logistics=logistics).order_by('-timestamp')
        serializer = self.get_serializer(tracks, many=True)
        
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })

    @action(detail=True, methods=['post'], url_path='sync')
    def sync_status(self, request, pk=None):
        """手动同步物流状态"""
        try:
            logistics = self.get_object()

            # 检查是否属于当前用户
            if logistics.order.user != request.user:
                return Response({
                    'code': 403,
                    'message': '无权限操作此物流信息'
                }, status=status.HTTP_403_FORBIDDEN)

            # 执行同步
            success, message = logistics_service.sync_logistics_status(logistics)

            if success:
                # 重新获取更新后的数据
                logistics.refresh_from_db()
                serializer = self.get_serializer(logistics)

                return Response({
                    'code': 200,
                    'message': message,
                    'data': serializer.data
                })
            else:
                return Response({
                    'code': 400,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'同步失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], url_path='sync-all')
    def sync_all_active(self, request):
        """批量同步用户的活跃物流"""
        try:
            # 获取用户的活跃物流
            user_logistics = Logistics.objects.filter(
                order__user=request.user,
                status__in=['pending', 'shipped', 'in_transit', 'out_for_delivery']
            )

            if not user_logistics.exists():
                return Response({
                    'code': 200,
                    'message': '没有需要同步的物流',
                    'data': {'synced_count': 0, 'total_count': 0}
                })

            # 执行批量同步
            results = logistics_service.batch_sync_logistics(user_logistics)

            # 统计结果
            success_count = sum(1 for r in results if r['success'])
            total_count = len(results)

            return Response({
                'code': 200,
                'message': f'同步完成，成功 {success_count}/{total_count}',
                'data': {
                    'synced_count': success_count,
                    'total_count': total_count,
                    'results': results
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'批量同步失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'], url_path='sync-status')
    def get_sync_status(self, request):
        """获取同步状态统计"""
        try:
            from django.db.models import Count, Q
            from datetime import timedelta

            now = timezone.now()
            last_24h = now - timedelta(hours=24)
            last_hour = now - timedelta(hours=1)

            # 用户物流统计
            user_logistics = Logistics.objects.filter(order__user=request.user)

            stats = {
                'total_logistics': user_logistics.count(),
                'active_logistics': user_logistics.filter(
                    status__in=['pending', 'shipped', 'in_transit', 'out_for_delivery']
                ).count(),
                'never_synced': user_logistics.filter(last_sync_at__isnull=True).count(),
                'synced_last_24h': user_logistics.filter(last_sync_at__gte=last_24h).count(),
                'synced_last_hour': user_logistics.filter(last_sync_at__gte=last_hour).count(),
                'status_distribution': {}
            }

            # 状态分布
            status_stats = user_logistics.values('status').annotate(count=Count('id'))
            for stat in status_stats:
                stats['status_distribution'][stat['status']] = stat['count']

            return Response({
                'code': 200,
                'message': '获取成功',
                'data': stats
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取同步状态失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

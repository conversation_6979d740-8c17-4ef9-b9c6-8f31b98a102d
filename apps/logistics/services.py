import requests
import json
import hashlib
import time
import logging
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from typing import Dict, List, Optional, Tuple
from .models import Logistics, LogisticsTrack, LogisticsConfig, ExpressCompany
from .exceptions import (
    APIException, SyncException, ValidationException,
    RateLimitException, ConfigurationException, retry_on_exception
)


class ExpressAPIService:
    """快递API服务基类"""
    
    def __init__(self):
        self.config = LogisticsConfig.get_config()
    
    def query_logistics(self, tracking_number, company_code):
        """查询物流信息"""
        raise NotImplementedError
    
    def parse_tracks(self, data):
        """解析轨迹数据"""
        raise NotImplementedError


class Kuaidi100Service(ExpressAPIService):
    """快递100 API服务"""

    BASE_URL = 'https://poll.kuaidi100.com/poll/query.do'

    def get_api_credentials(self):
        """获取API凭证，支持临时测试配置"""
        # 优先使用数据库配置
        if self.config and self.config.api_key and self.config.api_secret:
            customer = self.config.api_key.strip()  # customer (授权码)
            key = self.config.api_secret.strip()    # key (密钥)

            if customer and key:
                return customer, key

        # 如果数据库未配置，使用临时测试配置（您登录后可以替换这里）
        # TODO: 请在快递100官网获取正式API密钥后替换以下测试配置
        test_customer = ""  # 您的快递100 customer
        test_key = ""       # 您的快递100 key

        if test_customer and test_key:
            return test_customer, test_key

        return None, None

    @retry_on_exception(
        exceptions=(requests.exceptions.RequestException,),
        max_retries=3,
        delay=1.0,
        backoff=2.0
    )
    def query_logistics(self, tracking_number, company_code, phone=None):
        """查询物流信息（带重试机制）"""
        try:
            # 获取API凭证
            customer, key = self.get_api_credentials()
            if not customer or not key:
                return {
                    'success': False,
                    'message': '快递100 API配置不完整，请在后台配置API密钥或联系管理员'
                }

            # 构建请求参数
            param = {
                'com': company_code.lower(),  # 快递公司编码必须小写
                'num': tracking_number,
                'resultv2': '4'  # 开通行政解析功能以及物流轨迹增加物流高级状态名称
            }

            # 添加可选参数
            if phone:
                param['phone'] = phone

            # 生成签名
            param_str = json.dumps(param, separators=(',', ':'), ensure_ascii=False)
            sign = self._generate_sign(param_str)

            # 发送请求
            data = {
                'customer': customer,
                'sign': sign,
                'param': param_str
            }

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }

            response = requests.post(self.BASE_URL, data=data, headers=headers, timeout=10)
            response.raise_for_status()

            result = response.json()

            # 检查返回状态
            if result.get('status') == '200':
                return {
                    'success': True,
                    'data': result,
                    'message': result.get('message', '查询成功')
                }
            else:
                # 详细的错误处理
                error_code = result.get('returnCode', result.get('status', 'unknown'))
                error_message = result.get('message', '查询失败')

                # 根据错误码提供更友好的错误信息
                friendly_message = self._get_friendly_error_message(error_code, error_message)

                return {
                    'success': False,
                    'message': friendly_message,
                    'error_code': error_code,
                    'original_message': error_message
                }

        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'message': f'网络请求失败: {str(e)}'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'查询异常: {str(e)}'
            }
    
    def _generate_sign(self, param):
        """生成签名
        按照快递100文档要求：param + key + customer 的顺序进行MD5加密，转32位大写
        """
        customer, key = self.get_api_credentials()
        if not customer or not key:
            raise ConfigurationException("快递100 API凭证未配置")

        # 按照官方文档要求的顺序
        sign_str = param + key + customer
        sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()

        # 调试日志
        logging.debug(f"快递100签名字符串: {sign_str[:50]}...")
        logging.debug(f"快递100生成签名: {sign}")

        return sign

    def _get_friendly_error_message(self, error_code, original_message):
        """获取友好的错误信息"""
        error_messages = {
            '400': '请求参数错误或账号未充值，请检查快递公司编码和API配置',
            '408': '快递公司参数异常，顺丰快递需要提供收件人手机号',
            '500': '未查询到物流信息，请确认快递单号和快递公司是否正确',
            '501': '服务器错误，请稍后重试',
            '502': '服务器繁忙，请稍后重试',
            '503': '验证签名失败，请检查API密钥配置',
            '601': 'API密钥已过期，请充值或联系快递100客服',
        }

        friendly_message = error_messages.get(str(error_code), original_message)
        return f"{friendly_message} (错误码: {error_code})"

    def parse_tracks(self, data):
        """解析轨迹数据"""
        tracks = []
        track_list = data.get('data', [])

        for track in track_list:
            # 解析时间
            timestamp = self._parse_time(track.get('time', ''))

            # 解析状态
            status_code = track.get('statusCode', '0')
            status_name = track.get('status', '在途')

            # 解析位置信息
            location = track.get('location', '')
            area_name = track.get('areaName', '')
            if not location and area_name:
                location = area_name.replace(',', ' ')

            tracks.append({
                'location': location,
                'description': track.get('context', ''),
                'timestamp': timestamp,
                'status': self._map_status_code(status_code),
                'status_name': status_name,
                'status_code': status_code,
                'area_code': track.get('areaCode', ''),
                'area_name': area_name,
                'operator': ''
            })

        return tracks
    
    def _parse_time(self, time_str):
        """解析时间"""
        try:
            return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
        except:
            return timezone.now()
    
    def _parse_status(self, context):
        """解析状态"""
        if '已签收' in context or '签收' in context:
            return 'delivered'
        elif '派送' in context or '正在派送' in context:
            return 'out_for_delivery'
        elif '运输' in context or '在途' in context:
            return 'in_transit'
        elif '已发货' in context or '已收寄' in context:
            return 'shipped'
        elif '异常' in context or '滞留' in context:
            return 'exception'
        else:
            return 'in_transit'

    def _map_status_code(self, status_code):
        """映射快递100状态码到系统状态"""
        status_mapping = {
            '0': 'in_transit',      # 在途
            '1': 'shipped',         # 揽收
            '2': 'exception',       # 疑难
            '3': 'delivered',       # 签收
            '4': 'returned',        # 退签
            '5': 'out_for_delivery', # 派件
            '6': 'returned',        # 退回
            '7': 'in_transit',      # 转投
            '8': 'in_transit',      # 清关
            '10': 'in_transit',     # 待清关
            '11': 'in_transit',     # 清关中
            '12': 'in_transit',     # 已清关
            '13': 'exception',      # 清关异常
            '14': 'returned',       # 拒签
        }
        return status_mapping.get(str(status_code), 'in_transit')


class KuaidiNiaoService(ExpressAPIService):
    """快递鸟API服务"""
    
    BASE_URL = 'https://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx'
    
    def query_logistics(self, tracking_number, company_code):
        """查询物流信息"""
        try:
            # 构建请求参数
            request_data = {
                'OrderCode': '',
                'ShipperCode': company_code,
                'LogisticCode': tracking_number
            }
            
            data_sign = self._generate_sign(json.dumps(request_data))
            
            params = {
                'RequestData': json.dumps(request_data),
                'EBusinessID': self.config.api_key,
                'RequestType': '1002',
                'DataSign': data_sign,
                'DataType': '2'
            }
            
            response = requests.post(self.BASE_URL, data=params, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('Success'):
                return {
                    'success': True,
                    'data': result,
                    'message': result.get('Reason', '')
                }
            else:
                return {
                    'success': False,
                    'message': result.get('Reason', '查询失败')
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'查询异常: {str(e)}'
            }
    
    def _generate_sign(self, request_data):
        """生成签名"""
        sign_str = request_data + self.config.api_secret
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    
    def parse_tracks(self, data):
        """解析轨迹数据"""
        tracks = []
        track_list = data.get('Traces', [])
        
        for track in track_list:
            tracks.append({
                'location': track.get('AcceptStation', ''),
                'description': track.get('AcceptStation', ''),
                'timestamp': self._parse_time(track.get('AcceptTime', '')),
                'status': self._parse_status(track.get('AcceptStation', '')),
                'operator': ''
            })
        
        return tracks
    
    def _parse_time(self, time_str):
        """解析时间"""
        try:
            return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
        except:
            return timezone.now()
    
    def _parse_status(self, context):
        """解析状态"""
        if '已签收' in context or '签收' in context:
            return 'delivered'
        elif '派送' in context or '正在派送' in context:
            return 'out_for_delivery'
        elif '运输' in context or '在途' in context:
            return 'in_transit'
        elif '已发货' in context or '已收寄' in context:
            return 'shipped'
        elif '异常' in context or '滞留' in context:
            return 'exception'
        else:
            return 'in_transit'


class LogisticsService:
    """物流服务类"""

    def __init__(self):
        self._config = None
        self._api_service = None

    @property
    def config(self):
        """延迟加载配置"""
        if self._config is None:
            self._config = LogisticsConfig.get_config()
        return self._config

    @property
    def api_service(self):
        """延迟加载API服务"""
        if self._api_service is None:
            self._api_service = self._get_api_service()
        return self._api_service
    
    def _get_api_service(self):
        """获取API服务实例"""
        if self.config.api_provider == 'kuaidi100':
            return Kuaidi100Service()
        elif self.config.api_provider == 'kuaidiniao':
            return KuaidiNiaoService()
        else:
            return Kuaidi100Service()  # 默认使用快递100
    
    def sync_logistics_status(self, logistics):
        """同步物流状态"""
        try:
            # 查询最新物流信息
            result = self.api_service.query_logistics(
                logistics.tracking_number,
                logistics.express_company.api_code or logistics.express_company.code
            )
            
            if not result['success']:
                return False, result['message']
            
            # 解析轨迹数据
            tracks = self.api_service.parse_tracks(result['data'])
            
            # 更新物流轨迹
            self._update_tracks(logistics, tracks)
            
            # 更新物流状态
            if tracks:
                latest_track = tracks[0]  # 最新轨迹
                logistics.status = latest_track['status']
                
                # 如果是已签收状态，更新签收时间
                if latest_track['status'] == 'delivered' and not logistics.delivered_at:
                    logistics.delivered_at = latest_track['timestamp']
            
            logistics.last_sync_at = timezone.now()
            logistics.save()
            
            return True, '同步成功'
            
        except Exception as e:
            return False, f'同步失败: {str(e)}'

    def batch_sync_logistics(self, logistics_list):
        """批量同步物流状态"""
        results = []

        for logistics in logistics_list:
            try:
                success, message = self.sync_logistics_status(logistics)
                results.append({
                    'logistics_id': logistics.id,
                    'tracking_number': logistics.tracking_number,
                    'success': success,
                    'message': message
                })

                # 避免API频率限制
                import time
                time.sleep(0.5)

            except Exception as e:
                logger.error(f"批量同步异常: {logistics.tracking_number} - {str(e)}")
                results.append({
                    'logistics_id': logistics.id,
                    'tracking_number': logistics.tracking_number,
                    'success': False,
                    'message': f'异常: {str(e)}'
                })

        return results

    def auto_sync_active_logistics(self):
        """自动同步活跃物流"""
        from django.db.models import Q
        from datetime import timedelta

        # 获取配置
        config = LogisticsConfig.get_config()
        sync_interval = timedelta(minutes=config.sync_interval)
        cutoff_time = timezone.now() - sync_interval

        # 查找需要同步的物流
        logistics_to_sync = Logistics.objects.filter(
            status__in=['pending', 'shipped', 'in_transit', 'out_for_delivery']
        ).filter(
            Q(last_sync_at__isnull=True) | Q(last_sync_at__lt=cutoff_time)
        )

        logger.info(f"找到 {logistics_to_sync.count()} 个需要同步的物流")

        if logistics_to_sync.exists():
            # 使用Celery异步处理
            try:
                from .tasks import sync_all_active_logistics
                task = sync_all_active_logistics.delay()
                return True, f"已启动批量同步任务: {task.id}"
            except ImportError:
                # 如果没有Celery，直接同步
                results = self.batch_sync_logistics(logistics_to_sync)
                success_count = sum(1 for r in results if r['success'])
                return True, f"同步完成: {success_count}/{len(results)}"
        else:
            return True, "没有需要同步的物流"

    def _update_tracks(self, logistics, tracks):
        """更新物流轨迹"""
        for track_data in tracks:
            # 检查轨迹是否已存在
            existing_track = LogisticsTrack.objects.filter(
                logistics=logistics,
                timestamp=track_data['timestamp'],
                description=track_data['description']
            ).first()
            
            if not existing_track:
                LogisticsTrack.objects.create(
                    logistics=logistics,
                    location=track_data['location'],
                    description=track_data['description'],
                    status=track_data['status'],
                    timestamp=track_data['timestamp'],
                    operator=track_data['operator']
                )
    
    def query_logistics_by_number(self, tracking_number, company_code=None):
        """根据单号查询物流"""
        # 先从数据库查询
        logistics = Logistics.objects.filter(tracking_number=tracking_number).first()
        
        if logistics:
            # 如果距离上次同步超过30分钟，重新同步
            if (not logistics.last_sync_at or 
                (timezone.now() - logistics.last_sync_at).total_seconds() > 1800):
                self.sync_logistics_status(logistics)
            return logistics
        
        # 如果数据库中没有，尝试从API查询
        if company_code:
            result = self.api_service.query_logistics(tracking_number, company_code)
            if result['success']:
                # 这里可以创建临时的物流信息用于展示
                pass
        
        return None
    
    def get_logistics_stats(self, user=None):
        """获取物流统计"""
        queryset = Logistics.objects.all()
        
        if user:
            queryset = queryset.filter(order__user=user)
        
        stats = {
            'total': queryset.count(),
            'pending': queryset.filter(status='pending').count(),
            'shipped': queryset.filter(status='shipped').count(),
            'in_transit': queryset.filter(status='in_transit').count(),
            'out_for_delivery': queryset.filter(status='out_for_delivery').count(),
            'delivered': queryset.filter(status='delivered').count(),
            'returned': queryset.filter(status='returned').count(),
            'exception': queryset.filter(status='exception').count(),
        }
        
        return stats


# 导出服务实例
logistics_service = LogisticsService()

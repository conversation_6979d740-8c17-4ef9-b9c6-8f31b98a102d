#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试支付API中的openid获取
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.notifications.models import WechatUserInfo

User = get_user_model()


def test_payment_openid_logic():
    """测试支付中的openid获取逻辑"""
    print("🔧 测试支付API中的openid获取逻辑...")
    
    username = 'wx_oXYVEvjxO1wJ_itf'
    
    try:
        user = User.objects.get(username=username)
        print(f"✅ 找到用户: {user.username}")
        
        # 模拟支付代码中的openid获取逻辑
        user_openid = None
        
        # 尝试从微信用户表获取openid（修复后的逻辑）
        try:
            wechat_user = WechatUserInfo.objects.get(user=user)
            user_openid = wechat_user.openid
            print(f"✅ 从WechatUserInfo获取到openid: {user_openid}")
        except WechatUserInfo.DoesNotExist:
            print(f"❌ WechatUserInfo中没有找到用户记录")
        except Exception as e:
            print(f"❌ 获取用户微信信息失败: {e}")
        
        if not user_openid:
            print(f"❌ 用户未绑定微信，无法使用微信支付")
            return False
        else:
            print(f"✅ 用户已绑定微信，可以使用微信支付")
            print(f"   OpenID: {user_openid}")
            return True
            
    except User.DoesNotExist:
        print(f"❌ 用户 {username} 不存在")
        return False


def test_all_users_openid():
    """测试所有用户的openid状态"""
    print(f"\n🔍 检查所有用户的微信绑定状态...")
    
    users = User.objects.all()
    print(f"总用户数: {users.count()}")
    
    for user in users:
        try:
            wechat_user = WechatUserInfo.objects.get(user=user)
            print(f"✅ {user.username}: 已绑定微信 (OpenID: {wechat_user.openid})")
        except WechatUserInfo.DoesNotExist:
            print(f"❌ {user.username}: 未绑定微信")


def create_test_order_and_payment():
    """创建测试订单和支付"""
    print(f"\n🧪 创建测试订单和支付...")
    
    username = 'wx_oXYVEvjxO1wJ_itf'
    
    try:
        user = User.objects.get(username=username)
        
        # 检查是否有现有订单
        from apps.orders.models import Order
        
        existing_orders = Order.objects.filter(user=user).order_by('-created_at')[:3]
        
        if existing_orders:
            print(f"✅ 找到用户的现有订单:")
            for order in existing_orders:
                print(f"   订单ID: {order.order_id}")
                print(f"   状态: {order.status}")
                print(f"   金额: {order.total_amount}")
                print(f"   创建时间: {order.created_at}")
                print(f"   ---")
            
            # 使用第一个订单进行测试
            test_order = existing_orders[0]
            
            # 模拟支付创建逻辑
            print(f"🔄 模拟为订单 {test_order.order_id} 创建支付...")
            
            # 获取用户openid
            try:
                wechat_user = WechatUserInfo.objects.get(user=user)
                user_openid = wechat_user.openid
                
                print(f"✅ 支付参数准备:")
                print(f"   用户: {user.username}")
                print(f"   订单ID: {test_order.order_id}")
                print(f"   订单金额: {test_order.total_amount}")
                print(f"   用户OpenID: {user_openid}")
                print(f"   支付方式: wechat")
                
                print(f"✅ 支付创建应该成功")
                return True
                
            except WechatUserInfo.DoesNotExist:
                print(f"❌ 用户未绑定微信，支付创建会失败")
                return False
        else:
            print(f"❌ 用户没有订单，无法测试支付")
            return False
            
    except User.DoesNotExist:
        print(f"❌ 用户 {username} 不存在")
        return False


def main():
    """主函数"""
    print("🔧 测试支付API中的openid获取")
    print("=" * 60)
    
    # 测试特定用户的openid获取
    result1 = test_payment_openid_logic()
    
    # 测试所有用户的微信绑定状态
    test_all_users_openid()
    
    # 创建测试订单和支付
    result2 = create_test_order_and_payment()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成！")
    
    print(f"\n💡 测试结果:")
    print(f"   特定用户openid获取: {'✅ 成功' if result1 else '❌ 失败'}")
    print(f"   支付创建模拟: {'✅ 成功' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print(f"\n🎯 支付API修复成功！")
        print(f"   用户可以正常使用微信支付")
    else:
        print(f"\n❌ 支付API仍有问题")
        print(f"   需要进一步检查")


if __name__ == "__main__":
    main()

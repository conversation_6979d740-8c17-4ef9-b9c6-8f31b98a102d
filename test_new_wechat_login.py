#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新版微信登录API - 适配2025年授权政策
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth import get_user_model
from api.v1.views import wechat_login

User = get_user_model()


def test_new_wechat_login_api():
    """测试新版微信登录API"""
    print("🧪 测试新版微信登录API（适配2025年授权政策）...")
    
    factory = RequestFactory()
    
    # 模拟前端新版登录请求（用户手动选择头像和输入昵称）
    login_data = {
        'code': 'test_new_auth_2025',
        'userInfo': {
            'nickName': '2025新版授权用户',
            'avatarUrl': 'https://thirdwx.qlogo.cn/mmopen/new_auth_2025_avatar.jpg',
            'gender': 0,  # 新版API无法获取性别
            'city': '',   # 新版API无法获取城市
            'province': '', # 新版API无法获取省份
            'country': '',  # 新版API无法获取国家
            'language': 'zh_CN'
        }
    }
    
    print(f"📤 新版登录请求数据:")
    print(f"   Code: {login_data['code']}")
    print(f"   用户信息: {json.dumps(login_data['userInfo'], ensure_ascii=False, indent=2)}")
    
    # 创建POST请求
    request = factory.post(
        '/api/v1/users/login/',
        data=json.dumps(login_data),
        content_type='application/json'
    )
    
    try:
        # 调用登录视图
        response = wechat_login(request)
        
        print(f"\n📥 登录API响应:")
        print(f"   状态码: {response.status_code}")
        
        if hasattr(response, 'data'):
            response_data = response.data
            print(f"   响应代码: {response_data.get('code')}")
            print(f"   响应消息: {response_data.get('message')}")
            
            if response_data.get('code') == 200:
                print(f"\n✅ 新版登录成功!")
                user_data = response_data['data']['user']
                wechat_data = response_data['data']['wechat_info']
                token = response_data['data']['token']
                
                print(f"   Token: {token}")
                print(f"   用户ID: {user_data.get('id')}")
                print(f"   用户名: {user_data.get('username')}")
                print(f"   用户昵称: {user_data.get('nickname')}")
                print(f"   用户头像: {user_data.get('avatar')}")
                print(f"   微信昵称: {wechat_data.get('nickname')}")
                print(f"   微信头像: {wechat_data.get('avatar_url')}")
                
                # 验证数据一致性
                print(f"\n🔍 数据一致性验证:")
                nickname_match = user_data.get('nickname') == wechat_data.get('nickname')
                avatar_match = user_data.get('avatar') == wechat_data.get('avatar_url')
                
                print(f"   昵称一致: {'✅' if nickname_match else '❌'}")
                print(f"   头像一致: {'✅' if avatar_match else '❌'}")
                
                # 模拟前端合并逻辑
                merged_nickname = wechat_data.get('nickname') or user_data.get('nickname')
                merged_avatar = wechat_data.get('avatar_url') or user_data.get('avatar')
                
                print(f"\n🔄 前端合并结果:")
                print(f"   最终昵称: {merged_nickname}")
                print(f"   最终头像: {merged_avatar}")
                
                if merged_nickname and merged_nickname != '微信用户':
                    print(f"   ✅ 昵称正常，不会显示默认值")
                else:
                    print(f"   ❌ 昵称异常，会显示默认值")
                
                if merged_avatar and 'http' in merged_avatar:
                    print(f"   ✅ 头像正常，不会显示默认头像")
                else:
                    print(f"   ❌ 头像异常，会显示默认头像")
                
                return {
                    'success': True,
                    'user_data': user_data,
                    'wechat_data': wechat_data,
                    'merged_nickname': merged_nickname,
                    'merged_avatar': merged_avatar
                }
            else:
                print(f"❌ 新版登录失败: {response_data.get('message')}")
                return {'success': False, 'error': response_data.get('message')}
        
    except Exception as e:
        print(f"❌ 登录API调用异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}


def check_wechat_user_admin():
    """检查微信用户管理页面数据"""
    print(f"\n🔍 检查微信用户管理页面数据...")
    
    from apps.notifications.models import WechatUserInfo
    
    wechat_users = WechatUserInfo.objects.all().order_by('-created_at')
    
    print(f"微信用户总数: {wechat_users.count()}")
    
    for wu in wechat_users:
        print(f"  用户: {wu.user.username}")
        print(f"    OpenID: {wu.openid}")
        print(f"    昵称: '{wu.nickname}'")
        print(f"    头像: '{wu.avatar_url}'")
        print(f"    性别: {wu.gender}")
        print(f"    城市: '{wu.city}'")
        print(f"    创建时间: {wu.created_at}")
        print(f"    ---")
    
    if wechat_users.count() > 0:
        print(f"✅ 微信用户管理页面应该有数据显示")
        return True
    else:
        print(f"❌ 微信用户管理页面为空")
        return False


def test_old_vs_new_comparison():
    """对比新旧版本的差异"""
    print(f"\n📊 新旧版本对比分析...")
    
    print(f"旧版本（已废弃）:")
    print(f"  - 使用 wx.getUserProfile() 获取完整用户信息")
    print(f"  - 可以获取性别、城市、省份、国家等详细信息")
    print(f"  - 用户一次授权即可获取所有信息")
    print(f"  - 2022年11月8日24时后停止服务")
    
    print(f"\n新版本（2025年当前）:")
    print(f"  - 使用 open-type='chooseAvatar' 手动选择头像")
    print(f"  - 使用 type='nickname' 手动输入昵称")
    print(f"  - 无法获取性别、城市、省份、国家等信息")
    print(f"  - 需要用户主动交互，不能自动获取")
    print(f"  - 微信会对头像和昵称进行安全检测")
    
    print(f"\n影响分析:")
    print(f"  ✅ 优点: 更好的隐私保护，用户主动选择")
    print(f"  ❌ 缺点: 用户体验略有下降，信息获取受限")
    print(f"  🔧 解决方案: 引导用户完善信息，提供默认值")


def generate_frontend_integration_guide():
    """生成前端集成指南"""
    print(f"\n📱 前端集成指南...")
    
    print(f"1. 修改登录页面模板 (login.wxml):")
    print(f"   - 添加头像选择按钮: open-type='chooseAvatar'")
    print(f"   - 添加昵称输入框: type='nickname'")
    print(f"   - 添加确认登录按钮")
    
    print(f"\n2. 修改登录页面逻辑 (login.js):")
    print(f"   - 添加 onChooseAvatar 方法处理头像选择")
    print(f"   - 添加 onNicknameInput 方法处理昵称输入")
    print(f"   - 修改登录流程，先收集用户信息再调用API")
    
    print(f"\n3. 修改登录页面样式 (login.wxss):")
    print(f"   - 添加头像选择区域样式")
    print(f"   - 添加昵称输入框样式")
    print(f"   - 添加弹窗样式")
    
    print(f"\n4. 测试流程:")
    print(f"   - 清除小程序缓存")
    print(f"   - 测试头像选择功能")
    print(f"   - 测试昵称输入功能")
    print(f"   - 测试完整登录流程")


def main():
    """主函数"""
    print("🔧 测试新版微信登录API（适配2025年授权政策）")
    print("=" * 60)
    
    # 1. 测试新版微信登录API
    result = test_new_wechat_login_api()
    
    # 2. 检查微信用户管理页面数据
    admin_ok = check_wechat_user_admin()
    
    # 3. 对比新旧版本差异
    test_old_vs_new_comparison()
    
    # 4. 生成前端集成指南
    generate_frontend_integration_guide()
    
    print("\n" + "=" * 60)
    print("✅ 新版微信登录API测试完成！")
    
    print(f"\n💡 测试结果总结:")
    print(f"   新版登录API: {'✅ 正常' if result and result.get('success') else '❌ 异常'}")
    print(f"   微信用户管理: {'✅ 有数据' if admin_ok else '❌ 无数据'}")
    
    if result and result.get('success'):
        print(f"\n🎯 预期前端显示:")
        print(f"   个人中心昵称: {result.get('merged_nickname', '微信用户')}")
        print(f"   个人中心头像: {'真实头像' if result.get('merged_avatar') and 'http' in result.get('merged_avatar', '') else '默认头像'}")
        print(f"   后台微信用户: 显示完整信息")
    
    print(f"\n📋 下一步操作:")
    print(f"1. 按照前端集成指南修改小程序代码")
    print(f"2. 测试新版用户授权流程")
    print(f"3. 验证前后端数据同步")
    print(f"4. 检查后台微信用户管理页面")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试用户并添加订单数据
"""

import os
import sys
import django
from decimal import Decimal

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from apps.users.models import UserProfile
from apps.orders.models import Order
from apps.logistics.models import Logistics, ExpressCompany, LogisticsTrack
from apps.tea_fields.models import TeaField
from apps.notifications.models import WechatUserInfo
from django.utils import timezone
from datetime import timedelta

User = get_user_model()


def create_test_user():
    """创建测试用户"""
    print("👤 创建测试用户...")
    
    # 创建普通用户
    test_user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'nickname': '测试用户',
            'phone': '13800138001',
            'email': '<EMAIL>',
            'gender': 1,
            'city': '深圳',
            'province': '广东',
            'country': '中国'
        }
    )
    
    if created:
        test_user.set_password('123456')
        test_user.save()
        print(f"✅ 创建用户: {test_user.username}")
    else:
        print(f"ℹ️ 用户已存在: {test_user.username}")
    
    # 创建Token
    token, created = Token.objects.get_or_create(user=test_user)
    if created:
        print(f"✅ 创建Token: {token.key}")
    else:
        print(f"ℹ️ Token已存在: {token.key}")
    
    # 创建用户资料
    profile, created = UserProfile.objects.get_or_create(
        user=test_user,
        defaults={
            'real_name': '测试用户',
            'address': '广东省深圳市南山区测试地址123号',
            'total_investment': Decimal('1000.00'),
            'total_earnings': Decimal('150.00'),
            'risk_level': 'medium'
        }
    )
    
    if created:
        print(f"✅ 创建用户资料")
    else:
        print(f"ℹ️ 用户资料已存在")
    
    # 创建微信用户信息
    wechat_info, created = WechatUserInfo.objects.get_or_create(
        user=test_user,
        defaults={
            'openid': 'test_openid_123456789',
            'nickname': '测试用户',
            'avatar_url': 'https://thirdwx.qlogo.cn/mmopen/test.jpg',
            'gender': 1,
            'city': '深圳',
            'province': '广东',
            'country': '中国',
            'session_key': 'test_session_key_123',
            'last_login_time': timezone.now()
        }
    )
    
    if created:
        print(f"✅ 创建微信信息: {wechat_info.openid}")
    else:
        print(f"ℹ️ 微信信息已存在: {wechat_info.openid}")
    
    return test_user


def create_test_orders(user):
    """为用户创建测试订单"""
    print(f"\n🛒 为用户 {user.username} 创建测试订单...")
    
    # 获取茶地
    tea_field = TeaField.objects.first()
    if not tea_field:
        print("❌ 没有找到茶地，无法创建订单")
        return []
    
    print(f"📍 使用茶地: {tea_field.name}")
    
    # 创建多个订单
    orders_data = [
        {
            'quantity': Decimal('1.0'),
            'unit_price': Decimal('299.00'),
            'total_amount': Decimal('299.00'),
            'status': 'paid',
            'days_ago': 5
        },
        {
            'quantity': Decimal('2.0'),
            'unit_price': Decimal('399.00'),
            'total_amount': Decimal('798.00'),
            'status': 'shipped',
            'days_ago': 3
        },
        {
            'quantity': Decimal('0.5'),
            'unit_price': Decimal('199.00'),
            'total_amount': Decimal('99.50'),
            'status': 'pending',
            'days_ago': 1
        }
    ]
    
    created_orders = []
    
    for i, order_data in enumerate(orders_data):
        # 检查是否已存在订单
        existing_order = Order.objects.filter(
            user=user,
            total_amount=order_data['total_amount']
        ).first()
        
        if existing_order:
            print(f"ℹ️ 订单已存在: {existing_order.order_id}")
            created_orders.append(existing_order)
            continue
        
        # 创建订单
        order = Order.objects.create(
            user=user,
            tea_field=tea_field,
            quantity=order_data['quantity'],
            unit_price=order_data['unit_price'],
            total_amount=order_data['total_amount'],
            status=order_data['status'],
            contact_name=user.nickname or user.username,
            contact_phone=user.phone or '13800138001',
            contact_address='广东省深圳市南山区测试地址123号',
            created_at=timezone.now() - timedelta(days=order_data['days_ago']),
            paid_at=timezone.now() - timedelta(days=order_data['days_ago']) if order_data['status'] != 'pending' else None
        )
        
        print(f"✅ 创建订单: {order.order_id} (¥{order.total_amount})")
        created_orders.append(order)
    
    print(f"📊 总共创建/找到 {len(created_orders)} 个订单")
    return created_orders


def create_test_logistics(orders):
    """为订单创建物流信息"""
    print(f"\n📦 创建物流信息...")
    
    # 获取快递公司
    express_company = ExpressCompany.objects.filter(code='yuantong').first()
    if not express_company:
        express_company = ExpressCompany.objects.first()
    
    if not express_company:
        print("❌ 没有找到快递公司")
        return []
    
    print(f"🚚 使用快递公司: {express_company.name}")
    
    created_logistics = []
    
    for order in orders:
        # 只为已支付和已发货的订单创建物流
        if order.status in ['pending']:
            continue
        
        # 检查是否已有物流
        if hasattr(order, 'logistics') and order.logistics:
            print(f"ℹ️ 订单 {order.order_id} 已有物流: {order.logistics.tracking_number}")
            created_logistics.append(order.logistics)
            continue
        
        # 创建物流信息
        logistics = Logistics.objects.create(
            order=order,
            express_company=express_company,
            tracking_number=f"YT{order.id:012d}",
            sender_name="两山茶管家",
            sender_phone="************",
            sender_address="广东省潮州市凤凰镇茶叶基地",
            receiver_name=order.contact_name,
            receiver_phone=order.contact_phone,
            receiver_address=order.contact_address,
            status='in_transit' if order.status == 'shipped' else 'shipped',
            shipped_at=order.paid_at + timedelta(hours=2) if order.paid_at else timezone.now(),
            weight=Decimal('2.5'),
            package_count=1,
            shipping_fee=Decimal('15.00')
        )
        
        # 创建物流轨迹
        base_time = logistics.shipped_at
        tracks_data = [
            {
                'location': '广东省潮州市凤凰镇',
                'description': f'【{express_company.name}】快件已在凤凰镇营业点装车，准备发往下一站',
                'status': 'shipped',
                'timestamp': base_time,
                'operator': '张师傅'
            },
            {
                'location': '广东省潮州市分拨中心',
                'description': f'快件已到达潮州市分拨中心',
                'status': 'in_transit',
                'timestamp': base_time + timedelta(hours=2),
                'operator': '分拨中心'
            }
        ]
        
        if order.status == 'shipped':
            tracks_data.append({
                'location': '广东省广州市分拨中心',
                'description': f'快件已到达广州市分拨中心，准备发往深圳',
                'status': 'in_transit',
                'timestamp': base_time + timedelta(hours=6),
                'operator': '分拨中心'
            })
        
        for track_data in tracks_data:
            LogisticsTrack.objects.create(
                logistics=logistics,
                **track_data
            )
        
        print(f"✅ 创建物流: {logistics.tracking_number} (订单: {order.order_id})")
        created_logistics.append(logistics)
    
    print(f"📊 总共创建/找到 {len(created_logistics)} 个物流")
    return created_logistics


def verify_user_data(user):
    """验证用户数据"""
    print(f"\n🔍 验证用户 {user.username} 的数据...")
    
    # 检查订单
    orders = Order.objects.filter(user=user)
    print(f"📋 订单数量: {orders.count()}")
    
    for order in orders:
        print(f"  - {order.order_id}: ¥{order.total_amount} ({order.status})")
    
    # 检查物流
    logistics = Logistics.objects.filter(order__user=user)
    print(f"📦 物流数量: {logistics.count()}")
    
    for log in logistics:
        print(f"  - {log.tracking_number}: {log.status}")
    
    # 检查Token
    try:
        token = Token.objects.get(user=user)
        print(f"🔑 API Token: {token.key}")
    except Token.DoesNotExist:
        print(f"❌ 没有API Token")
    
    # 检查微信信息
    try:
        wechat_info = WechatUserInfo.objects.get(user=user)
        print(f"📱 微信OpenID: {wechat_info.openid}")
    except WechatUserInfo.DoesNotExist:
        print(f"❌ 没有微信信息")


def main():
    """主函数"""
    print("🔧 创建测试用户和数据")
    print("=" * 50)
    
    # 创建测试用户
    test_user = create_test_user()
    
    # 创建订单
    orders = create_test_orders(test_user)
    
    # 创建物流
    logistics = create_test_logistics(orders)
    
    # 验证数据
    verify_user_data(test_user)
    
    print("\n" + "=" * 50)
    print("✅ 测试用户和数据创建完成！")
    print("\n💡 测试用户信息:")
    print(f"   用户名: {test_user.username}")
    print(f"   密码: 123456")
    print(f"   微信OpenID: test_openid_123456789")
    
    try:
        token = Token.objects.get(user=test_user)
        print(f"   API Token: {token.key}")
    except:
        pass
    
    print("\n🔍 现在可以:")
    print("1. 使用这个用户在小程序中登录测试")
    print("2. 检查订单列表是否正常显示")
    print("3. 检查物流信息是否正常显示")
    print("4. 验证API权限是否正确")


if __name__ == "__main__":
    main()

# 微信用户信息显示修复说明

## 问题描述

用户在微信小程序登录授权获取用户头像昵称信息后，个人中心页面没有显示对应的头像昵称，而是显示默认头像和"茶园投资者"这样的默认昵称。

## 问题原因分析

经过详细分析，发现问题出现在以下几个方面：

### 1. 后端序列化器问题
- `WechatUserSerializer` 只包含了 `openid` 和 `unionid` 字段
- 缺少 `nickname`、`avatar_url`、`gender`、`city`、`province`、`country` 等用户信息字段
- 导致前端无法获取到微信用户的真实昵称和头像

### 2. 前端登录逻辑问题
- 登录成功后只保存了 `res.data.user` 信息
- 没有合并 `res.data.wechat_info` 中的微信用户信息
- 导致微信昵称和头像信息丢失

### 3. 用户信息传递问题
- 登录页面虽然有获取用户头像和昵称的逻辑，但在调用登录API时没有正确传递这些信息

## 修复方案

### 1. 修复后端序列化器

**文件**: `api/v1/serializers.py`

```python
# 修复前
class WechatUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = WechatUser
        fields = ['openid', 'unionid']
        read_only_fields = ['openid', 'unionid']

# 修复后
class WechatUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = WechatUser
        fields = ['openid', 'unionid', 'nickname', 'avatar_url', 'gender', 'city', 'province', 'country']
        read_only_fields = ['openid', 'unionid']
```

### 2. 修复前端登录逻辑

**文件**: `xcx1/pages/login/login.js`

#### 修复 `wxLoginWithCode` 方法

```javascript
// 修复前
const res = await api.userApi.wechatLogin({
  code: code
})

if (res.code === 200) {
  app.login(res.data.user, res.data.token)
  // ...
}

// 修复后
const userInfo = {
  nickName: this.data.nickname || '',
  avatarUrl: this.data.avatarUrl || '',
  gender: 0,
  city: '',
  province: '',
  country: '中国',
  language: 'zh_CN'
}

const res = await api.userApi.wechatLogin({
  code: code,
  userInfo: userInfo  // 传递用户信息
})

if (res.code === 200) {
  // 合并用户信息和微信信息
  const userInfo = {
    ...res.data.user,
    // 优先使用微信信息中的昵称和头像
    nickname: res.data.wechat_info?.nickname || res.data.user.nickname || this.data.nickname,
    avatar: res.data.wechat_info?.avatar_url || res.data.user.avatar || this.data.avatarUrl,
    wechat_info: res.data.wechat_info
  }

  app.login(userInfo, res.data.token)
  // ...
}
```

#### 修复另一个登录方法

类似地修复了另一个登录方法，确保正确合并用户信息。

## 测试验证

### 1. 创建了测试脚本

**文件**: `test_wechat_user_info.py`

测试脚本验证了：
- 微信用户序列化器是否正确返回所有字段
- 用户详情序列化器是否包含微信信息
- 前端合并逻辑是否正确工作

### 2. 测试结果

```
🔄 序列化结果:
   openid: test_openid_123456789
   unionid: None
   nickname: 测试茶友小明
   avatar_url: https://thirdwx.qlogo.cn/mmopen/test_avatar.jpg
   gender: 1
   city: 深圳
   province: 广东
   country: 中国

🔄 前端合并后的用户信息:
   最终昵称: 测试茶友小明
   最终头像: https://thirdwx.qlogo.cn/mmopen/test_avatar.jpg

✅ 昵称显示应该正常
✅ 头像显示应该正常
```

## 修复效果

### 修复前
- 个人中心显示默认头像（绿色圆圈图标）
- 昵称显示"茶园投资者"
- 后台没有同步显示相应的头像昵称

### 修复后
- 个人中心显示用户真实的微信头像
- 昵称显示用户真实的微信昵称
- 后台正确保存和返回微信用户信息

## 数据流程

### 1. 用户授权流程
1. 用户在登录页面点击头像授权按钮
2. 小程序调用 `wx.chooseAvatar` 获取头像
3. 用户输入昵称
4. 调用微信登录API，传递用户信息

### 2. 后端处理流程
1. 接收前端传递的 `code` 和 `userInfo`
2. 通过 `code` 获取 `openid` 和 `session_key`
3. 创建或更新 `WechatUserInfo` 记录，保存用户昵称和头像
4. 返回用户信息和微信信息

### 3. 前端显示流程
1. 接收后端返回的 `user` 和 `wechat_info`
2. 合并两个对象，优先使用微信信息
3. 保存到全局状态和本地存储
4. 个人中心页面从全局状态获取用户信息显示

## 注意事项

1. **优先级**: 微信信息的昵称和头像优先于用户表中的信息
2. **兼容性**: 保持了向后兼容，如果没有微信信息则使用用户表信息
3. **数据同步**: 登录时会同时更新用户表和微信用户表的信息
4. **错误处理**: 如果获取微信信息失败，不会影响基本登录功能

## 测试建议

1. **清除缓存**: 测试前清除小程序缓存和本地存储
2. **重新授权**: 删除用户授权记录，重新进行授权流程
3. **检查网络**: 确保头像URL可以正常访问
4. **后台验证**: 在Django admin中检查微信用户信息是否正确保存

## 相关文件

- `api/v1/serializers.py` - 后端序列化器
- `xcx1/pages/login/login.js` - 前端登录逻辑
- `xcx1/pages/profile/profile.js` - 个人中心页面
- `xcx1/pages/profile/profile.wxml` - 个人中心模板
- `test_wechat_user_info.py` - 测试脚本

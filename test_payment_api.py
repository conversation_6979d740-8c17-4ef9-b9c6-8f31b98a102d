#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试支付API
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from api.v1.views import PaymentCreateView
from apps.orders.models import Order

User = get_user_model()


def test_payment_create_api():
    """测试支付创建API"""
    print("🧪 测试支付创建API...")
    
    username = 'wx_oXYVEvjxO1wJ_itf'
    
    try:
        user = User.objects.get(username=username)
        token = Token.objects.get(user=user)
        
        # 获取用户的订单
        order = Order.objects.filter(user=user, status='pending_payment').first()
        
        if not order:
            print(f"❌ 用户没有待支付订单")
            return False
        
        print(f"✅ 找到待支付订单: {order.order_id}")
        print(f"   订单金额: {order.total_amount}")
        
        factory = RequestFactory()
        
        # 创建支付请求数据
        payment_data = {
            'order_id': order.order_id,
            'payment_method': 'wechat',
            'amount': str(order.total_amount)
        }
        
        print(f"📤 支付请求数据:")
        print(f"   {json.dumps(payment_data, ensure_ascii=False, indent=2)}")
        
        # 创建POST请求
        request = factory.post(
            '/api/v1/payments/',
            data=json.dumps(payment_data),
            content_type='application/json'
        )
        request.META['HTTP_AUTHORIZATION'] = f'Token {token.key}'
        
        # 模拟用户认证
        request.user = user
        
        try:
            # 调用支付创建视图
            view = PaymentCreateView()
            view.request = request
            response = view.create(request)
            
            print(f"\n📥 支付API响应:")
            print(f"   状态码: {response.status_code}")
            
            if hasattr(response, 'data'):
                response_data = response.data
                print(f"   响应代码: {response_data.get('code')}")
                print(f"   响应消息: {response_data.get('message')}")
                
                if response_data.get('code') == 200:
                    print(f"\n✅ 支付创建成功!")
                    payment_data = response_data['data']
                    
                    print(f"   支付ID: {payment_data.get('payment_id')}")
                    print(f"   微信订单号: {payment_data.get('wechat_order_no')}")
                    print(f"   AppID: {payment_data.get('appId')}")
                    print(f"   时间戳: {payment_data.get('timeStamp')}")
                    print(f"   随机字符串: {payment_data.get('nonceStr')}")
                    print(f"   Package: {payment_data.get('package')}")
                    print(f"   签名: {payment_data.get('paySign')}")
                    
                    # 检查必要的支付参数
                    required_params = ['appId', 'timeStamp', 'nonceStr', 'package', 'paySign']
                    missing_params = [param for param in required_params if not payment_data.get(param)]
                    
                    if missing_params:
                        print(f"❌ 缺少必要的支付参数: {missing_params}")
                        return False
                    else:
                        print(f"✅ 支付参数完整，可以调用微信支付")
                        return True
                else:
                    print(f"❌ 支付创建失败: {response_data.get('message')}")
                    return False
            else:
                print(f"❌ 响应格式错误")
                return False
        
        except Exception as e:
            print(f"❌ 支付API调用异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
    except User.DoesNotExist:
        print(f"❌ 用户 {username} 不存在")
        return False
    except Token.DoesNotExist:
        print(f"❌ 用户 {username} 没有token")
        return False


def test_wechat_pay_config():
    """测试微信支付配置"""
    print(f"\n🔧 测试微信支付配置...")
    
    from apps.system.utils import WechatConfigManager, WechatPayConfigManager
    
    # 检查微信基础配置
    app_id = WechatConfigManager.get_app_id()
    app_secret = WechatConfigManager.get_app_secret()
    
    print(f"微信基础配置:")
    print(f"   AppID: {app_id[:10]}..." if app_id else "   AppID: 未配置")
    print(f"   AppSecret: {'已配置' if app_secret else '未配置'}")
    
    # 检查微信支付配置
    mch_id = WechatPayConfigManager.get_mch_id()
    api_key = WechatPayConfigManager.get_api_key()
    notify_url = WechatPayConfigManager.get_notify_url()
    
    print(f"微信支付配置:")
    print(f"   商户号: {mch_id}")
    print(f"   API密钥: {'已配置' if api_key else '未配置'}")
    print(f"   回调URL: {notify_url}")
    
    # 验证配置完整性
    if app_id and app_secret and mch_id and api_key:
        print(f"✅ 微信支付配置完整")
        return True
    else:
        print(f"❌ 微信支付配置不完整")
        return False


def main():
    """主函数"""
    print("🔧 测试支付API")
    print("=" * 60)
    
    # 测试微信支付配置
    config_ok = test_wechat_pay_config()
    
    # 测试支付创建API
    if config_ok:
        api_ok = test_payment_create_api()
    else:
        print(f"\n⚠️ 微信支付配置不完整，跳过API测试")
        api_ok = False
    
    print("\n" + "=" * 60)
    print("✅ 支付API测试完成！")
    
    print(f"\n💡 测试结果:")
    print(f"   微信支付配置: {'✅ 完整' if config_ok else '❌ 不完整'}")
    print(f"   支付API: {'✅ 正常' if api_ok else '❌ 异常'}")
    
    if config_ok and api_ok:
        print(f"\n🎯 支付功能完全正常！")
        print(f"   用户可以正常使用微信支付")
        print(f"   前端支付流程应该能正常工作")
    elif config_ok and not api_ok:
        print(f"\n⚠️ 配置正常但API有问题")
        print(f"   需要检查API代码逻辑")
    elif not config_ok:
        print(f"\n❌ 微信支付配置有问题")
        print(f"   需要检查系统配置")
    
    print(f"\n📱 前端测试建议:")
    print(f"1. 清除小程序缓存")
    print(f"2. 重新进入购买页面")
    print(f"3. 点击'立即支付'")
    print(f"4. 检查控制台是否有错误")


if __name__ == "__main__":
    main()

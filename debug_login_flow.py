#!/usr/bin/env python3
"""
调试登录流程问题
模拟完整的登录流程，检查每个步骤
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.notifications.models import WechatUserInfo
from apps.users.wechat_service import WechatUserManager
from api.v1.serializers import UserDetailSerializer, WechatUserSerializer

User = get_user_model()


def simulate_login_with_userinfo():
    """模拟带用户信息的登录流程"""
    print("🧪 模拟带用户信息的登录流程...")
    
    try:
        # 模拟前端传递的数据
        test_openid = "simulate_openid_with_info"
        user_info = {
            'nickName': '模拟用户昵称',
            'avatarUrl': 'https://thirdwx.qlogo.cn/mmopen/simulate_avatar.jpg',
            'gender': 1,
            'city': '深圳',
            'province': '广东',
            'country': '中国',
            'language': 'zh_CN'
        }
        
        print(f"模拟OpenID: {test_openid}")
        print(f"模拟用户信息:")
        for key, value in user_info.items():
            print(f"   {key}: '{value}'")
        
        # 清理可能存在的测试数据
        try:
            existing_wechat_user = WechatUserInfo.objects.get(openid=test_openid)
            existing_user = existing_wechat_user.user
            existing_wechat_user.delete()
            existing_user.delete()
            print("清理了已存在的测试数据")
        except WechatUserInfo.DoesNotExist:
            pass
        
        # 执行登录流程
        print(f"\n🔄 执行登录流程...")
        result = WechatUserManager.create_or_update_user_v2(test_openid, user_info)
        
        if result['success']:
            user = result['user']
            wechat_user = result['wechat_user']
            
            print(f"✅ 登录成功!")
            print(f"   用户: {user.username}")
            print(f"   用户昵称: '{user.nickname}'")
            print(f"   微信OpenID: {wechat_user.openid}")
            print(f"   微信昵称: '{wechat_user.nickname}'")
            print(f"   微信头像: '{wechat_user.avatar_url}'")
            
            # 测试API序列化
            print(f"\n📊 测试API序列化...")
            user_serializer = UserDetailSerializer(user)
            wechat_serializer = WechatUserSerializer(wechat_user)
            
            api_response = {
                'code': 200,
                'message': '登录成功',
                'data': {
                    'token': 'test_token_123',
                    'user': user_serializer.data,
                    'wechat_info': wechat_serializer.data,
                    'is_new_user': result['is_new_user']
                }
            }
            
            print(f"API响应关键字段:")
            print(f"   user.nickname: '{api_response['data']['user'].get('nickname')}'")
            print(f"   user.avatar: '{api_response['data']['user'].get('avatar')}'")
            print(f"   wechat_info.nickname: '{api_response['data']['wechat_info'].get('nickname')}'")
            print(f"   wechat_info.avatar_url: '{api_response['data']['wechat_info'].get('avatar_url')}'")
            
            # 模拟前端合并逻辑
            print(f"\n🔄 模拟前端合并逻辑...")
            res_data = api_response['data']
            merged_user_info = {
                **res_data['user'],
                'nickname': res_data['wechat_info'].get('nickname') or res_data['user'].get('nickname') or '默认昵称',
                'avatar': res_data['wechat_info'].get('avatar_url') or res_data['user'].get('avatar') or '默认头像',
                'wechat_info': res_data['wechat_info']
            }
            
            print(f"前端最终显示:")
            print(f"   昵称: '{merged_user_info.get('nickname')}'")
            print(f"   头像: '{merged_user_info.get('avatar')}'")
            
            # 清理测试数据
            wechat_user.delete()
            user.delete()
            print(f"\n已清理测试数据")
            
            return True
        else:
            print(f"❌ 登录失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 模拟登录失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_existing_user_issue():
    """检查现有用户的问题"""
    print(f"\n🔍 检查现有用户的问题...")
    
    try:
        # 查找现有的微信用户
        wechat_users = WechatUserInfo.objects.all()
        
        for wechat_user in wechat_users:
            user = wechat_user.user
            print(f"\n用户: {user.username}")
            print(f"   OpenID: {wechat_user.openid}")
            print(f"   微信昵称: '{wechat_user.nickname}'")
            print(f"   微信头像: '{wechat_user.avatar_url}'")
            print(f"   用户昵称: '{user.nickname}'")
            print(f"   最后登录: {wechat_user.last_login_time}")
            
            # 检查问题
            issues = []
            if not wechat_user.nickname:
                issues.append("微信昵称为空")
            if not wechat_user.avatar_url:
                issues.append("微信头像为空")
            if not user.nickname:
                issues.append("用户昵称为空")
            
            if issues:
                print(f"   ❌ 问题: {', '.join(issues)}")
                
                # 尝试修复 - 设置一些测试数据
                print(f"   🔧 尝试修复...")
                wechat_user.nickname = wechat_user.nickname or f"用户{user.username[-4:]}"
                wechat_user.avatar_url = wechat_user.avatar_url or "https://thirdwx.qlogo.cn/mmopen/default_avatar.jpg"
                user.nickname = user.nickname or wechat_user.nickname
                
                wechat_user.save()
                user.save()
                
                print(f"   ✅ 已设置默认值")
            else:
                print(f"   ✅ 数据完整")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查现有用户失败: {e}")
        return False


def test_api_response_format():
    """测试API响应格式"""
    print(f"\n🌐 测试API响应格式...")
    
    try:
        # 查找一个有数据的用户
        wechat_user = WechatUserInfo.objects.first()
        if not wechat_user:
            print("❌ 没有找到微信用户数据")
            return False
        
        user = wechat_user.user
        
        # 测试序列化器
        user_serializer = UserDetailSerializer(user)
        wechat_serializer = WechatUserSerializer(wechat_user)
        
        print(f"用户序列化结果:")
        user_data = user_serializer.data
        for key, value in user_data.items():
            if key == 'wechat_info':
                print(f"   {key}:")
                if value:
                    for wk, wv in value.items():
                        print(f"     {wk}: '{wv}'")
                else:
                    print(f"     None")
            else:
                print(f"   {key}: '{value}'")
        
        print(f"\n微信用户序列化结果:")
        wechat_data = wechat_serializer.data
        for key, value in wechat_data.items():
            print(f"   {key}: '{value}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试API响应格式失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    print("=" * 60)
    print("登录流程调试")
    print("=" * 60)
    
    # 模拟完整登录流程
    login_ok = simulate_login_with_userinfo()
    
    # 检查现有用户问题
    existing_ok = check_existing_user_issue()
    
    # 测试API响应格式
    api_ok = test_api_response_format()
    
    print(f"\n" + "=" * 60)
    print(f"调试结果:")
    print(f"   模拟登录: {'✅ 正常' if login_ok else '❌ 异常'}")
    print(f"   现有用户: {'✅ 正常' if existing_ok else '❌ 异常'}")
    print(f"   API响应: {'✅ 正常' if api_ok else '❌ 异常'}")
    
    print(f"\n💡 问题分析:")
    print(f"   1. 用户创建和序列化逻辑正常")
    print(f"   2. 主要问题可能在于:")
    print(f"      - 前端传递的用户信息格式不正确")
    print(f"      - 微信code2session调用失败")
    print(f"      - 用户信息在登录时没有正确传递")
    
    print(f"\n🔧 建议修复:")
    print(f"   1. 检查前端登录时是否正确传递userInfo")
    print(f"   2. 检查微信小程序的AppID和AppSecret")
    print(f"   3. 确认前端获取的code是否有效")
    print(f"   4. 查看后端登录API的日志")
    print("=" * 60)

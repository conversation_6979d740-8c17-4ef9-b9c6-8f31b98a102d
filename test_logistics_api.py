#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试物流API端点
"""

import os
import sys
import django
import requests
import json

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from apps.logistics.models import Logistics, LogisticsTrack
from apps.users.models import User
from django.contrib.auth import get_user_model


def test_logistics_api():
    """测试物流API"""
    print("🧪 测试物流API端点")
    print("=" * 50)
    
    # 获取测试用户
    User = get_user_model()
    user = User.objects.first()
    
    if not user:
        print("❌ 没有找到测试用户")
        return
    
    print(f"👤 使用测试用户: {user.username}")
    
    # 获取物流信息
    logistics = Logistics.objects.filter(order__user=user).first()
    
    if not logistics:
        print("❌ 没有找到物流信息")
        return
    
    print(f"📦 测试物流: {logistics.tracking_number}")
    
    # 测试API端点
    base_url = 'http://127.0.0.1:8000/api/v1'
    
    # 模拟用户token（实际应用中需要真实token）
    headers = {
        'Authorization': f'Token {user.auth_token.key if hasattr(user, "auth_token") else "test-token"}',
        'Content-Type': 'application/json'
    }
    
    # 测试物流详情API
    print("\n🔍 测试物流详情API...")
    try:
        url = f"{base_url}/logistics/logistics/{logistics.id}/"
        response = requests.get(url, headers=headers)
        
        print(f"请求URL: {url}")
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 物流详情API正常")
            print(f"   物流ID: {data.get('id')}")
            print(f"   快递单号: {data.get('tracking_number')}")
            print(f"   快递公司: {data.get('express_company', {}).get('name')}")
            print(f"   状态: {data.get('status')}")
        else:
            print(f"❌ 物流详情API失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 物流详情API异常: {e}")
    
    # 测试物流轨迹API
    print("\n🚚 测试物流轨迹API...")
    try:
        url = f"{base_url}/logistics/logistics/{logistics.id}/tracks/"
        response = requests.get(url, headers=headers)
        
        print(f"请求URL: {url}")
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 物流轨迹API正常")
            
            if data.get('code') == 200:
                tracks = data.get('data', [])
                print(f"   轨迹数量: {len(tracks)}")
                
                if tracks:
                    latest_track = tracks[0]
                    print(f"   最新轨迹: {latest_track.get('description')}")
                    print(f"   位置: {latest_track.get('location')}")
                    print(f"   时间: {latest_track.get('timestamp')}")
            else:
                print(f"   API返回错误: {data.get('message')}")
        else:
            print(f"❌ 物流轨迹API失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 物流轨迹API异常: {e}")
    
    # 测试物流列表API
    print("\n📋 测试物流列表API...")
    try:
        url = f"{base_url}/logistics/logistics/"
        response = requests.get(url, headers=headers)
        
        print(f"请求URL: {url}")
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 物流列表API正常")
            
            if isinstance(data, dict) and 'results' in data:
                # DRF分页格式
                logistics_list = data.get('results', [])
                print(f"   物流数量: {len(logistics_list)}")
                print(f"   总数: {data.get('count', 0)}")
            elif isinstance(data, list):
                # 直接列表格式
                print(f"   物流数量: {len(data)}")
            else:
                print(f"   未知格式: {type(data)}")
        else:
            print(f"❌ 物流列表API失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 物流列表API异常: {e}")


def check_database_data():
    """检查数据库数据"""
    print("\n📊 检查数据库数据...")
    
    # 检查物流数据
    logistics_count = Logistics.objects.count()
    print(f"📦 物流记录数量: {logistics_count}")
    
    if logistics_count > 0:
        logistics = Logistics.objects.first()
        print(f"   示例物流: {logistics.tracking_number}")
        print(f"   快递公司: {logistics.express_company.name}")
        print(f"   状态: {logistics.status}")
        
        # 检查轨迹数据
        tracks_count = LogisticsTrack.objects.filter(logistics=logistics).count()
        print(f"   轨迹数量: {tracks_count}")
        
        if tracks_count > 0:
            latest_track = LogisticsTrack.objects.filter(logistics=logistics).order_by('-timestamp').first()
            print(f"   最新轨迹: {latest_track.description}")
    
    # 检查用户数据
    User = get_user_model()
    user_count = User.objects.count()
    print(f"👤 用户数量: {user_count}")


def main():
    """主函数"""
    check_database_data()
    test_logistics_api()
    
    print("\n✅ API测试完成！")
    print("💡 如果API测试正常，前端应该可以正常显示物流信息")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试前端用户信息获取
模拟前端调用用户资料API的情况
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.notifications.models import WechatUserInfo
from api.v1.serializers import UserDetailSerializer
from rest_framework.authtoken.models import Token

User = get_user_model()


def test_user_profile_api():
    """测试用户资料API返回的数据格式"""
    print("🧪 测试用户资料API...")
    
    try:
        # 查找一个有微信信息的用户
        wechat_user = WechatUserInfo.objects.filter(
            nickname__isnull=False,
            avatar_url__isnull=False
        ).exclude(
            nickname='',
            avatar_url=''
        ).first()
        
        if not wechat_user:
            print("❌ 没有找到有完整微信信息的用户")
            return False
            
        user = wechat_user.user
        print(f"📱 测试用户: {user.username}")
        
        # 模拟API调用
        serializer = UserDetailSerializer(user)
        api_response = {
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        }
        
        print(f"\n📊 用户资料API响应:")
        print(json.dumps(api_response, ensure_ascii=False, indent=2))
        
        # 检查关键字段
        user_data = api_response['data']
        wechat_info = user_data.get('wechat_info')
        
        print(f"\n🔍 关键字段检查:")
        print(f"   用户昵称: '{user_data.get('nickname')}'")
        print(f"   用户头像: '{user_data.get('avatar')}'")
        
        if wechat_info:
            print(f"   微信昵称: '{wechat_info.get('nickname')}'")
            print(f"   微信头像: '{wechat_info.get('avatar_url')}'")
            print(f"   微信性别: {wechat_info.get('gender')}")
            print(f"   微信城市: '{wechat_info.get('city')}'")
        else:
            print("   ❌ 微信信息为空!")
            return False
        
        # 模拟前端处理逻辑
        print(f"\n🔄 模拟前端处理:")
        final_nickname = wechat_info.get('nickname') or user_data.get('nickname') or '默认用户'
        final_avatar = wechat_info.get('avatar_url') or user_data.get('avatar') or '/static/images/default-avatar.png'
        
        print(f"   最终昵称: '{final_nickname}'")
        print(f"   最终头像: '{final_avatar}'")
        
        # 验证是否有有效的微信信息
        if final_nickname != '默认用户' and final_avatar != '/static/images/default-avatar.png':
            print("✅ 微信用户信息正常!")
            return True
        else:
            print("❌ 微信用户信息缺失!")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_login_api_response():
    """测试登录API响应格式"""
    print(f"\n🔐 测试登录API响应格式...")
    
    try:
        # 查找一个有微信信息的用户
        wechat_user = WechatUserInfo.objects.filter(
            nickname__isnull=False,
            avatar_url__isnull=False
        ).exclude(
            nickname='',
            avatar_url=''
        ).first()
        
        if not wechat_user:
            print("❌ 没有找到测试用户")
            return False
            
        user = wechat_user.user
        
        # 获取或创建token
        token, created = Token.objects.get_or_create(user=user)
        
        # 模拟登录API响应
        from api.v1.serializers import WechatUserSerializer
        
        login_response = {
            'code': 200,
            'message': '登录成功',
            'data': {
                'token': token.key,
                'user': UserDetailSerializer(user).data,
                'wechat_info': WechatUserSerializer(wechat_user).data,
                'is_new_user': False
            }
        }
        
        print(f"登录API响应关键字段:")
        print(f"   token: '{login_response['data']['token'][:20]}...'")
        print(f"   user.nickname: '{login_response['data']['user'].get('nickname')}'")
        print(f"   user.avatar: '{login_response['data']['user'].get('avatar')}'")
        print(f"   wechat_info.nickname: '{login_response['data']['wechat_info'].get('nickname')}'")
        print(f"   wechat_info.avatar_url: '{login_response['data']['wechat_info'].get('avatar_url')}'")
        
        # 模拟前端合并逻辑（来自 xcx1/pages/login/login.js）
        res_data = login_response['data']
        merged_user_info = {
            **res_data['user'],
            'nickname': res_data['wechat_info'].get('nickname') or res_data['user'].get('nickname') or '默认昵称',
            'avatar': res_data['wechat_info'].get('avatar_url') or res_data['user'].get('avatar') or '默认头像',
            'wechat_info': res_data['wechat_info']
        }
        
        print(f"\n前端合并后的用户信息:")
        print(f"   nickname: '{merged_user_info.get('nickname')}'")
        print(f"   avatar: '{merged_user_info.get('avatar')}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 登录API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    print("=" * 60)
    print("前端用户信息获取测试")
    print("=" * 60)
    
    # 测试用户资料API
    profile_ok = test_user_profile_api()
    
    # 测试登录API
    login_ok = test_login_api_response()
    
    print(f"\n" + "=" * 60)
    print(f"测试结果:")
    print(f"   用户资料API: {'✅ 通过' if profile_ok else '❌ 失败'}")
    print(f"   登录API: {'✅ 通过' if login_ok else '❌ 失败'}")
    
    if profile_ok and login_ok:
        print(f"🎉 前端应该能正常获取到微信用户信息了!")
        print(f"💡 建议:")
        print(f"   1. 清除小程序缓存重新登录测试")
        print(f"   2. 检查后台'微信用户'管理页面是否显示用户信息")
        print(f"   3. 在个人中心页面查看头像和昵称是否正确显示")
    else:
        print(f"❌ 仍有问题需要进一步排查")
    print("=" * 60)

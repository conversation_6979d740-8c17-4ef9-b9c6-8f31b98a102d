/* 物流API调试页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  text-align: center;
}

.title {
  display: block;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

/* 通用区块样式 */
.config-section,
.action-section,
.results-section,
.help-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

/* 输入组件 */
.input-group {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.input:focus {
  border-color: #667eea;
  background: white;
}

.picker {
  height: 80rpx;
  line-height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background: #fafafa;
  color: #333;
}

/* 按钮组 */
.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.test-btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.test-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.test-btn.secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.test-btn.info {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.mini-btn {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  border-radius: 20rpx;
  border: 1rpx solid #ddd;
  background: white;
  color: #666;
}

.mini-btn.danger {
  color: #ff4757;
  border-color: #ff4757;
}

/* 测试结果 */
.results-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.result-item {
  border-radius: 15rpx;
  padding: 25rpx;
  border-left: 8rpx solid;
}

.result-item.success {
  background: #f0f9ff;
  border-left-color: #10b981;
}

.result-item.error {
  background: #fef2f2;
  border-left-color: #ef4444;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.status-icon {
  font-size: 32rpx;
}

.status-text {
  font-size: 28rpx;
  font-weight: bold;
}

.result-time {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5rpx;
}

.time {
  font-size: 24rpx;
  color: #666;
}

.duration {
  font-size: 22rpx;
  color: #999;
}

.result-params,
.result-response,
.result-code {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 26rpx;
}

.param-label,
.response-label,
.code-label {
  color: #666;
  margin-right: 15rpx;
  min-width: 80rpx;
}

.param-value,
.response-value,
.code-value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 28rpx;
}

/* 帮助说明 */
.help-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.help-item {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.help-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.help-desc {
  font-size: 26rpx;
  color: #666;
  padding-left: 20rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .button-group {
    flex-direction: column;
  }
  
  .test-btn {
    flex: none;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10rpx;
  }
  
  .result-time {
    align-items: flex-start;
  }
}

<!--物流API调试页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">🔧 物流API调试工具</text>
    <text class="subtitle">用于测试快递100 API对接</text>
  </view>

  <!-- 测试参数配置 -->
  <view class="config-section">
    <view class="section-title">📝 测试参数配置</view>
    
    <!-- 快递单号 -->
    <view class="input-group">
      <text class="label">快递单号:</text>
      <input 
        class="input" 
        placeholder="请输入快递单号" 
        value="{{testParams.trackingNumber}}"
        bindinput="onTrackingNumberInput"
      />
    </view>

    <!-- 快递公司 -->
    <view class="input-group">
      <text class="label">快递公司:</text>
      <picker 
        range="{{expressCompanies}}" 
        range-key="name"
        bindchange="onCompanyChange"
      >
        <view class="picker">
          {{testParams.companyName || '请选择快递公司'}}
        </view>
      </picker>
    </view>

    <!-- 手机号（顺丰必填） -->
    <view class="input-group" wx:if="{{testParams.needPhone}}">
      <text class="label">手机号:</text>
      <input 
        class="input" 
        placeholder="顺丰快递必填" 
        value="{{testParams.phone}}"
        bindinput="onPhoneInput"
        type="number"
      />
    </view>
  </view>

  <!-- 测试操作 -->
  <view class="action-section">
    <view class="section-title">🚀 测试操作</view>
    
    <view class="button-group">
      <button 
        class="test-btn primary" 
        bindtap="testSingleQuery"
        loading="{{loading}}"
        disabled="{{loading}}"
      >
        单次测试
      </button>
      
      <button 
        class="test-btn secondary" 
        bindtap="testBatchQuery"
        loading="{{loading}}"
        disabled="{{loading}}"
      >
        批量测试
      </button>
      
      <button 
        class="test-btn info" 
        bindtap="testApiConfig"
        loading="{{loading}}"
        disabled="{{loading}}"
      >
        配置测试
      </button>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="results-section" wx:if="{{testResults.length > 0}}">
    <view class="section-header">
      <text class="section-title">📊 测试结果 ({{testResults.length}})</text>
      <view class="header-actions">
        <button class="mini-btn" bindtap="copyResults">复制</button>
        <button class="mini-btn danger" bindtap="clearResults">清空</button>
      </view>
    </view>

    <view class="results-list">
      <view 
        class="result-item {{item.success ? 'success' : 'error'}}"
        wx:for="{{testResults}}" 
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="viewResultDetail"
      >
        <!-- 结果头部 -->
        <view class="result-header">
          <view class="result-status">
            <text class="status-icon">{{item.success ? '✅' : '❌'}}</text>
            <text class="status-text">{{item.success ? '成功' : '失败'}}</text>
          </view>
          <view class="result-time">
            <text class="time">{{item.timestamp}}</text>
            <text class="duration">{{item.duration}}ms</text>
          </view>
        </view>

        <!-- 请求参数 -->
        <view class="result-params">
          <text class="param-label">参数:</text>
          <text class="param-value">
            {{item.params.company_code || item.params.companyCode}} - {{item.params.tracking_number || item.params.trackingNumber}}
          </text>
        </view>

        <!-- 响应信息 -->
        <view class="result-response">
          <text class="response-label">响应:</text>
          <text class="response-value">
            {{item.response.message || item.error || '无响应信息'}}
          </text>
        </view>

        <!-- 错误码 -->
        <view class="result-code" wx:if="{{item.response.code || item.response.error_code}}">
          <text class="code-label">代码:</text>
          <text class="code-value">{{item.response.code || item.response.error_code}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{testResults.length === 0}}">
    <view class="empty-icon">🧪</view>
    <view class="empty-title">暂无测试结果</view>
    <view class="empty-desc">点击上方按钮开始测试API</view>
  </view>

  <!-- 使用说明 -->
  <view class="help-section">
    <view class="section-title">💡 使用说明</view>
    <view class="help-content">
      <view class="help-item">
        <text class="help-title">1. 单次测试:</text>
        <text class="help-desc">测试指定快递单号的查询功能</text>
      </view>
      <view class="help-item">
        <text class="help-title">2. 批量测试:</text>
        <text class="help-desc">测试多个快递公司的API调用</text>
      </view>
      <view class="help-item">
        <text class="help-title">3. 配置测试:</text>
        <text class="help-desc">检查API密钥和服务器配置</text>
      </view>
      <view class="help-item">
        <text class="help-title">4. 注意事项:</text>
        <text class="help-desc">顺丰快递需要提供收件人手机号</text>
      </view>
    </view>
  </view>
</view>

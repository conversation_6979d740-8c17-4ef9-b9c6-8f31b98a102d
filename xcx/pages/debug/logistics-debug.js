// 物流API调试页面
const api = require('../../api/index.js')

Page({
  data: {
    // 测试参数
    testParams: {
      trackingNumber: 'SF1234567890',
      companyCode: 'shunfeng',
      phone: '13800138000'
    },
    
    // 测试结果
    testResults: [],
    
    // 加载状态
    loading: false,
    
    // 快递公司列表
    expressCompanies: [
      { code: 'shunfeng', name: '顺丰速运', needPhone: true },
      { code: 'yuantong', name: '圆通速递', needPhone: false },
      { code: 'zhongtong', name: '中通快递', needPhone: false },
      { code: 'yunda', name: '韵达速递', needPhone: false },
      { code: 'shentong', name: '申通快递', needPhone: false },
      { code: 'ems', name: '邮政包裹', needPhone: false }
    ]
  },

  onLoad() {
    console.log('物流API调试页面加载')
    this.loadExpressCompanies()
  },

  // 加载快递公司列表
  async loadExpressCompanies() {
    try {
      const res = await api.logisticsApi.getExpressCompanies()
      if (res.code === 200) {
        this.setData({
          expressCompanies: res.data
        })
      }
    } catch (error) {
      console.error('加载快递公司失败:', error)
    }
  },

  // 输入快递单号
  onTrackingNumberInput(e) {
    this.setData({
      'testParams.trackingNumber': e.detail.value
    })
  },

  // 输入手机号
  onPhoneInput(e) {
    this.setData({
      'testParams.phone': e.detail.value
    })
  },

  // 选择快递公司
  onCompanyChange(e) {
    const index = e.detail.value
    const company = this.data.expressCompanies[index]
    
    this.setData({
      'testParams.companyCode': company.code,
      'testParams.companyName': company.name,
      'testParams.needPhone': company.needPhone
    })
  },

  // 测试单个API调用
  async testSingleQuery() {
    const { trackingNumber, companyCode, phone } = this.data.testParams
    
    if (!trackingNumber || !companyCode) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    try {
      const startTime = Date.now()
      
      const params = {
        tracking_number: trackingNumber,
        company_code: companyCode
      }
      
      if (phone) {
        params.phone = phone
      }

      console.log('🔍 测试API调用:', params)
      
      const res = await api.logisticsApi.query(params)
      const endTime = Date.now()
      const duration = endTime - startTime

      const result = {
        id: Date.now(),
        timestamp: new Date().toLocaleString(),
        params: params,
        response: res,
        duration: duration,
        success: res.code === 200
      }

      console.log('✅ API响应:', result)

      // 添加到测试结果
      const results = [result, ...this.data.testResults.slice(0, 9)] // 保留最近10次测试
      this.setData({ testResults: results })

      if (res.code === 200) {
        wx.showToast({
          title: '测试成功',
          icon: 'success'
        })
      } else {
        wx.showModal({
          title: '测试失败',
          content: res.message || '未知错误',
          showCancel: false
        })
      }

    } catch (error) {
      console.error('❌ API调用失败:', error)
      
      const result = {
        id: Date.now(),
        timestamp: new Date().toLocaleString(),
        params: { trackingNumber, companyCode, phone },
        error: error.message || '网络错误',
        duration: 0,
        success: false
      }

      const results = [result, ...this.data.testResults.slice(0, 9)]
      this.setData({ testResults: results })

      wx.showModal({
        title: '网络错误',
        content: error.message || '请检查网络连接',
        showCancel: false
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 批量测试多个快递公司
  async testBatchQuery() {
    this.setData({ loading: true })

    const testCases = [
      { trackingNumber: 'SF1234567890', companyCode: 'shunfeng', phone: '13800138000' },
      { trackingNumber: 'YT1234567890', companyCode: 'yuantong' },
      { trackingNumber: 'ZT1234567890', companyCode: 'zhongtong' },
      { trackingNumber: 'YD1234567890', companyCode: 'yunda' },
      { trackingNumber: 'ST1234567890', companyCode: 'shentong' }
    ]

    const results = []

    for (const testCase of testCases) {
      try {
        const startTime = Date.now()
        
        const params = {
          tracking_number: testCase.trackingNumber,
          company_code: testCase.companyCode
        }
        
        if (testCase.phone) {
          params.phone = testCase.phone
        }

        const res = await api.logisticsApi.query(params)
        const endTime = Date.now()

        results.push({
          id: Date.now() + Math.random(),
          timestamp: new Date().toLocaleString(),
          params: params,
          response: res,
          duration: endTime - startTime,
          success: res.code === 200
        })

        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000))

      } catch (error) {
        results.push({
          id: Date.now() + Math.random(),
          timestamp: new Date().toLocaleString(),
          params: testCase,
          error: error.message,
          duration: 0,
          success: false
        })
      }
    }

    // 更新测试结果
    const allResults = [...results, ...this.data.testResults].slice(0, 20)
    this.setData({ 
      testResults: allResults,
      loading: false 
    })

    const successCount = results.filter(r => r.success).length
    wx.showModal({
      title: '批量测试完成',
      content: `成功: ${successCount}/${results.length}`,
      showCancel: false
    })
  },

  // 测试API配置
  async testApiConfig() {
    this.setData({ loading: true })

    try {
      const res = await api.logisticsApi.testApi()
      
      if (res.code === 200) {
        wx.showModal({
          title: 'API配置测试',
          content: '✅ API配置正常',
          showCancel: false
        })
      } else {
        wx.showModal({
          title: 'API配置测试',
          content: `❌ ${res.message}`,
          showCancel: false
        })
      }
    } catch (error) {
      wx.showModal({
        title: 'API配置测试',
        content: `❌ 网络错误: ${error.message}`,
        showCancel: false
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 查看测试结果详情
  viewResultDetail(e) {
    const { id } = e.currentTarget.dataset
    const result = this.data.testResults.find(r => r.id === id)
    
    if (result) {
      const content = JSON.stringify(result.response || result.error, null, 2)
      
      wx.showModal({
        title: '测试结果详情',
        content: content.length > 200 ? content.substring(0, 200) + '...' : content,
        showCancel: false
      })
    }
  },

  // 清空测试结果
  clearResults() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有测试结果吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ testResults: [] })
        }
      }
    })
  },

  // 复制测试结果
  copyResults() {
    const results = JSON.stringify(this.data.testResults, null, 2)
    
    wx.setClipboardData({
      data: results,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        })
      }
    })
  }
})

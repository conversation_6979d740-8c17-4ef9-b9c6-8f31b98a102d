const api = require('../../api/index.js')

Page({
  data: {
    // 页面状态
    loading: false,
    
    // 物流信息
    logisticsInfo: null,
    logisticsTracks: [],
    
    // 物流ID
    logisticsId: null
  },

  onLoad(options) {
    console.log('📦 物流详情页面加载', options)
    
    if (options.id) {
      this.setData({ logisticsId: options.id })
      this.loadLogisticsDetail()
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onShow() {
    console.log('📦 物流详情页面显示')
  },

  onPullDownRefresh() {
    console.log('📦 下拉刷新物流详情')
    this.loadLogisticsDetail()
  },

  // 加载物流详情
  async loadLogisticsDetail() {
    if (!this.data.logisticsId) return

    this.setData({ loading: true })

    try {
      // 并行加载物流信息和轨迹
      const [logisticsRes, tracksRes] = await Promise.all([
        api.logisticsApi.getDetail(this.data.logisticsId),
        api.logisticsApi.getTracks(this.data.logisticsId)
      ])

      console.log('✅ 物流详情API响应:', logisticsRes)
      console.log('✅ 物流轨迹API响应:', tracksRes)

      // 处理物流详情数据 - 支持DRF格式和统一响应格式
      let logistics = null
      if (logisticsRes.code === 200) {
        // 统一响应格式
        logistics = logisticsRes.data
      } else if (logisticsRes.id) {
        // DRF格式，直接使用返回的对象
        logistics = logisticsRes
      }

      if (logistics) {
        
        this.setData({
          logisticsInfo: {
            ...logistics,
            status_color: this.getStatusColor(logistics.status),
            status_display: this.getStatusDisplay(logistics.status),
            express_company_name: logistics.express_company?.name || '未知快递',
            express_company_phone: logistics.express_company?.phone || '',
            shipped_date: this.formatDateTime(logistics.shipped_at),
            delivered_date: this.formatDateTime(logistics.delivered_at),
            estimated_date: this.formatDateTime(logistics.estimated_delivery),
            package_info: `${logistics.package_count || 1}件 ${logistics.weight || 0}kg`,
            shipping_info: `运费: ¥${logistics.shipping_fee || 0} | 保价: ¥${logistics.insurance_fee || 0}`
          }
        })

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: `${logistics.express_company?.name || '物流'} - ${logistics.tracking_number}`
        })
      }

      // 处理物流轨迹数据 - 支持DRF格式和统一响应格式
      let tracks = []
      if (tracksRes.code === 200) {
        // 统一响应格式
        tracks = tracksRes.data || []
      } else if (Array.isArray(tracksRes)) {
        // 直接数组格式
        tracks = tracksRes
      } else if (tracksRes.results) {
        // DRF分页格式
        tracks = tracksRes.results || []
      }

      this.setData({
        logisticsTracks: tracks.map((track, index) => ({
          ...track,
          formatted_time: this.formatDateTime(track.timestamp),
          status_display: this.getStatusDisplay(track.status),
          is_latest: index === 0,
          status_icon: this.getStatusIcon(track.status)
        }))
      })

    } catch (error) {
      console.error('加载物流详情失败:', error)

      // 显示具体错误信息
      let errorMessage = '加载失败'
      if (error.code === 404) {
        errorMessage = '物流信息不存在'
      } else if (error.code === 403) {
        errorMessage = '无权限访问'
      } else if (error.message) {
        errorMessage = error.message
      }

      wx.showModal({
        title: '加载失败',
        content: errorMessage,
        showCancel: false,
        success: () => {
          wx.navigateBack()
        }
      })
    } finally {
      this.setData({ loading: false })
      wx.stopPullDownRefresh()
    }
  },

  // 手动同步物流状态
  async syncLogisticsStatus() {
    if (!this.data.logisticsId) return

    wx.showLoading({
      title: '同步中...',
      mask: true
    })

    try {
      const res = await api.logisticsApi.syncStatus(this.data.logisticsId)

      if (res.code === 200) {
        wx.showToast({
          title: '同步成功',
          icon: 'success'
        })

        // 重新加载数据
        this.loadLogisticsDetail()
      } else {
        wx.showToast({
          title: res.message || '同步失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('同步物流状态失败:', error)
      wx.showToast({
        title: '同步失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 拨打客服电话
  callService() {
    const phone = this.data.logisticsInfo?.express_company_phone

    if (!phone) {
      wx.showToast({
        title: '暂无客服电话',
        icon: 'none'
      })
      return
    }

    wx.makePhoneCall({
      phoneNumber: phone
    })
  },

  // 复制快递单号
  copyTrackingNumber() {
    const trackingNumber = this.data.logisticsInfo?.tracking_number

    if (!trackingNumber) {
      wx.showToast({
        title: '暂无快递单号',
        icon: 'none'
      })
      return
    }

    wx.setClipboardData({
      data: trackingNumber,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        })
      }
    })
  },

  // 查看订单详情
  viewOrderDetail() {
    const orderId = this.data.logisticsInfo?.order_id

    if (!orderId) {
      wx.showToast({
        title: '暂无订单信息',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/orders/detail?id=${orderId}`
    })
  },

  // 分享物流信息
  onShareAppMessage() {
    const logistics = this.data.logisticsInfo

    if (!logistics) {
      return {
        title: '物流信息',
        path: '/pages/logistics/logistics'
      }
    }

    return {
      title: `${logistics.express_company_name} - ${logistics.tracking_number}`,
      path: `/pages/logistics/detail?id=${this.data.logisticsId}`,
      imageUrl: '/images/share-logistics.jpg'
    }
  },

  // 获取状态显示文本
  getStatusDisplay(status) {
    const statusMap = {
      'pending': '待发货',
      'shipped': '已发货',
      'in_transit': '运输中',
      'out_for_delivery': '派送中',
      'delivered': '已签收',
      'exception': '异常',
      'returned': '已退回'
    }
    return statusMap[status] || status
  },

  // 获取状态颜色
  getStatusColor(status) {
    const colors = {
      pending: '#ffc107',
      shipped: '#17a2b8',
      in_transit: '#007bff',
      out_for_delivery: '#fd7e14',
      delivered: '#28a745',
      returned: '#6c757d',
      exception: '#dc3545'
    }
    return colors[status] || '#6c757d'
  },

  // 获取状态图标
  getStatusIcon(status) {
    const icons = {
      pending: '⏳',
      shipped: '🚚',
      in_transit: '🚛',
      out_for_delivery: '🚴',
      delivered: '✅',
      returned: '↩️',
      exception: '⚠️'
    }
    return icons[status] || '📦'
  },

  // 格式化日期时间
  formatDateTime(dateStr) {
    if (!dateStr) return ''

    try {
      const date = new Date(dateStr)
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      
      return `${month}-${day} ${hours}:${minutes}`
    } catch (error) {
      return dateStr
    }
  }
})

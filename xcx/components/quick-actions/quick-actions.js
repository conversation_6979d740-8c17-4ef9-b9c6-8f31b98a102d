// 快捷操作组件
const { pageMessenger } = require('../../utils/pageMessenger.js')

Component({
  properties: {
    // 操作列表
    actions: {
      type: Array,
      value: []
    },
    
    // 是否显示更多按钮
    showMore: {
      type: Boolean,
      value: false
    },
    
    // 当前页面类型
    pageType: {
      type: String,
      value: 'order' // order, contract, invoice, logistics
    },
    
    // 当前数据ID
    dataId: {
      type: String,
      value: ''
    }
  },

  data: {
    showModal: false,
    modalData: {},
    pendingAction: null
  },

  methods: {
    // 操作点击
    onActionTap(e) {
      const { action } = e.currentTarget.dataset
      
      if (action.disabled) {
        return
      }

      // 如果需要确认，显示确认弹窗
      if (action.confirm) {
        this.setData({
          showModal: true,
          modalData: {
            title: action.confirmTitle || '确认操作',
            message: action.confirmMessage || `确定要${action.title}吗？`
          },
          pendingAction: action
        })
        return
      }

      // 直接执行操作
      this.executeAction(action)
    },

    // 执行操作
    executeAction(action) {
      try {
        // 触发操作事件
        this.triggerEvent('actionTap', { action })

        // 根据操作类型执行相应逻辑
        switch (action.type) {
          case 'navigate':
            this.handleNavigate(action)
            break
          case 'call':
            this.handleCall(action)
            break
          case 'share':
            this.handleShare(action)
            break
          case 'download':
            this.handleDownload(action)
            break
          case 'sync':
            this.handleSync(action)
            break
          case 'refresh':
            this.handleRefresh(action)
            break
          default:
            // 自定义操作，由父组件处理
            break
        }

        // 显示成功提示
        if (action.successMessage) {
          wx.showToast({
            title: action.successMessage,
            icon: 'success'
          })
        }

      } catch (error) {
        console.error('执行操作失败:', error)
        wx.showToast({
          title: action.errorMessage || '操作失败',
          icon: 'none'
        })
      }
    },

    // 处理导航
    handleNavigate(action) {
      if (action.url) {
        const url = this.replaceUrlParams(action.url)
        
        if (action.redirect) {
          wx.redirectTo({ url })
        } else if (action.reLaunch) {
          wx.reLaunch({ url })
        } else if (action.switchTab) {
          wx.switchTab({ url })
        } else {
          wx.navigateTo({ url })
        }
      }
    },

    // 处理电话拨打
    handleCall(action) {
      if (action.phoneNumber) {
        wx.makePhoneCall({
          phoneNumber: action.phoneNumber
        })
      }
    },

    // 处理分享
    handleShare(action) {
      // 触发分享事件，由页面处理
      this.triggerEvent('share', { action })
    },

    // 处理下载
    handleDownload(action) {
      if (action.downloadUrl) {
        wx.downloadFile({
          url: action.downloadUrl,
          success: (res) => {
            if (res.statusCode === 200) {
              wx.openDocument({
                filePath: res.tempFilePath
              })
            }
          }
        })
      }
    },

    // 处理同步
    handleSync(action) {
      // 触发同步事件
      this.triggerEvent('sync', { action })
    },

    // 处理刷新
    handleRefresh(action) {
      // 发送刷新消息
      pageMessenger.broadcast('refresh', {
        source: this.data.pageType,
        dataId: this.data.dataId
      })
    },

    // 替换URL参数
    replaceUrlParams(url) {
      return url
        .replace('{dataId}', this.data.dataId)
        .replace('{pageType}', this.data.pageType)
    },

    // 更多操作
    onMoreTap() {
      this.triggerEvent('moreTap', {
        pageType: this.data.pageType,
        dataId: this.data.dataId
      })
    },

    // 确认操作
    confirmAction() {
      if (this.data.pendingAction) {
        this.executeAction(this.data.pendingAction)
      }
      this.closeModal()
    },

    // 关闭弹窗
    closeModal() {
      this.setData({
        showModal: false,
        modalData: {},
        pendingAction: null
      })
    }
  },

  // 预定义操作配置
  lifetimes: {
    attached() {
      // 根据页面类型设置默认操作
      this.setDefaultActions()
    }
  },

  methods: {
    // 设置默认操作
    setDefaultActions() {
      if (this.data.actions.length === 0) {
        const defaultActions = this.getDefaultActions(this.data.pageType)
        this.setData({ actions: defaultActions })
      }
    },

    // 获取默认操作
    getDefaultActions(pageType) {
      const actionConfigs = {
        order: [
          {
            id: 'view_contract',
            title: '查看合同',
            description: '查看相关合同信息',
            icon: '📄',
            type: 'navigate',
            url: '/pages/contracts/contracts?orderId={dataId}'
          },
          {
            id: 'view_invoice',
            title: '查看发票',
            description: '查看发票开具情况',
            icon: '🧾',
            type: 'navigate',
            url: '/pages/invoices/invoices?orderId={dataId}'
          },
          {
            id: 'track_logistics',
            title: '物流跟踪',
            description: '查看物流配送进度',
            icon: '🚚',
            type: 'navigate',
            url: '/pages/logistics/detail?orderId={dataId}'
          }
        ],
        contract: [
          {
            id: 'view_order',
            title: '查看订单',
            description: '查看相关订单信息',
            icon: '📋',
            type: 'navigate',
            url: '/pages/order-detail/order-detail?orderId={dataId}'
          },
          {
            id: 'download_contract',
            title: '下载合同',
            description: '下载合同PDF文件',
            icon: '📥',
            type: 'download'
          }
        ],
        invoice: [
          {
            id: 'view_order',
            title: '查看订单',
            description: '查看相关订单信息',
            icon: '📋',
            type: 'navigate',
            url: '/pages/order-detail/order-detail?orderId={dataId}'
          },
          {
            id: 'download_invoice',
            title: '下载发票',
            description: '下载发票PDF文件',
            icon: '📥',
            type: 'download'
          }
        ],
        logistics: [
          {
            id: 'view_order',
            title: '查看订单',
            description: '查看相关订单信息',
            icon: '📋',
            type: 'navigate',
            url: '/pages/order-detail/order-detail?orderId={dataId}'
          },
          {
            id: 'sync_status',
            title: '同步状态',
            description: '更新最新物流信息',
            icon: '🔄',
            type: 'sync',
            successMessage: '同步成功'
          },
          {
            id: 'contact_courier',
            title: '联系快递员',
            description: '拨打快递员电话',
            icon: '📞',
            type: 'call',
            phoneNumber: '************'
          }
        ]
      }

      return actionConfigs[pageType] || []
    }
  }
})

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微信登录流程
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth import get_user_model
from apps.notifications.models import WechatUserInfo
from apps.users.models import UserProfile
from apps.users.wechat_service import WechatUserManager
from api.v1.views import wechat_login
from api.v1.serializers import UserDetailSerializer, WechatUserSerializer

User = get_user_model()


def test_wechat_user_manager():
    """测试微信用户管理器"""
    print("🧪 测试微信用户管理器...")
    
    # 模拟前端传递的用户信息
    test_user_info = {
        'nickName': '测试茶友小王',
        'avatarUrl': 'https://thirdwx.qlogo.cn/mmopen/test_avatar_wang.jpg',
        'gender': 1,
        'city': '广州',
        'province': '广东',
        'country': '中国',
        'language': 'zh_CN'
    }
    
    test_openid = 'test_openid_wechat_flow_123'
    
    print(f"📱 测试OpenID: {test_openid}")
    print(f"👤 测试用户信息: {json.dumps(test_user_info, ensure_ascii=False, indent=2)}")
    
    # 测试创建或更新用户
    result = WechatUserManager.create_or_update_user(test_openid, test_user_info)
    
    if result['success']:
        user = result['user']
        wechat_user = result['wechat_user']
        is_new_user = result['is_new_user']
        
        print(f"\n✅ 用户创建/更新成功:")
        print(f"   用户名: {user.username}")
        print(f"   昵称: {user.nickname}")
        print(f"   是否新用户: {is_new_user}")
        
        print(f"\n📱 微信用户信息:")
        print(f"   OpenID: {wechat_user.openid}")
        print(f"   昵称: {wechat_user.nickname}")
        print(f"   头像: {wechat_user.avatar_url}")
        print(f"   性别: {wechat_user.get_gender_display()}")
        print(f"   城市: {wechat_user.city}")
        
        # 检查用户资料
        try:
            profile = UserProfile.objects.get(user=user)
            print(f"\n👤 用户资料:")
            print(f"   头像: {profile.avatar}")
        except UserProfile.DoesNotExist:
            print(f"\n❌ 没有用户资料")
        
        return user, wechat_user
    else:
        print(f"❌ 用户创建/更新失败: {result['error']}")
        return None, None


def test_serializers(user, wechat_user):
    """测试序列化器"""
    print(f"\n🧪 测试序列化器...")
    
    if not user or not wechat_user:
        print("❌ 没有用户数据，跳过序列化器测试")
        return None
    
    # 测试用户详情序列化器
    user_serializer = UserDetailSerializer(user)
    print(f"👤 用户详情序列化:")
    for key, value in user_serializer.data.items():
        if key == 'wechat_info' and value:
            print(f"   {key}:")
            for wk, wv in value.items():
                print(f"     {wk}: {wv}")
        else:
            print(f"   {key}: {value}")
    
    # 测试微信用户序列化器
    wechat_serializer = WechatUserSerializer(wechat_user)
    print(f"\n📱 微信用户序列化:")
    for key, value in wechat_serializer.data.items():
        print(f"   {key}: {value}")
    
    return {
        'user': user_serializer.data,
        'wechat_info': wechat_serializer.data
    }


def test_api_response(serialized_data):
    """测试API响应格式"""
    print(f"\n🧪 测试API响应格式...")
    
    if not serialized_data:
        print("❌ 没有序列化数据，跳过API响应测试")
        return
    
    # 模拟API响应
    api_response = {
        'code': 200,
        'message': '登录成功',
        'data': {
            'token': 'test_token_123456789',
            'user': serialized_data['user'],
            'wechat_info': serialized_data['wechat_info'],
            'is_new_user': False
        }
    }
    
    print(f"🔄 API响应:")
    print(f"   状态码: {api_response['code']}")
    print(f"   消息: {api_response['message']}")
    print(f"   用户昵称: {api_response['data']['user'].get('nickname')}")
    print(f"   用户头像: {api_response['data']['user'].get('avatar')}")
    print(f"   微信昵称: {api_response['data']['wechat_info'].get('nickname')}")
    print(f"   微信头像: {api_response['data']['wechat_info'].get('avatar_url')}")
    
    # 模拟前端合并逻辑
    merged_user_info = {
        **api_response['data']['user'],
        'nickname': api_response['data']['wechat_info'].get('nickname') or api_response['data']['user'].get('nickname'),
        'avatar': api_response['data']['wechat_info'].get('avatar_url') or api_response['data']['user'].get('avatar'),
        'wechat_info': api_response['data']['wechat_info']
    }
    
    print(f"\n🔄 前端合并后的用户信息:")
    print(f"   最终昵称: {merged_user_info.get('nickname')}")
    print(f"   最终头像: {merged_user_info.get('avatar')}")
    
    return merged_user_info


def test_login_api():
    """测试登录API"""
    print(f"\n🧪 测试登录API...")
    
    # 创建请求工厂
    factory = RequestFactory()
    
    # 模拟前端登录请求
    login_data = {
        'code': 'test_code_123456',
        'userInfo': {
            'nickName': '测试茶友小李',
            'avatarUrl': 'https://thirdwx.qlogo.cn/mmopen/test_avatar_li.jpg',
            'gender': 2,
            'city': '深圳',
            'province': '广东',
            'country': '中国',
            'language': 'zh_CN'
        }
    }
    
    print(f"📤 登录请求数据:")
    print(f"   Code: {login_data['code']}")
    print(f"   用户信息: {json.dumps(login_data['userInfo'], ensure_ascii=False, indent=2)}")
    
    # 创建POST请求
    request = factory.post(
        '/api/v1/users/login/',
        data=json.dumps(login_data),
        content_type='application/json'
    )
    
    try:
        # 调用登录视图
        response = wechat_login(request)
        
        print(f"\n📥 API响应:")
        print(f"   状态码: {response.status_code}")
        
        if hasattr(response, 'data'):
            response_data = response.data
            print(f"   响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            if response_data.get('code') == 200:
                print(f"\n✅ 登录成功!")
                user_data = response_data['data']['user']
                wechat_data = response_data['data']['wechat_info']
                
                print(f"   用户昵称: {user_data.get('nickname')}")
                print(f"   微信昵称: {wechat_data.get('nickname')}")
                print(f"   微信头像: {wechat_data.get('avatar_url')}")
            else:
                print(f"❌ 登录失败: {response_data.get('message')}")
        
    except Exception as e:
        print(f"❌ API调用异常: {str(e)}")


def clean_test_data():
    """清理测试数据"""
    print(f"\n🧹 清理测试数据...")
    
    # 删除测试用户
    test_users = User.objects.filter(username__startswith='wx_test_openid_wechat_flow')
    if test_users.exists():
        count = test_users.count()
        test_users.delete()
        print(f"✅ 删除了 {count} 个测试用户")
    else:
        print(f"ℹ️ 没有测试用户需要清理")


def main():
    """主函数"""
    print("🔧 测试微信登录流程")
    print("=" * 60)
    
    # 清理旧的测试数据
    clean_test_data()
    
    # 测试微信用户管理器
    user, wechat_user = test_wechat_user_manager()
    
    # 测试序列化器
    serialized_data = test_serializers(user, wechat_user)
    
    # 测试API响应格式
    merged_info = test_api_response(serialized_data)
    
    # 测试登录API
    test_login_api()
    
    print("\n" + "=" * 60)
    print("✅ 微信登录流程测试完成！")
    
    if merged_info:
        print(f"\n💡 预期前端显示效果:")
        print(f"   个人中心昵称: {merged_info.get('nickname', '茶园投资者')}")
        print(f"   个人中心头像: {'真实微信头像' if merged_info.get('avatar') and 'http' in merged_info.get('avatar', '') else '默认头像'}")
        
        if merged_info.get('nickname') and merged_info.get('nickname') != '茶园投资者':
            print(f"\n✅ 昵称应该正常显示")
        else:
            print(f"\n❌ 昵称可能仍显示默认值")
        
        if merged_info.get('avatar') and 'http' in merged_info.get('avatar', ''):
            print(f"✅ 头像应该正常显示")
        else:
            print(f"❌ 头像可能仍显示默认值")
    
    print(f"\n🔍 下一步测试建议:")
    print(f"1. 在小程序中清除缓存和本地存储")
    print(f"2. 重新进行微信登录授权流程")
    print(f"3. 检查个人中心页面是否显示真实头像和昵称")
    print(f"4. 在Django admin中检查微信用户信息是否正确保存")


if __name__ == "__main__":
    main()

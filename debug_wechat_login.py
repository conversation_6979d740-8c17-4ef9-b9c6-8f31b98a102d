#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试微信登录流程
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.notifications.models import WechatUserInfo
from apps.users.wechat_service import WechatMiniProgramService, WechatUserManager
from api.v1.serializers import UserDetailSerializer, WechatUserSerializer

User = get_user_model()


def test_wechat_config():
    """测试微信配置"""
    print("🔧 检查微信配置...")
    
    from django.conf import settings
    
    appid = getattr(settings, 'WECHAT_APPID', None)
    secret = getattr(settings, 'WECHAT_SECRET', None)
    
    print(f"WECHAT_APPID: {'已配置' if appid else '未配置'}")
    print(f"WECHAT_SECRET: {'已配置' if secret else '未配置'}")
    
    if appid:
        print(f"AppID: {appid[:8]}...")
    if secret:
        print(f"Secret: {secret[:8]}...")


def simulate_wechat_login():
    """模拟微信登录流程"""
    print("\n🧪 模拟微信登录流程...")
    
    # 模拟前端传递的数据
    test_code = "test_code_123456"
    test_user_info = {
        'nickName': '测试用户小明',
        'avatarUrl': 'https://thirdwx.qlogo.cn/mmopen/test_avatar_123.jpg',
        'gender': 1,
        'city': '深圳',
        'province': '广东',
        'country': '中国',
        'language': 'zh_CN'
    }
    
    print(f"模拟code: {test_code}")
    print(f"模拟用户信息: {json.dumps(test_user_info, ensure_ascii=False, indent=2)}")
    
    # 测试code2session
    print("\n🔄 测试code2session...")
    session_result = WechatMiniProgramService.code2session(test_code)
    print(f"Session结果: {session_result}")
    
    if session_result['success']:
        openid = session_result['openid']
        session_key = session_result.get('session_key', '')
        
        print(f"OpenID: {openid}")
        print(f"Session Key: {session_key}")
        
        # 测试创建或更新用户
        print("\n🔄 测试创建或更新用户...")
        user_result = WechatUserManager.create_or_update_user(openid, test_user_info)
        print(f"用户创建结果: {user_result}")
        
        if user_result['success']:
            user = user_result['user']
            wechat_user = user_result['wechat_user']
            
            print(f"\n👤 用户信息:")
            print(f"  ID: {user.id}")
            print(f"  用户名: {user.username}")
            print(f"  昵称: {user.nickname}")
            print(f"  头像: {user.avatar}")
            
            print(f"\n📱 微信用户信息:")
            print(f"  OpenID: {wechat_user.openid}")
            print(f"  昵称: {wechat_user.nickname}")
            print(f"  头像: {wechat_user.avatar_url}")
            print(f"  性别: {wechat_user.get_gender_display()}")
            print(f"  城市: {wechat_user.city}")
            
            # 测试序列化器
            print(f"\n🔄 测试序列化器...")
            user_serializer = UserDetailSerializer(user)
            wechat_serializer = WechatUserSerializer(wechat_user)
            
            print(f"用户序列化结果:")
            print(json.dumps(user_serializer.data, ensure_ascii=False, indent=2))
            
            print(f"\n微信用户序列化结果:")
            print(json.dumps(wechat_serializer.data, ensure_ascii=False, indent=2))
            
            # 模拟API响应
            api_response = {
                'code': 200,
                'message': '登录成功',
                'data': {
                    'token': 'test_token_123456',
                    'user': user_serializer.data,
                    'wechat_info': wechat_serializer.data,
                    'is_new_user': user_result['is_new_user']
                }
            }
            
            print(f"\n🔄 模拟API响应:")
            print(json.dumps(api_response, ensure_ascii=False, indent=2))
            
            return api_response
    
    return None


def test_frontend_merge_logic(api_response):
    """测试前端合并逻辑"""
    if not api_response:
        return
    
    print("\n🔄 测试前端合并逻辑...")
    
    # 模拟前端页面的数据
    page_data = {
        'nickname': '页面输入的昵称',
        'avatarUrl': '/temp/avatar.jpg'
    }
    
    # 模拟前端合并逻辑
    merged_user_info = {
        **api_response['data']['user'],
        # 优先使用微信信息中的昵称和头像
        'nickname': (api_response['data']['wechat_info'].get('nickname') or 
                    api_response['data']['user'].get('nickname') or 
                    page_data['nickname']),
        'avatar': (api_response['data']['wechat_info'].get('avatar_url') or 
                  api_response['data']['user'].get('avatar') or 
                  page_data['avatarUrl']),
        'wechat_info': api_response['data']['wechat_info']
    }
    
    print(f"页面数据: {json.dumps(page_data, ensure_ascii=False, indent=2)}")
    print(f"合并后的用户信息: {json.dumps(merged_user_info, ensure_ascii=False, indent=2)}")
    
    print(f"\n📊 最终显示效果:")
    print(f"  昵称: {merged_user_info.get('nickname', '茶园投资者')}")
    print(f"  头像: {merged_user_info.get('avatar', '默认头像')}")
    
    # 判断是否会显示默认值
    if merged_user_info.get('nickname') == '茶园投资者':
        print("❌ 昵称将显示默认值")
    else:
        print("✅ 昵称将显示真实值")
    
    if not merged_user_info.get('avatar') or merged_user_info.get('avatar') == '默认头像':
        print("❌ 头像将显示默认值")
    else:
        print("✅ 头像将显示真实值")


def check_existing_users():
    """检查现有用户"""
    print("\n📊 检查现有用户...")
    
    users = User.objects.all()
    wechat_users = WechatUserInfo.objects.all()
    
    print(f"总用户数: {users.count()}")
    print(f"微信用户数: {wechat_users.count()}")
    
    for wechat_user in wechat_users[:3]:
        print(f"\n用户: {wechat_user.user.username}")
        print(f"  用户昵称: {wechat_user.user.nickname}")
        print(f"  用户头像: {wechat_user.user.avatar}")
        print(f"  微信昵称: {wechat_user.nickname}")
        print(f"  微信头像: {wechat_user.avatar_url}")
        
        # 测试序列化
        user_data = UserDetailSerializer(wechat_user.user).data
        wechat_data = WechatUserSerializer(wechat_user).data
        
        print(f"  序列化用户昵称: {user_data.get('nickname')}")
        print(f"  序列化微信昵称: {wechat_data.get('nickname')}")


def test_avatar_upload():
    """测试头像上传逻辑"""
    print("\n📸 测试头像上传逻辑...")
    
    # 检查是否有文件上传相关的配置
    from django.conf import settings
    
    print(f"MEDIA_URL: {getattr(settings, 'MEDIA_URL', '未配置')}")
    print(f"MEDIA_ROOT: {getattr(settings, 'MEDIA_ROOT', '未配置')}")
    
    # 检查是否有头像上传的API
    print("检查头像上传API...")
    
    # 这里可以添加更多头像上传相关的测试


def main():
    """主函数"""
    print("🔧 微信登录流程调试")
    print("=" * 60)
    
    # 检查微信配置
    test_wechat_config()
    
    # 检查现有用户
    check_existing_users()
    
    # 模拟登录流程
    api_response = simulate_wechat_login()
    
    # 测试前端合并逻辑
    test_frontend_merge_logic(api_response)
    
    # 测试头像上传
    test_avatar_upload()
    
    print("\n" + "=" * 60)
    print("🎯 调试总结:")
    print("1. 检查微信配置是否正确")
    print("2. 检查用户信息是否正确保存")
    print("3. 检查序列化器是否返回正确数据")
    print("4. 检查前端合并逻辑是否正确")
    print("5. 检查头像上传是否正常工作")
    
    print("\n💡 可能的问题:")
    print("1. 微信配置未正确设置")
    print("2. 头像是临时路径，需要上传到服务器")
    print("3. 前端没有正确传递用户信息")
    print("4. 后端没有正确保存微信用户信息")
    print("5. 序列化器没有返回完整的微信信息")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的登录流程
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth import get_user_model
from apps.notifications.models import WechatUserInfo
from api.v1.views import wechat_login, user_profile

User = get_user_model()


def test_wechat_login_api():
    """测试微信登录API"""
    print("🧪 测试微信登录API...")
    
    factory = RequestFactory()
    
    # 模拟前端登录请求（使用修复后的格式）
    login_data = {
        'code': 'test_code_fixed_user',
        'userInfo': {
            'nickName': '真实茶友小张',
            'avatarUrl': 'https://thirdwx.qlogo.cn/mmopen/real_avatar_zhang.jpg',
            'gender': 1,
            'city': '杭州',
            'province': '浙江',
            'country': '中国',
            'language': 'zh_CN'
        }
    }
    
    print(f"📤 登录请求数据:")
    print(f"   Code: {login_data['code']}")
    print(f"   用户信息: {json.dumps(login_data['userInfo'], ensure_ascii=False, indent=2)}")
    
    # 创建POST请求
    request = factory.post(
        '/api/v1/users/login/',
        data=json.dumps(login_data),
        content_type='application/json'
    )
    
    try:
        # 调用登录视图
        response = wechat_login(request)
        
        print(f"\n📥 登录API响应:")
        print(f"   状态码: {response.status_code}")
        
        if hasattr(response, 'data'):
            response_data = response.data
            print(f"   响应代码: {response_data.get('code')}")
            print(f"   响应消息: {response_data.get('message')}")
            
            if response_data.get('code') == 200:
                print(f"\n✅ 登录成功!")
                user_data = response_data['data']['user']
                wechat_data = response_data['data']['wechat_info']
                token = response_data['data']['token']
                
                print(f"   Token: {token}")
                print(f"   用户ID: {user_data.get('id')}")
                print(f"   用户名: {user_data.get('username')}")
                print(f"   用户昵称: {user_data.get('nickname')}")
                print(f"   用户头像: {user_data.get('avatar')}")
                print(f"   微信昵称: {wechat_data.get('nickname')}")
                print(f"   微信头像: {wechat_data.get('avatar_url')}")
                
                return {
                    'token': token,
                    'user_id': user_data.get('id'),
                    'response_data': response_data
                }
            else:
                print(f"❌ 登录失败: {response_data.get('message')}")
                return None
        
    except Exception as e:
        print(f"❌ 登录API调用异常: {str(e)}")
        return None


def test_user_profile_api(token, user_id):
    """测试用户资料API"""
    print(f"\n🧪 测试用户资料API...")
    
    if not token or not user_id:
        print("❌ 没有token或用户ID，跳过测试")
        return None
    
    factory = RequestFactory()
    
    # 创建GET请求，带上认证头
    request = factory.get('/api/v1/users/profile/')
    request.META['HTTP_AUTHORIZATION'] = f'Token {token}'
    
    try:
        # 调用用户资料视图
        response = user_profile(request)
        
        print(f"📥 用户资料API响应:")
        print(f"   状态码: {response.status_code}")
        
        if hasattr(response, 'data'):
            response_data = response.data
            print(f"   响应代码: {response_data.get('code')}")
            
            if response_data.get('code') == 200:
                print(f"\n✅ 获取用户资料成功!")
                user_data = response_data['data']
                
                print(f"   用户ID: {user_data.get('id')}")
                print(f"   用户名: {user_data.get('username')}")
                print(f"   昵称: {user_data.get('nickname')}")
                print(f"   头像: {user_data.get('avatar')}")
                print(f"   手机: {user_data.get('phone')}")
                print(f"   邮箱: {user_data.get('email')}")
                print(f"   性别: {user_data.get('gender')}")
                print(f"   城市: {user_data.get('city')}")
                
                # 检查是否有微信信息
                wechat_info = user_data.get('wechat_info')
                if wechat_info:
                    print(f"   微信信息:")
                    print(f"     OpenID: {wechat_info.get('openid')}")
                    print(f"     昵称: {wechat_info.get('nickname')}")
                    print(f"     头像: {wechat_info.get('avatar_url')}")
                    print(f"     性别: {wechat_info.get('gender')}")
                    print(f"     城市: {wechat_info.get('city')}")
                else:
                    print(f"   ❌ 没有微信信息")
                
                return user_data
            else:
                print(f"❌ 获取用户资料失败: {response_data.get('message')}")
                return None
        
    except Exception as e:
        print(f"❌ 用户资料API调用异常: {str(e)}")
        return None


def test_frontend_merge_logic(login_response_data):
    """测试前端合并逻辑"""
    print(f"\n🧪 测试前端合并逻辑...")
    
    if not login_response_data:
        print("❌ 没有登录响应数据，跳过测试")
        return None
    
    # 模拟前端合并逻辑
    user_data = login_response_data['data']['user']
    wechat_data = login_response_data['data']['wechat_info']
    
    merged_user_info = {
        **user_data,
        'nickname': wechat_data.get('nickname') or user_data.get('nickname'),
        'avatar': wechat_data.get('avatar_url') or user_data.get('avatar'),
        'wechat_info': wechat_data
    }
    
    print(f"🔄 前端合并逻辑结果:")
    print(f"   最终昵称: {merged_user_info.get('nickname')}")
    print(f"   最终头像: {merged_user_info.get('avatar')}")
    print(f"   是否有微信信息: {'是' if merged_user_info.get('wechat_info') else '否'}")
    
    # 检查合并效果
    if merged_user_info.get('nickname') and merged_user_info.get('nickname') != '茶园投资者':
        print(f"✅ 昵称合并成功")
    else:
        print(f"❌ 昵称合并失败")
    
    if merged_user_info.get('avatar') and 'http' in merged_user_info.get('avatar', ''):
        print(f"✅ 头像合并成功")
    else:
        print(f"❌ 头像合并失败")
    
    return merged_user_info


def check_database_consistency():
    """检查数据库一致性"""
    print(f"\n🧪 检查数据库一致性...")
    
    # 检查最新创建的用户
    latest_user = User.objects.filter(username__startswith='wx_test_').order_by('-id').first()
    
    if latest_user:
        print(f"📊 最新用户信息:")
        print(f"   ID: {latest_user.id}")
        print(f"   用户名: {latest_user.username}")
        print(f"   昵称: {latest_user.nickname}")
        print(f"   性别: {latest_user.gender}")
        print(f"   城市: {latest_user.city}")
        
        # 检查微信用户信息
        try:
            wechat_user = WechatUserInfo.objects.get(user=latest_user)
            print(f"   微信信息:")
            print(f"     OpenID: {wechat_user.openid}")
            print(f"     昵称: {wechat_user.nickname}")
            print(f"     头像: {wechat_user.avatar_url}")
            print(f"     性别: {wechat_user.gender}")
            print(f"     城市: {wechat_user.city}")
            
            # 检查一致性
            if latest_user.nickname == wechat_user.nickname:
                print(f"✅ 用户昵称与微信昵称一致")
            else:
                print(f"❌ 用户昵称与微信昵称不一致")
            
            return True
            
        except WechatUserInfo.DoesNotExist:
            print(f"❌ 没有对应的微信用户信息")
            return False
    else:
        print(f"❌ 没有找到测试用户")
        return False


def main():
    """主函数"""
    print("🔧 测试完整的登录流程")
    print("=" * 60)
    
    # 1. 测试微信登录API
    login_result = test_wechat_login_api()
    
    # 2. 测试用户资料API
    if login_result:
        profile_data = test_user_profile_api(login_result['token'], login_result['user_id'])
        
        # 3. 测试前端合并逻辑
        merged_info = test_frontend_merge_logic(login_result['response_data'])
        
        # 4. 检查数据库一致性
        db_consistent = check_database_consistency()
        
        print("\n" + "=" * 60)
        print("✅ 完整流程测试完成！")
        
        print(f"\n💡 测试结果总结:")
        print(f"   登录API: {'✅ 成功' if login_result else '❌ 失败'}")
        print(f"   用户资料API: {'✅ 成功' if profile_data else '❌ 失败'}")
        print(f"   前端合并逻辑: {'✅ 成功' if merged_info else '❌ 失败'}")
        print(f"   数据库一致性: {'✅ 一致' if db_consistent else '❌ 不一致'}")
        
        if merged_info:
            print(f"\n🎯 预期前端显示:")
            print(f"   个人中心昵称: {merged_info.get('nickname', '茶园投资者')}")
            print(f"   个人中心头像: {'真实头像' if merged_info.get('avatar') and 'http' in merged_info.get('avatar', '') else '默认头像'}")
    
    else:
        print("\n❌ 登录失败，无法继续测试")
    
    print(f"\n🔍 下一步建议:")
    print(f"1. 在小程序中清除缓存：开发工具 -> 清缓存 -> 清除数据缓存")
    print(f"2. 重新进行微信登录授权流程")
    print(f"3. 在个人中心页面下拉刷新")
    print(f"4. 检查开发工具控制台是否有错误日志")


if __name__ == "__main__":
    main()

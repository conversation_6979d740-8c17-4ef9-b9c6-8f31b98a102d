# 微信小程序2025年用户授权修复方案

## 🔍 问题分析

### 微信政策变化
1. **`wx.getUserProfile` 已废弃**（2022年11月8日24时）
2. **`wx.getUserInfo` 已废弃**（2021年4月28日24时）
3. **新政策**：用户头像昵称必须通过手动交互获取

### 当前项目问题
1. 前端登录流程仍使用废弃API
2. 后端期望接收完整用户信息
3. 微信用户管理页面为空
4. 前后端数据不同步

## 🔧 修复方案

### 1. 前端修复 - 新版用户授权流程

#### 登录页面模板修改 (login.wxml)
```xml
<!-- 头像选择 -->
<view class="avatar-section">
  <text class="section-title">选择头像</text>
  <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
    <image class="avatar" src="{{userAvatar || defaultAvatar}}" mode="aspectFill"></image>
    <view class="avatar-tip">点击选择头像</view>
  </button>
</view>

<!-- 昵称输入 -->
<view class="nickname-section">
  <text class="section-title">输入昵称</text>
  <input 
    type="nickname" 
    class="nickname-input" 
    placeholder="请输入昵称"
    value="{{userNickname}}"
    bind:input="onNicknameInput"
    bind:blur="onNicknameBlur"
  />
</view>

<!-- 登录按钮 -->
<button 
  class="login-btn" 
  bind:tap="performWechatLogin"
  disabled="{{!canLogin}}"
>
  微信登录
</button>
```

#### 登录页面逻辑修改 (login.js)
```javascript
Page({
  data: {
    userAvatar: '',
    userNickname: '',
    defaultAvatar: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
    canLogin: false
  },

  // 选择头像
  onChooseAvatar(e) {
    console.log('🖼️ 用户选择头像:', e.detail)
    const { avatarUrl } = e.detail
    
    this.setData({
      userAvatar: avatarUrl
    })
    
    this.checkCanLogin()
  },

  // 输入昵称
  onNicknameInput(e) {
    const nickname = e.detail.value
    this.setData({
      userNickname: nickname
    })
    
    this.checkCanLogin()
  },

  // 昵称失焦检查
  onNicknameBlur(e) {
    // 微信会自动进行内容安全检测
    // 如果不通过，会自动清空内容
    setTimeout(() => {
      this.checkCanLogin()
    }, 100)
  },

  // 检查是否可以登录
  checkCanLogin() {
    const canLogin = this.data.userAvatar && 
                    this.data.userNickname && 
                    this.data.userNickname.trim().length > 0
    
    this.setData({ canLogin })
  },

  // 执行微信登录
  async performWechatLogin() {
    if (!this.data.canLogin) {
      wx.showToast({
        title: '请先选择头像和输入昵称',
        icon: 'none'
      })
      return
    }

    wx.showLoading({ title: '登录中...' })

    try {
      // 1. 获取登录凭证
      const loginRes = await this.wxLogin()
      
      // 2. 构造用户信息
      const userInfo = {
        nickName: this.data.userNickname,
        avatarUrl: this.data.userAvatar,
        gender: 0, // 新版API无法获取性别
        city: '', // 新版API无法获取城市
        province: '', // 新版API无法获取省份
        country: '', // 新版API无法获取国家
        language: 'zh_CN'
      }

      // 3. 调用后端登录API
      const result = await api.userApi.wechatLogin({
        code: loginRes.code,
        userInfo: userInfo
      })

      if (result.code === 200) {
        // 登录成功
        const app = getApp()
        app.login(result.data.user, result.data.token)

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 跳转到主页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          })
        }, 1000)
      } else {
        throw new Error(result.message || '登录失败')
      }

    } catch (error) {
      console.error('微信登录失败:', error)
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 微信登录
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  }
})
```

### 2. 后端修复 - 适配新版用户信息

#### 修改微信登录API (api/v1/views.py)
```python
@api_view(['POST'])
@permission_classes([AllowAny])
def wechat_login(request):
    """微信登录 - 适配2025年新版授权"""
    try:
        data = request.data
        code = data.get('code')
        user_info = data.get('userInfo', {})
        
        if not code:
            return Response({
                'code': 400,
                'message': '缺少登录凭证'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 通过code获取openid
        session_result = WechatMiniProgramService.code2session(code)
        if not session_result['success']:
            return Response({
                'code': 400,
                'message': session_result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

        openid = session_result['openid']
        
        # 创建或更新用户（新版逻辑）
        user_result = WechatUserManager.create_or_update_user_v2(openid, user_info)
        if not user_result['success']:
            return Response({
                'code': 400,
                'message': user_result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

        user = user_result['user']
        wechat_user = user_result['wechat_user']
        
        # 生成token
        token, created = Token.objects.get_or_create(user=user)
        
        return Response({
            'code': 200,
            'message': '登录成功',
            'data': {
                'token': token.key,
                'user': UserDetailSerializer(user).data,
                'wechat_info': WechatUserSerializer(wechat_user).data,
                'is_new_user': user_result['is_new_user']
            }
        })

    except Exception as e:
        logger.error(f'微信登录异常: {e}', exc_info=True)
        return Response({
            'code': 500,
            'message': '登录失败，请重试'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
```

#### 新增用户管理器方法 (apps/users/wechat_service.py)
```python
class WechatUserManager:
    
    @classmethod
    def create_or_update_user_v2(cls, openid, user_info=None):
        """创建或更新微信用户 - 2025年新版"""
        from .models import User
        from apps.notifications.models import WechatUserInfo
        
        try:
            # 先查找是否已有微信用户记录
            try:
                wechat_user = WechatUserInfo.objects.get(openid=openid)
                user = wechat_user.user
                is_new_user = False
                
                # 更新用户信息（如果提供了新信息）
                if user_info:
                    # 更新微信用户信息
                    if user_info.get('nickName'):
                        wechat_user.nickname = user_info['nickName']
                        user.nickname = user_info['nickName']
                    
                    if user_info.get('avatarUrl'):
                        wechat_user.avatar_url = user_info['avatarUrl']
                        
                        # 更新用户资料头像
                        from .models import UserProfile
                        profile, created = UserProfile.objects.get_or_create(user=user)
                        profile.avatar = user_info['avatarUrl']
                        profile.save()
                    
                    # 保存其他信息
                    wechat_user.gender = user_info.get('gender', 0)
                    wechat_user.city = user_info.get('city', '')
                    wechat_user.province = user_info.get('province', '')
                    wechat_user.country = user_info.get('country', '')
                    wechat_user.language = user_info.get('language', 'zh_CN')
                    
                    wechat_user.save()
                    user.save()
                
            except WechatUserInfo.DoesNotExist:
                # 创建新用户
                if not WechatConfigManager.is_auto_register_enabled():
                    return {
                        'success': False,
                        'error': '自动注册已关闭，请联系管理员'
                    }
                
                # 生成唯一用户名
                base_username = f"wx_{openid[:16]}"
                username = base_username
                counter = 1
                while User.objects.filter(username=username).exists():
                    username = f"{base_username}_{counter}"
                    counter += 1
                
                # 创建用户
                user = User.objects.create_user(
                    username=username,
                    nickname=user_info.get('nickName', '微信用户') if user_info else '微信用户',
                    gender=user_info.get('gender', 0) if user_info else 0,
                    city=user_info.get('city', '') if user_info else '',
                    province=user_info.get('province', '') if user_info else '',
                    country=user_info.get('country', '') if user_info else '',
                )
                
                # 创建用户资料
                avatar_url = user_info.get('avatarUrl', '') if user_info else ''
                if avatar_url:
                    from .models import UserProfile
                    UserProfile.objects.create(
                        user=user,
                        avatar=avatar_url
                    )
                
                # 创建微信用户信息
                wechat_user = WechatUserInfo.objects.create(
                    user=user,
                    openid=openid,
                    nickname=user_info.get('nickName', '') if user_info else '',
                    avatar_url=user_info.get('avatarUrl', '') if user_info else '',
                    gender=user_info.get('gender', 0) if user_info else 0,
                    city=user_info.get('city', '') if user_info else '',
                    province=user_info.get('province', '') if user_info else '',
                    country=user_info.get('country', '') if user_info else '',
                    language=user_info.get('language', 'zh_CN') if user_info else 'zh_CN',
                )
                
                is_new_user = True
            
            return {
                'success': True,
                'user': user,
                'wechat_user': wechat_user,
                'is_new_user': is_new_user
            }
            
        except Exception as e:
            logger.error(f'创建或更新微信用户失败: {e}')
            return {
                'success': False,
                'error': '用户信息处理失败'
            }
```

### 3. 后台管理修复

#### 微信用户管理页面显示修复
确保后台能正确显示微信用户信息，需要检查admin配置。

## 📱 实施步骤

1. **修改前端登录页面**：实现新版头像昵称选择
2. **更新后端API**：适配新版用户信息格式
3. **测试登录流程**：确保前后端数据同步
4. **验证后台显示**：检查微信用户管理页面

## 🎯 预期效果

修复后：
- 用户可以手动选择头像和输入昵称
- 前后端数据正确同步
- 后台微信用户管理页面显示完整信息
- 个人中心显示真实头像和昵称

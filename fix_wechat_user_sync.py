#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复微信用户头像昵称同步问题
基于老版本teabuy3的成功实现进行修复
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.notifications.models import WechatUserInfo
from apps.users.models import UserProfile, WechatUser

User = get_user_model()


def analyze_current_state():
    """分析当前状态"""
    print("🔍 分析当前微信用户数据状态...")
    
    # 检查两个微信用户表的数据
    wechat_users_old = WechatUser.objects.all()
    wechat_users_new = WechatUserInfo.objects.all()
    
    print(f"旧微信用户表 (WechatUser): {wechat_users_old.count()} 条记录")
    for wu in wechat_users_old:
        print(f"  OpenID: {wu.openid}, 用户: {wu.user.username if wu.user else '无'}, 昵称: '{wu.nickname}', 头像: '{wu.avatar_url}'")
    
    print(f"新微信用户表 (WechatUserInfo): {wechat_users_new.count()} 条记录")
    for wu in wechat_users_new:
        print(f"  OpenID: {wu.openid}, 用户: {wu.user.username}, 昵称: '{wu.nickname}', 头像: '{wu.avatar_url}'")
    
    # 检查用户表
    users = User.objects.all()
    print(f"用户表: {users.count()} 条记录")
    for user in users:
        print(f"  用户: {user.username}, 昵称: '{user.nickname}', 头像: '{getattr(user, 'avatar', '无此字段')}'")
        
        # 检查用户资料
        try:
            profile = UserProfile.objects.get(user=user)
            print(f"    资料头像: '{profile.avatar}'")
        except UserProfile.DoesNotExist:
            print(f"    无用户资料")


def migrate_data_to_unified_model():
    """迁移数据到统一模型"""
    print(f"\n🔄 迁移数据到统一模型...")
    
    # 检查是否有旧模型的数据需要迁移
    old_wechat_users = WechatUser.objects.all()
    
    for old_wu in old_wechat_users:
        print(f"处理旧微信用户: {old_wu.openid}")
        
        # 检查新模型中是否已存在
        try:
            new_wu = WechatUserInfo.objects.get(openid=old_wu.openid)
            print(f"  新模型中已存在，更新数据...")
            
            # 更新数据（如果旧数据更完整）
            if old_wu.nickname and not new_wu.nickname:
                new_wu.nickname = old_wu.nickname
            if old_wu.avatar_url and not new_wu.avatar_url:
                new_wu.avatar_url = old_wu.avatar_url
            if old_wu.gender and not new_wu.gender:
                new_wu.gender = old_wu.gender
            if old_wu.city and not new_wu.city:
                new_wu.city = old_wu.city
            if old_wu.province and not new_wu.province:
                new_wu.province = old_wu.province
            if old_wu.country and not new_wu.country:
                new_wu.country = old_wu.country
                
            new_wu.save()
            print(f"  ✅ 更新完成")
            
        except WechatUserInfo.DoesNotExist:
            print(f"  新模型中不存在，创建新记录...")
            
            # 创建新记录
            if old_wu.user:
                WechatUserInfo.objects.create(
                    user=old_wu.user,
                    openid=old_wu.openid,
                    unionid=old_wu.unionid or '',
                    nickname=old_wu.nickname or '',
                    avatar_url=old_wu.avatar_url or '',
                    gender=old_wu.gender or 0,
                    city=old_wu.city or '',
                    province=old_wu.province or '',
                    country=old_wu.country or '',
                    language=old_wu.language or 'zh_CN'
                )
                print(f"  ✅ 创建完成")
            else:
                print(f"  ⚠️ 旧记录没有关联用户，跳过")


def fix_user_avatar_nickname():
    """修复用户头像和昵称"""
    print(f"\n🔧 修复用户头像和昵称...")
    
    users = User.objects.all()
    
    for user in users:
        print(f"处理用户: {user.username}")
        
        # 获取微信用户信息
        try:
            wechat_user = WechatUserInfo.objects.get(user=user)
            print(f"  找到微信信息: 昵称='{wechat_user.nickname}', 头像='{wechat_user.avatar_url}'")
            
            # 更新用户基本信息
            updated = False
            if wechat_user.nickname and user.nickname != wechat_user.nickname:
                user.nickname = wechat_user.nickname
                updated = True
                print(f"  更新用户昵称: '{wechat_user.nickname}'")
            
            if wechat_user.gender and user.gender != wechat_user.gender:
                user.gender = wechat_user.gender
                updated = True
                print(f"  更新用户性别: {wechat_user.gender}")
                
            if wechat_user.city and user.city != wechat_user.city:
                user.city = wechat_user.city
                updated = True
                print(f"  更新用户城市: '{wechat_user.city}'")
            
            if updated:
                user.save()
                print(f"  ✅ 用户基本信息已更新")
            
            # 更新用户资料头像
            if wechat_user.avatar_url:
                profile, created = UserProfile.objects.get_or_create(user=user)
                if profile.avatar != wechat_user.avatar_url:
                    profile.avatar = wechat_user.avatar_url
                    profile.save()
                    print(f"  ✅ 用户头像已更新: '{wechat_user.avatar_url}'")
                else:
                    print(f"  头像已是最新")
            
        except WechatUserInfo.DoesNotExist:
            print(f"  ❌ 没有微信信息")


def test_serializer_output():
    """测试序列化器输出"""
    print(f"\n🧪 测试序列化器输出...")
    
    from api.v1.serializers import UserDetailSerializer, WechatUserSerializer
    
    users = User.objects.all()
    
    for user in users:
        print(f"测试用户: {user.username}")
        
        # 测试用户详情序列化器
        user_serializer = UserDetailSerializer(user)
        user_data = user_serializer.data
        
        print(f"  用户序列化结果:")
        print(f"    昵称: '{user_data.get('nickname')}'")
        print(f"    头像: '{user_data.get('avatar')}'")
        
        wechat_info = user_data.get('wechat_info')
        if wechat_info:
            print(f"    微信信息:")
            print(f"      昵称: '{wechat_info.get('nickname')}'")
            print(f"      头像: '{wechat_info.get('avatar_url')}'")
        else:
            print(f"    ❌ 没有微信信息")
        
        # 检查前端合并逻辑
        final_nickname = wechat_info.get('nickname') if wechat_info else user_data.get('nickname')
        final_avatar = wechat_info.get('avatar_url') if wechat_info else user_data.get('avatar')
        
        print(f"  前端合并结果:")
        print(f"    最终昵称: '{final_nickname}'")
        print(f"    最终头像: '{final_avatar}'")
        
        if final_nickname and final_nickname != '茶园投资者':
            print(f"    ✅ 昵称正常")
        else:
            print(f"    ❌ 昵称会显示默认值")
            
        if final_avatar and 'http' in final_avatar:
            print(f"    ✅ 头像正常")
        else:
            print(f"    ❌ 头像会显示默认值")


def create_test_user_with_wechat_info():
    """创建测试用户验证流程"""
    print(f"\n🧪 创建测试用户验证流程...")
    
    # 模拟前端传来的用户信息
    test_user_info = {
        'nickName': '测试茶友2024',
        'avatarUrl': 'https://thirdwx.qlogo.cn/mmopen/test_avatar_2024.jpg',
        'gender': 1,
        'city': '北京',
        'province': '北京',
        'country': '中国',
        'language': 'zh_CN'
    }
    
    test_openid = 'test_openid_2024_verification'
    
    print(f"模拟用户信息: {test_user_info}")
    print(f"模拟OpenID: {test_openid}")
    
    # 使用修复后的逻辑创建用户
    from apps.users.wechat_service import WechatUserManager
    
    result = WechatUserManager.create_or_update_user(test_openid, test_user_info)
    
    if result['success']:
        user = result['user']
        wechat_user = result['wechat_user']
        
        print(f"✅ 用户创建成功:")
        print(f"  用户名: {user.username}")
        print(f"  昵称: '{user.nickname}'")
        print(f"  性别: {user.gender}")
        print(f"  城市: '{user.city}'")
        
        print(f"  微信信息:")
        print(f"    OpenID: {wechat_user.openid}")
        print(f"    昵称: '{wechat_user.nickname}'")
        print(f"    头像: '{wechat_user.avatar_url}'")
        
        # 测试序列化器
        from api.v1.serializers import UserDetailSerializer
        serializer = UserDetailSerializer(user)
        data = serializer.data
        
        print(f"  序列化器输出:")
        print(f"    昵称: '{data.get('nickname')}'")
        print(f"    头像: '{data.get('avatar')}'")
        print(f"    微信昵称: '{data.get('wechat_info', {}).get('nickname')}'")
        print(f"    微信头像: '{data.get('wechat_info', {}).get('avatar_url')}'")
        
        return True
    else:
        print(f"❌ 用户创建失败: {result['error']}")
        return False


def main():
    """主函数"""
    print("🔧 修复微信用户头像昵称同步问题")
    print("=" * 60)
    
    # 1. 分析当前状态
    analyze_current_state()
    
    # 2. 迁移数据到统一模型
    migrate_data_to_unified_model()
    
    # 3. 修复用户头像和昵称
    fix_user_avatar_nickname()
    
    # 4. 测试序列化器输出
    test_serializer_output()
    
    # 5. 创建测试用户验证流程
    test_success = create_test_user_with_wechat_info()
    
    print("\n" + "=" * 60)
    print("✅ 微信用户同步修复完成！")
    
    print(f"\n💡 修复总结:")
    print(f"1. 数据迁移: 统一使用 WechatUserInfo 模型")
    print(f"2. 用户信息: 从微信信息同步到用户基本信息")
    print(f"3. 头像处理: 同步到 UserProfile")
    print(f"4. 序列化器: 确保正确返回微信信息")
    print(f"5. 测试验证: {'✅ 通过' if test_success else '❌ 失败'}")
    
    print(f"\n📱 前端操作建议:")
    print(f"1. 清除小程序缓存")
    print(f"2. 重新登录授权")
    print(f"3. 检查个人中心显示")


if __name__ == "__main__":
    main()

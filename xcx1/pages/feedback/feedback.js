// 意见反馈页面
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    // 反馈类型
    feedbackType: '',
    feedbackTypes: [],
    
    // 反馈内容
    feedbackContent: '',
    
    // 联系方式
    contactPhone: '',
    contactWechat: '',
    contactEmail: '',
    
    // 图片上传
    uploadedImages: [],
    
    // 系统信息
    systemInfo: {},
    
    // 历史反馈
    historyFeedbacks: [],
    
    // 页面状态
    canSubmit: false,
    showSuccessModal: false,
    submitting: false
  },

  // 页面加载
  onLoad(options) {
    this.loadSystemInfo()
    this.loadUserInfo()
    this.loadFeedbackTypes()
    this.loadHistoryFeedbacks()
  },

  // 页面显示
  onShow() {
  },

  // 加载系统信息
  loadSystemInfo() {
    const systemInfo = wx.getSystemInfoSync()
    this.setData({
      systemInfo: {
        model: systemInfo.model,
        system: systemInfo.system,
        version: systemInfo.version
      }
    })
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = app.getUserInfo()
    if (userInfo) {
      this.setData({
        contactPhone: userInfo.phone || '',
        contactWechat: userInfo.wechat || '',
        contactEmail: userInfo.email || ''
      })
    }
  },

  // 加载反馈类型
  async loadFeedbackTypes() {
    try {
      // 调用反馈类型API
      const res = await api.feedbackApi.getTypes()

      if (res.code === 200) {
        this.setData({
          feedbackTypes: res.data.results || []
        })
      } else {
        // 使用默认反馈类型
        this.setData({
          feedbackTypes: [
            { value: 'bug', name: '功能异常', icon: '🐛' },
            { value: 'suggestion', name: '功能建议', icon: '💡' },
            { value: 'ui', name: '界面问题', icon: '🎨' },
            { value: 'performance', name: '性能问题', icon: '⚡' },
            { value: 'content', name: '内容问题', icon: '📝' },
            { value: 'other', name: '其他问题', icon: '❓' }
          ]
        })
      }
    } catch (error) {
      console.error('加载反馈类型失败:', error)
      // 使用默认反馈类型
      this.setData({
        feedbackTypes: [
          { value: 'bug', name: '功能异常', icon: '🐛' },
          { value: 'suggestion', name: '功能建议', icon: '💡' },
          { value: 'other', name: '其他问题', icon: '❓' }
        ]
      })
    }
  },

  // 加载历史反馈
  async loadHistoryFeedbacks() {
    try {
      // 调用历史反馈API
      const res = await api.feedbackApi.getHistory()

      if (res.code === 200) {
        this.setData({
          historyFeedbacks: res.data.results || []
        })
      } else {
        this.setData({
          historyFeedbacks: []
        })
      }
    } catch (error) {
      console.error('加载历史反馈失败:', error)
    }
  },

  // 选择反馈类型
  selectFeedbackType(e) {
    const { type } = e.currentTarget.dataset
    this.setData({ feedbackType: type })
    this.checkCanSubmit()
  },

  // 内容输入
  onContentInput(e) {
    const content = e.detail.value
    this.setData({ feedbackContent: content })
    this.checkCanSubmit()
  },

  // 手机号输入
  onPhoneInput(e) {
    this.setData({ contactPhone: e.detail.value })
  },

  // 微信号输入
  onWechatInput(e) {
    this.setData({ contactWechat: e.detail.value })
  },

  // 邮箱输入
  onEmailInput(e) {
    this.setData({ contactEmail: e.detail.value })
  },

  // 选择图片
  chooseImage() {
    const remainCount = 3 - this.data.uploadedImages.length
    
    wx.chooseMedia({
      count: remainCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.uploadImages(res.tempFiles)
      }
    })
  },

  // 上传图片
  async uploadImages(files) {
    try {
      wx.showLoading({
        title: '上传中...'
      })
      
      const uploadPromises = files.map(file => this.uploadSingleImage(file.tempFilePath))
      const uploadResults = await Promise.all(uploadPromises)
      
      const newImages = [...this.data.uploadedImages, ...uploadResults]
      this.setData({ uploadedImages: newImages })
      
      wx.hideLoading()
      wx.showToast({
        title: '上传成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('上传图片失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '上传失败',
        icon: 'none'
      })
    }
  },

  // 上传单张图片
  async uploadSingleImage(filePath) {
    try {
      // 调用图片上传API
      const res = await api.uploadApi.uploadFile(filePath)

      if (res.code === 200) {
        return res.data.url
      } else {
        throw new Error(res.message || '上传失败')
      }
    } catch (error) {
      console.error('图片上传失败:', error)
      // 返回临时路径作为后备
      return filePath
    }
  },

  // 预览图片
  previewImage(e) {
    const { url } = e.currentTarget.dataset
    wx.previewImage({
      current: url,
      urls: this.data.uploadedImages
    })
  },

  // 删除图片
  deleteImage(e) {
    const { index } = e.currentTarget.dataset
    const images = this.data.uploadedImages
    images.splice(index, 1)
    this.setData({ uploadedImages: images })
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { feedbackType, feedbackContent } = this.data
    const canSubmit = feedbackType && feedbackContent.trim().length >= 10
    this.setData({ canSubmit })
  },

  // 提交反馈
  async submitFeedback() {
    if (!this.data.canSubmit || this.data.submitting) return
    
    // 验证内容长度
    if (this.data.feedbackContent.trim().length < 10) {
      wx.showToast({
        title: '请详细描述问题（至少10个字）',
        icon: 'none'
      })
      return
    }
    
    this.setData({ submitting: true })
    
    try {
      wx.showLoading({
        title: '提交中...'
      })
      
      const feedbackData = {
        type: this.data.feedbackType,
        content: this.data.feedbackContent.trim(),
        contact: {
          phone: this.data.contactPhone,
          wechat: this.data.contactWechat,
          email: this.data.contactEmail
        },
        images: this.data.uploadedImages,
        system_info: this.data.systemInfo
      }
      
      // 调用提交反馈API
      const res = await api.feedbackApi.submit(feedbackData)

      wx.hideLoading()

      if (res.code === 200) {
        // 显示成功弹窗
        this.setData({ showSuccessModal: true })

        // 清空表单
        this.resetForm()

        // 刷新历史反馈
        this.loadHistoryFeedbacks()
      } else {
        throw new Error(res.message || '提交失败')
      }
    } catch (error) {
      console.error('提交反馈失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ submitting: false })
    }
  },

  // 重置表单
  resetForm() {
    this.setData({
      feedbackType: '',
      feedbackContent: '',
      uploadedImages: [],
      canSubmit: false
    })
  },

  // 隐藏成功弹窗
  hideSuccessModal() {
    this.setData({ showSuccessModal: false })
  },

  // 查看反馈详情
  viewFeedbackDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/feedback-detail/feedback-detail?id=${id}`
    })
  },

  // 查看全部历史
  viewAllHistory() {
    wx.navigateTo({
      url: '/pages/feedback-history/feedback-history'
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击成功弹窗内容时关闭弹窗
  }
})

# Profile页面数据同步测试文档

## 修改总结

### 1. 移除硬编码数据
- ✅ 删除WXML中的默认值（'4.3'、'¥15,680'、'15.2%'、'2'、'深圳科技有限公司' 等）
- ✅ 移除JS中的示例数据
- ✅ 所有数据现在都从后台API动态获取

### 2. 实现后台数据同步
- ✅ 页面加载时调用相应的API接口
- ✅ 数据获取成功后再渲染页面内容
- ✅ 添加加载状态提示，避免空白页面

### 3. 同步的数据模块

#### 用户统计数据
- **API**: `api.userApi.getStats()` -> `/analytics/user-stats/`
- **数据字段**:
  - `tea_fields`: 认购亩数 (从 `stats.tea_count` 映射)
  - `total_earnings`: 累计收益 (从 `stats.total_earnings` 映射)
  - `return_rate`: 年化收益 (从 `stats.return_rate` 或计算得出)
  - `field_count`: 认购茶地数量 (从 `stats.tea_count` 映射)
  - `level`: 会员等级 (从 `stats.level` 映射)

#### 企业信息
- **API**: `api.userApi.getProfile()` -> `/users/profile/`
- **数据字段**:
  - `companyName`: 企业名称 (从 `profile.company_name` 映射)
  - `creditCode`: 信用代码 (从 `profile.credit_code` 映射)
  - `contactPerson`: 联系人 (从 `profile.contact_person` 映射)
  - `contactPhone`: 联系电话 (从 `profile.contact_phone` 映射)
  - `registerTime`: 注册时间 (从 `data.date_joined` 映射)

#### 认证状态
- **API**: `api.authApi.getStatus()` -> `/verification/info/`
- **数据字段**:
  - `enterprise`: 企业资质认证状态
  - `realName`: 实名认证状态
  - `bankCard`: 银行账户认证状态

#### 未读消息数
- **API**: `api.messageApi.getUnreadCount()` -> `/notifications/unread-count/`
- **数据字段**:
  - `unreadCount`: 未读消息数量

### 4. 错误处理
- ✅ 添加网络请求失败的错误处理
- ✅ 数据获取失败时显示合适的提示信息
- ✅ 保持页面的可用性，避免因数据问题导致页面崩溃
- ✅ 添加重试机制

### 5. 界面一致性
- ✅ 数据同步后的界面布局与优化后的设计保持一致
- ✅ 维持现有的交互功能和视觉效果
- ✅ 添加加载状态动画

## 新增功能

### 加载状态
- 页面加载时显示加载动画
- 绿色背景的加载指示器
- 防止用户在数据加载期间看到空白内容

### 动态认证状态
- 根据后台数据动态显示认证状态
- 已认证显示绿色勾选图标
- 未认证显示红色叉号图标
- 支持点击跳转到对应认证页面

### 错误处理机制
- 统一的错误处理函数
- 网络错误重试机制
- 静默失败处理（对于非关键数据）

## 测试要点

### 1. 登录状态测试
- [ ] 未登录时显示登录提示
- [ ] 已登录时正常显示用户数据

### 2. 数据加载测试
- [ ] 页面首次加载显示加载状态
- [ ] 数据加载完成后正常显示
- [ ] 下拉刷新功能正常

### 3. API调用测试
- [ ] 用户统计数据正确获取和显示
- [ ] 企业信息正确获取和显示
- [ ] 认证状态正确获取和显示
- [ ] 未读消息数正确获取和显示

### 4. 错误处理测试
- [ ] 网络断开时的错误提示
- [ ] API返回错误时的处理
- [ ] 重试机制正常工作

### 5. 界面一致性测试
- [ ] 加载状态界面美观
- [ ] 数据显示格式正确
- [ ] 认证状态图标正确显示
- [ ] 交互功能正常

## 注意事项

1. **数据格式化**: 金额和数字都经过格式化处理，大于10000的金额显示为万元
2. **默认值处理**: 当API返回空数据时，显示"未设置"而不是硬编码的示例数据
3. **性能优化**: 使用Promise.all并行加载多个API，提高加载速度
4. **用户体验**: 加载状态和错误处理确保用户始终知道当前状态

## 后续优化建议

1. 添加数据缓存机制，减少重复API调用
2. 实现增量更新，只更新变化的数据
3. 添加离线数据支持
4. 优化加载动画和过渡效果

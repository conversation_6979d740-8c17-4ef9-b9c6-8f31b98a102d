<!-- 个人中心页 -->
<scroll-view class="container" scroll-y="true" refresher-enabled="true" refresher-triggered="{{refreshing}}" bindrefresherrefresh="onRefresh">
  <!-- 登录提示界面 -->
  <view wx:if="{{showLoginTip}}" class="login-tip-container">
    <view class="login-tip-card">
      <view class="login-tip-icon">🔐</view>
      <text class="login-tip-title">欢迎使用两山·茶管家</text>
      <text class="login-tip-desc">登录后可查看个人信息、茶地认购记录等更多功能</text>
      <button class="login-btn" bindtap="navigateTo" data-url="/pages/login/login">立即登录</button>
      <text class="login-tip-note">您也可以继续浏览其他功能</text>
    </view>
  </view>

  <!-- 用户信息区域 -->
  <view wx:else class="user-content">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 头部企业信息区域 -->
    <view wx:elif="{{dataLoadStatus.certificationStatus !== 'success'}}" class="header-section">
      <view class="company-header">
        <view class="company-info">
          <view class="company-icon">
            <text class="icon-text">🏢</text>
          </view>
          <view class="company-details">
            <text class="company-name">{{enterpriseInfo.companyName || userInfo.company_name || userInfo.nickname || '茶园投资者'}}</text>
            <text class="company-subtitle">{{certificationStatus.enterprise ? '企业认证' : '个人用户'}} · {{userStats.level || 'VIP会员'}}</text>
            <view class="vip-badge">
              <text class="star">⭐</text>
              <text class="vip-text">{{userStats.level || 'VIP会员'}}</text>
            </view>
          </view>
        </view>
        <view class="header-actions">
          <view class="action-btn" bindtap="navigateTo" data-url="/pages/profile-edit/profile-edit">
            <text class="action-icon">✏️</text>
          </view>
          <view class="action-btn" bindtap="navigateTo" data-url="/pages/settings/settings">
            <text class="action-icon">⚙️</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 数据统计区域 -->
    <view wx:if="{{!loading}}" class="stats-section">
      <view class="stats-grid">
        <view class="stat-item" bindtap="navigateTo" data-url="/pages/my-fields/my-fields">
          <text class="stat-value">{{userStats.tea_fields || '0'}}</text>
          <text class="stat-label">认购亩数</text>
        </view>
        <view class="stat-item" bindtap="navigateTo" data-url="/pages/earnings/earnings">
          <text class="stat-value blue">¥{{userStats.total_earnings || '0'}}</text>
          <text class="stat-label">累计收益</text>
        </view>
        <view class="stat-item" bindtap="navigateTo" data-url="/pages/analytics/analytics">
          <text class="stat-value orange">{{userStats.return_rate || '0'}}%</text>
          <text class="stat-label">年化收益</text>
        </view>
        <view class="stat-item" bindtap="navigateTo" data-url="/pages/my-fields/my-fields">
          <text class="stat-value purple">{{userStats.field_count || '0'}}</text>
          <text class="stat-label">认购茶地</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单区域 -->
    <view wx:if="{{!loading}}" class="menu-section">
      <view class="menu-item" bindtap="navigateTo" data-url="/pages/my-fields/my-fields">
        <view class="menu-icon green">
          <text class="icon">🌱</text>
        </view>
        <text class="menu-text">我的认购</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-url="/pages/orders/orders">
        <view class="menu-icon blue">
          <text class="icon">📋</text>
        </view>
        <text class="menu-text">订单管理</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-url="/pages/earnings/earnings">
        <view class="menu-icon green">
          <text class="icon">📊</text>
        </view>
        <text class="menu-text">收益记录</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-url="/pages/contracts/contracts">
        <view class="menu-icon orange">
          <text class="icon">📄</text>
        </view>
        <text class="menu-text">合同管理</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-url="/pages/invoices/invoices">
        <view class="menu-icon purple">
          <text class="icon">🧾</text>
        </view>
        <text class="menu-text">发票管理</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 账户支持区域 -->
    <view wx:if="{{!loading}}" class="support-section">
      <text class="section-title">账户支持</text>

      <view class="support-item" bindtap="showQrCode">
        <view class="support-icon">
          <text class="icon">📱</text>
        </view>
        <text class="support-text">客服咨询</text>
        <view class="support-status">
          <text class="status-text">在线</text>
          <text class="chat-icon">💬</text>
        </view>
      </view>

      <view class="support-item" bindtap="navigateTo" data-url="/pages/help/help">
        <view class="support-icon">
          <text class="icon">❓</text>
        </view>
        <text class="support-text">帮助中心</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="support-item" bindtap="navigateTo" data-url="/pages/feedback/feedback">
        <view class="support-icon">
          <text class="icon">💬</text>
        </view>
        <text class="support-text">意见反馈</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="support-item" bindtap="navigateTo" data-url="/pages/about/about">
        <view class="support-icon">
          <text class="icon">ℹ️</text>
        </view>
        <text class="support-text">关于我们</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 身份认证中心区域 -->
    <view wx:if="{{!loading && dataLoadStatus.certificationStatus === 'success'}}" class="verification-center-section">
      <view class="section-header">
        <text class="section-title">🔐 身份认证中心</text>
        <view class="verification-progress">
          <text class="progress-text">认证进度 {{verificationProgress || 0}}%</text>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{verificationProgress || 0}}%"></view>
          </view>
        </view>
      </view>

      <!-- 认证概览卡片 -->
      <view class="verification-overview" bindtap="navigateToVerificationCenter">
        <view class="overview-header">
          <text class="overview-title">认证概览</text>
          <text class="overview-arrow">></text>
        </view>
        <view class="overview-stats">
          <view class="stat-item">
            <text class="stat-number">{{(certificationStatus.realName ? 1 : 0) + (certificationStatus.enterprise ? 1 : 0) + (certificationStatus.bankCard ? 1 : 0)}}</text>
            <text class="stat-label">已完成</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">{{3 - ((certificationStatus.realName ? 1 : 0) + (certificationStatus.enterprise ? 1 : 0) + (certificationStatus.bankCard ? 1 : 0))}}</text>
            <text class="stat-label">待认证</text>
          </view>
        </view>
      </view>

      <!-- 快速认证入口 -->
      <view class="quick-verification" bindtap="quickVerification">
        <view class="quick-icon">⚡</view>
        <view class="quick-content">
          <text class="quick-title">快速认证</text>
          <text class="quick-desc">一键完成未完成的认证项目</text>
        </view>
        <text class="quick-arrow">></text>
      </view>

      <!-- 认证项目列表 -->
      <view class="verification-items">
        <view class="cert-item {{certificationStatus.realName ? 'verified' : 'unverified'}}" bindtap="navigateToCertification" data-type="realname">
          <view class="cert-icon {{certificationStatus.realName ? 'verified' : 'unverified'}}">
            <text class="icon">{{certificationStatus.realName ? '✅' : '👤'}}</text>
          </view>
          <view class="cert-content">
            <text class="cert-text">实名认证</text>
            <text class="cert-desc">身份证+人脸识别</text>
          </view>
          <view class="cert-status-wrapper">
            <text class="cert-status {{certificationStatus.realName ? 'verified' : 'unverified'}}">{{certificationStatus.realName ? '已认证' : '未认证'}}</text>
            <text class="cert-arrow">></text>
          </view>
        </view>

        <view class="cert-item {{certificationStatus.enterprise ? 'verified' : 'unverified'}}" bindtap="navigateToCertification" data-type="enterprise">
          <view class="cert-icon {{certificationStatus.enterprise ? 'verified' : 'unverified'}}">
            <text class="icon">{{certificationStatus.enterprise ? '✅' : '🏢'}}</text>
          </view>
          <view class="cert-content">
            <text class="cert-text">企业认证</text>
            <text class="cert-desc">营业执照+企业信息</text>
          </view>
          <view class="cert-status-wrapper">
            <text class="cert-status {{certificationStatus.enterprise ? 'verified' : 'unverified'}}">{{certificationStatus.enterprise ? '已认证' : '未认证'}}</text>
            <text class="cert-arrow">></text>
          </view>
        </view>

        <view class="cert-item {{certificationStatus.bankCard ? 'verified' : 'unverified'}}" bindtap="navigateToCertification" data-type="bankcard">
          <view class="cert-icon {{certificationStatus.bankCard ? 'verified' : 'unverified'}}">
            <text class="icon">{{certificationStatus.bankCard ? '✅' : '💳'}}</text>
          </view>
          <view class="cert-content">
            <text class="cert-text">银行卡认证</text>
            <text class="cert-desc">银行卡绑定+支付安全</text>
          </view>
          <view class="cert-status-wrapper">
            <text class="cert-status {{certificationStatus.bankCard ? 'verified' : 'unverified'}}">{{certificationStatus.bankCard ? '已认证' : '未认证'}}</text>
            <text class="cert-arrow">></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view wx:if="{{!loading}}" class="logout-section">
      <button class="logout-btn" bindtap="logout">退出登录</button>
    </view>
  </view> <!-- 关闭 user-content -->
</scroll-view>

<!-- 二维码弹窗 -->
<view class="qrcode-modal" wx:if="{{showQrCode}}" bindtap="hideQrCode">
  <view class="qrcode-content" catchtap="stopPropagation">
    <view class="qrcode-header">
      <text class="qrcode-title">我的二维码</text>
      <view class="qrcode-close" bindtap="hideQrCode">×</view>
    </view>
    <view class="qrcode-body">
      <canvas canvas-id="qrCodeCanvas" class="qrcode-canvas"></canvas>
      <text class="qrcode-desc">扫描二维码，分享给好友</text>
    </view>
    <view class="qrcode-footer">
      <button class="save-qrcode-btn" bindtap="saveQrCode">保存到相册</button>
    </view>
  </view>
</view>

<!-- 自定义底部导航 -->
<custom-tabbar
  id="custom-tabbar"
  config="{{customTabBarConfig}}"
  current="{{currentTabIndex}}"
  bind:tabchange="onCustomTabChange"
/>

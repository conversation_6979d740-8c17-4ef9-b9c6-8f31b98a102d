/* 个人中心页面样式 - 仿照图片设计 */

.container {
  min-height: 100vh;
  background: #f5f5f5;
  position: relative;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  background: #4CAF50;
  color: white;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: white;
}

/* 登录提示样式 - 玻璃拟态设计 */
.login-tip-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 64rpx 32rpx;
}

.login-tip-card {
  background: white;
  border-radius: 32rpx;
  padding: 80rpx 60rpx;
  text-align: center;
  border: 1rpx solid #e5e7eb;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
  max-width: 600rpx;
  width: 100%;
}

.login-tip-icon {
  font-size: 140rpx;
  margin-bottom: 40rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.2));
}

.login-tip-title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 20rpx;
}

.login-tip-desc {
  display: block;
  font-size: 30rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 60rpx;
}

.login-btn {
  background: #2E7D32;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 28rpx 80rpx;
  font-size: 34rpx;
  font-weight: 700;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(46, 125, 50, 0.3);
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.3);
}

.login-tip-note {
  display: block;
  font-size: 26rpx;
  color: #9ca3af;
}

/* 头部企业信息区域 */
.header-section {
  background: #4CAF50;
  padding: 40rpx 32rpx;
  color: white;
}

.company-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.company-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.company-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.icon-text {
  font-size: 40rpx;
  color: white;
}

.company-details {
  flex: 1;
}

.company-name {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  display: block;
  margin-bottom: 8rpx;
}

.company-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  display: block;
  margin-bottom: 12rpx;
}

.vip-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.star {
  font-size: 24rpx;
  color: #FFD700;
}

.vip-text {
  font-size: 24rpx;
  color: #FFD700;
  font-weight: bold;
}

/* 头部操作按钮 */
.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 16rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}

.action-icon {
  font-size: 32rpx;
  color: white;
}

/* 数据统计区域 */
.stats-section {
  background: white;
  padding: 32rpx;
  margin: 0 32rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
}

.stat-item {
  text-align: center;
  cursor: pointer;
}

.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
  color: #333;
}

.stat-value.blue {
  color: #2196F3;
}

.stat-value.orange {
  color: #FF9800;
}

.stat-value.purple {
  color: #9C27B0;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  display: block;
}

/* 功能菜单区域 */
.menu-section {
  background: white;
  margin: 0 32rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #f5f5f5;
}

.menu-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.menu-icon.green {
  background: #E8F5E8;
}

.menu-icon.blue {
  background: #E3F2FD;
}

.menu-icon.orange {
  background: #FFF3E0;
}

.menu-icon.purple {
  background: #F3E5F5;
}

.menu-icon .icon {
  font-size: 32rpx;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.menu-arrow {
  font-size: 32rpx;
  color: #999;
}

/* 清理重复的统计样式 */

/* 快捷功能 */
.quick-section {
  margin-bottom: 24rpx;
}

.card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212121;
}

.quick-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 16rpx;
  border-radius: 12rpx;
  background: #F8F8F8;
}

.quick-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: white;
}

.quick-icon.fields {
  background: #2E7D32;
}

.quick-icon.earnings {
  background: #FF6B35;
}

.quick-icon.orders {
  background: #1976D2;
}

.quick-icon.contracts {
  background: #7B1FA2;
}

.quick-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 清理重复样式 */

/* 认证状态 */
.auth-section {
  margin-bottom: 24rpx;
}

.auth-status {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background: #F8F8F8;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.auth-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: white;
}

.auth-icon.verified {
  background: #4CAF50;
}

.auth-icon.pending {
  background: #FF9800;
}

.auth-icon.unverified {
  background: #F44336;
}

.auth-content {
  flex: 1;
}

.auth-title {
  font-size: 28rpx;
  color: #212121;
  margin-bottom: 8rpx;
}

.auth-desc {
  font-size: 24rpx;
  color: #666;
}

.auth-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
}

.auth-btn.verify {
  background: #2E7D32;
  color: white;
}

.auth-btn.resubmit {
  background: #FF9800;
  color: white;
}

.auth-btn.verified {
  background: #E8F5E8;
  color: #2E7D32;
}

/* 推荐功能 */
.recommend-section {
  margin-bottom: 24rpx;
}

.recommend-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.recommend-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background: #F8F8F8;
  border-radius: 12rpx;
}

.recommend-icon {
  font-size: 32rpx;
  color: #2E7D32;
}

.recommend-content {
  flex: 1;
}

.recommend-title {
  font-size: 28rpx;
  color: #212121;
  margin-bottom: 8rpx;
}

.recommend-desc {
  font-size: 24rpx;
  color: #666;
}

.recommend-tag {
  background: #2E7D32;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
}

.recommend-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 版本信息 */
.version-section {
  margin-bottom: 24rpx;
}

.version-info {
  text-align: center;
  padding: 24rpx;
  background: #F8F8F8;
  border-radius: 12rpx;
}

.app-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  margin: 0 auto 16rpx;
}

.app-name {
  font-size: 28rpx;
  color: #212121;
  margin-bottom: 8rpx;
}

.app-version {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.update-btn {
  background: #2E7D32;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 32rpx;
  font-size: 24rpx;
}

.copyright {
  font-size: 22rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 头像上传弹窗 */
.avatar-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.avatar-content {
  width: 100%;
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
}

.avatar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #E0E0E0;
}

.avatar-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212121;
}

.avatar-close {
  font-size: 48rpx;
  color: #666;
  padding: 8rpx;
}

.avatar-options {
  display: flex;
  flex-direction: column;
  gap: 2rpx;
}

.avatar-option {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 32rpx;
  background: white;
}

.option-icon {
  font-size: 32rpx;
  color: #2E7D32;
}

.option-text {
  font-size: 28rpx;
  color: #212121;
}

.cancel-btn {
  background: #F5F5F5;
  color: #666;
  border: none;
  padding: 32rpx;
  font-size: 28rpx;
  margin-top: 16rpx;
}

/* 清理重复的企业信息样式 */

/* 清理重复的认证状态样式 */

/* 账户支持区域 */
.support-section {
  margin: 0 32rpx 24rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  padding-left: 16rpx;
  font-weight: bold;
}

.support-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.support-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.support-icon .icon {
  font-size: 32rpx;
}

.support-text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.support-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-text {
  font-size: 24rpx;
  color: #4CAF50;
}

.chat-icon {
  font-size: 24rpx;
}

/* 企业信息区域 - 重新定义 */
.enterprise-section {
  margin: 0 32rpx 24rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.edit-enterprise-btn {
  background: #4CAF50;
  color: white;
  padding: 24rpx;
  border-radius: 16rpx;
  text-align: center;
  margin-top: 16rpx;
}

.edit-text {
  font-size: 32rpx;
  color: white;
}

/* 认证状态区域 - 重新定义 */
.certification-section {
  margin: 0 32rpx 24rpx;
}

.cert-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.cert-item.verified {
  border-left: 6rpx solid #4CAF50;
}

.cert-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.cert-icon.verified {
  background: #E8F5E8;
}

.cert-icon.unverified {
  background: #FFEBEE;
}

.cert-icon .icon {
  font-size: 32rpx;
}

.cert-text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.cert-status {
  font-size: 24rpx;
  font-weight: bold;
}

.cert-item.verified .cert-status {
  color: #4CAF50;
}

.cert-item.unverified .cert-status {
  color: #F44336;
}

.cert-item.unverified {
  border-left: 6rpx solid #F44336;
}

/* 退出登录 */
.logout-section {
  padding: 32rpx;
}

.logout-btn {
  width: 100%;
  background: #F44336;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(244, 67, 54, 0.3);
  transition: all 0.3s ease;
}

.logout-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(244, 67, 54, 0.4);
}

/* 自定义底部导航样式 */
.container {
  padding-bottom: 120rpx; /* 为自定义底部导航留出空间 */
}

/* 确保滚动容器也有底部留白 */
scroll-view {
  padding-bottom: 120rpx;
}

/* 身份认证中心区域 */
.verification-center-section {
  background: white;
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.verification-progress {
  margin-top: 16rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #F0F0F0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #66BB6A);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 认证概览卡片 */
.verification-overview {
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  color: white;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.overview-title {
  font-size: 28rpx;
  font-weight: 600;
}

.overview-arrow {
  font-size: 24rpx;
  opacity: 0.8;
}

.overview-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 22rpx;
  opacity: 0.9;
}

.stat-divider {
  width: 1rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.3);
}

/* 快速认证入口 */
.quick-verification {
  display: flex;
  align-items: center;
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  margin-bottom: 24rpx;
  border: 2rpx dashed #E0E0E0;
}

.quick-verification .quick-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.quick-content {
  flex: 1;
}

.quick-title {
  font-size: 26rpx;
  color: #212121;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.quick-desc {
  font-size: 22rpx;
  color: #666;
}

.quick-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 认证项目列表 */
.verification-items {
  border-top: 1rpx solid #F0F0F0;
  padding-top: 24rpx;
}

.verification-items .cert-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F8F8F8;
}

.verification-items .cert-item:last-child {
  border-bottom: none;
}

.verification-items .cert-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 24rpx;
}

.verification-items .cert-icon.verified {
  background: #E8F5E8;
}

.verification-items .cert-icon.unverified {
  background: #FFF8E1;
}

.cert-content {
  flex: 1;
}

.cert-content .cert-text {
  font-size: 28rpx;
  color: #212121;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.cert-desc {
  font-size: 22rpx;
  color: #666;
}

.cert-status-wrapper {
  display: flex;
  align-items: center;
}

.cert-status-wrapper .cert-status {
  font-size: 24rpx;
  font-weight: 500;
  margin-right: 8rpx;
}

.cert-status-wrapper .cert-status.verified {
  color: #4CAF50;
}

.cert-status-wrapper .cert-status.unverified {
  color: #FF9800;
}

.cert-arrow {
  font-size: 20rpx;
  color: #999;
}

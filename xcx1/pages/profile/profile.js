// 个人中心页
const app = getApp()
const { formatAvatarUrl } = require('../../utils/util')
const api = require('../../api/index.js')

Page({
  data: {
    // 自定义底部导航
    customTabBarConfig: null,
    currentTabIndex: 4,
    userInfo: {},
    userStats: {
      tea_fields: 0,
      total_earnings: 0,
      return_rate: 0,
      field_count: 0,
      level: '普通会员',
      points: 0,
      total_yield: 0
    },
    unreadCount: 0,
    showQrCode: false,
    showAvatarModal: false,
    showLoginTip: false,
    refreshing: false,
    loading: true, // 添加加载状态

    // 数据加载状态
    dataLoadStatus: {
      userStats: 'loading',      // loading, success, error
      enterpriseInfo: 'loading',
      certificationStatus: 'loading',
      unreadCount: 'loading'
    },

    // 企业信息
    enterpriseInfo: {
      companyName: '',
      creditCode: '',
      contactPerson: '',
      contactPhone: '',
      registerTime: ''
    },

    // 认证状态
    certificationStatus: {
      enterprise: false,
      realName: false,
      bankCard: false
    },

    // 计算属性
    enterpriseCompletionRate: 0,
    certificationProgress: 0
  },

  // 页面加载
  onLoad(options) {
    console.log('🏠 个人中心页加载', options)

    // 初始化自定义底部导航
    this.initCustomTabBar()
  },

  // 页面显示
  async onShow() {
    console.log('🏠 个人中心页显示')

    // 重新检查登录状态
    app.checkLoginStatus()

    // 等待一下，确保token验证完成
    await new Promise(resolve => setTimeout(resolve, 200))

    // 加载用户数据
    await this.loadUserData()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadUserData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // scroll-view刷新
  async onRefresh() {
    this.setData({ refreshing: true })
    try {
      await this.loadUserData()
    } catch (error) {
      console.error('刷新数据失败:', error)
    } finally {
      this.setData({ refreshing: false })
    }
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '两山·茶管家 - 专业的茶园认购投资平台',
      path: '/pages/index/index',
      imageUrl: '/images/share-profile.jpg'
    }
  },

  // 加载用户数据
  async loadUserData() {
    try {
      // 设置加载状态
      this.setData({ loading: true })

      // 检查登录状态
      const isLogin = app.globalData.isLogin
      const token = wx.getStorageSync('token')
      const globalUserInfo = app.getUserInfo()

      console.log('个人中心检查登录状态:', {
        isLogin,
        hasToken: !!token,
        hasUserInfo: !!globalUserInfo,
        currentShowLoginTip: this.data.showLoginTip
      })

      if (isLogin && token && globalUserInfo) {
        console.log('✅ 用户已登录，显示用户信息界面')

        // 先显示缓存的用户信息
        const processedUserInfo = {
          ...globalUserInfo,
          avatar: formatAvatarUrl(globalUserInfo.avatar)
        }

        this.setData({
          userInfo: processedUserInfo,
          showLoginTip: false  // 确保隐藏登录提示
        })

        console.log('✅ 已设置showLoginTip为false，当前值:', this.data.showLoginTip)

        // 尝试从服务器获取最新用户信息
        try {
          console.log('🔄 从服务器获取最新用户信息...')
          const profileRes = await api.userApi.getProfile()

          if (profileRes.code === 200 && profileRes.data) {
            console.log('✅ 获取到最新用户信息:', profileRes.data)

            // 更新全局用户信息
            const updatedUserInfo = {
              ...profileRes.data,
              avatar: formatAvatarUrl(profileRes.data.avatar)
            }

            // 更新全局状态和本地存储
            app.setUserInfo(updatedUserInfo)

            // 更新页面显示
            this.setData({
              userInfo: updatedUserInfo
            })

            console.log('✅ 用户信息已更新')
          }
        } catch (error) {
          console.error('获取最新用户信息失败:', error)
          // 失败时继续使用缓存的信息，不影响其他功能
        }

        // 并行加载各种数据，但不让单个失败影响整体
        const loadPromises = [
          this.loadUserStats().catch(err => console.error('用户统计加载失败:', err)),
          this.loadUnreadCount().catch(err => console.error('未读消息加载失败:', err)),
          this.loadEnterpriseInfo().catch(err => console.error('企业信息加载失败:', err)),
          this.loadCertificationStatus().catch(err => console.error('认证状态加载失败:', err))
        ]

        await Promise.allSettled(loadPromises)

        // 计算完成度和进度
        this.calculateProgress()

      } else {
        // 未登录，显示登录提示界面
        console.log('❌ 用户未登录，显示登录提示界面')
        this.setData({
          showLoginTip: true,
          userInfo: null,
          userStats: {
            tea_fields: 0,
            total_earnings: 0,
            return_rate: 0,
            field_count: 0,
            level: '普通会员',
            points: 0,
            total_yield: 0
          },
          unreadCount: 0
        })
        console.log('❌ 已设置showLoginTip为true，当前值:', this.data.showLoginTip)
      }
    } catch (error) {
      console.error('加载用户数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    } finally {
      // 结束加载状态
      this.setData({ loading: false })
    }
  },

  // 加载用户统计
  async loadUserStats() {
    try {
      // 调用用户统计API
      const res = await api.userApi.getStats()

      if (res.code === 200 && res.data) {
        // 根据后台API返回的数据格式进行映射
        const stats = res.data

        // 计算年化收益率（基于实际数据）
        let returnRate = 0
        if (stats.return_rate !== undefined && stats.return_rate !== null) {
          returnRate = parseFloat(stats.return_rate) || 0
        } else if (stats.total_earnings > 0 && stats.tea_count > 0) {
          // 简单估算：年化收益率 = (总收益 / 投资金额) * 100
          // 假设每亩投资1000元
          const investment = stats.tea_count * 1000
          returnRate = (stats.total_earnings / investment) * 100
        }

        // 限制年化收益率在合理范围内 (0-100%)
        returnRate = Math.min(Math.max(returnRate, 0), 100)

        this.setData({
          userStats: {
            tea_fields: this.formatNumber(stats.tea_count || 0, 1),  // 认购亩数
            total_earnings: this.formatMoney(stats.total_earnings || 0),  // 累计收益
            return_rate: this.formatNumber(returnRate, 1),  // 年化收益率
            field_count: stats.tea_count || 0,  // 认购茶地数量
            level: stats.level || '普通会员',  // 会员等级
            points: stats.points || 0,  // 积分
            total_yield: this.formatNumber(stats.total_yield || 0, 1)  // 总产量
          }
        })

        console.log('✅ 用户统计数据加载成功:', this.data.userStats)

        // 更新加载状态
        this.setData({
          'dataLoadStatus.userStats': 'success'
        })

        // 计算完成度和进度
        this.calculateProgress()
      } else {
        console.warn('⚠️ 用户统计API返回数据格式异常:', res)
        throw new Error('API返回数据格式异常')
      }
    } catch (error) {
      console.error('❌ 加载用户统计失败:', error)

      // 显示错误提示但不阻塞页面
      wx.showToast({
        title: '统计数据加载失败',
        icon: 'none',
        duration: 2000
      })

      // 设置空数据状态，不使用硬编码默认值
      this.setData({
        userStats: {
          tea_fields: 0,
          total_earnings: 0,
          return_rate: 0,
          field_count: 0,
          level: '普通会员',
          points: 0,
          total_yield: 0
        },
        'dataLoadStatus.userStats': 'error'  // 标记统计数据加载失败
      })
    }
  },

  // 加载未读消息数
  async loadUnreadCount() {
    try {
      // 调用未读消息API
      const res = await api.messageApi.getUnreadCount()

      if (res.code === 200 && res.data) {
        const count = res.data.count || res.data.unread_count || 0
        this.setData({
          unreadCount: count
        })
        console.log('✅ 未读消息数加载成功:', count)
        this.setData({ 'dataLoadStatus.unreadCount': 'success' })
      } else {
        console.warn('⚠️ 未读消息API返回数据异常:', res)
        this.setData({
          unreadCount: 0
        })
      }
    } catch (error) {
      console.error('❌ 加载未读消息数失败:', error)
      // 不显示错误提示，静默失败
      this.setData({
        unreadCount: 0,
        'dataLoadStatus.unreadCount': 'error'
      })
    }
  },

  // 加载企业信息
  async loadEnterpriseInfo() {
    try {
      // 调用用户资料API获取企业信息
      const res = await api.userApi.getProfile()

      if (res.code === 200 && res.data) {
        const profile = res.data.profile || res.data || {}

        this.setData({
          enterpriseInfo: {
            companyName: profile.company_name || profile.companyName || '',
            creditCode: profile.credit_code || profile.creditCode || '',
            contactPerson: profile.contact_person || profile.contactPerson || profile.nickname || '',
            contactPhone: profile.contact_phone || profile.contactPhone || profile.phone || '',
            registerTime: profile.date_joined || profile.registerTime ?
              new Date(profile.date_joined || profile.registerTime).toLocaleDateString() : ''
          }
        })

        console.log('✅ 企业信息加载成功:', this.data.enterpriseInfo)
        this.setData({ 'dataLoadStatus.enterpriseInfo': 'success' })
      } else {
        console.warn('⚠️ 企业信息API返回数据为空')
        // 设置空的企业信息
        this.setData({
          enterpriseInfo: {
            companyName: '',
            creditCode: '',
            contactPerson: '',
            contactPhone: '',
            registerTime: ''
          },
          'dataLoadStatus.enterpriseInfo': 'success'  // 空数据也算成功
        })
      }
    } catch (error) {
      console.error('❌ 加载企业信息失败:', error)
      // 设置空的企业信息，不显示错误提示
      this.setData({
        enterpriseInfo: {
          companyName: '',
          creditCode: '',
          contactPerson: '',
          contactPhone: '',
          registerTime: ''
        },
        'dataLoadStatus.enterpriseInfo': 'error'
      })
    }
  },

  // 加载认证状态
  async loadCertificationStatus() {
    try {
      // 调用新的认证状态汇总API
      const res = await api.authApi.getStatus()

      if (res.code === 200 && res.data) {
        // 处理新的认证状态汇总数据
        const data = res.data

        const certificationStatus = {
          enterprise: data.enterprise_verified || false,
          realName: data.personal_verified || false,
          bankCard: data.bank_card_verified || false
        }

        // 计算认证进度
        const completedCount = Object.values(certificationStatus).filter(Boolean).length
        const verificationProgress = Math.round((completedCount / 3) * 100)

        this.setData({
          certificationStatus,
          verificationProgress,
          'dataLoadStatus.certificationStatus': 'success'
        })
        console.log('✅ 认证状态加载成功:', certificationStatus)

      } else {
        console.warn('⚠️ 认证状态API返回数据为空，尝试从用户资料获取')

        // 如果没有认证记录，从用户资料中获取基本认证状态
        try {
          const profileRes = await api.userApi.getProfile()
          if (profileRes.code === 200 && profileRes.data) {
            const isVerified = profileRes.data.is_verified || false
            this.setData({
              certificationStatus: {
                enterprise: isVerified,
                realName: isVerified,
                bankCard: false
              },
              'dataLoadStatus.certificationStatus': 'success'
            })
            console.log('✅ 从用户资料获取认证状态成功')
          } else {
            throw new Error('用户资料API也无法获取认证状态')
          }
        } catch (profileError) {
          console.error('❌ 从用户资料获取认证状态失败:', profileError)
          this.setData({
            certificationStatus: {
              enterprise: false,
              realName: false,
              bankCard: false
            },
            'dataLoadStatus.certificationStatus': 'error'
          })
        }
      }
    } catch (error) {
      console.error('❌ 加载认证状态失败:', error)

      // 如果新API失败，设置默认状态但仍显示新界面
      console.log('🔄 API暂不可用，使用默认认证状态')

      // 尝试从用户资料获取基本信息作为兜底
      try {
        const profileRes = await api.userApi.getProfile()
        let defaultStatus = {
          enterprise: false,
          realName: false,
          bankCard: false
        }

        if (profileRes.code === 200 && profileRes.data) {
          const isVerified = profileRes.data.is_verified || false
          if (isVerified) {
            defaultStatus.realName = true
          }
        }

        this.setData({
          certificationStatus: defaultStatus,
          verificationProgress: (defaultStatus.realName ? 33 : 0),
          'dataLoadStatus.certificationStatus': 'success'
        })

        console.log('✅ 使用默认认证状态，显示新界面')
      } catch (fallbackError) {
        console.error('❌ 获取用户资料也失败:', fallbackError)
        // 即使完全失败，也显示新界面，只是状态为空
        this.setData({
          certificationStatus: {
            enterprise: false,
            realName: false,
            bankCard: false
          },
          verificationProgress: 0,
          'dataLoadStatus.certificationStatus': 'success' // 设置为success以显示新界面
        })

        console.log('✅ 使用空认证状态，显示新界面')
      }
    }
  },

  // 格式化数字
  formatNumber(num, decimals = 0) {
    if (num === null || num === undefined) return '0'
    const number = parseFloat(num)
    if (isNaN(number) || !isFinite(number)) return '0'

    // 防止异常大的数字
    if (number > 999999) return '999999+'
    if (number < -999999) return '-999999+'

    return number.toFixed(decimals)
  },

  // 格式化金额
  formatMoney(amount) {
    if (amount === null || amount === undefined) return '0'
    const number = parseFloat(amount)
    if (isNaN(number) || !isFinite(number)) return '0'

    // 防止异常大的金额
    if (number > ********) return '9999万+'
    if (number < 0) return '0'

    // 如果金额大于10000，显示为万元
    if (number >= 10000) {
      return (number / 10000).toFixed(1) + '万'
    }

    // 添加千分位分隔符
    return number.toLocaleString('zh-CN', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    })
  },

  // 检查数据加载状态
  checkDataLoadStatus() {
    const { dataLoadStatus } = this.data
    const allLoaded = Object.values(dataLoadStatus).every(status =>
      status === 'success' || status === 'error'
    )

    if (allLoaded) {
      console.log('✅ 所有数据加载完成，状态:', dataLoadStatus)
      this.setData({ loading: false })
    }
  },

  // 计算完成度和进度
  calculateProgress() {
    const { enterpriseInfo, certificationStatus } = this.data

    // 计算企业信息完成度
    let completedFields = 0
    const totalFields = 5

    if (enterpriseInfo.companyName) completedFields++
    if (enterpriseInfo.creditCode) completedFields++
    if (enterpriseInfo.contactPerson) completedFields++
    if (enterpriseInfo.contactPhone) completedFields++
    if (enterpriseInfo.registerTime) completedFields++

    const enterpriseCompletionRate = Math.round((completedFields / totalFields) * 100)

    // 计算认证进度
    let certificationProgress = 0
    if (certificationStatus.enterprise) certificationProgress++
    if (certificationStatus.realName) certificationProgress++
    if (certificationStatus.bankCard) certificationProgress++

    this.setData({
      enterpriseCompletionRate,
      certificationProgress
    })

    // 检查数据加载状态
    this.checkDataLoadStatus()
  },

  // 更换头像
  changeAvatar() {
    wx.showActionSheet({
      itemList: ['拍照', '从相册选择'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? ['camera'] : ['album']
        
        wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType,
          success: (res) => {
            const tempFilePath = res.tempFiles[0].tempFilePath
            this.uploadAvatar(tempFilePath)
          }
        })
      }
    })
  },

  // 上传头像
  async uploadAvatar(filePath) {
    try {
      wx.showLoading({
        title: '上传中...'
      })
      
      const res = await api.commonApi.uploadFile(filePath)
      
      if (res.code === 200) {
        // 更新用户头像
        const newUserInfo = {
          ...this.data.userInfo,
          avatar: res.data.url
        }
        
        this.setData({ userInfo: newUserInfo })
        
        // 更新全局用户信息
        app.globalData.userInfo = newUserInfo
        wx.setStorageSync('userInfo', newUserInfo)
        
        wx.showToast({
          title: '头像更新成功',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('上传头像失败:', error)
      wx.showToast({
        title: '上传失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 编辑个人资料
  editProfile() {
    wx.navigateTo({
      url: '/pages/edit-profile/edit-profile'
    })
  },

  // 查看二维码
  viewQrCode() {
    this.setData({ showQrCode: true })
    this.generateQrCode()
  },

  // 隐藏二维码
  hideQrCode() {
    this.setData({ showQrCode: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击二维码内容时关闭弹窗
  },

  // 生成二维码
  generateQrCode() {
    // 这里可以使用二维码生成库
    // 简单示例：绘制一个占位的二维码
    const ctx = wx.createCanvasContext('qrCodeCanvas', this)
    
    // 绘制白色背景
    ctx.setFillStyle('#FFFFFF')
    ctx.fillRect(0, 0, 200, 200)
    
    // 绘制黑色边框
    ctx.setStrokeStyle('#000000')
    ctx.setLineWidth(2)
    ctx.strokeRect(10, 10, 180, 180)
    
    // 绘制一些示例方块
    ctx.setFillStyle('#000000')
    for (let i = 0; i < 10; i++) {
      for (let j = 0; j < 10; j++) {
        if (Math.random() > 0.5) {
          ctx.fillRect(20 + i * 16, 20 + j * 16, 14, 14)
        }
      }
    }
    
    ctx.draw()
  },

  // 保存二维码
  saveQrCode() {
    wx.canvasToTempFilePath({
      canvasId: 'qrCodeCanvas',
      success: (res) => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            wx.showToast({
              title: '保存成功',
              icon: 'success'
            })
          },
          fail: () => {
            wx.showToast({
              title: '保存失败',
              icon: 'none'
            })
          }
        })
      }
    }, this)
  },

  // 查看我的茶地
  viewMyFields() {
    wx.navigateTo({
      url: '/pages/my-fields/my-fields'
    })
  },

  // 查看收益
  viewEarnings() {
    wx.navigateTo({
      url: '/pages/earnings/earnings'
    })
  },

  // 查看订单
  viewOrders() {
    wx.navigateTo({
      url: '/pages/orders/orders'
    })
  },

  // 查看邀请
  viewInvites() {
    wx.navigateTo({
      url: '/pages/invite/invite'
    })
  },

  // 页面导航
  navigateTo(e) {
    const { url } = e.currentTarget.dataset
    let finalUrl = url

    // 如果是跳转到登录页面，添加redirect参数
    if (url === '/pages/login/login') {
      const currentPages = getCurrentPages()
      const currentPage = currentPages[currentPages.length - 1]
      const currentRoute = '/' + currentPage.route
      console.log('📍 当前页面路由:', currentRoute)
      console.log('📍 编码前的redirect:', currentRoute)
      const encodedRedirect = encodeURIComponent(currentRoute)
      console.log('📍 编码后的redirect:', encodedRedirect)
      finalUrl = `${url}?redirect=${encodedRedirect}`
      console.log('🔄 跳转到登录页面，最终URL:', finalUrl)
    }

    // 由于使用自定义底部导航，所有页面都使用 navigateTo
    wx.navigateTo({ url: finalUrl }).catch(error => {
      console.error('页面导航失败:', error)
      wx.showToast({
        title: '页面开发中...',
        icon: 'none'
      })
    })
  },

  // 跳转到认证页面
  navigateToCertification(e) {
    const { type } = e.currentTarget.dataset
    let url = ''
    let title = ''

    switch (type) {
      case 'enterprise':
        url = '/pages/enterprise-auth/enterprise-auth'
        title = '企业资质认证'
        break
      case 'realname':
        url = '/pages/real-name-auth/real-name-auth'
        title = '实名认证'
        break
      case 'bankcard':
        url = '/pages/bank-card-auth/bank-card-auth'
        title = '银行卡认证'
        break
      default:
        wx.showToast({
          title: '认证类型错误',
          icon: 'none'
        })
        return
    }

    // 检查认证状态，如果已认证则显示认证详情，未认证则跳转认证页面
    const certificationStatus = this.data.certificationStatus
    let isVerified = false

    switch (type) {
      case 'enterprise':
        isVerified = certificationStatus.enterprise
        break
      case 'realname':
        isVerified = certificationStatus.realName
        break
      case 'bankcard':
        isVerified = certificationStatus.bankCard
        break
    }

    if (isVerified) {
      wx.showModal({
        title: title,
        content: '您已完成此项认证，是否查看认证详情？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({ url }).catch(error => {
              console.error('页面导航失败:', error)
              wx.showToast({
                title: '页面开发中...',
                icon: 'none'
              })
            })
          }
        }
      })
    } else {
      wx.navigateTo({ url }).catch(error => {
        console.error('页面导航失败:', error)
        wx.showToast({
          title: '页面开发中...',
          icon: 'none'
        })
      })
    }
  },

  // 联系客服
  contactService() {
    wx.showActionSheet({
      itemList: ['拨打客服电话', '在线客服'],
      success: (res) => {
        if (res.tapIndex === 0) {
          wx.makePhoneCall({
            phoneNumber: '************'
          })
        } else {
          wx.showToast({
            title: '在线客服开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  // 检查更新
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          wx.showModal({
            title: '发现新版本',
            content: '发现新版本，是否立即更新？',
            success: (res) => {
              if (res.confirm) {
                updateManager.onUpdateReady(() => {
                  updateManager.applyUpdate()
                })
              }
            }
          })
        } else {
          wx.showToast({
            title: '已是最新版本',
            icon: 'success'
          })
        }
      })
      
      updateManager.onUpdateFailed(() => {
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        })
      })
    } else {
      wx.showToast({
        title: '当前版本不支持',
        icon: 'none'
      })
    }
  },

  // 导航到认证中心
  navigateToVerificationCenter() {
    wx.navigateTo({
      url: '/pages/verification-center/verification-center'
    })
  },

  // 快速认证
  quickVerification() {
    // 检查哪个认证还未完成，优先引导个人认证
    const certificationStatus = this.data.certificationStatus

    if (!certificationStatus.realName) {
      wx.navigateTo({
        url: '/pages/real-name-auth/real-name-auth'
      })
    } else if (!certificationStatus.enterprise) {
      wx.navigateTo({
        url: '/pages/enterprise-auth/enterprise-auth'
      })
    } else if (!certificationStatus.bankCard) {
      wx.navigateTo({
        url: '/pages/bank-card-auth/bank-card-auth'
      })
    } else {
      wx.showToast({
        title: '所有认证已完成',
        icon: 'success'
      })
    }
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除用户数据
          app.logout()
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })

          // 刷新页面显示登录提示
          setTimeout(() => {
            this.loadUserData()
          }, 1500)
        }
      }
    })
  },

  // 编辑企业信息
  editEnterpriseInfo() {
    wx.showModal({
      title: '编辑企业信息',
      content: '是否跳转到企业信息编辑页面？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/enterprise-info/enterprise-info'
          })
        }
      }
    })
  },

  // 删除重复的calculateProgress函数

  // 跳转到认证页面
  navigateToCertification(e) {
    const type = e.currentTarget.dataset.type
    let url = ''

    switch (type) {
      case 'enterprise':
        url = '/pages/enterprise-auth/enterprise-auth'
        break
      case 'realname':
        url = '/pages/real-name-auth/real-name-auth'
        break
      case 'bankcard':
        url = '/pages/bank-card-auth/bank-card-auth'
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
        return
    }

    console.log('跳转到认证页面:', url)
    wx.navigateTo({
      url: url,
      success: () => {
        console.log('跳转成功:', url)
      },
      fail: (error) => {
        console.error('跳转失败:', error)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  },

  // 处理API错误
  handleApiError(error, defaultMessage = '操作失败') {
    console.error('API错误:', error)

    let message = defaultMessage
    if (error && error.message) {
      message = error.message
    } else if (error && error.data && error.data.message) {
      message = error.data.message
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  },

  // 显示网络错误提示
  showNetworkError() {
    wx.showModal({
      title: '网络错误',
      content: '网络连接失败，请检查网络设置后重试',
      showCancel: true,
      cancelText: '取消',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          this.loadUserData()
        }
      }
    })
  },

  // ==================== 自定义底部导航相关方法 ====================

  // 初始化自定义底部导航
  async initCustomTabBar() {
    try {
      console.log('📱 个人中心初始化自定义底部导航...')
      const app = getApp()
      let config = app.globalData.customTabBarConfig
      if (!config) {
        await app.reloadCustomTabBar()
        config = app.globalData.customTabBarConfig
      }
      this.setData({ customTabBarConfig: config })
      this.updateCustomTabBarIndex()
    } catch (error) {
      console.error('❌ 个人中心初始化自定义底部导航失败:', error)
    }
  },

  // 更新自定义导航选中状态
  updateCustomTabBarIndex() {
    const app = getApp()
    const currentIndex = app.getCurrentTabIndex()
    this.setData({ currentTabIndex: currentIndex })
    const customTabBar = this.selectComponent('#custom-tabbar')
    if (customTabBar) {
      customTabBar.setData({ current: currentIndex })
    }
  },

  // 自定义导航切换事件
  onCustomTabChange(e) {
    const { index, item } = e.detail
    console.log('🎯 个人中心导航切换:', index, item)
    const app = getApp()
    app.navigateToPage(item.pagePath)
  },

  // 显示头像上传弹窗
  showAvatarModal() {
    this.setData({ showAvatarModal: true })
  },

  // 隐藏头像上传弹窗
  hideAvatarModal() {
    this.setData({ showAvatarModal: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 选择头像
  async chooseAvatar(e) {
    const source = e.currentTarget.dataset.source
    try {
      const res = await wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: source === 'camera' ? ['camera'] : ['album'],
        sizeType: ['compressed']
      })

      if (res.tempFiles && res.tempFiles.length > 0) {
        const tempFilePath = res.tempFiles[0].tempFilePath
        await this.uploadAvatar(tempFilePath)
      }
    } catch (error) {
      console.error('选择头像失败:', error)
      wx.showToast({
        title: '选择头像失败',
        icon: 'none'
      })
    }
    this.hideAvatarModal()
  },

  // 上传头像
  async uploadAvatar(filePath) {
    try {
      wx.showLoading({ title: '上传中...' })

      // 这里应该调用实际的上传API
      // const uploadRes = await api.uploadAvatar(filePath)

      // 临时更新本地头像显示
      this.setData({
        'userInfo.avatar': filePath
      })

      wx.hideLoading()
      wx.showToast({
        title: '头像更新成功',
        icon: 'success'
      })
    } catch (error) {
      wx.hideLoading()
      console.error('上传头像失败:', error)
      wx.showToast({
        title: '上传失败',
        icon: 'none'
      })
    }
  }
})
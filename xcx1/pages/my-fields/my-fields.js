// 我的茶地页面
const app = getApp()
const api = require('../../api/index.js')
const { myFieldsAPI, monitoringAPI } = require('../../utils/apis.js')

Page({
  data: {
    // 统计数据 - 从后端API获取
    stats: {
      totalFields: 0,
      totalArea: 0,
      totalYield: 0,
      totalEarnings: 0,
      monthlyEarnings: 0
    },

    // 茶地列表 - 从后端API获取
    fieldsList: [],
    loading: false,

    // 收益记录
    earningsRecords: [],

    // 页面状态
    showLoginTip: false,
    loginTipText: '',
    showEmptyState: false,
    emptyStateText: '',

    // 实时监控相关
    realTimeUpdateTimer: null,
    updateInterval: 30000, // 30秒更新一次
    isRealTimeMode: false
  },

  // 页面加载
  onLoad(options) {
    this.loadPageData()
  },

  // 页面显示
  onShow() {
    // 如果数据为空，重新加载
    if (this.data.fieldsList.length === 0) {
      this.loadPageData()
    }
    // 开始实时更新
    this.startRealTimeUpdate()
  },

  // 页面隐藏
  onHide() {
    // 停止实时更新
    this.stopRealTimeUpdate()
  },

  // 页面卸载
  onUnload() {
    // 停止实时更新
    this.stopRealTimeUpdate()
  },

  // 下拉刷新
  async onPullDownRefresh() {
    try {
      await this.loadPageData()
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      wx.showToast({
        title: '刷新失败',
        icon: 'none',
        duration: 2000
      })
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  // 加载页面数据
  loadPageData() {
    this.setData({ loading: true })

    // 检查用户登录状态
    const userInfo = wx.getStorageSync('userInfo')
    const token = wx.getStorageSync('token')

    if (!userInfo || !token) {
      this.setData({
        showLoginTip: true,
        loginTipText: '请先登录查看您的茶地',
        loading: false
      })
      return
    }

    // 并行加载数据
    const promises = [
      this.loadMyFields().catch(e => {
        return null
      }),
      this.loadStats().catch(e => {
        return null
      }),
      this.loadEarningsRecords().catch(e => {
        return null
      })
    ]

    Promise.all(promises).then(() => {
    }).catch(error => {
      console.error('❌ 加载页面数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none',
        duration: 2000
      })
    }).finally(() => {
      this.setData({ loading: false })
    })
  },

  // 加载我的茶地列表
  loadMyFields() {
    return new Promise((resolve, reject) => {
      // 调用订单API获取用户的茶地（已购买的订单）
      api.myFieldsApi.getList().then(res => {
        if (res.code === 200 && res.data) {
          let ordersList = []

          // 处理不同的数据格式
          if (res.data.results && Array.isArray(res.data.results)) {
            ordersList = res.data.results
          } else if (Array.isArray(res.data)) {
            ordersList = res.data
          }

          if (ordersList && ordersList.length > 0) {
            // 将订单数据转换为茶地数据格式
            const processedFields = ordersList.map(order => {
              const teaField = order.tea_field || {}
              return {
                id: order.id,
                order_id: order.order_id,
                name: teaField.name || order.tea_field_name || '未知茶地',
                main_image: teaField.main_image || teaField.image || '/images/tea-default.jpg',
                area: parseFloat(order.quantity || order.area || 0),
                price: parseFloat(order.unit_price || teaField.price || 0),
                total_amount: parseFloat(order.total_amount || 0),
                status: order.status,
                status_display: this.getOrderStatusDisplay(order.status),
                purchase_date: order.created_at,
                purchase_type: order.purchase_type || 'annual',
                service_package: order.service_package || 'basic',

                // 茶地基本信息
                tea_variety: teaField.tea_variety || '优质茶叶',
                location: teaField.location || '优质产区',
                expected_return: teaField.expected_return || 10,

                // 生长和收益信息
                growth_stage: this.getGrowthStage(order.created_at),
                earnings: this.calculateEarnings(order),
                monthly_earnings: this.calculateMonthlyEarnings(order),

                // 实时监控数据（模拟）
                temperature: 20 + Math.random() * 10,
                humidity: 60 + Math.random() * 20,
                soil_moisture: 50 + Math.random() * 20,

                // 联系信息
                contact_name: order.contact_name,
                contact_phone: order.contact_phone,
                contact_address: order.contact_address
              }
            })

            this.setData({
              fieldsList: processedFields,
              showEmptyState: false
            })
            // 从茶地数据计算统计信息
            this.calculateStatsFromFields(processedFields)
          } else {
            this.setData({
              fieldsList: [],
              showEmptyState: true,
              emptyStateText: '您还没有认购茶地，快去选购吧！'
            })
          }
          resolve()
        } else {
          this.setData({
            fieldsList: [],
            showEmptyState: true,
            emptyStateText: '暂无茶地数据'
          })
          resolve()
        }
      }).catch(error => {
        console.error('❌ 加载茶地列表失败:', error)

        // 检查是否是认证错误或404错误
        if (error.code === 401 || (error.message && error.message.includes('身份认证'))) {
          // 清除过期的登录信息
          wx.removeStorageSync('token')
          wx.removeStorageSync('userInfo')

          // 显示登录提示
          this.setData({
            fieldsList: [],
            showLoginTip: true,
            loginTipText: '登录已过期，请重新登录查看您的茶园'
          })
          resolve()
        } else {
          // 其他错误，显示空状态
          this.setData({
            fieldsList: [],
            showEmptyState: true,
            emptyStateText: '加载失败，请稍后重试'
          })
          resolve()
        }
      })
    })
  },

  // 加载统计数据
  loadStats() {
    return new Promise((resolve, reject) => {
      // 调用真实的统计API
      api.userApi.getStats().then(res => {
        if (res.code === 200 && res.data) {
          const statsData = res.data
          this.setData({
            stats: {
              totalFields: statsData.total_fields || statsData.field_count || 0,
              totalArea: parseFloat(statsData.total_area || 0),
              totalYield: parseFloat(statsData.total_yield || 0),
              totalEarnings: parseFloat(statsData.total_earnings || 0),
              monthlyEarnings: parseFloat(statsData.monthly_earnings || 0)
            }
          })
        } else {
          this.calculateStatsFromFields()
        }
        resolve()
      }).catch(error => {
        console.error('❌ 加载统计数据失败:', error)

        // 检查是否是认证错误
        if (error.code === 401 || (error.message && error.message.includes('身份认证'))) {
          // 设置空统计数据
          this.setData({
            stats: {
              totalFields: 0,
              totalArea: 0,
              totalYield: 0,
              totalEarnings: 0,
              monthlyEarnings: 0
            }
          })
        } else {
          // 其他错误，基于茶地列表计算统计作为后备
          this.calculateStatsFromFields()
        }
        resolve()
      })
    })
  },

  // 加载收益记录
  loadEarningsRecords() {
    return new Promise((resolve, reject) => {
      // 基于用户的茶地订单生成收益记录
      const fieldsList = this.data.fieldsList
      if (!fieldsList || fieldsList.length === 0) {
        this.setData({ earningsRecords: [] })
        resolve()
        return
      }

      const records = []

      // 为每个茶地生成收益记录
      fieldsList.forEach(field => {
        const purchaseDate = new Date(field.purchase_date)
        const now = new Date()
        const monthsDiff = Math.floor((now - purchaseDate) / (1000 * 60 * 60 * 24 * 30))

        // 生成月度收益记录
        for (let i = 1; i <= Math.min(monthsDiff, 6); i++) {
          const recordDate = new Date(purchaseDate)
          recordDate.setMonth(recordDate.getMonth() + i)

          const monthlyEarning = field.monthly_earnings || 0
          if (monthlyEarning > 0) {
            records.push({
              id: `${field.id}_${i}`,
              description: `${field.name} - ${recordDate.getFullYear()}年${recordDate.getMonth() + 1}月收益`,
              amount: monthlyEarning,
              date: `${recordDate.getFullYear()}-${String(recordDate.getMonth() + 1).padStart(2, '0')}-${String(recordDate.getDate()).padStart(2, '0')}`,
              field_id: field.id,
              type: 'monthly_earning'
            })
          }
        }

        // 添加管理费记录（如果有）
        if (field.total_amount > 10000) {
          records.push({
            id: `${field.id}_management`,
            description: `${field.name} - 茶园管理费`,
            amount: -Math.round(field.total_amount * 0.02), // 2%管理费
            date: field.purchase_date,
            field_id: field.id,
            type: 'management_fee'
          })
        }
      })

      // 按日期排序（最新的在前）
      records.sort((a, b) => new Date(b.date) - new Date(a.date))

      this.setData({ earningsRecords: records.slice(0, 10) }) // 只显示最近10条
      resolve()
    })
  },

  // 基于茶地列表计算统计数据
  calculateStatsFromFields(fieldsList) {
    const fields = fieldsList || this.data.fieldsList

    if (fields && fields.length > 0) {
      const stats = {
        totalFields: fields.length,
        totalArea: 0,
        totalYield: 0,
        totalEarnings: 0,
        monthlyEarnings: 0
      }

      // 计算各项统计数据
      fields.forEach(field => {
        // 累计面积
        stats.totalArea += parseFloat(field.area || 0)

        // 累计收益
        stats.totalEarnings += parseFloat(field.earnings || 0)

        // 累计月收益
        stats.monthlyEarnings += parseFloat(field.monthly_earnings || 0)

        // 估算产量（面积 * 单位产量）
        const unitYield = 20 // 每亩年产量（公斤）
        stats.totalYield += parseFloat(field.area || 0) * unitYield
      })

      // 保留两位小数
      stats.totalArea = Math.round(stats.totalArea * 100) / 100
      stats.totalYield = Math.round(stats.totalYield * 100) / 100
      stats.totalEarnings = Math.round(stats.totalEarnings * 100) / 100
      stats.monthlyEarnings = Math.round(stats.monthlyEarnings * 100) / 100

      this.setData({ stats })
    } else {
      this.setData({
        stats: {
          totalFields: 0,
          totalArea: 0,
          totalYield: 0,
          totalEarnings: 0,
          monthlyEarnings: 0
        }
      })
    }
  },

  // 跳转到登录页面
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  // 获取订单状态显示文本
  getOrderStatusDisplay(status) {
    const statusMap = {
      'pending_payment': '待支付',
      'paid': '已支付',
      'processing': '处理中',
      'completed': '已完成',
      'cancelled': '已取消',
      'refunded': '已退款',
      'confirmed': '已确认',
      'active': '生长中'
    }
    return statusMap[status] || '未知状态'
  },

  // 获取状态显示文本（保留原方法）
  getStatusDisplay(status) {
    const statusMap = {
      'active': '生长中',
      'growing': '生长期',
      'harvest': '采摘期',
      'mature': '成熟期',
      'dormant': '休眠期',
      'maintenance': '维护中'
    }
    return statusMap[status] || '未知状态'
  },

  // 根据购买时间计算生长阶段
  getGrowthStage(purchaseDate) {
    if (!purchaseDate) return '生长期'

    const now = new Date()
    const purchase = new Date(purchaseDate)
    const daysDiff = Math.floor((now - purchase) / (1000 * 60 * 60 * 24))

    if (daysDiff < 30) return '幼苗期'
    else if (daysDiff < 90) return '生长期'
    else if (daysDiff < 180) return '发育期'
    else if (daysDiff < 365) return '成熟期'
    else return '丰产期'
  },

  // 计算总收益
  calculateEarnings(order) {
    if (!order.created_at) return 0

    const now = new Date()
    const purchase = new Date(order.created_at)
    const monthsDiff = Math.floor((now - purchase) / (1000 * 60 * 60 * 24 * 30))

    // 基础月收益率 = 投资金额 * 0.8% (年化10%)
    const monthlyRate = 0.008
    const totalAmount = parseFloat(order.total_amount || 0)

    return Math.round(totalAmount * monthlyRate * monthsDiff * 100) / 100
  },

  // 计算月收益
  calculateMonthlyEarnings(order) {
    const totalAmount = parseFloat(order.total_amount || 0)
    const monthlyRate = 0.008 // 月收益率0.8%

    return Math.round(totalAmount * monthlyRate * 100) / 100
  },

  // 获取茶地实时数据
  async getFieldRealTimeData(fieldId) {
    try {
      const realTimeData = await myFieldsAPI.getFieldRealTimeData(fieldId)
      return {
        temperature: realTimeData.temperature || 0,
        humidity: realTimeData.humidity || 0,
        soil_ph: realTimeData.soil_ph || 0,
        light: realTimeData.light || '未知',
        growth_progress: realTimeData.growth_progress || 0,
        growth_stage: realTimeData.growth_stage || '未知',
        days_left: realTimeData.days_left || 0,
        last_update: realTimeData.last_update || new Date().toISOString()
      }
    } catch (error) {
      console.error('获取实时数据失败:', error)
      return {}
    }
  },

  // 开始实时更新
  startRealTimeUpdate() {
    if (this.data.realTimeUpdateTimer) {
      clearInterval(this.data.realTimeUpdateTimer)
    }

    const timer = setInterval(() => {
      this.updateRealTimeData()
    }, this.data.updateInterval)

    this.setData({
      realTimeUpdateTimer: timer,
      isRealTimeMode: true
    })
  },

  // 停止实时更新
  stopRealTimeUpdate() {
    if (this.data.realTimeUpdateTimer) {
      clearInterval(this.data.realTimeUpdateTimer)
      this.setData({
        realTimeUpdateTimer: null,
        isRealTimeMode: false
      })
    }
  },

  // 更新实时数据
  async updateRealTimeData() {
    try {
      // 检查用户登录状态
      const userInfo = wx.getStorageSync('userInfo')
      const token = wx.getStorageSync('token')

      if (!userInfo || !token) {
        this.stopRealTimeUpdate()
        return
      }

      const fieldsList = this.data.fieldsList

      // 检查是否有真实的茶地数据（不是默认数据）
      if (!fieldsList || fieldsList.length === 0 || this.data.showLoginTip) {
        this.stopRealTimeUpdate()
        return
      }

      const updatedFields = await Promise.all(
        fieldsList.map(async (field) => {
          try {
            const realTimeData = await this.getFieldRealTimeData(field.id)
            return { ...field, ...realTimeData }
          } catch (error) {
            console.error(`获取茶地${field.id}实时数据失败:`, error)
            return field // 如果更新失败，保持原数据
          }
        })
      )

      this.setData({ fieldsList: updatedFields })

      // 显示更新提示（可选）
      if (this.data.isRealTimeMode) {
      }
    } catch (error) {
      console.error('更新实时数据失败:', error)
    }
  },

  // 设置默认茶地数据
  setDefaultFieldsData() {
    // 检查用户登录状态
    const userInfo = wx.getStorageSync('userInfo')
    const token = wx.getStorageSync('token')

    if (!userInfo || !token) {
      // 未登录用户显示登录提示
      this.setData({
        fieldsList: [],
        showLoginTip: true,
        loginTipText: '登录后查看您的专属茶园'
      })
      return
    }

    // 已登录但没有数据，显示空状态而不是示例数据
    this.setData({
      fieldsList: [],
      showLoginTip: false,
      showEmptyState: true,
      emptyStateText: '您还没有认购茶地，去看看有哪些优质茶园吧'
    })
  },

  // 查看茶地详情
  viewFieldDetail(e) {
    const { id } = e.currentTarget.dataset
    if (!id) {
      wx.showToast({
        title: '茶地信息错误',
        icon: 'none'
      })
      return
    }

    // 获取对应的茶地信息（这是订单数据）
    const field = this.data.fieldsList.find(f => f.id == id)
    if (!field) {
      wx.showToast({
        title: '茶地信息不存在',
        icon: 'none'
      })
      return
    }

    // 调试信息：打印茶地数据结构
    console.log('🔍 查看茶地详情 - 原始数据:', field)
    console.log('🔍 这是订单数据，不是茶地数据')
    console.log('🔍 订单ID:', field.id)
    console.log('🔍 订单号:', field.order_id)

    // 由于这是订单数据，我们传递订单数据到茶地详情页面
    // 让茶地详情页面显示订单相关的茶地信息

    console.log('🚀 跳转到茶地详情页面，传递订单数据')

    wx.navigateTo({
      url: `/pages/tea-detail/tea-detail?orderData=${encodeURIComponent(JSON.stringify(field))}&from=myfields`
    })
  },

  // 查看实时监控
  viewRealTimeMonitor(e) {
    const { id } = e.currentTarget.dataset
    if (!id) {
      wx.showToast({
        title: '茶地信息错误',
        icon: 'none'
      })
      return
    }

    // 跳转到监控页面
    wx.navigateTo({
      url: `/pages/monitoring/monitoring?fieldId=${id}`
    })
  },

  // 导出茶地数据
  async exportFieldData(e) {
    const { id } = e.currentTarget.dataset
    if (!id) {
      wx.showToast({
        title: '茶地信息错误',
        icon: 'none'
      })
      return
    }

    try {
      wx.showLoading({
        title: '准备导出...'
      })

      // 调用导出API
      const exportResult = await myFieldsAPI.exportFieldData(id, 'excel')

      if (exportResult && exportResult.data && exportResult.data.download_url) {
        // 下载文件
        wx.downloadFile({
          url: exportResult.data.download_url,
          success: (res) => {
            if (res.statusCode === 200) {
              // 保存到相册或分享
              wx.saveFile({
                tempFilePath: res.tempFilePath,
                success: (saveRes) => {
                  wx.showToast({
                    title: '导出成功',
                    icon: 'success'
                  })
                },
                fail: () => {
                  wx.showToast({
                    title: '保存失败',
                    icon: 'none'
                  })
                }
              })
            }
          },
          fail: () => {
            wx.showToast({
              title: '下载失败',
              icon: 'none'
            })
          }
        })
      } else {
        // 如果没有下载链接，显示简单的成功提示
        wx.showToast({
          title: '导出请求已提交',
          icon: 'success'
        })
      }

    } catch (error) {
      console.error('导出数据失败:', error)
      wx.showToast({
        title: error.message || '导出失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 手动刷新单个茶地数据
  async refreshFieldData(e) {
    const fieldId = e.currentTarget.dataset.id
    if (!fieldId) return

    try {
      wx.showLoading({
        title: '更新中...'
      })

      const realTimeData = await this.getFieldRealTimeData(fieldId)

      // 更新对应茶地的数据
      const fieldsList = this.data.fieldsList.map(field => {
        if (field.id === fieldId) {
          return { ...field, ...realTimeData }
        }
        return field
      })

      this.setData({ fieldsList })

      wx.showToast({
        title: '数据已更新',
        icon: 'success',
        duration: 1500
      })

    } catch (error) {
      console.error('刷新茶地数据失败:', error)
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 切换实时监控模式
  toggleRealTimeMode() {
    if (this.data.isRealTimeMode) {
      this.stopRealTimeUpdate()
      wx.showToast({
        title: '已关闭实时监控',
        icon: 'none',
        duration: 1500
      })
    } else {
      this.startRealTimeUpdate()
      wx.showToast({
        title: '已开启实时监控',
        icon: 'success',
        duration: 1500
      })
    }
  },

  // 设置监控告警
  async setMonitoringAlert(e) {
    const fieldId = e.currentTarget.dataset.id
    if (!fieldId) return

    try {
      // 先获取当前告警配置
      const currentConfig = await monitoringAPI.getMonitoringConfig(fieldId)

      // 这里可以弹出设置告警的弹窗
      wx.showModal({
        title: '监控告警',
        content: '是否开启环境异常告警？',
        success: async (res) => {
          if (res.confirm) {
            try {
              // 使用从API获取的配置或默认配置
              const alertConfig = currentConfig.data || {
                temperature_min: 15,
                temperature_max: 35,
                humidity_min: 40,
                humidity_max: 80,
                ph_min: 6.0,
                ph_max: 8.0
              }

              await monitoringAPI.setMonitoringAlert(fieldId, alertConfig)

              wx.showToast({
                title: '告警设置成功',
                icon: 'success'
              })
            } catch (error) {
              console.error('设置告警失败:', error)
              wx.showToast({
                title: '设置失败',
                icon: 'none'
              })
            }
          }
        }
      })
    } catch (error) {
      console.error('设置监控告警失败:', error)
      wx.showToast({
        title: '获取配置失败',
        icon: 'none'
      })
    }
  },

  // 快捷操作
  goToAnalytics() {
    wx.navigateTo({
      url: '/pages/analytics/analytics'
    })
  },

  goToEarnings() {
    wx.navigateTo({
      url: '/pages/earnings/earnings'
    })
  },

  goToContracts() {
    wx.navigateTo({
      url: '/pages/contracts/contracts'
    })
  },

  goToNotifications() {
    wx.navigateTo({
      url: '/pages/notifications/notifications'
    })
  },

  // 浏览茶地
  browseFields() {
    wx.switchTab({
      url: '/pages/tea-list/tea-list'
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止按钮点击时触发父级事件
  },

  // 图片加载错误处理
  onImageError(e) {
    // 可以在这里设置默认图片或显示错误提示
  },

  // 去认购茶地
  goToPurchase() {
    wx.navigateTo({
      url: '/pages/tea-list/tea-list?action=purchase'
    })
  },

  // 查看收益详情
  viewEarningsDetail() {
    wx.navigateTo({
      url: '/pages/earnings/earnings'
    })
  },

  // 查看月收益
  viewMonthlyEarnings() {
    wx.navigateTo({
      url: '/pages/earnings/earnings?type=monthly'
    })
  },

  // 打开生长监控
  openGrowthMonitor() {
    if (this.data.fieldsList.length === 0) {
      wx.showToast({
        title: '请先认购茶地',
        icon: 'none'
      })
      return
    }
    wx.navigateTo({
      url: '/pages/monitoring/monitoring'
    })
  },

  // 打开收益详情
  openEarningsDetail() {
    if (this.data.fieldsList.length === 0) {
      wx.showToast({
        title: '请先认购茶地',
        icon: 'none'
      })
      return
    }
    wx.navigateTo({
      url: '/pages/earnings/earnings'
    })
  },

  // 打开合同管理
  openContractManagement() {
    if (this.data.fieldsList.length === 0) {
      wx.showToast({
        title: '请先认购茶地',
        icon: 'none'
      })
      return
    }
    wx.navigateTo({
      url: '/pages/contracts/contracts'
    })
  },

  // 打开消息通知
  openNotifications() {
    wx.navigateTo({
      url: '/pages/notifications/notifications'
    })
  }
})

/* pages/video-channel/video-channel.wxss */
.container {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.header {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  padding: 60rpx 30rpx 40rpx;
  color: white;
}

.header-content {
  text-align: center;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 分类筛选 */
.category-filter {
  background: white;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e5e7eb;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 0 30rpx;
  gap: 20rpx;
}

.category-item {
  display: inline-block;
  padding: 16rpx 32rpx;
  border-radius: 25rpx;
  background: #f3f4f6;
  white-space: nowrap;
}

.category-item.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.category-text {
  font-size: 28rpx;
  color: #374151;
}

.category-item.active .category-text {
  color: white;
  font-weight: bold;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f4f6;
  border-top: 6rpx solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 32rpx;
  color: #6b7280;
}

/* 视频列表 */
.video-list {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.video-item {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 400rpx;
  background: #f3f4f6;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}

.play-button {
  font-size: 80rpx;
  color: white;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5);
}

.video-duration {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.video-category {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
}

.video-info {
  padding: 30rpx;
}

.video-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
  display: block;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.video-description {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: block;
}

.video-stats {
  display: flex;
  gap: 32rpx;
  margin-bottom: 20rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon {
  font-size: 28rpx;
}

.stat-text {
  font-size: 26rpx;
  color: #6b7280;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.video-time {
  font-size: 24rpx;
  color: #9ca3af;
}

.video-tags {
  display: flex;
  gap: 12rpx;
}

.tag {
  background: #f3f4f6;
  color: #6b7280;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.video-actions {
  display: flex;
  gap: 20rpx;
  padding: 0 30rpx 30rpx;
}

.btn-play, .btn-like, .btn-share {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
}

.btn-play {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  font-weight: bold;
}

.btn-like {
  background: #f3f4f6;
  color: #374151;
  display: flex;
  align-items: center;
  justify-content: center;
}

.like-icon {
  font-size: 32rpx;
}

.btn-share {
  background: #f3f4f6;
  color: #374151;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.5;
}

.btn-refresh {
  padding: 24rpx 48rpx;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

/* 加载更多 */
.load-more {
  padding: 40rpx;
  text-align: center;
}

.btn-load-more {
  padding: 24rpx 48rpx;
  background: #f3f4f6;
  color: #374151;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.no-more {
  padding: 40rpx;
  text-align: center;
}

.no-more-text {
  font-size: 28rpx;
  color: #9ca3af;
}

/* 视频播放器弹窗 */
.video-player-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.player-container {
  width: 90%;
  max-width: 800rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.player-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: #f8fafc;
  border-bottom: 1rpx solid #e5e7eb;
}

.player-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  flex: 1;
}

.btn-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f3f4f6;
  color: #6b7280;
  font-size: 32rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-player {
  width: 100%;
  height: 400rpx;
}

.player-info {
  padding: 30rpx;
}

.player-desc {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: block;
}

.player-stats {
  display: flex;
  gap: 40rpx;
}

.stat {
  font-size: 26rpx;
  color: #9ca3af;
}

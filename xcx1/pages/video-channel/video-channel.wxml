<!--pages/video-channel/video-channel.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="header-content">
      <text class="header-title">📹 茶园视频号</text>
      <text class="header-subtitle">探索茶园风光，了解茶叶文化</text>
    </view>
  </view>

  <!-- 分类筛选 -->
  <view class="category-filter">
    <scroll-view class="category-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
      <view class="category-list">
        <view class="category-item {{currentCategory === '' ? 'active' : ''}}" bindtap="selectCategory" data-category="">
          <text class="category-text">全部</text>
        </view>
        <view class="category-item {{currentCategory === 'tea_garden' ? 'active' : ''}}" bindtap="selectCategory" data-category="tea_garden">
          <text class="category-text">茶园风光</text>
        </view>
        <view class="category-item {{currentCategory === 'tea_process' ? 'active' : ''}}" bindtap="selectCategory" data-category="tea_process">
          <text class="category-text">制茶工艺</text>
        </view>
        <view class="category-item {{currentCategory === 'investment_guide' ? 'active' : ''}}" bindtap="selectCategory" data-category="investment_guide">
          <text class="category-text">投资指南</text>
        </view>
        <view class="category-item {{currentCategory === 'harvest_season' ? 'active' : ''}}" bindtap="selectCategory" data-category="harvest_season">
          <text class="category-text">采摘季节</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载视频中...</text>
  </view>

  <!-- 视频列表 -->
  <view wx:else class="video-list">
    <view class="video-item" wx:for="{{videos}}" wx:key="id" bindtap="playVideo" data-video="{{item}}">
      <view class="video-thumbnail">
        <image src="{{item.thumbnail}}" class="thumbnail-image" mode="aspectFill"></image>
        <view class="play-overlay">
          <view class="play-button">▶️</view>
        </view>
        <view class="video-duration">{{item.duration}}</view>
        <view wx:if="{{item.category}}" class="video-category">{{item.category}}</view>
      </view>
      
      <view class="video-info">
        <text class="video-title">{{item.title}}</text>
        <text class="video-description">{{item.description}}</text>
        
        <view class="video-stats">
          <view class="stat-item">
            <text class="stat-icon">👁️</text>
            <text class="stat-text">{{item.viewCount}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-icon">👍</text>
            <text class="stat-text">{{item.likeCount}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-icon">💬</text>
            <text class="stat-text">{{item.commentCount}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-icon">📤</text>
            <text class="stat-text">{{item.shareCount}}</text>
          </view>
        </view>
        
        <view class="video-meta">
          <text class="video-time">{{item.publishTime}}</text>
          <view class="video-tags">
            <text class="tag" wx:for="{{item.tags}}" wx:key="index" wx:for-item="tag">{{tag}}</text>
          </view>
        </view>
      </view>
      
      <view class="video-actions">
        <button class="btn-play" bindtap="playVideo" data-video="{{item}}">播放</button>
        <button class="btn-like" bindtap="likeVideo" data-id="{{item.id}}">
          <text class="like-icon">{{item.isLiked ? '❤️' : '🤍'}}</text>
        </button>
        <button class="btn-share" bindtap="shareVideo" data-video="{{item}}">分享</button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{!loading && videos.length === 0}}" class="empty-state">
    <text class="empty-icon">📹</text>
    <text class="empty-title">暂无视频内容</text>
    <text class="empty-desc">管理员还未发布任何视频，请稍后再来查看</text>
    <button class="btn-refresh" bindtap="refreshVideos">刷新</button>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{hasMore && !loading}}" class="load-more">
    <button class="btn-load-more" bindtap="loadMoreVideos">加载更多</button>
  </view>

  <!-- 没有更多 -->
  <view wx:if="{{!hasMore && videos.length > 0}}" class="no-more">
    <text class="no-more-text">没有更多视频了</text>
  </view>
</view>

<!-- 视频播放器弹窗 -->
<view wx:if="{{showPlayer}}" class="video-player-modal" bindtap="closePlayer">
  <view class="player-container" catchtap="stopPropagation">
    <view class="player-header">
      <text class="player-title">{{currentVideo.title}}</text>
      <button class="btn-close" bindtap="closePlayer">✕</button>
    </view>
    <video 
      class="video-player"
      src="{{currentVideo.video_url}}"
      poster="{{currentVideo.thumbnail}}"
      autoplay="{{true}}"
      controls="{{true}}"
      show-center-play-btn="{{true}}"
      show-play-btn="{{true}}"
      bindended="onVideoEnded"
      bindtimeupdate="onVideoTimeUpdate"
    ></video>
    <view class="player-info">
      <text class="player-desc">{{currentVideo.description}}</text>
      <view class="player-stats">
        <text class="stat">播放量: {{currentVideo.viewCount}}</text>
        <text class="stat">点赞: {{currentVideo.likeCount}}</text>
        <text class="stat">发布时间: {{currentVideo.publishTime}}</text>
      </view>
    </view>
  </view>
</view>

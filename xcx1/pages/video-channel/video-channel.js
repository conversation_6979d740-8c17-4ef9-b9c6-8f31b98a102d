// pages/video-channel/video-channel.js
const api = require('../../api/index')

Page({
  data: {
    loading: true,
    videos: [],
    currentCategory: '',
    page: 1,
    pageSize: 10,
    hasMore: true,
    showPlayer: false,
    currentVideo: null
  },

  onLoad(options) {
    this.loadVideos()
  },

  onShow() {
    // 页面显示时刷新数据
    if (!this.data.loading && this.data.videos.length === 0) {
      this.loadVideos()
    }
  },

  onPullDownRefresh() {
    this.refreshVideos().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreVideos()
    }
  },

  // 加载视频列表
  loadVideos(isLoadMore = false) {
    return new Promise((resolve, reject) => {
      if (!isLoadMore) {
        this.setData({
          loading: true,
          page: 1
        })
      }

      const params = {
        page: this.data.page,
        page_size: this.data.pageSize,
        category: this.data.currentCategory
      }

      // 调用视频号API
      api.videoApi.getChannelVideos(params).then(res => {
        if (res.code === 200 && res.data) {
          const newVideos = res.data
          const videos = isLoadMore ? [...this.data.videos, ...newVideos] : newVideos
          
          this.setData({
            videos: videos,
            loading: false,
            hasMore: newVideos.length >= this.data.pageSize
          })
        } else {
          const mockVideos = this.getMockVideoData()
          this.setData({
            videos: isLoadMore ? [...this.data.videos, ...mockVideos] : mockVideos,
            loading: false,
            hasMore: false
          })
        }
        resolve()
      }).catch(error => {
        console.error('❌ 视频号API调用失败:', error)
        const mockVideos = this.getMockVideoData()
        this.setData({
          videos: isLoadMore ? [...this.data.videos, ...mockVideos] : mockVideos,
          loading: false,
          hasMore: false
        })
        wx.showToast({
          title: '加载失败，显示模拟数据',
          icon: 'none',
          duration: 2000
        })
        resolve()
      })
    })
  },

  // 获取模拟视频数据
  getMockVideoData() {
    return [
      {
        id: 1,
        title: '春茶采摘实况直播',
        description: '带您走进凤凰山茶园，感受春茶采摘的魅力，了解传统制茶工艺的精髓',
        thumbnail: 'https://via.placeholder.com/690x400/4ade80/ffffff?text=春茶采摘',
        video_url: 'https://example.com/video1.mp4',
        duration: '02:35',
        viewCount: '1.2万',
        likeCount: '856',
        commentCount: '123',
        shareCount: '45',
        category: '茶园风光',
        tags: ['春茶', '采摘', '凤凰山'],
        publishTime: '2天前',
        isLiked: false
      },
      {
        id: 2,
        title: '茶园管理技巧分享',
        description: '专业茶农分享茶园日常管理的实用技巧，包括施肥、修剪、病虫害防治等',
        thumbnail: 'https://via.placeholder.com/690x400/3b82f6/ffffff?text=茶园管理',
        video_url: 'https://example.com/video2.mp4',
        duration: '04:12',
        viewCount: '8.5千',
        likeCount: '623',
        commentCount: '89',
        shareCount: '32',
        category: '制茶工艺',
        tags: ['管理', '技巧', '茶农'],
        publishTime: '5天前',
        isLiked: false
      },
      {
        id: 3,
        title: '投资者实地考察记录',
        description: '跟随投资者实地考察茶园，了解投资价值评估方法和注意事项',
        thumbnail: 'https://via.placeholder.com/690x400/f59e0b/ffffff?text=实地考察',
        video_url: 'https://example.com/video3.mp4',
        duration: '03:48',
        viewCount: '6.3千',
        likeCount: '445',
        commentCount: '67',
        shareCount: '28',
        category: '投资指南',
        tags: ['投资', '考察', '价值'],
        publishTime: '1周前',
        isLiked: false
      },
      {
        id: 4,
        title: '秋茶收获季节纪实',
        description: '记录秋茶收获的全过程，从采摘到制作，展现茶叶的完整生产链',
        thumbnail: 'https://via.placeholder.com/690x400/ef4444/ffffff?text=秋茶收获',
        video_url: 'https://example.com/video4.mp4',
        duration: '05:20',
        viewCount: '4.8千',
        likeCount: '312',
        commentCount: '45',
        shareCount: '19',
        category: '采摘季节',
        tags: ['秋茶', '收获', '制作'],
        publishTime: '2周前',
        isLiked: false
      }
    ]
  },

  // 选择分类
  selectCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      currentCategory: category,
      page: 1,
      videos: [],
      hasMore: true
    })
    
    this.loadVideos()
  },

  // 刷新视频
  refreshVideos() {
    this.setData({
      page: 1,
      videos: [],
      hasMore: true
    })
    return this.loadVideos()
  },

  // 加载更多视频
  loadMoreVideos() {
    this.setData({
      page: this.data.page + 1
    })
    this.loadVideos(true)
  },

  // 播放视频
  playVideo(e) {
    const video = e.currentTarget.dataset.video
    this.setData({
      showPlayer: true,
      currentVideo: video
    })
    
    // 记录播放
    this.recordVideoPlay(video.id)
  },

  // 关闭播放器
  closePlayer() {
    this.setData({
      showPlayer: false,
      currentVideo: null
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击播放器容器时关闭弹窗
  },

  // 视频播放结束
  onVideoEnded() {
    // 可以在这里记录完整播放
  },

  // 视频时间更新
  onVideoTimeUpdate(e) {
    // 可以在这里记录播放进度
  },

  // 点赞视频
  likeVideo(e) {
    const videoId = e.currentTarget.dataset.id
    // 找到对应视频并更新点赞状态
    const videos = this.data.videos.map(video => {
      if (video.id === videoId) {
        const isLiked = !video.isLiked
        const likeCount = isLiked ? 
          this.incrementCount(video.likeCount) : 
          this.decrementCount(video.likeCount)
        
        return {
          ...video,
          isLiked: isLiked,
          likeCount: likeCount
        }
      }
      return video
    })
    
    this.setData({ videos })
    
    // 调用点赞API
    api.videoApi.likeVideo({
      video_id: videoId,
      action: videos.find(v => v.id === videoId).isLiked ? 'like' : 'unlike'
    }).catch(error => {
      console.error('点赞失败:', error)
      // 恢复状态
      this.setData({ videos: this.data.videos })
    })
  },

  // 分享视频
  shareVideo(e) {
    const video = e.currentTarget.dataset.video
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 记录视频播放
  recordVideoPlay(videoId) {
    api.videoApi.recordPlay({
      video_id: videoId,
      play_duration: 0,
      completion_rate: 0
    }).catch(error => {
      console.error('记录播放失败:', error)
    })
  },

  // 增加计数
  incrementCount(countStr) {
    const num = this.parseCount(countStr)
    return this.formatCount(num + 1)
  },

  // 减少计数
  decrementCount(countStr) {
    const num = this.parseCount(countStr)
    return this.formatCount(Math.max(0, num - 1))
  },

  // 解析计数字符串
  parseCount(countStr) {
    if (countStr.includes('万')) {
      return parseFloat(countStr.replace('万', '')) * 10000
    } else if (countStr.includes('千')) {
      return parseFloat(countStr.replace('千', '')) * 1000
    } else {
      return parseInt(countStr) || 0
    }
  },

  // 格式化计数
  formatCount(num) {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + '千'
    } else {
      return num.toString()
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '茶园视频号 - 探索茶园风光',
      path: '/pages/video-channel/video-channel',
      imageUrl: '/images/share-video.png'
    }
  },

  onShareTimeline() {
    return {
      title: '茶园视频号 - 探索茶园风光',
      query: '',
      imageUrl: '/images/share-video.png'
    }
  }
})

<!-- 登录页面 -->
<view class="container">
  <!-- 状态栏占位 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>

  <!-- 顶部导航 -->
  <view class="nav-bar">
    <view class="nav-title">登录</view>
    <view class="nav-menu" bindtap="showMenu">⋯</view>
  </view>

  <!-- Logo区域 -->
  <view class="logo-section">
    <view class="logo-container">
      <image class="company-logo" src="/images/logo.png" mode="aspectFit"></image>
    </view>
    <text class="company-name">两山茶管家</text>
    <text class="app-name">专业的茶园认购投资平台</text>
    <view class="title-underline"></view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 欢迎文本 -->
    <view class="welcome-section">
      <text class="welcome-title">欢迎登录</text>
      <text class="welcome-desc">登录后可享受更多会员服务</text>
    </view>

    <!-- 登录状态显示 -->
    <view class="login-status" wx:if="{{loginStep > 0}}">
      <!-- 头像授权状态 -->
      <view class="status-item {{loginStep >= 1 ? 'completed' : ''}}">
        <text class="status-text">已成功授权头像</text>
      </view>

      <!-- 昵称设置状态 -->
      <view class="status-item {{loginStep >= 2 ? 'completed' : ''}}" wx:if="{{loginStep >= 1}}">
        <text class="status-text">已成功设置昵称: {{nickname}}</text>
      </view>
    </view>

    <!-- 登录按钮组 -->
    <view class="login-buttons">
      <!-- 微信授权头像按钮 -->
      <button class="login-btn primary {{loginStep >= 1 ? 'completed' : ''}}"
              open-type="chooseAvatar"
              bind:chooseavatar="onChooseAvatar"
              wx:if="{{loginStep < 1}}">
        <view class="btn-icon">👤</view>
        <text class="btn-text">授权获取头像</text>
      </button>

      <!-- 已授权头像按钮 -->
      <button class="login-btn completed" wx:if="{{loginStep >= 1 && loginStep < 2}}">
        <image class="avatar-preview" src="{{avatarUrl}}" mode="aspectFill"></image>
        <text class="btn-text">已授权头像</text>
      </button>

      <!-- 设置昵称按钮 -->
      <button class="login-btn secondary {{loginStep >= 2 ? 'completed' : ''}}"
              bindtap="showNicknameInput"
              wx:if="{{loginStep >= 1 && loginStep < 2}}">
        <view class="btn-icon">🛒</view>
        <text class="btn-text">点击获取昵称</text>
      </button>

      <!-- 已设置昵称按钮 -->
      <button class="login-btn completed" wx:if="{{loginStep >= 2}}">
        <view class="btn-icon">✓</view>
        <text class="btn-text">已设置昵称</text>
      </button>

      <!-- 微信登录按钮 -->
      <button class="login-btn wechat" bindtap="wechatLogin" wx:if="{{loginStep >= 2}}">
        <view class="btn-icon">👤</view>
        <text class="btn-text">微信登录</text>
      </button>
    </view>

    <!-- 用户头像显示 -->
    <view class="user-avatar" wx:if="{{loginStep >= 1}}">
      <image class="avatar-image" src="{{avatarUrl}}" mode="aspectFill"></image>
    </view>
  </view>

  <!-- 底部功能区 -->
  <view class="bottom-section">
    <!-- 协议条款 -->
    <view class="agreement-section">
      <view class="agreement-checkbox" bindtap="toggleAgreement">
        <view class="checkbox {{agreeProtocol ? 'checked' : ''}}">
          <text class="check-icon" wx:if="{{agreeProtocol}}">✓</text>
        </view>
        <text class="agreement-text">
          登录即表示同意
          <text class="agreement-link" bindtap="viewUserAgreement">《用户协议》</text>
          和
          <text class="agreement-link" bindtap="viewPrivacyPolicy">《隐私政策》</text>
        </text>
      </view>
    </view>

    <!-- 安全保障 -->
    <view class="security-features">
      <view class="feature-item">
        <view class="feature-icon">✓</view>
        <text class="feature-text">安全可靠</text>
      </view>
      <view class="feature-item">
        <view class="feature-icon">🔒</view>
        <text class="feature-text">隐私保护</text>
      </view>
      <view class="feature-item">
        <view class="feature-icon">👤</view>
        <text class="feature-text">优质服务</text>
      </view>
    </view>

    <!-- 访客模式 -->
    <view class="visitor-mode">
      <text class="visitor-text" bindtap="visitorLogin">访客模式</text>
    </view>
  </view>
</view>

<!-- 昵称输入弹窗 -->
<view class="nickname-modal" wx:if="{{showNicknameModal}}" bindtap="hideNicknameModal">
  <view class="nickname-content" catchtap="stopPropagation">
    <view class="nickname-header">
      <text class="nickname-title">设置昵称</text>
      <view class="nickname-close" bindtap="hideNicknameModal">×</view>
    </view>
    <view class="nickname-body">
      <input class="nickname-input"
             type="nickname"
             placeholder="请输入昵称"
             value="{{nickname}}"
             bindinput="onNicknameInput"
             bindconfirm="confirmNickname"
             maxlength="20"
             focus="{{showNicknameModal}}" />
    </view>
    <view class="nickname-footer">
      <button class="nickname-cancel-btn" bindtap="hideNicknameModal">取消</button>
      <button class="nickname-confirm-btn {{nickname ? '' : 'disabled'}}"
              bindtap="confirmNickname"
              disabled="{{!nickname}}">确定</button>
    </view>
  </view>
</view>

<!-- 协议弹窗 -->
<view class="agreement-modal" wx:if="{{showAgreementModal}}" bindtap="hideAgreementModal">
  <view class="agreement-content" catchtap="stopPropagation">
    <view class="agreement-header">
      <text class="agreement-title">{{agreementTitle}}</text>
      <view class="agreement-close" bindtap="hideAgreementModal">×</view>
    </view>
    <scroll-view class="agreement-body" scroll-y="{{true}}">
      <text class="agreement-detail">{{agreementContent}}</text>
    </scroll-view>
    <view class="agreement-footer">
      <button class="agreement-confirm-btn" bindtap="confirmAgreement">我已阅读</button>
    </view>
  </view>
</view>

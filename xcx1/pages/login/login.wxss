/* 登录页面样式 - 现代化设计 */

.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  background: transparent;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  position: relative;
}

.nav-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 36rpx;
  font-weight: 600;
  color: #1e293b;
}

.nav-menu {
  font-size: 48rpx;
  color: #64748b;
  padding: 8rpx;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  padding: 60rpx 0 80rpx;
  background: white;
  margin: 0 32rpx 40rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.logo-container {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 24rpx;
  background: white;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.company-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
}

.company-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8rpx;
}

.app-name {
  display: block;
  font-size: 28rpx;
  color: #64748b;
  margin-bottom: 16rpx;
}

.title-underline {
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  margin: 0 auto;
  border-radius: 2rpx;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 0 32rpx;
}

/* 欢迎区域 */
.welcome-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.welcome-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 16rpx;
}

.welcome-desc {
  display: block;
  font-size: 28rpx;
  color: #64748b;
}

/* 登录状态 */
.login-status {
  margin-bottom: 40rpx;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #f1f5f9;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  border-left: 4rpx solid #e2e8f0;
  transition: all 0.3s ease;
}

.status-item.completed {
  background: #dcfce7;
  border-left-color: #22c55e;
}

.status-text {
  font-size: 26rpx;
  color: #475569;
}

.status-item.completed .status-text {
  color: #166534;
}

/* 登录按钮组 */
.login-buttons {
  margin-bottom: 60rpx;
}

.login-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 32rpx 24rpx;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-btn::after {
  border: none;
}

.login-btn.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);
}

.login-btn.secondary {
  background: linear-gradient(135deg, #ec4899, #be185d);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(236, 72, 153, 0.3);
}

.login-btn.wechat {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(34, 197, 94, 0.3);
}

.login-btn.completed {
  background: #f8fafc;
  color: #22c55e;
  border: 2rpx solid #22c55e;
  box-shadow: 0 4rpx 16rpx rgba(34, 197, 94, 0.1);
}

.login-btn:active {
  transform: translateY(2rpx);
}

.btn-icon {
  font-size: 36rpx;
}

.btn-text {
  font-size: 32rpx;
}

.avatar-preview {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
}

/* 用户头像显示 */
.user-avatar {
  text-align: center;
  margin-bottom: 40rpx;
}

.avatar-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid #22c55e;
  box-shadow: 0 4rpx 16rpx rgba(34, 197, 94, 0.2);
}

/* 底部功能区 */
.bottom-section {
  margin-top: auto;
  padding: 40rpx 32rpx 20rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 32rpx 32rpx 0 0;
  position: relative;
}

.bottom-section::before {
  content: '';
  position: absolute;
  top: 16rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: #cbd5e1;
  border-radius: 3rpx;
}

.security-features {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
  padding: 40rpx 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.security-features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, #22c55e, #3b82f6, #8b5cf6);
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  position: relative;
}

.feature-icon {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 16rpx rgba(34, 197, 94, 0.3);
  transition: all 0.3s ease;
}

.feature-item:nth-child(2) .feature-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.3);
}

.feature-item:nth-child(3) .feature-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  box-shadow: 0 4rpx 16rpx rgba(139, 92, 246, 0.3);
}

.feature-text {
  font-size: 26rpx;
  color: #475569;
  font-weight: 600;
  text-align: center;
}

.visitor-mode {
  text-align: center;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.visitor-text {
  font-size: 30rpx;
  color: #64748b;
  text-decoration: underline;
  padding: 12rpx 24rpx;
  border-radius: 16rpx;
  background: rgba(100, 116, 139, 0.1);
  display: inline-block;
  transition: all 0.3s ease;
}

.visitor-text:active {
  background: rgba(100, 116, 139, 0.2);
  transform: scale(0.98);
}

/* 协议区域 */
.agreement-section {
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  margin: 0 32rpx 32rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  justify-content: center;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 3rpx solid #cbd5e1;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: white;
  transition: all 0.3s ease;
  margin-top: 2rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.checkbox.checked {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-color: #22c55e;
  transform: scale(1.1);
  box-shadow: 0 4rpx 16rpx rgba(34, 197, 94, 0.3);
}

.check-icon {
  color: white;
  font-size: 22rpx;
  font-weight: bold;
}

.agreement-text {
  font-size: 26rpx;
  color: #475569;
  line-height: 1.5;
  text-align: center;
  flex: 1;
}

.agreement-link {
  color: #3b82f6;
  text-decoration: underline;
  font-weight: 600;
  padding: 2rpx 4rpx;
  border-radius: 4rpx;
  transition: all 0.3s ease;
}

.agreement-link:active {
  background: rgba(59, 130, 246, 0.1);
}



/* 昵称输入弹窗 */
.nickname-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
}

.nickname-content {
  width: 100%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
}

.nickname-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f1f5f9;
}

.nickname-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
}

.nickname-close {
  font-size: 48rpx;
  color: #64748b;
  padding: 8rpx;
}

.nickname-body {
  padding: 32rpx;
}

.nickname-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  box-sizing: border-box;
  background: #f8fafc;
  color: #1e293b;
  transition: all 0.3s ease;
}

.nickname-input:focus {
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.1);
}

.nickname-input::placeholder {
  color: #94a3b8;
}

.nickname-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 2rpx solid #f1f5f9;
}

.nickname-cancel-btn {
  flex: 1;
  background: #f8fafc;
  color: #64748b;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
}

.nickname-confirm-btn {
  flex: 1;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
}

.nickname-confirm-btn.disabled {
  background: #cbd5e1;
  color: #94a3b8;
}

/* 协议弹窗 */
.agreement-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
}

.agreement-content {
  width: 100%;
  max-height: 80vh;
  background: white;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
}

.agreement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f1f5f9;
}

.agreement-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
}

.agreement-close {
  font-size: 48rpx;
  color: #64748b;
  padding: 8rpx;
}

.agreement-body {
  flex: 1;
  padding: 32rpx;
  max-height: 60vh;
}

.agreement-detail {
  font-size: 26rpx;
  color: #64748b;
  line-height: 1.8;
  white-space: pre-line;
}

.agreement-footer {
  padding: 32rpx;
  border-top: 2rpx solid #f1f5f9;
}

.agreement-confirm-btn {
  width: 100%;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
}

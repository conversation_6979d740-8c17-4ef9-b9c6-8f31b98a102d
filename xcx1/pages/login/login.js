// 登录页面
const app = getApp()
const api = require('../../api/index.js')
const { BASE_URL } = require('../../api/request')

Page({
  data: {
    // 系统信息
    statusBarHeight: 0,

    // 登录状态
    loginStep: 0, // 0: 初始状态, 1: 已授权头像, 2: 已设置昵称
    agreeProtocol: false, // 默认不同意协议，需要用户手动勾选
    isLoading: false,

    // 用户信息
    avatarUrl: '/images/avatar-default.png',
    nickname: '',
    hasAvatar: false,

    // 弹窗控制
    showNicknameModal: false,
    showAgreementModal: false,
    agreementTitle: '',
    agreementContent: '',

    // 页面状态
    redirectUrl: ''
  },

  // 页面加载
  onLoad(options) {
    console.log('🚀 登录页面加载', options)

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    })

    // 保存重定向URL
    if (options.redirect) {
      const redirectUrl = decodeURIComponent(options.redirect)
      console.log('📍 接收到redirect参数:', options.redirect)
      console.log('📍 解码后的redirect URL:', redirectUrl)
      this.setData({ redirectUrl: redirectUrl })
    } else {
      console.log('📍 没有接收到redirect参数')
    }

    // 检查是否已登录
    this.checkLoginStatus()
  },

  // 页面显示
  onShow() {
    console.log('🔄 登录页面显示')
  },

  // 页面卸载
  onUnload() {
    // 清理定时器
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer)
    }
  },



  // 检查登录状态
  checkLoginStatus() {
    const app = getApp()
    const userInfo = wx.getStorageSync('userInfo')
    const token = wx.getStorageSync('token')

    if (userInfo && token && app.globalData.isLogin) {
      // 已登录，直接跳转
      this.redirectAfterLogin()
    }
  },

  // 显示菜单
  showMenu() {
    wx.showActionSheet({
      itemList: ['关于我们', '联系客服', '用户协议', '隐私政策'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.showAbout()
            break
          case 1:
            this.contactService()
            break
          case 2:
            this.viewUserAgreement()
            break
          case 3:
            this.viewPrivacyPolicy()
            break
        }
      }
    })
  },

  // 显示关于我们
  showAbout() {
    wx.showModal({
      title: '关于我们',
      content: '两山茶管家\n专业的茶园认购投资平台\n为您提供优质的茶园投资服务',
      showCancel: false
    })
  },

  // 新版头像授权
  onChooseAvatar(e) {
    console.log('🖼️ 选择头像:', e)
    const { avatarUrl } = e.detail

    if (avatarUrl) {
      this.setData({
        avatarUrl: avatarUrl,
        hasAvatar: true,
        loginStep: 1
      })

      console.log('✅ 头像设置成功，loginStep:', 1)

      // 将头像转换为Base64
      this.convertAvatarToBase64(avatarUrl)

      wx.showToast({
        title: '头像授权成功',
        icon: 'success'
      })
    }
  },

  // 将头像转换为Base64
  convertAvatarToBase64(filePath) {
    const fileSystemManager = wx.getFileSystemManager()
    try {
      const base64 = fileSystemManager.readFileSync(filePath, 'base64')
      this.setData({
        tempBase64Avatar: `data:image/png;base64,${base64}`
      })
      console.log('头像转换为Base64成功')
    } catch (error) {
      console.error('头像转换失败:', error)
    }
  },

  // 显示昵称输入弹窗
  showNicknameInput() {
    if (!this.data.hasAvatar) {
      wx.showToast({
        title: '请先获取头像',
        icon: 'none'
      })
      return
    }

    this.setData({
      showNicknameModal: true
    })
  },

  // 隐藏昵称输入弹窗
  hideNicknameModal() {
    this.setData({
      showNicknameModal: false
    })
  },

  // 确认昵称
  confirmNickname() {
    const nickname = this.data.nickname.trim()
    if (!nickname) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    this.setData({
      loginStep: 2,
      showNicknameModal: false
    })

    wx.showToast({
      title: '昵称设置成功',
      icon: 'success'
    })
  },

  // 昵称输入
  onNicknameInput(e) {
    const nickname = e.detail.value
    this.setData({ nickname: nickname })
  },

  // 微信登录
  wechatLogin() {
    if (!this.data.hasAvatar) {
      wx.showToast({
        title: '请先授权头像',
        icon: 'none'
      })
      return
    }

    if (!this.data.nickname.trim()) {
      wx.showToast({
        title: '请先设置昵称',
        icon: 'none'
      })
      return
    }

    if (!this.data.agreeProtocol) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    this.performWechatLogin()
  },

  // 执行微信登录
  performWechatLogin() {
    wx.showLoading({
      title: '登录中...',
      mask: true
    })

    // 获取微信登录code
    wx.login({
      success: (res) => {
        if (res.code) {
          this.sendLoginRequest(res.code)
        } else {
          wx.hideLoading()
          wx.showToast({
            title: '获取登录凭证失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.hideLoading()
        wx.showToast({
          title: '微信登录失败',
          icon: 'none'
        })
      }
    })
  },

  // 发送登录请求
  sendLoginRequest(code) {
    // 按照后端API期望的格式传递用户信息
    const userInfo = {
      nickName: this.data.nickname.trim(),
      avatarUrl: this.data.avatarUrl,
      gender: 0,
      city: '',
      province: '',
      country: '中国',
      language: 'zh_CN'
    }

    const loginData = {
      code: code,
      userInfo: userInfo  // 使用userInfo格式
    }

    console.log('🔄 发送登录请求，用户信息:', userInfo)

    api.userApi.wechatLogin(loginData).then(result => {
      wx.hideLoading()
      console.log('登录API响应:', result)

      if (result.code === 200 || result.success) {
        // 登录成功，合并用户信息和微信信息
        const mergedUserInfo = {
          ...(result.data.user || result.data || {}),
          // 优先使用微信信息中的昵称和头像
          nickname: result.data.wechat_info?.nickname || (result.data.user || result.data)?.nickname || this.data.nickname,
          avatar: result.data.wechat_info?.avatar_url || (result.data.user || result.data)?.avatar || this.data.avatarUrl,
          wechat_info: result.data.wechat_info
        }

        const token = result.token || result.data?.token || ''

        console.log('🔄 sendLoginRequest 合并后的用户信息:', mergedUserInfo)

        // 使用app.login方法统一处理登录状态
        const app = getApp()
        app.login(mergedUserInfo, token)

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 跳转到指定页面或首页
        setTimeout(() => {
          this.redirectAfterLogin()
        }, 800)
      } else {
        wx.showToast({
          title: result.message || result.msg || '登录失败',
          icon: 'none'
        })
      }
    }).catch(error => {
      wx.hideLoading()
      console.error('登录失败:', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    })
  },

  // 登录后跳转
  redirectAfterLogin() {
    if (this.data.redirectUrl) {
      console.log('🔄 登录成功，准备跳转到:', this.data.redirectUrl)

      // 由于使用自定义底部导航，所有页面都使用 redirectTo
      // 自定义底部导航页面（主要页面）
      const customTabBarPages = [
        '/pages/index/index',
        '/pages/tea-list/tea-list',
        '/pages/monitoring/monitoring',
        '/pages/analytics/analytics',
        '/pages/profile/profile'
      ]

      const isCustomTabBarPage = customTabBarPages.includes(this.data.redirectUrl)

      if (isCustomTabBarPage) {
        console.log('🎯 跳转到自定义导航页面:', this.data.redirectUrl)
        wx.redirectTo({
          url: this.data.redirectUrl,
          success: () => {
            console.log('✅ 自定义导航页面跳转成功')
          },
          fail: (error) => {
            console.error('❌ 自定义导航页面跳转失败:', error)
            // 如果跳转失败，回到首页
            wx.redirectTo({
              url: '/pages/index/index'
            })
          }
        })
      } else {
        console.log('🎯 跳转到普通页面:', this.data.redirectUrl)
        wx.redirectTo({
          url: this.data.redirectUrl,
          success: () => {
            console.log('✅ 普通页面跳转成功')
          },
          fail: (error) => {
            console.error('❌ 普通页面跳转失败:', error)
            // 如果跳转失败，回到首页
            wx.redirectTo({
              url: '/pages/index/index'
            })
          }
        })
      }
    } else {
      console.log('🏠 没有redirect参数，跳转到首页')
      wx.redirectTo({
        url: '/pages/index/index'
      })
    }
  },

  // 跳过登录（符合微信审核要求）
  skipLogin() {
    console.log('👤 用户选择跳过登录')

    // 设置访客标识
    wx.setStorageSync('isVisitor', true)
    wx.setStorageSync('skipLoginTime', Date.now())

    // 显示友好提示
    wx.showToast({
      title: '进入体验模式',
      icon: 'success',
      duration: 1500
    })

    // 延迟跳转，让用户看到提示
    setTimeout(() => {
      // 如果有重定向URL，跳转到指定页面
      if (this.data.redirectUrl) {
        console.log('📍 跳转到重定向页面:', this.data.redirectUrl)
        wx.navigateTo({
          url: this.data.redirectUrl,
          fail: () => {
            // 如果跳转失败，回到首页
            wx.switchTab({
              url: '/pages/index/index'
            })
          }
        })
      } else {
        // 跳转到首页
        wx.switchTab({
          url: '/pages/index/index'
        })
      }
    }, 1500)
  },

  // 切换协议同意状态
  toggleAgreement() {
    this.setData({
      agreeProtocol: !this.data.agreeProtocol
    })
  },

  // 查看用户协议
  viewUserAgreement() {
    this.setData({
      showAgreementModal: true,
      agreementTitle: '用户协议',
      agreementContent: `欢迎使用两山茶管家！

1. 服务条款
本协议是您与两山茶管家之间关于使用本服务的法律协议。

2. 用户权利与义务
- 用户有权享受本平台提供的各项服务
- 用户应遵守相关法律法规，不得从事违法活动
- 用户应保护好自己的账户信息

3. 隐私保护
我们承诺保护用户隐私，不会泄露用户个人信息。

4. 服务变更
我们保留随时修改或中断服务的权利。

5. 免责声明
在法律允许的范围内，我们不承担因使用本服务而产生的任何损失。

如有疑问，请联系客服。`
    })
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    this.setData({
      showAgreementModal: true,
      agreementTitle: '隐私政策',
      agreementContent: `两山茶管家隐私政策

我们非常重视您的隐私保护，本政策说明我们如何收集、使用和保护您的信息。

1. 信息收集
- 基本信息：昵称、头像等
- 使用信息：操作记录、偏好设置等
- 设备信息：设备型号、系统版本等

2. 信息使用
- 提供和改善服务
- 个性化推荐
- 安全保障

3. 信息保护
- 采用加密技术保护数据传输
- 严格限制访问权限
- 定期安全审计

4. 信息共享
除法律要求外，我们不会与第三方分享您的个人信息。

5. 用户权利
您有权查看、修改或删除个人信息。

更新日期：2024年1月1日`
    })
  },

  // 隐藏协议弹窗
  hideAgreementModal() {
    this.setData({
      showAgreementModal: false
    })
  },

  // 确认协议
  confirmAgreement() {
    this.setData({
      showAgreementModal: false,
      agreeProtocol: true
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空方法，用于阻止事件冒泡
  },

  // 联系客服
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00\n或者您可以在小程序内留言',
      showCancel: false
    })
  },

  // 新版微信登录（保留原有方法以防兼容性问题）
  async startLogin() {
    console.log('开始登录，当前状态:', {
      agreeProtocol: this.data.agreeProtocol,
      hasAvatar: this.data.hasAvatar,
      nickname: this.data.nickname,
      isLoading: this.data.isLoading
    })

    if (!this.data.agreeProtocol) {
      wx.showToast({
        title: '请先同意协议',
        icon: 'none'
      })
      return
    }

    if (!this.data.hasAvatar) {
      wx.showToast({
        title: '请先获取头像',
        icon: 'none'
      })
      return
    }

    if (!this.data.nickname || this.data.nickname.trim() === '') {
      wx.showToast({
        title: '请先设置昵称',
        icon: 'none'
      })
      return
    }

    this.setData({ isLoading: true })

    try {
      // 获取微信登录code
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        })
      })

      if (!loginRes.code) {
        throw new Error('获取登录凭证失败')
      }

      // 准备用户信息（按照后端API期望的格式）
      const userInfo = {
        nickName: this.data.nickname,
        avatarUrl: this.data.avatarUrl,
        gender: 0,
        city: '',
        province: '',
        country: '中国',
        language: 'zh_CN'
      }

      console.log('准备登录，用户信息:', {
        nickName: userInfo.nickName,
        hasAvatar: !!userInfo.avatarUrl,
        hasBase64Avatar: !!userInfo.tempBase64Avatar
      })

      // 调用后端登录接口
      const res = await api.userApi.wechatLogin({
        code: loginRes.code,
        userInfo: userInfo
      })

      if (res.code === 200) {
        // 登录成功
        console.log('🎉 登录接口返回数据:', res.data)

        // 合并用户信息和微信信息
        const mergedUserInfo = {
          ...(res.data.user || res.data || {}),
          // 优先使用微信信息中的昵称和头像
          nickname: res.data.wechat_info?.nickname || (res.data.user || res.data)?.nickname || this.data.nickname,
          avatar: res.data.wechat_info?.avatar_url || (res.data.user || res.data)?.avatar || this.data.avatarUrl,
          wechat_info: res.data.wechat_info
        }

        const token = res.data.token || res.token || ''

        console.log('🔄 合并后的用户信息:', mergedUserInfo)

        if (token) {
          wx.setStorageSync('token', token)
        }

        if (mergedUserInfo) {
          wx.setStorageSync('userInfo', mergedUserInfo)
          // 更新全局状态
          const app = getApp()
          app.globalData.userInfo = mergedUserInfo
          app.globalData.isLogin = true
        }

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        setTimeout(() => {
          this.redirectAfterLogin()
        }, 800)
      } else {
        throw new Error(res.message || '登录失败')
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
    } finally {
      this.setData({ isLoading: false })
    }
  },

  // 切换协议同意状态
  toggleAgreement() {
    const newAgreeState = !this.data.agreeProtocol
    this.setData({
      agreeProtocol: newAgreeState,
      agreedToTerms: newAgreeState // 保持兼容性
    })

    console.log('📋 协议状态切换:', newAgreeState)
  },

  // 访客模式（保持兼容性）
  visitorLogin() {
    this.skipLogin()
  },



  // 微信授权登录 (新版本)
  onGetUserProfile(e) {
    if (!this.data.agreedToTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    if (e.detail.userInfo) {
      this.wxLoginWithUserInfo(e.detail.userInfo)
    } else {
      wx.showToast({
        title: '授权失败',
        icon: 'none'
      })
    }
  },

  // 微信快速登录 (旧版本兼容)
  wxLogin() {
    if (!this.data.agreedToTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    wx.login({
      success: (res) => {
        if (res.code) {
          this.wxLoginWithCode(res.code)
        } else {
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          })
        }
      }
    })
  },

  // 使用用户信息登录
  wxLoginWithUserInfo(userInfo) {
    wx.showLoading({
      title: '登录中...'
    })

    // 获取登录凭证
    wx.login({
      success: (loginRes) => {
        if (!loginRes.code) {
          wx.hideLoading()
          wx.showToast({
            title: '获取登录凭证失败',
            icon: 'none'
          })
          return
        }

        // 调用后端登录接口
        api.userApi.wechatLogin({
          code: loginRes.code,
          userInfo: userInfo
        }).then(res => {
          if (res.code === 200) {
            // 登录成功，合并用户信息和微信信息
            const mergedUserInfo = {
              ...(res.data.user || res.data || {}),
              // 优先使用微信信息中的昵称和头像
              nickname: res.data.wechat_info?.nickname || (res.data.user || res.data)?.nickname || this.data.nickname,
              avatar: res.data.wechat_info?.avatar_url || (res.data.user || res.data)?.avatar || this.data.avatarUrl,
              wechat_info: res.data.wechat_info
            }

            console.log('🔄 wxLoginWithUserInfo 合并后的用户信息:', mergedUserInfo)

            app.login(mergedUserInfo, res.data.token)

            wx.showToast({
              title: '登录成功',
              icon: 'success'
            })

            setTimeout(() => {
              this.redirectAfterLogin()
            }, 800)
          } else {
            throw new Error(res.message || '登录失败')
          }
        }).catch(error => {
          console.error('微信登录失败:', error)
          wx.showToast({
            title: error.message || '登录失败',
            icon: 'none'
          })
        }).finally(() => {
          wx.hideLoading()
        })
      },
      fail: (error) => {
        wx.hideLoading()
        console.error('微信登录失败:', error)
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
      }
    })
  },

  // 使用code登录
  async wxLoginWithCode(code) {
    try {
      wx.showLoading({
        title: '登录中...'
      })

      // 准备用户信息
      const userInfo = {
        nickName: this.data.nickname || '',
        avatarUrl: this.data.avatarUrl || '',
        gender: 0,
        city: '',
        province: '',
        country: '中国',
        language: 'zh_CN'
      }

      console.log('🔄 发送登录请求，用户信息:', userInfo)

      const res = await api.userApi.wechatLogin({
        code: code,
        userInfo: userInfo  // 传递用户信息
      })

      if (res.code === 200) {
        // 合并用户信息和微信信息
        const userInfo = {
          ...res.data.user,
          // 优先使用微信信息中的昵称和头像
          nickname: res.data.wechat_info?.nickname || res.data.user.nickname || this.data.nickname,
          avatar: res.data.wechat_info?.avatar_url || res.data.user.avatar || this.data.avatarUrl,
          wechat_info: res.data.wechat_info
        }

        console.log('🔄 合并后的用户信息:', userInfo)

        app.login(userInfo, res.data.token)

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        setTimeout(() => {
          this.redirectAfterLogin()
        }, 800)
      } else {
        throw new Error(res.message || '登录失败')
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 手机号输入
  onPhoneInput(e) {
    const phoneNumber = e.detail.value
    this.setData({ 
      phoneNumber,
      canSendCode: this.validatePhone(phoneNumber)
    })
    this.checkCanLogin()
  },

  // 验证码输入
  onCodeInput(e) {
    const verifyCode = e.detail.value
    this.setData({ verifyCode })
    this.checkCanLogin()
  },

  // 验证手机号
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },

  // 检查是否可以登录（手机号登录专用）
  checkCanLogin() {
    // 只在手机号登录模式下才检查
    if (!this.data.showPhoneLogin) {
      return
    }

    const { phoneNumber, verifyCode } = this.data
    const canLogin = this.validatePhone(phoneNumber) && verifyCode.length === 6
    this.setData({ canLogin })
  },

  // 发送验证码
  async sendVerifyCode() {
    if (!this.data.canSendCode || this.data.countdown > 0) {
      return
    }

    try {
      const res = await api.userApi.sendSms({
        phone: this.data.phoneNumber
      })

      if (res.code === 200) {
        wx.showToast({
          title: '验证码已发送',
          icon: 'success'
        })

        // 开始倒计时
        this.startCountdown()
      } else {
        throw new Error(res.message || '发送失败')
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      wx.showToast({
        title: error.message || '发送失败',
        icon: 'none'
      })
    }
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60
    this.setData({ 
      countdown,
      codeButtonText: `${countdown}s后重发`
    })

    const timer = setInterval(() => {
      countdown--
      if (countdown <= 0) {
        clearInterval(timer)
        this.setData({
          countdown: 0,
          codeButtonText: '获取验证码',
          countdownTimer: null
        })
      } else {
        this.setData({
          countdown,
          codeButtonText: `${countdown}s后重发`
        })
      }
    }, 1000)

    this.setData({ countdownTimer: timer })
  },

  // 手机号登录
  async phoneLogin() {
    if (!this.data.canLogin) {
      return
    }

    if (!this.data.agreedToTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    this.setData({ loginLoading: true })

    try {
      const res = await api.userApi.phoneLogin({
        phone: this.data.phoneNumber,
        code: this.data.verifyCode
      })

      if (res.code === 200) {
        app.login(res.data.user, res.data.token)
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        setTimeout(() => {
          this.redirectAfterLogin()
        }, 800)
      } else {
        throw new Error(res.message || '登录失败')
      }
    } catch (error) {
      console.error('手机号登录失败:', error)
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loginLoading: false })
    }
  },





  // 查看用户协议
  viewUserAgreement() {
    this.setData({
      showAgreementModal: true,
      agreementTitle: '用户协议',
      agreementContent: `欢迎使用万博科技礼品卡商城！

1. 服务条款
本协议是您与万博科技礼品卡商城之间关于使用本服务的法律协议。

2. 用户权利与义务
- 用户有权享受本平台提供的各项服务
- 用户应遵守相关法律法规，不得从事违法活动
- 用户应保护好自己的账户信息

3. 隐私保护
我们承诺保护用户隐私，不会泄露用户个人信息。

4. 服务变更
我们保留随时修改或中断服务的权利。

5. 免责声明
在法律允许的范围内，我们不承担因使用本服务而产生的任何损失。

如有疑问，请联系客服。`
    })
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    this.setData({
      showAgreementModal: true,
      agreementTitle: '隐私政策',
      agreementContent: `万博科技礼品卡商城隐私政策

我们非常重视您的隐私保护，本政策说明我们如何收集、使用和保护您的信息。

1. 信息收集
- 基本信息：昵称、头像等
- 使用信息：操作记录、偏好设置等
- 设备信息：设备型号、系统版本等

2. 信息使用
- 提供和改善服务
- 个性化推荐
- 安全保障

3. 信息保护
- 采用加密技术保护数据传输
- 严格限制访问权限
- 定期安全审计

4. 信息共享
除法律要求外，我们不会与第三方分享您的个人信息。

5. 用户权利
您有权查看、修改或删除个人信息。

更新日期：2024年1月1日`
    })
  },

  // 隐藏协议弹窗
  hideAgreementModal() {
    this.setData({ showAgreementModal: false })
  },

  // 确认协议
  confirmAgreement() {
    this.setData({
      showAgreementModal: false,
      agreedToTerms: true
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击协议内容时关闭弹窗
  },

  // 快速体验
  quickExperience() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 联系客服
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00\n或者您可以在小程序内留言',
      showCancel: false
    })
  }
})

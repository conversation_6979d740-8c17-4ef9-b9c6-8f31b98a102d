// 登录页面
const app = getApp()
const api = require('../../api/index.js')
const { BASE_URL } = require('../../api/request')

Page({
  data: {
    // 系统信息
    statusBarHeight: 0,

    // 登录状态
    loginStep: 0, // 0: 初始状态, 1: 已授权头像, 2: 已设置昵称
    agreeProtocol: false, // 默认不同意协议，需要用户手动勾选
    isLoading: false,

    // 用户信息
    avatarUrl: '/images/avatar-default.png',
    nickname: '',
    hasAvatar: false,

    // 弹窗控制
    showNicknameModal: false,
    showAgreementModal: false,
    agreementTitle: '',
    agreementContent: '',

    // 页面状态
    redirectUrl: ''
  },

  // 页面加载
  onLoad(options) {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    })

    // 保存重定向URL
    if (options.redirect) {
      const redirectUrl = decodeURIComponent(options.redirect)
      // Debug log removed
      this.setData({ redirectUrl: redirectUrl })
    } else {
    }

    // 检查是否已登录
    this.checkLoginStatus()
  },

  // 页面显示
  onShow() {
  },

  // 页面卸载
  onUnload() {
    // 清理定时器
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer)
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const app = getApp()
    const userInfo = wx.getStorageSync('userInfo')
    const token = wx.getStorageSync('token')

    if (userInfo && token && app.globalData.isLogin) {
      // 已登录，直接跳转
      this.redirectAfterLogin()
    }
  },

  // 显示菜单
  showMenu() {
    wx.showActionSheet({
      itemList: ['关于我们', '联系客服', '用户协议', '隐私政策'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.showAbout()
            break
          case 1:
            this.contactService()
            break
          case 2:
            this.viewUserAgreement()
            break
          case 3:
            this.viewPrivacyPolicy()
            break
        }
      }
    })
  },

  // 显示关于我们
  showAbout() {
    wx.showModal({
      title: '关于我们',
      content: '两山茶管家\n专业的茶园认购投资平台\n为您提供优质的茶园投资服务',
      showCancel: false
    })
  },

  // 新版头像授权
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail

    if (avatarUrl) {
      this.setData({
        avatarUrl: avatarUrl,
        hasAvatar: true,
        loginStep: 1
      })
      // 将头像转换为Base64
      this.convertAvatarToBase64(avatarUrl)

      wx.showToast({
        title: '头像授权成功',
        icon: 'success'
      })
    }
  },

  // 将头像转换为Base64
  convertAvatarToBase64(filePath) {
    const fileSystemManager = wx.getFileSystemManager()
    try {
      const base64 = fileSystemManager.readFileSync(filePath, 'base64')
      this.setData({
        tempBase64Avatar: `data:image/png;base64,${base64}`
      })
    } catch (error) {
      console.error('头像转换失败:', error)
    }
  },

  // 显示昵称输入弹窗
  showNicknameInput() {
    if (!this.data.hasAvatar) {
      wx.showToast({
        title: '请先获取头像',
        icon: 'none'
      })
      return
    }

    this.setData({
      showNicknameModal: true
    })
  },

  // 隐藏昵称输入弹窗
  hideNicknameModal() {
    this.setData({
      showNicknameModal: false
    })
  },

  // 确认昵称
  confirmNickname() {
    const nickname = this.data.nickname.trim()
    if (!nickname) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    this.setData({
      loginStep: 2,
      showNicknameModal: false
    })

    wx.showToast({
      title: '昵称设置成功',
      icon: 'success'
    })
  },

  // 昵称输入
  onNicknameInput(e) {
    const nickname = e.detail.value
    this.setData({ nickname: nickname })
  },

  // 微信登录
  wechatLogin() {
    if (!this.data.hasAvatar) {
      wx.showToast({
        title: '请先授权头像',
        icon: 'none'
      })
      return
    }

    if (!this.data.nickname.trim()) {
      wx.showToast({
        title: '请先设置昵称',
        icon: 'none'
      })
      return
    }

    if (!this.data.agreeProtocol) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    this.performWechatLogin()
  },

  // 执行微信登录
  performWechatLogin() {
    wx.showLoading({
      title: '登录中...',
      mask: true
    })

    // 获取微信登录code
    wx.login({
      success: (res) => {
        if (res.code) {
          this.sendLoginRequest(res.code)
        } else {
          wx.hideLoading()
          wx.showToast({
            title: '获取登录凭证失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.hideLoading()
        wx.showToast({
          title: '微信登录失败',
          icon: 'none'
        })
      }
    })
  },

  // 发送登录请求
  sendLoginRequest(code) {
    const loginData = {
      code: code,
      avatar: this.data.tempBase64Avatar || this.data.avatarUrl,
      nickname: this.data.nickname.trim()
    }

    api.userApi.wechatLogin(loginData).then(result => {
      wx.hideLoading()
      if (result.code === 200 || result.success) {
        // 保存用户信息和token
        const userInfo = result.data || result.user_info || {}
        const token = result.token || result.data?.token || ''

        if (token) {
          wx.setStorageSync('token', token)
        }

        if (userInfo) {
          wx.setStorageSync('userInfo', userInfo)
          // 更新全局状态
          const app = getApp()
          app.globalData.userInfo = userInfo
          app.globalData.isLogin = true
        }

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 跳转到指定页面或首页
        setTimeout(() => {
          this.redirectAfterLogin()
        }, 800)
      } else {
        wx.showToast({
          title: result.message || result.msg || '登录失败',
          icon: 'none'
        })
      }
    }).catch(error => {
      wx.hideLoading()
      console.error('登录失败:', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    })
  },

  // 登录后跳转
  redirectAfterLogin() {
    if (this.data.redirectUrl) {
      // 由于使用自定义底部导航，所有页面都使用 redirectTo
      // 自定义底部导航页面（主要页面）
      const customTabBarPages = [
        '/pages/index/index',
        '/pages/tea-list/tea-list',
        '/pages/monitoring/monitoring',
        '/pages/analytics/analytics',
        '/pages/profile/profile'
      ]

      const isCustomTabBarPage = customTabBarPages.includes(this.data.redirectUrl)

      if (isCustomTabBarPage) {
        wx.redirectTo({
          url: this.data.redirectUrl,
          success: () => {
          },
          fail: (error) => {
            console.error('❌ 自定义导航页面跳转失败:', error)
            // 如果跳转失败，回到首页
            wx.redirectTo({
              url: '/pages/index/index'
            })
          }
        })
      } else {
        wx.redirectTo({
          url: this.data.redirectUrl,
          success: () => {
          },
          fail: (error) => {
            console.error('❌ 普通页面跳转失败:', error)
            // 如果跳转失败，回到首页
            wx.redirectTo({
              url: '/pages/index/index'
            })
          }
        })
      }
    } else {
      wx.redirectTo({
        url: '/pages/index/index'
      })
    }
  },

  // 访客登录
  visitorLogin() {
    wx.showModal({
      title: '访客模式',
      content: '访客模式下功能有限，建议您登录以获得完整体验',
      confirmText: '继续访问',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 设置访客标识
          wx.setStorageSync('isVisitor', true)

          // 跳转到首页
          wx.switchTab({
            url: '/pages/index/index'
          })
        }
      }
    })
  },

  // 切换协议同意状态
  toggleAgreement() {
    this.setData({
      agreeProtocol: !this.data.agreeProtocol
    })
  },

  // 查看用户协议
  viewUserAgreement() {
    this.setData({
      showAgreementModal: true,
      agreementTitle: '用户协议',
      agreementContent: `欢迎使用两山茶管家！

1. 服务条款
本协议是您与两山茶管家之间关于使用本服务的法律协议。

2. 用户权利与义务
- 用户有权享受本平台提供的各项服务
- 用户应遵守相关法律法规，不得从事违法活动
- 用户应保护好自己的账户信息

3. 隐私保护
我们承诺保护用户隐私，不会泄露用户个人信息。

4. 服务变更
我们保留随时修改或中断服务的权利。

5. 免责声明
在法律允许的范围内，我们不承担因使用本服务而产生的任何损失。

如有疑问，请联系客服。`
    })
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    this.setData({
      showAgreementModal: true,
      agreementTitle: '隐私政策',
      agreementContent: `两山茶管家隐私政策

我们非常重视您的隐私保护，本政策说明我们如何收集、使用和保护您的信息。

1. 信息收集
- 基本信息：昵称、头像等
- 使用信息：操作记录、偏好设置等
- 设备信息：设备型号、系统版本等

2. 信息使用
- 提供和改善服务
- 个性化推荐
- 安全保障

3. 信息保护
- 采用加密技术保护数据传输
- 严格限制访问权限
- 定期安全审计

4. 信息共享
除法律要求外，我们不会与第三方分享您的个人信息。

5. 用户权利
您有权查看、修改或删除个人信息。

更新日期：2024年1月1日`
    })
  },

  // 隐藏协议弹窗
  hideAgreementModal() {
    this.setData({
      showAgreementModal: false
    })
  },

  // 确认协议
  confirmAgreement() {
    this.setData({
      showAgreementModal: false,
      agreeProtocol: true
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空方法，用于阻止事件冒泡
  },

  // 联系客服
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00\n或者您可以在小程序内留言',
      showCancel: false
    })
  },

  // 新版微信登录（保留原有方法以防兼容性问题）
  async startLogin() {
    // 检查协议同意状态
    if (!this.data.agreeProtocol) {
      wx.showToast({
        title: '请先同意协议',
        icon: 'none'
      })
      return
    }

    if (!this.data.hasAvatar) {
      wx.showToast({
        title: '请先获取头像',
        icon: 'none'
      })
      return
    }

    if (!this.data.nickname || this.data.nickname.trim() === '') {
      wx.showToast({
        title: '请先设置昵称',
        icon: 'none'
      })
      return
    }

    this.setData({ isLoading: true })

    try {
      // 获取微信登录code
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        })
      })

      if (!loginRes.code) {
        throw new Error('获取登录凭证失败')
      }

      // 准备用户信息
      const userInfo = {
        nickName: this.data.nickname,
        avatarUrl: this.data.avatarUrl,
        tempBase64Avatar: this.data.tempBase64Avatar,
        updateUserInfo: true // 标记需要更新用户信息
      }

      // 调用后端登录接口
      const res = await api.userApi.wechatLogin({
        code: loginRes.code,
        userInfo: userInfo
      })

      if (res.code === 200) {
        // 登录成功
        // 保存用户信息和token
        const userInfo = res.data.user || res.data || {}
        const token = res.data.token || res.token || ''

        if (token) {
          wx.setStorageSync('token', token)
        }

        if (userInfo) {
          wx.setStorageSync('userInfo', userInfo)
          // 更新全局状态
          const app = getApp()
          app.globalData.userInfo = userInfo
          app.globalData.isLogin = true
        }

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        setTimeout(() => {
          this.redirectAfterLogin()
        }, 800)
      } else {
        throw new Error(res.message || '登录失败')
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
    } finally {
      this.setData({ isLoading: false })
    }
  },

  // 切换协议同意状态
  toggleAgreement() {
    const newAgreeState = !this.data.agreeProtocol
    this.setData({
      agreeProtocol: newAgreeState,
      agreedToTerms: newAgreeState // 保持兼容性
    })
  },

  // 访客模式
  visitorLogin() {
    wx.showModal({
      title: '访客模式',
      content: '访客模式下功能有限，建议您登录以获得完整体验',
      confirmText: '继续访问',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 设置访客标识
          wx.setStorageSync('isVisitor', true)

          // 跳转到首页
          wx.switchTab({
            url: '/pages/index/index'
          })
        }
      }
    })
  },

  // 微信授权登录 (新版本)
  onGetUserProfile(e) {
    if (!this.data.agreedToTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    if (e.detail.userInfo) {
      this.wxLoginWithUserInfo(e.detail.userInfo)
    } else {
      wx.showToast({
        title: '授权失败',
        icon: 'none'
      })
    }
  },

  // 微信快速登录 (旧版本兼容)
  wxLogin() {
    if (!this.data.agreedToTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    wx.login({
      success: (res) => {
        if (res.code) {
          this.wxLoginWithCode(res.code)
        } else {
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          })
        }
      }
    })
  },

  // 使用用户信息登录
  wxLoginWithUserInfo(userInfo) {
    wx.showLoading({
      title: '登录中...'
    })

    // 获取登录凭证
    wx.login({
      success: (loginRes) => {
        if (!loginRes.code) {
          wx.hideLoading()
          wx.showToast({
            title: '获取登录凭证失败',
            icon: 'none'
          })
          return
        }

        // 调用后端登录接口
        api.userApi.wechatLogin({
          code: loginRes.code,
          userInfo: userInfo
        }).then(res => {
          if (res.code === 200) {
            // 登录成功
            app.login(res.data.user, res.data.token)

            wx.showToast({
              title: '登录成功',
              icon: 'success'
            })

            setTimeout(() => {
              this.redirectAfterLogin()
            }, 800)
          } else {
            throw new Error(res.message || '登录失败')
          }
        }).catch(error => {
          console.error('微信登录失败:', error)
          wx.showToast({
            title: error.message || '登录失败',
            icon: 'none'
          })
        }).finally(() => {
          wx.hideLoading()
        })
      },
      fail: (error) => {
        wx.hideLoading()
        console.error('微信登录失败:', error)
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
      }
    })
  },

  // 使用code登录
  async wxLoginWithCode(code) {
    try {
      wx.showLoading({
        title: '登录中...'
      })

      const res = await api.userApi.wechatLogin({
        code: code
      })

      if (res.code === 200) {
        app.login(res.data.user, res.data.token)
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        setTimeout(() => {
          this.redirectAfterLogin()
        }, 800)
      } else {
        throw new Error(res.message || '登录失败')
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 手机号输入
  onPhoneInput(e) {
    const phoneNumber = e.detail.value
    this.setData({ 
      phoneNumber,
      canSendCode: this.validatePhone(phoneNumber)
    })
    this.checkCanLogin()
  },

  // 验证码输入
  onCodeInput(e) {
    const verifyCode = e.detail.value
    this.setData({ verifyCode })
    this.checkCanLogin()
  },

  // 验证手机号
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },

  // 检查是否可以登录（手机号登录专用）
  checkCanLogin() {
    // 只在手机号登录模式下才检查
    if (!this.data.showPhoneLogin) {
      return
    }

    const { phoneNumber, verifyCode } = this.data
    const canLogin = this.validatePhone(phoneNumber) && verifyCode.length === 6
    this.setData({ canLogin })
  },

  // 发送验证码
  async sendVerifyCode() {
    if (!this.data.canSendCode || this.data.countdown > 0) {
      return
    }

    try {
      const res = await api.userApi.sendSms({
        phone: this.data.phoneNumber
      })

      if (res.code === 200) {
        wx.showToast({
          title: '验证码已发送',
          icon: 'success'
        })

        // 开始倒计时
        this.startCountdown()
      } else {
        throw new Error(res.message || '发送失败')
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      wx.showToast({
        title: error.message || '发送失败',
        icon: 'none'
      })
    }
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60
    this.setData({ 
      countdown,
      codeButtonText: `${countdown}s后重发`
    })

    const timer = setInterval(() => {
      countdown--
      if (countdown <= 0) {
        clearInterval(timer)
        this.setData({
          countdown: 0,
          codeButtonText: '获取验证码',
          countdownTimer: null
        })
      } else {
        this.setData({
          countdown,
          codeButtonText: `${countdown}s后重发`
        })
      }
    }, 1000)

    this.setData({ countdownTimer: timer })
  },

  // 手机号登录
  async phoneLogin() {
    if (!this.data.canLogin) {
      return
    }

    if (!this.data.agreedToTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    this.setData({ loginLoading: true })

    try {
      const res = await api.userApi.phoneLogin({
        phone: this.data.phoneNumber,
        code: this.data.verifyCode
      })

      if (res.code === 200) {
        app.login(res.data.user, res.data.token)
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        setTimeout(() => {
          this.redirectAfterLogin()
        }, 800)
      } else {
        throw new Error(res.message || '登录失败')
      }
    } catch (error) {
      console.error('手机号登录失败:', error)
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loginLoading: false })
    }
  },

  // 查看用户协议
  viewUserAgreement() {
    this.setData({
      showAgreementModal: true,
      agreementTitle: '用户协议',
      agreementContent: `欢迎使用万博科技礼品卡商城！

1. 服务条款
本协议是您与万博科技礼品卡商城之间关于使用本服务的法律协议。

2. 用户权利与义务
- 用户有权享受本平台提供的各项服务
- 用户应遵守相关法律法规，不得从事违法活动
- 用户应保护好自己的账户信息

3. 隐私保护
我们承诺保护用户隐私，不会泄露用户个人信息。

4. 服务变更
我们保留随时修改或中断服务的权利。

5. 免责声明
在法律允许的范围内，我们不承担因使用本服务而产生的任何损失。

如有疑问，请联系客服。`
    })
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    this.setData({
      showAgreementModal: true,
      agreementTitle: '隐私政策',
      agreementContent: `万博科技礼品卡商城隐私政策

我们非常重视您的隐私保护，本政策说明我们如何收集、使用和保护您的信息。

1. 信息收集
- 基本信息：昵称、头像等
- 使用信息：操作记录、偏好设置等
- 设备信息：设备型号、系统版本等

2. 信息使用
- 提供和改善服务
- 个性化推荐
- 安全保障

3. 信息保护
- 采用加密技术保护数据传输
- 严格限制访问权限
- 定期安全审计

4. 信息共享
除法律要求外，我们不会与第三方分享您的个人信息。

5. 用户权利
您有权查看、修改或删除个人信息。

更新日期：2024年1月1日`
    })
  },

  // 隐藏协议弹窗
  hideAgreementModal() {
    this.setData({ showAgreementModal: false })
  },

  // 确认协议
  confirmAgreement() {
    this.setData({
      showAgreementModal: false,
      agreedToTerms: true
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击协议内容时关闭弹窗
  },

  // 快速体验
  quickExperience() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 联系客服
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00\n或者您可以在小程序内留言',
      showCancel: false
    })
  }
})

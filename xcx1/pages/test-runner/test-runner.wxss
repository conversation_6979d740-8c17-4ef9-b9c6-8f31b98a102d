/* 测试运行页面样式 */
@import '/styles/theme.wxss';

.container {
  background: var(--color-bg-secondary);
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 概览区域 */
.overview-section {
  background: var(--color-bg-primary);
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--color-text-primary);
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid var(--color-border-secondary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 测试摘要 */
.test-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  padding: 24rpx 32rpx;
}

.summary-card {
  background: var(--color-bg-secondary);
  border-radius: 12rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  border: 2rpx solid var(--color-border-secondary);
}

.summary-card.total {
  border-color: var(--color-info);
}

.summary-card.passed {
  border-color: var(--color-success);
}

.summary-card.failed {
  border-color: var(--color-error);
}

.summary-card.duration {
  border-color: var(--color-warning);
}

.card-icon {
  font-size: 40rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-bg-primary);
  border-radius: 50%;
}

.card-info {
  flex: 1;
}

.card-value {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: 4rpx;
}

.card-label {
  font-size: 22rpx;
  color: var(--color-text-secondary);
}

/* 进度区域 */
.progress-section {
  padding: 0 32rpx 32rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-text {
  font-size: 26rpx;
  color: var(--color-text-secondary);
}

.progress-percent {
  font-size: 26rpx;
  font-weight: 500;
  color: var(--color-primary);
}

.progress-bar {
  height: 12rpx;
  background: var(--color-border-secondary);
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to right, var(--color-primary), var(--color-primary-light));
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

/* 控制区域 */
.control-section {
  background: var(--color-bg-primary);
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.control-buttons {
  padding: 24rpx 32rpx 32rpx;
  display: flex;
  gap: 16rpx;
}

/* 筛选区域 */
.filter-section {
  background: var(--color-bg-primary);
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.filter-tabs {
  display: flex;
  border-bottom: 1rpx solid var(--color-border-secondary);
}

.filter-tab {
  flex: 1;
  padding: 24rpx 16rpx;
  text-align: center;
  font-size: 26rpx;
  color: var(--color-text-secondary);
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.filter-tab.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  font-weight: 500;
}

.tab-count {
  font-size: 20rpx;
  background: var(--color-bg-secondary);
  color: var(--color-text-tertiary);
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 32rpx;
  text-align: center;
}

.filter-tab.active .tab-count {
  background: var(--color-primary-bg);
  color: var(--color-primary);
}

/* 测试列表 */
.tests-section {
  background: var(--color-bg-primary);
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.test-list {
  padding: 0 32rpx 32rpx;
}

.test-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: var(--color-bg-secondary);
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  border: 2rpx solid var(--color-border-secondary);
  transition: all 0.3s ease;
}

.test-item:active {
  transform: scale(0.98);
  border-color: var(--color-primary);
}

.test-item.running {
  border-color: var(--color-info);
  background: var(--color-info-bg);
}

.test-item.passed {
  border-color: var(--color-success);
  background: var(--color-success-bg);
}

.test-item.failed {
  border-color: var(--color-error);
  background: var(--color-error-bg);
}

.test-status {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-bg-primary);
  border-radius: 50%;
  flex-shrink: 0;
}

.status-icon {
  font-size: 24rpx;
}

.test-info {
  flex: 1;
}

.test-name {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: 8rpx;
}

.test-description {
  font-size: 24rpx;
  color: var(--color-text-secondary);
  line-height: 1.4;
  display: block;
  margin-bottom: 12rpx;
}

.test-meta {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.test-type {
  font-size: 20rpx;
  background: var(--color-border-primary);
  color: var(--color-text-tertiary);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.test-duration {
  font-size: 20rpx;
  color: var(--color-text-tertiary);
}

.test-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.error-indicator {
  font-size: 20rpx;
  color: var(--color-error);
}

.action-arrow {
  font-size: 24rpx;
  color: var(--color-text-tertiary);
}

/* 日志区域 */
.log-section {
  background: var(--color-bg-primary);
  border-radius: 16rpx;
  overflow: hidden;
}

.log-controls {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.auto-scroll-switch {
  transform: scale(0.8);
}

.switch-label {
  font-size: 24rpx;
  color: var(--color-text-secondary);
}

.log-container {
  height: 400rpx;
  background: #1a1a1a;
  margin: 0 32rpx 32rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.log-content {
  padding: 16rpx;
}

.log-item {
  display: flex;
  gap: 16rpx;
  margin-bottom: 8rpx;
  font-family: 'Courier New', monospace;
  font-size: 22rpx;
  line-height: 1.4;
}

.log-time {
  color: #888;
  flex-shrink: 0;
  width: 120rpx;
}

.log-message {
  color: #fff;
  flex: 1;
  word-break: break-all;
}

.log-item.error .log-message {
  color: #ff6b6b;
}

.log-item.warning .log-message {
  color: #ffd93d;
}

.log-item.success .log-message {
  color: #51cf66;
}

.log-item.info .log-message {
  color: #74c0fc;
}

/* 模态框 */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 32rpx;
}

.modal-content {
  background: var(--color-bg-primary);
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--color-border-secondary);
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--color-text-primary);
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--color-text-secondary);
  border-radius: 50%;
  background: var(--color-bg-secondary);
}

.modal-close:active {
  background: var(--color-border-primary);
}

.modal-body {
  padding: 32rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24rpx;
}

.detail-label {
  font-size: 24rpx;
  color: var(--color-text-secondary);
  display: block;
  margin-bottom: 8rpx;
}

.detail-value {
  font-size: 26rpx;
  color: var(--color-text-primary);
  line-height: 1.4;
}

.detail-value.error {
  color: var(--color-error);
  background: var(--color-error-bg);
  padding: 12rpx;
  border-radius: 8rpx;
  font-family: 'Courier New', monospace;
}

.detail-value.passed {
  color: var(--color-success);
}

.detail-value.failed {
  color: var(--color-error);
}

.detail-value.running {
  color: var(--color-info);
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid var(--color-border-secondary);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .test-summary {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }
  
  .control-buttons {
    flex-direction: column;
  }
  
  .filter-tabs {
    flex-wrap: wrap;
  }
  
  .filter-tab {
    min-width: 25%;
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .log-container {
    background: #0a0a0a;
  }
}

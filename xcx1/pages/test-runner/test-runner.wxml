<!-- 测试运行页面 -->
<page-transition id="page-transition" animation-type="slide-left">
  <view class="container {{themeClass}}">
    <!-- 测试概览 -->
    <view class="overview-section">
      <view class="section-title">测试概览</view>
      
      <view class="test-summary">
        <view class="summary-card total">
          <view class="card-icon">🧪</view>
          <view class="card-info">
            <text class="card-value">{{testResults.total}}</text>
            <text class="card-label">总测试数</text>
          </view>
        </view>
        
        <view class="summary-card passed">
          <view class="card-icon">✅</view>
          <view class="card-info">
            <text class="card-value">{{testResults.passed}}</text>
            <text class="card-label">通过</text>
          </view>
        </view>
        
        <view class="summary-card failed">
          <view class="card-icon">❌</view>
          <view class="card-info">
            <text class="card-value">{{testResults.failed}}</text>
            <text class="card-label">失败</text>
          </view>
        </view>
        
        <view class="summary-card duration">
          <view class="card-icon">⏱️</view>
          <view class="card-info">
            <text class="card-value">{{testResults.duration}}ms</text>
            <text class="card-label">耗时</text>
          </view>
        </view>
      </view>
      
      <view class="progress-section">
        <view class="progress-info">
          <text class="progress-text">测试进度</text>
          <text class="progress-percent">{{progressPercent}}%</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercent}}%;"></view>
        </view>
      </view>
    </view>

    <!-- 测试控制 -->
    <view class="control-section">
      <view class="section-title">测试控制</view>
      
      <view class="control-buttons">
        <enhanced-button 
          text="{{isRunning ? '停止测试' : '开始测试'}}"
          type="{{isRunning ? 'error' : 'primary'}}"
          size="medium"
          prefix-icon="{{isRunning ? 'close' : 'play'}}"
          loading="{{isRunning}}"
          bindtap="{{isRunning ? 'stopTests' : 'startTests'}}">
        </enhanced-button>
        
        <enhanced-button 
          text="清除结果"
          type="outline"
          size="medium"
          prefix-icon="clean"
          disabled="{{isRunning}}"
          bindtap="clearResults">
        </enhanced-button>
        
        <enhanced-button 
          text="导出报告"
          type="secondary"
          size="medium"
          prefix-icon="download"
          disabled="{{isRunning || testResults.total === 0}}"
          bindtap="exportReport">
        </enhanced-button>
      </view>
    </view>

    <!-- 测试类型筛选 -->
    <view class="filter-section">
      <view class="section-title">测试类型</view>
      
      <view class="filter-tabs">
        <view class="filter-tab {{currentFilter === 'all' ? 'active' : ''}}" 
              bindtap="filterTests" 
              data-filter="all">
          <text>全部</text>
          <text class="tab-count">{{allTests.length}}</text>
        </view>
        <view class="filter-tab {{currentFilter === 'functional' ? 'active' : ''}}" 
              bindtap="filterTests" 
              data-filter="functional">
          <text>功能测试</text>
          <text class="tab-count">{{functionalTests.length}}</text>
        </view>
        <view class="filter-tab {{currentFilter === 'performance' ? 'active' : ''}}" 
              bindtap="filterTests" 
              data-filter="performance">
          <text>性能测试</text>
          <text class="tab-count">{{performanceTests.length}}</text>
        </view>
        <view class="filter-tab {{currentFilter === 'ui' ? 'active' : ''}}" 
              bindtap="filterTests" 
              data-filter="ui">
          <text>UI测试</text>
          <text class="tab-count">{{uiTests.length}}</text>
        </view>
      </view>
    </view>

    <!-- 测试用例列表 -->
    <view class="tests-section">
      <view class="section-title">测试用例</view>
      
      <view class="test-list">
        <view class="test-item {{item.status}}" 
              wx:for="{{filteredTests}}" 
              wx:key="id"
              bindtap="viewTestDetail"
              data-test="{{item}}">
          
          <view class="test-status">
            <view class="status-icon">
              <text wx:if="{{item.status === 'pending'}}">⏳</text>
              <text wx:elif="{{item.status === 'running'}}">🔄</text>
              <text wx:elif="{{item.status === 'passed'}}">✅</text>
              <text wx:elif="{{item.status === 'failed'}}">❌</text>
              <text wx:else>⏸️</text>
            </view>
          </view>
          
          <view class="test-info">
            <text class="test-name">{{item.name}}</text>
            <text class="test-description">{{item.description}}</text>
            <view class="test-meta">
              <text class="test-type">{{item.type}}</text>
              <text wx:if="{{item.duration > 0}}" class="test-duration">{{item.duration}}ms</text>
            </view>
          </view>
          
          <view class="test-actions">
            <view wx:if="{{item.status === 'failed'}}" class="error-indicator">
              <text class="error-icon">⚠️</text>
            </view>
            <view class="action-arrow">></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 实时日志 -->
    <view class="log-section">
      <view class="section-title">
        <text>实时日志</text>
        <view class="log-controls">
          <switch class="auto-scroll-switch" 
                 checked="{{autoScroll}}"
                 bindchange="onAutoScrollChange"
                 color="#2E7D32"></switch>
          <text class="switch-label">自动滚动</text>
        </view>
      </view>
      
      <scroll-view class="log-container" 
                   scroll-y="{{true}}"
                   scroll-top="{{autoScroll ? logScrollTop : 0}}"
                   scroll-with-animation="{{true}}">
        <view class="log-content">
          <view class="log-item {{item.level}}" 
                wx:for="{{testLogs}}" 
                wx:key="index">
            <text class="log-time">{{item.time}}</text>
            <text class="log-message">{{item.message}}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 测试详情模态框 -->
    <view wx:if="{{showDetailModal}}" class="modal-backdrop" bindtap="hideDetailModal">
      <view class="modal-content" catchtap="preventClose">
        <view class="modal-header">
          <text class="modal-title">{{selectedTest.name}}</text>
          <view class="modal-close" bindtap="hideDetailModal">×</view>
        </view>
        
        <view class="modal-body">
          <view class="detail-section">
            <text class="detail-label">描述:</text>
            <text class="detail-value">{{selectedTest.description}}</text>
          </view>
          
          <view class="detail-section">
            <text class="detail-label">类型:</text>
            <text class="detail-value">{{selectedTest.type}}</text>
          </view>
          
          <view class="detail-section">
            <text class="detail-label">状态:</text>
            <text class="detail-value {{selectedTest.status}}">{{selectedTest.statusText}}</text>
          </view>
          
          <view wx:if="{{selectedTest.duration > 0}}" class="detail-section">
            <text class="detail-label">耗时:</text>
            <text class="detail-value">{{selectedTest.duration}}ms</text>
          </view>
          
          <view wx:if="{{selectedTest.error}}" class="detail-section">
            <text class="detail-label">错误信息:</text>
            <text class="detail-value error">{{selectedTest.error}}</text>
          </view>
          
          <view wx:if="{{selectedTest.result}}" class="detail-section">
            <text class="detail-label">结果:</text>
            <text class="detail-value">{{selectedTest.result.message}}</text>
          </view>
        </view>
        
        <view class="modal-footer">
          <enhanced-button 
            text="重新运行"
            type="primary"
            size="small"
            disabled="{{isRunning}}"
            bindtap="rerunTest">
          </enhanced-button>
          
          <enhanced-button 
            text="关闭"
            type="outline"
            size="small"
            bindtap="hideDetailModal">
          </enhanced-button>
        </view>
      </view>
    </view>
  </view>
</page-transition>

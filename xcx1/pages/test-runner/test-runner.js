/**
 * 测试运行页面
 */
const { globalTestSuite, TEST_STATUS } = require('../../utils/test-suite.js')

Page({
  data: {
    // 主题状态
    themeClass: '',
    
    // 测试状态
    isRunning: false,
    currentFilter: 'all',
    
    // 测试结果
    testResults: {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0
    },
    
    // 进度
    progressPercent: 0,
    
    // 测试用例
    allTests: [],
    functionalTests: [],
    performanceTests: [],
    uiTests: [],
    filteredTests: [],
    
    // 日志
    testLogs: [],
    autoScroll: true,
    logScrollTop: 0,
    
    // 模态框
    showDetailModal: false,
    selectedTest: null
  },

  onLoad(options) {
    this.initTestRunner()
  },

  onShow() {
    this.refreshTestData()
  },

  onUnload() {
    // 移除测试观察者
    globalTestSuite.observers = []
  },

  // 初始化测试运行器
  initTestRunner() {
    try {
      // 添加测试观察者
      globalTestSuite.addObserver(this.onTestEvent.bind(this))
      
      // 加载测试用例
      this.loadTestCases()
      
      // 初始化日志
      this.addLog('info', '测试运行器初始化完成')
      
    } catch (error) {
      console.error('❌ 初始化测试运行器失败:', error)
      this.addLog('error', `初始化失败: ${error.message}`)
    }
  },

  // 加载测试用例
  loadTestCases() {
    const allTests = globalTestSuite.testCases.map(testCase => ({
      id: testCase.id,
      name: testCase.name,
      description: testCase.description,
      type: testCase.type,
      status: testCase.status,
      duration: testCase.duration,
      error: testCase.error?.message,
      result: testCase.result,
      statusText: this.getStatusText(testCase.status)
    }))

    // 按类型分组
    const functionalTests = allTests.filter(test => test.type === 'functional')
    const performanceTests = allTests.filter(test => test.type === 'performance')
    const uiTests = allTests.filter(test => test.type === 'ui')

    this.setData({
      allTests,
      functionalTests,
      performanceTests,
      uiTests
    })

    // 应用当前筛选
    this.applyFilter()
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      [TEST_STATUS.PENDING]: '等待中',
      [TEST_STATUS.RUNNING]: '运行中',
      [TEST_STATUS.PASSED]: '通过',
      [TEST_STATUS.FAILED]: '失败',
      [TEST_STATUS.SKIPPED]: '跳过'
    }
    return statusMap[status] || '未知'
  },

  // 测试事件处理
  onTestEvent(event, data) {
    switch (event) {
      case 'suite_start':
        this.onSuiteStart(data)
        break
      case 'test_start':
        this.onTestStart(data)
        break
      case 'test_complete':
        this.onTestComplete(data)
        break
      case 'suite_complete':
        this.onSuiteComplete(data)
        break
      case 'suite_error':
        this.onSuiteError(data)
        break
    }
  },

  // 测试套件开始
  onSuiteStart(data) {
    this.setData({ isRunning: true, progressPercent: 0 })
    this.addLog('info', `开始运行测试套件: ${data.name}`)
  },

  // 单个测试开始
  onTestStart(testCase) {
    this.addLog('info', `开始测试: ${testCase.name}`)
    this.updateTestCase(testCase)
  },

  // 单个测试完成
  onTestComplete(testCase) {
    const status = testCase.status === TEST_STATUS.PASSED ? 'success' : 'error'
    const message = `测试完成: ${testCase.name} - ${this.getStatusText(testCase.status)}`
    
    if (testCase.duration) {
      this.addLog(status, `${message} (${testCase.duration}ms)`)
    } else {
      this.addLog(status, message)
    }

    if (testCase.error) {
      this.addLog('error', `错误: ${testCase.error.message}`)
    }

    this.updateTestCase(testCase)
    this.updateProgress()
  },

  // 测试套件完成
  onSuiteComplete(results) {
    this.setData({ 
      isRunning: false,
      testResults: results,
      progressPercent: 100
    })
    
    this.addLog('success', `测试套件完成 - 总计: ${results.total}, 通过: ${results.passed}, 失败: ${results.failed}`)
    
    if (results.failed > 0) {
      wx.showModal({
        title: '测试完成',
        content: `发现 ${results.failed} 个失败的测试用例，请查看详情`,
        showCancel: false
      })
    } else {
      wx.showToast({
        title: '所有测试通过！',
        icon: 'success'
      })
    }
  },

  // 测试套件错误
  onSuiteError(data) {
    this.setData({ isRunning: false })
    this.addLog('error', `测试套件错误: ${data.error.message}`)
    
    wx.showModal({
      title: '测试失败',
      content: data.error.message,
      showCancel: false
    })
  },

  // 更新测试用例
  updateTestCase(testCase) {
    const updatedTest = {
      id: testCase.id,
      name: testCase.name,
      description: testCase.description,
      type: testCase.type,
      status: testCase.status,
      duration: testCase.duration,
      error: testCase.error?.message,
      result: testCase.result,
      statusText: this.getStatusText(testCase.status)
    }

    // 更新所有测试列表
    const allTests = this.data.allTests.map(test => 
      test.id === testCase.id ? updatedTest : test
    )

    // 更新分类列表
    const functionalTests = allTests.filter(test => test.type === 'functional')
    const performanceTests = allTests.filter(test => test.type === 'performance')
    const uiTests = allTests.filter(test => test.type === 'ui')

    this.setData({
      allTests,
      functionalTests,
      performanceTests,
      uiTests
    })

    // 重新应用筛选
    this.applyFilter()
  },

  // 更新进度
  updateProgress() {
    const completedTests = this.data.allTests.filter(test => 
      test.status === TEST_STATUS.PASSED || test.status === TEST_STATUS.FAILED
    ).length
    
    const progressPercent = this.data.allTests.length > 0 
      ? Math.round((completedTests / this.data.allTests.length) * 100)
      : 0

    this.setData({ progressPercent })
  },

  // 添加日志
  addLog(level, message) {
    const time = new Date().toLocaleTimeString()
    const logItem = { time, message, level }
    
    const testLogs = [...this.data.testLogs, logItem]
    
    // 保持最近100条日志
    if (testLogs.length > 100) {
      testLogs.shift()
    }

    this.setData({ testLogs })

    // 自动滚动到底部
    if (this.data.autoScroll) {
      setTimeout(() => {
        this.setData({ logScrollTop: testLogs.length * 50 })
      }, 100)
    }
  },

  // 刷新测试数据
  refreshTestData() {
    this.loadTestCases()
    
    // 更新测试结果
    const results = globalTestSuite.results
    this.setData({ testResults: results })
    
    this.updateProgress()
  },

  // 开始测试
  async startTests() {
    if (this.data.isRunning) return

    try {
      this.addLog('info', '准备开始测试...')
      
      // 清除之前的日志
      this.setData({ testLogs: [] })
      
      // 运行测试套件
      await globalTestSuite.runAll()
      
    } catch (error) {
      console.error('❌ 运行测试失败:', error)
      this.addLog('error', `运行测试失败: ${error.message}`)
      
      wx.showToast({
        title: '测试运行失败',
        icon: 'none'
      })
    }
  },

  // 停止测试
  stopTests() {
    // 注意：当前测试套件不支持中途停止
    // 这里只是更新UI状态
    this.setData({ isRunning: false })
    this.addLog('warning', '测试已停止')
    
    wx.showToast({
      title: '测试已停止',
      icon: 'none'
    })
  },

  // 清除结果
  clearResults() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有测试结果吗？',
      success: (res) => {
        if (res.confirm) {
          // 重置测试状态
          globalTestSuite.resetResults()
          
          this.setData({
            testResults: {
              total: 0,
              passed: 0,
              failed: 0,
              skipped: 0,
              duration: 0
            },
            progressPercent: 0,
            testLogs: []
          })
          
          this.loadTestCases()
          this.addLog('info', '测试结果已清除')
          
          wx.showToast({
            title: '结果已清除',
            icon: 'success'
          })
        }
      }
    })
  },

  // 导出报告
  exportReport() {
    const report = globalTestSuite.getReport()
    
    const reportText = `
测试报告
========

套件名称: ${report.suiteName}
运行时间: ${new Date().toLocaleString()}

测试摘要:
- 总测试数: ${report.summary.total}
- 通过: ${report.summary.passed}
- 失败: ${report.summary.failed}
- 跳过: ${report.summary.skipped}
- 总耗时: ${report.summary.duration}ms

详细结果:
${report.testCases.map(test => 
  `- ${test.name}: ${test.status} (${test.duration}ms)${test.error ? ` - ${test.error}` : ''}`
).join('\n')}
    `.trim()

    wx.setClipboardData({
      data: reportText,
      success: () => {
        wx.showToast({
          title: '报告已复制到剪贴板',
          icon: 'success'
        })
      }
    })
  },

  // 筛选测试
  filterTests(e) {
    const { filter } = e.currentTarget.dataset
    this.setData({ currentFilter: filter })
    this.applyFilter()
  },

  // 应用筛选
  applyFilter() {
    let filteredTests = []
    
    switch (this.data.currentFilter) {
      case 'functional':
        filteredTests = this.data.functionalTests
        break
      case 'performance':
        filteredTests = this.data.performanceTests
        break
      case 'ui':
        filteredTests = this.data.uiTests
        break
      default:
        filteredTests = this.data.allTests
    }
    
    this.setData({ filteredTests })
  },

  // 查看测试详情
  viewTestDetail(e) {
    const { test } = e.currentTarget.dataset
    this.setData({
      selectedTest: test,
      showDetailModal: true
    })
  },

  // 隐藏详情模态框
  hideDetailModal() {
    this.setData({ showDetailModal: false })
  },

  // 阻止模态框关闭
  preventClose() {
    // 阻止事件冒泡
  },

  // 重新运行测试
  rerunTest() {
    // 注意：当前测试套件不支持单独运行单个测试
    // 这里提示用户重新运行整个套件
    wx.showModal({
      title: '重新运行测试',
      content: '当前只支持重新运行整个测试套件，是否继续？',
      success: (res) => {
        if (res.confirm) {
          this.hideDetailModal()
          this.startTests()
        }
      }
    })
  },

  // 自动滚动开关
  onAutoScrollChange(e) {
    const { value } = e.detail
    this.setData({ autoScroll: value })
  }
})

<!-- 设置页面 -->
<view class="container">
  <!-- 账户设置 -->
  <view class="settings-section">
    <view class="card">
      <view class="section-header">
        <text class="section-title">账户设置</text>
      </view>
      
      <view class="settings-list">
        <view class="setting-item" bindtap="editProfile">
          <view class="setting-icon">👤</view>
          <view class="setting-content">
            <text class="setting-title">个人资料</text>
            <text class="setting-desc">修改昵称、头像等信息</text>
          </view>
          <text class="setting-arrow">></text>
        </view>
        
        <view class="setting-item" bindtap="bindPhone">
          <view class="setting-icon">📱</view>
          <view class="setting-content">
            <text class="setting-title">手机号码</text>
            <text class="setting-desc">{{userInfo.phone || '未绑定'}}</text>
          </view>
          <text class="setting-arrow">></text>
        </view>
        
        <view class="setting-item" bindtap="setPaymentPassword">
          <view class="setting-icon">🔐</view>
          <view class="setting-content">
            <text class="setting-title">支付密码</text>
            <text class="setting-desc">{{hasPaymentPassword ? '已设置' : '未设置'}}</text>
          </view>
          <text class="setting-arrow">></text>
        </view>
        
        <view class="setting-item" bindtap="realNameAuth">
          <view class="setting-icon">✅</view>
          <view class="setting-content">
            <text class="setting-title">实名认证</text>
            <text class="setting-desc {{authStatus}}">{{authStatusText}}</text>
          </view>
          <text class="setting-arrow">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 通知设置 -->
  <view class="settings-section">
    <view class="card">
      <view class="section-header">
        <text class="section-title">通知设置</text>
      </view>
      
      <view class="settings-list">
        <view class="setting-item">
          <view class="setting-icon">🔔</view>
          <view class="setting-content">
            <text class="setting-title">订单通知</text>
            <text class="setting-desc">订单状态变更提醒</text>
          </view>
          <switch class="setting-switch" 
                  checked="{{notifications.order}}" 
                  bindchange="toggleNotification" 
                  data-type="order" />
        </view>
        
        <view class="setting-item">
          <view class="setting-icon">💰</view>
          <view class="setting-content">
            <text class="setting-title">收益通知</text>
            <text class="setting-desc">收益到账提醒</text>
          </view>
          <switch class="setting-switch" 
                  checked="{{notifications.earnings}}" 
                  bindchange="toggleNotification" 
                  data-type="earnings" />
        </view>
        
        <view class="setting-item">
          <view class="setting-icon">📊</view>
          <view class="setting-content">
            <text class="setting-title">监控通知</text>
            <text class="setting-desc">环境异常告警</text>
          </view>
          <switch class="setting-switch" 
                  checked="{{notifications.monitoring}}" 
                  bindchange="toggleNotification" 
                  data-type="monitoring" />
        </view>
        
        <view class="setting-item">
          <view class="setting-icon">🎁</view>
          <view class="setting-content">
            <text class="setting-title">活动通知</text>
            <text class="setting-desc">优惠活动推送</text>
          </view>
          <switch class="setting-switch" 
                  checked="{{notifications.activity}}" 
                  bindchange="toggleNotification" 
                  data-type="activity" />
        </view>
      </view>
    </view>
  </view>

  <!-- 隐私设置 -->
  <view class="settings-section">
    <view class="card">
      <view class="section-header">
        <text class="section-title">隐私设置</text>
      </view>
      
      <view class="settings-list">
        <view class="setting-item">
          <view class="setting-icon">👁️</view>
          <view class="setting-content">
            <text class="setting-title">收益公开</text>
            <text class="setting-desc">是否公开收益排行</text>
          </view>
          <switch class="setting-switch" 
                  checked="{{privacy.showEarnings}}" 
                  bindchange="togglePrivacy" 
                  data-type="showEarnings" />
        </view>
        
        <view class="setting-item">
          <view class="setting-icon">📍</view>
          <view class="setting-content">
            <text class="setting-title">位置信息</text>
            <text class="setting-desc">允许获取位置信息</text>
          </view>
          <switch class="setting-switch" 
                  checked="{{privacy.location}}" 
                  bindchange="togglePrivacy" 
                  data-type="location" />
        </view>
        
        <view class="setting-item">
          <view class="setting-icon">📊</view>
          <view class="setting-content">
            <text class="setting-title">数据分析</text>
            <text class="setting-desc">允许收集使用数据</text>
          </view>
          <switch class="setting-switch" 
                  checked="{{privacy.analytics}}" 
                  bindchange="togglePrivacy" 
                  data-type="analytics" />
        </view>
      </view>
    </view>
  </view>

  <!-- 应用设置 -->
  <view class="settings-section">
    <view class="card">
      <view class="section-header">
        <text class="section-title">应用设置</text>
      </view>
      
      <view class="settings-list">
        <view class="setting-item" bindtap="setLanguage">
          <view class="setting-icon">🌐</view>
          <view class="setting-content">
            <text class="setting-title">语言设置</text>
            <text class="setting-desc">{{currentLanguage}}</text>
          </view>
          <text class="setting-arrow">></text>
        </view>
        
        <view class="setting-item">
          <view class="setting-icon">🌙</view>
          <view class="setting-content">
            <text class="setting-title">深色模式</text>
            <text class="setting-desc">跟随系统设置</text>
          </view>
          <switch class="setting-switch" 
                  checked="{{appSettings.darkMode}}" 
                  bindchange="toggleAppSetting" 
                  data-type="darkMode" />
        </view>
        
        <view class="setting-item">
          <view class="setting-icon">📳</view>
          <view class="setting-content">
            <text class="setting-title">震动反馈</text>
            <text class="setting-desc">操作时震动提示</text>
          </view>
          <switch class="setting-switch" 
                  checked="{{appSettings.vibration}}" 
                  bindchange="toggleAppSetting" 
                  data-type="vibration" />
        </view>
        
        <view class="setting-item">
          <view class="setting-icon">🔄</view>
          <view class="setting-content">
            <text class="setting-title">自动刷新</text>
            <text class="setting-desc">监控数据自动刷新</text>
          </view>
          <switch class="setting-switch" 
                  checked="{{appSettings.autoRefresh}}" 
                  bindchange="toggleAppSetting" 
                  data-type="autoRefresh" />
        </view>
      </view>
    </view>
  </view>

  <!-- 存储管理 -->
  <view class="settings-section">
    <view class="card">
      <view class="section-header">
        <text class="section-title">存储管理</text>
      </view>
      
      <view class="storage-info">
        <view class="storage-item">
          <text class="storage-label">缓存大小</text>
          <text class="storage-value">{{cacheSize}}</text>
        </view>
        <view class="storage-item">
          <text class="storage-label">图片缓存</text>
          <text class="storage-value">{{imageCache}}</text>
        </view>
        <view class="storage-item">
          <text class="storage-label">数据缓存</text>
          <text class="storage-value">{{dataCache}}</text>
        </view>
      </view>
      
      <view class="storage-actions">
        <button class="clear-cache-btn" bindtap="clearCache">清理缓存</button>
      </view>
    </view>
  </view>

  <!-- 其他设置 -->
  <view class="settings-section">
    <view class="card">
      <view class="section-header">
        <text class="section-title">其他</text>
      </view>
      
      <view class="settings-list">
        <view class="setting-item" bindtap="checkUpdate">
          <view class="setting-icon">🔄</view>
          <view class="setting-content">
            <text class="setting-title">检查更新</text>
            <text class="setting-desc">当前版本 v1.0.0</text>
          </view>
          <text class="setting-arrow">></text>
        </view>
        
        <view class="setting-item" bindtap="viewHelp">
          <view class="setting-icon">❓</view>
          <view class="setting-content">
            <text class="setting-title">帮助中心</text>
            <text class="setting-desc">使用帮助和常见问题</text>
          </view>
          <text class="setting-arrow">></text>
        </view>
        
        <view class="setting-item" bindtap="contactService">
          <view class="setting-icon">💬</view>
          <view class="setting-content">
            <text class="setting-title">联系客服</text>
            <text class="setting-desc">在线客服和电话支持</text>
          </view>
          <text class="setting-arrow">></text>
        </view>
        
        <view class="setting-item" bindtap="feedback">
          <view class="setting-icon">📝</view>
          <view class="setting-content">
            <text class="setting-title">意见反馈</text>
            <text class="setting-desc">提交建议和问题反馈</text>
          </view>
          <text class="setting-arrow">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 账户操作 -->
  <view class="settings-section">
    <view class="account-actions">
      <button class="logout-btn" bindtap="logout">退出登录</button>
      <button class="delete-account-btn" bindtap="deleteAccount">注销账户</button>
    </view>
  </view>
</view>

<!-- 语言选择弹窗 -->
<view class="language-modal" wx:if="{{showLanguageModal}}" bindtap="hideLanguageModal">
  <view class="language-content" catchtap="stopPropagation">
    <view class="language-header">
      <text class="language-title">选择语言</text>
      <view class="language-close" bindtap="hideLanguageModal">×</view>
    </view>
    
    <view class="language-list">
      <view class="language-item {{currentLanguage === item.name ? 'selected' : ''}}" 
            wx:for="{{languages}}" wx:key="code"
            bindtap="selectLanguage" data-code="{{item.code}}" data-name="{{item.name}}">
        <text class="language-name">{{item.name}}</text>
        <view class="language-check" wx:if="{{currentLanguage === item.name}}">✓</view>
      </view>
    </view>
  </view>
</view>

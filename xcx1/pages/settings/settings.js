// 设置页面
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    // 用户信息
    userInfo: {},
    hasPaymentPassword: false,
    authStatus: 'pending',
    authStatusText: '未认证',
    
    // 通知设置
    notifications: {
      order: true,
      earnings: true,
      monitoring: true,
      activity: false
    },
    
    // 隐私设置
    privacy: {
      showEarnings: true,
      location: true,
      analytics: true
    },
    
    // 应用设置
    appSettings: {
      darkMode: false,
      vibration: true,
      autoRefresh: true
    },
    
    // 语言设置
    currentLanguage: '简体中文',
    showLanguageModal: false,
    languages: [
      { code: 'zh-CN', name: '简体中文' },
      { code: 'zh-TW', name: '繁體中文' },
      { code: 'en-US', name: 'English' }
    ],
    
    // 存储信息
    cacheSize: '0MB',
    imageCache: '0MB',
    dataCache: '0MB'
  },

  // 页面加载
  onLoad(options) {
    this.loadUserData()
    this.loadSettings()
    this.calculateCacheSize()
  },

  // 页面显示
  onShow() {
    // 刷新用户信息
    this.loadUserData()
  },

  // 加载用户数据
  loadUserData() {
    const userInfo = app.getUserInfo()
    if (userInfo) {
      this.setData({
        userInfo,
        hasPaymentPassword: userInfo.has_payment_password || false,
        authStatus: userInfo.auth_status || 'pending',
        authStatusText: this.getAuthStatusText(userInfo.auth_status)
      })
    }
  },

  // 获取认证状态文本
  getAuthStatusText(status) {
    const statusMap = {
      'pending': '未认证',
      'reviewing': '审核中',
      'approved': '已认证',
      'rejected': '认证失败'
    }
    return statusMap[status] || '未认证'
  },

  // 加载设置
  async loadSettings() {
    try {
      // 从本地存储加载设置
      const notifications = wx.getStorageSync('notifications') || this.data.notifications
      const privacy = wx.getStorageSync('privacy') || this.data.privacy
      const appSettings = wx.getStorageSync('appSettings') || this.data.appSettings
      const currentLanguage = wx.getStorageSync('currentLanguage') || this.data.currentLanguage
      
      this.setData({
        notifications,
        privacy,
        appSettings,
        currentLanguage
      })
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  },

  // 保存设置到本地
  saveSettings() {
    try {
      wx.setStorageSync('notifications', this.data.notifications)
      wx.setStorageSync('privacy', this.data.privacy)
      wx.setStorageSync('appSettings', this.data.appSettings)
      wx.setStorageSync('currentLanguage', this.data.currentLanguage)
    } catch (error) {
      console.error('保存设置失败:', error)
    }
  },

  // 计算缓存大小
  calculateCacheSize() {
    try {
      // 这里应该计算实际的缓存大小
      // 模拟数据
      this.setData({
        cacheSize: '12.5MB',
        imageCache: '8.2MB',
        dataCache: '4.3MB'
      })
    } catch (error) {
      console.error('计算缓存大小失败:', error)
    }
  },

  // 编辑个人资料
  editProfile() {
    wx.navigateTo({
      url: '/pages/edit-profile/edit-profile'
    })
  },

  // 绑定手机号
  bindPhone() {
    wx.navigateTo({
      url: '/pages/bind-phone/bind-phone'
    })
  },

  // 设置支付密码
  setPaymentPassword() {
    const url = this.data.hasPaymentPassword 
      ? '/pages/change-payment-password/change-payment-password'
      : '/pages/set-payment-password/set-payment-password'
    
    wx.navigateTo({ url })
  },

  // 实名认证
  realNameAuth() {
    if (this.data.authStatus === 'approved') {
      wx.showToast({
        title: '您已完成实名认证',
        icon: 'success'
      })
      return
    }
    
    wx.navigateTo({
      url: '/pages/real-name-auth/real-name-auth'
    })
  },

  // 切换通知设置
  toggleNotification(e) {
    const { type } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`notifications.${type}`]: value
    })
    
    this.saveSettings()
    
    // 这里可以调用API同步到服务器
    // api.userApi.updateNotificationSettings(this.data.notifications)
  },

  // 切换隐私设置
  togglePrivacy(e) {
    const { type } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`privacy.${type}`]: value
    })
    
    this.saveSettings()
    
    // 这里可以调用API同步到服务器
    // api.userApi.updatePrivacySettings(this.data.privacy)
  },

  // 切换应用设置
  toggleAppSetting(e) {
    const { type } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`appSettings.${type}`]: value
    })
    
    this.saveSettings()
    
    // 特殊处理
    if (type === 'vibration' && value) {
      wx.vibrateShort()
    }
  },

  // 设置语言
  setLanguage() {
    this.setData({ showLanguageModal: true })
  },

  // 隐藏语言弹窗
  hideLanguageModal() {
    this.setData({ showLanguageModal: false })
  },

  // 选择语言
  selectLanguage(e) {
    const { code, name } = e.currentTarget.dataset
    
    this.setData({
      currentLanguage: name,
      showLanguageModal: false
    })
    
    this.saveSettings()
    
    wx.showToast({
      title: '语言设置已保存',
      icon: 'success'
    })
  },

  // 清理缓存
  clearCache() {
    wx.showModal({
      title: '清理缓存',
      content: '确定要清理所有缓存数据吗？',
      success: (res) => {
        if (res.confirm) {
          this.processClearCache()
        }
      }
    })
  },

  // 处理清理缓存
  async processClearCache() {
    try {
      wx.showLoading({
        title: '清理中...'
      })
      
      // 清理图片缓存
      // wx.clearStorage() // 谨慎使用，会清理所有本地数据
      
      // 模拟清理过程
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      this.setData({
        cacheSize: '0MB',
        imageCache: '0MB',
        dataCache: '0MB'
      })
      
      wx.showToast({
        title: '缓存清理完成',
        icon: 'success'
      })
    } catch (error) {
      console.error('清理缓存失败:', error)
      wx.showToast({
        title: '清理失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 检查更新
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          wx.showModal({
            title: '发现新版本',
            content: '发现新版本，是否立即更新？',
            success: (res) => {
              if (res.confirm) {
                updateManager.onUpdateReady(() => {
                  updateManager.applyUpdate()
                })
              }
            }
          })
        } else {
          wx.showToast({
            title: '已是最新版本',
            icon: 'success'
          })
        }
      })
      
      updateManager.onUpdateFailed(() => {
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        })
      })
    } else {
      wx.showToast({
        title: '当前版本不支持',
        icon: 'none'
      })
    }
  },

  // 查看帮助
  viewHelp() {
    wx.navigateTo({
      url: '/pages/help/help'
    })
  },

  // 联系客服
  contactService() {
    wx.showActionSheet({
      itemList: ['拨打客服电话', '在线客服'],
      success: (res) => {
        if (res.tapIndex === 0) {
          wx.makePhoneCall({
            phoneNumber: '************'
          })
        } else {
          wx.showToast({
            title: '在线客服开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  // 意见反馈
  feedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.logout()
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
          
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            })
          }, 1500)
        }
      }
    })
  },

  // 注销账户
  deleteAccount() {
    wx.showModal({
      title: '注销账户',
      content: '注销账户将删除所有数据且无法恢复，确定要继续吗？',
      confirmText: '确认注销',
      confirmColor: '#FF4444',
      success: (res) => {
        if (res.confirm) {
          this.processDeleteAccount()
        }
      }
    })
  },

  // 处理注销账户
  async processDeleteAccount() {
    try {
      wx.showLoading({
        title: '处理中...'
      })
      
      // 调用注销账户API
      const res = await api.userApi.deleteAccount()

      if (res.code !== 200) {
        throw new Error(res.message || '注销失败')
      }
      
      wx.showToast({
        title: '账户注销成功',
        icon: 'success'
      })
      
      // 清理本地数据
      app.logout()
      
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        })
      }, 1500)
    } catch (error) {
      console.error('注销账户失败:', error)
      wx.showToast({
        title: '注销失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击语言弹窗内容时关闭弹窗
  }
})

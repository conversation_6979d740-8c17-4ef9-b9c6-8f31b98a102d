/* 设置页面样式 */

.container {
  min-height: 100vh;
  background: #F5F5F5;
  padding: 24rpx;
}

/* 设置区域 */
.settings-section {
  margin-bottom: 24rpx;
}

.card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212121;
}

/* 设置列表 */
.settings-list {
  display: flex;
  flex-direction: column;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #F5F5F5;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #F5F5F5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 24rpx;
}

.setting-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.setting-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #212121;
  margin-bottom: 8rpx;
}

.setting-desc {
  font-size: 24rpx;
  color: #666;
}

.setting-desc.verified {
  color: #4CAF50;
}

.setting-desc.pending {
  color: #FF9800;
}

.setting-desc.rejected {
  color: #F44336;
}

.setting-arrow {
  font-size: 32rpx;
  color: #999;
}

/* 开关控件 */
.setting-switch {
  margin-left: 16rpx;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 32rpx;
  color: #999;
  font-size: 24rpx;
}

.version-number {
  margin-bottom: 8rpx;
}

.copyright {
  font-size: 22rpx;
}

/* 退出登录按钮 */
.logout-section {
  margin: 48rpx 32rpx 32rpx;
}

.logout-btn {
  width: 100%;
  background: #F44336;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.logout-btn:active {
  background: #D32F2F;
}

/* 危险操作确认弹窗 */
.danger-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.danger-modal-content {
  background: white;
  border-radius: 16rpx;
  padding: 48rpx 32rpx 32rpx;
  margin: 0 48rpx;
  max-width: 600rpx;
  width: 100%;
}

.danger-modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #212121;
  text-align: center;
  margin-bottom: 24rpx;
}

.danger-modal-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 48rpx;
  line-height: 1.6;
}

.danger-modal-actions {
  display: flex;
  gap: 16rpx;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
}

.modal-btn.cancel {
  background: #F5F5F5;
  color: #666;
}

.modal-btn.confirm {
  background: #F44336;
  color: white;
}

/* 设置项状态样式 */
.setting-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
}

.status-dot.success {
  background: #4CAF50;
}

.status-dot.warning {
  background: #FF9800;
}

.status-dot.error {
  background: #F44336;
}

.status-text {
  font-size: 24rpx;
}

.status-text.success {
  color: #4CAF50;
}

.status-text.warning {
  color: #FF9800;
}

.status-text.error {
  color: #F44336;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .setting-item {
    padding: 20rpx 0;
  }
  
  .setting-icon {
    width: 64rpx;
    height: 64rpx;
    font-size: 32rpx;
    margin-right: 16rpx;
  }
  
  .setting-title {
    font-size: 28rpx;
  }
  
  .setting-desc {
    font-size: 22rpx;
  }
}

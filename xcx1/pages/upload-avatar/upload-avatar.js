// 头像上传页面
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    // 当前头像
    currentAvatar: '',
    
    // 上传的头像列表
    avatarList: [],
    
    // 上传方式
    uploadMethod: 'file', // file | camera | base64
    
    // 裁剪参数
    cropOptions: {
      width: 200,
      height: 200,
      quality: 0.8
    },
    
    // 加载状态
    loading: false,
    saving: false
  },

  // 页面加载
  onLoad(options) {
    this.loadCurrentAvatar()
  },

  // 加载当前头像
  async loadCurrentAvatar() {
    try {
      this.setData({ loading: true })
      
      const response = await api.userApi.getProfile()
      if (response.code === 200 && response.data.avatar) {
        this.setData({
          currentAvatar: response.data.avatar
        })
      }
    } catch (error) {
      console.error('加载头像失败:', error)
    } finally {
      this.setData({ loading: false })
    }
  },

  // 文件上传组件事件处理
  onFileChange(e) {
    const { fileList } = e.detail
    this.setData({ avatarList: fileList })
  },

  onFileSuccess(e) {
    const { file, response } = e.detail
    wx.showToast({
      title: '头像上传成功',
      icon: 'success'
    })
    
    // 更新当前头像
    this.setData({
      currentAvatar: file.url
    })
  },

  onFileError(e) {
    const { error } = e.detail
    console.error('头像上传失败:', error)
    
    wx.showToast({
      title: '头像上传失败',
      icon: 'none'
    })
  },

  // 切换上传方式
  switchUploadMethod(e) {
    const { method } = e.currentTarget.dataset
    this.setData({ uploadMethod: method })
  },

  // 拍照上传
  takePhoto() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      success: (res) => {
        this.uploadAvatar(res.tempFilePaths[0])
      },
      fail: (err) => {
        console.error('拍照失败:', err)
        wx.showToast({
          title: '拍照失败',
          icon: 'none'
        })
      }
    })
  },

  // 从相册选择
  chooseFromAlbum() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album'],
      success: (res) => {
        this.uploadAvatar(res.tempFilePaths[0])
      },
      fail: (err) => {
        console.error('选择图片失败:', err)
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        })
      }
    })
  },

  // Base64上传
  uploadBase64() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.convertToBase64(res.tempFilePaths[0])
      }
    })
  },

  // 转换为Base64
  convertToBase64(filePath) {
    wx.getFileSystemManager().readFile({
      filePath,
      encoding: 'base64',
      success: (res) => {
        this.uploadAvatarBase64(`data:image/jpeg;base64,${res.data}`)
      },
      fail: (err) => {
        console.error('Base64转换失败:', err)
        wx.showToast({
          title: 'Base64转换失败',
          icon: 'none'
        })
      }
    })
  },

  // 上传头像文件
  async uploadAvatar(filePath) {
    try {
      this.setData({ saving: true })
      
      const response = await api.userApi.uploadAvatar(filePath)
      if (response.code === 200) {
        this.setData({
          currentAvatar: response.data.avatar_url
        })
        
        wx.showToast({
          title: '头像更新成功',
          icon: 'success'
        })
        
        // 更新全局用户信息
        const app = getApp()
        if (app.globalData.userInfo) {
          app.globalData.userInfo.avatar = response.data.avatar_url
        }
        
      } else {
        throw new Error(response.message || '上传失败')
      }
    } catch (error) {
      console.error('头像上传失败:', error)
      wx.showToast({
        title: error.message || '上传失败',
        icon: 'none'
      })
    } finally {
      this.setData({ saving: false })
    }
  },

  // 上传Base64头像
  async uploadAvatarBase64(base64Data) {
    try {
      this.setData({ saving: true })
      
      const response = await api.userApi.uploadAvatarBase64(base64Data)
      if (response.code === 200) {
        this.setData({
          currentAvatar: response.data.avatar_url
        })
        
        wx.showToast({
          title: '头像更新成功',
          icon: 'success'
        })
        
      } else {
        throw new Error(response.message || '上传失败')
      }
    } catch (error) {
      console.error('Base64头像上传失败:', error)
      wx.showToast({
        title: error.message || '上传失败',
        icon: 'none'
      })
    } finally {
      this.setData({ saving: false })
    }
  },

  // 预览头像
  previewAvatar() {
    if (this.data.currentAvatar) {
      wx.previewImage({
        current: this.data.currentAvatar,
        urls: [this.data.currentAvatar]
      })
    }
  },

  // 删除头像
  deleteAvatar() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除当前头像吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            currentAvatar: '',
            avatarList: []
          })
          
          // 这里可以调用删除头像的API
          wx.showToast({
            title: '头像已删除',
            icon: 'success'
          })
        }
      }
    })
  },

  // 保存并返回
  saveAndBack() {
    if (this.data.currentAvatar) {
      wx.showToast({
        title: '头像已保存',
        icon: 'success',
        success: () => {
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        }
      })
    } else {
      wx.showToast({
        title: '请先上传头像',
        icon: 'none'
      })
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '个性化头像设置',
      path: '/pages/upload-avatar/upload-avatar'
    }
  }
})

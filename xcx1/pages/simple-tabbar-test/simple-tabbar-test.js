// 简单底部导航测试页面

// 内联的简单底部导航管理器
const simpleTabBarManager = {
  // 获取底部导航配置
  async getTabBarConfig() {
    try {
      return new Promise((resolve, reject) => {
        wx.request({
          url: 'https://teabuy.yizhangkj.com/api/v1/page-decoration/tabbar/config/',
          method: 'GET',
          header: {
            'Cache-Control': 'no-cache'
          },
          success: (res) => {
            if (res.data && res.data.code === 200) {
              resolve(res.data.data.config)
            } else {
              resolve(this.getDefaultConfig())
            }
          },
          fail: (error) => {
            console.error('❌ 请求失败，使用默认配置:', error)
            resolve(this.getDefaultConfig())
          }
        })
      })
    } catch (error) {
      console.error('❌ 获取配置异常，使用默认配置:', error)
      return this.getDefaultConfig()
    }
  },

  // 默认配置
  getDefaultConfig() {
    return {
      color: '#999999',
      selectedColor: '#2E7D32',
      backgroundColor: '#FFFFFF',
      borderStyle: 'black',
      position: 'bottom',
      list: [
        {
          pagePath: 'pages/index/index',
          text: '首页',
          iconPath: 'images/tabbar/home.png',
          selectedIconPath: 'images/tabbar/home-active.png'
        },
        {
          pagePath: 'pages/tea-list/tea-list',
          text: '茶地',
          iconPath: 'images/tabbar/tea.png',
          selectedIconPath: 'images/tabbar/tea-active.png'
        },
        {
          pagePath: 'pages/monitoring/monitoring',
          text: '监控',
          iconPath: 'images/tabbar/monitor.png',
          selectedIconPath: 'images/tabbar/monitor-active.png'
        },
        {
          pagePath: 'pages/analytics/analytics',
          text: '分析',
          iconPath: 'images/tabbar/chart.png',
          selectedIconPath: 'images/tabbar/chart-active.png'
        },
        {
          pagePath: 'pages/profile/profile',
          text: '我的',
          iconPath: 'images/tabbar/profile.png',
          selectedIconPath: 'images/tabbar/profile-active.png'
        }
      ]
    }
  },

  // 应用配置
  async applyTabBarConfig(config) {
    try {
      return new Promise((resolve, reject) => {
        wx.setTabBarStyle({
          color: config.color,
          selectedColor: config.selectedColor,
          backgroundColor: config.backgroundColor,
          borderStyle: config.borderStyle,
          success: () => {
            resolve(true)
          },
          fail: (error) => {
            console.error('❌ 底部导航样式应用失败:', error)
            resolve(false)
          }
        })
      })
    } catch (error) {
      console.error('❌ 应用配置异常:', error)
      return false
    }
  }
}

Page({
  data: {
    config: null,
    testLog: []
  },

  onLoad() {
    this.addLog('页面加载完成')
  },

  // 测试获取配置
  async testGetConfig() {
    this.addLog('🔄 开始获取配置...')
    
    try {
      const config = await simpleTabBarManager.getTabBarConfig()
      this.setData({ config })
      this.addLog(`✅ 配置获取成功，导航项数量: ${config.list.length}`)
      this.addLog(`🎨 主题色: ${config.selectedColor}`)
    } catch (error) {
      this.addLog(`❌ 配置获取失败: ${error.message}`)
    }
  },

  // 测试应用配置
  async testApplyConfig() {
    if (!this.data.config) {
      this.addLog('❌ 请先获取配置')
      return
    }

    this.addLog('🎨 开始应用配置...')
    
    try {
      const success = await simpleTabBarManager.applyTabBarConfig(this.data.config)
      if (success) {
        this.addLog('✅ 配置应用成功')
      } else {
        this.addLog('❌ 配置应用失败')
      }
    } catch (error) {
      this.addLog(`❌ 配置应用异常: ${error.message}`)
    }
  },

  // 测试颜色变化
  async testColorChange() {
    this.addLog('🧪 测试颜色变化...')
    
    try {
      // 临时改为红色
      const redConfig = {
        color: '#999999',
        selectedColor: '#FF0000',
        backgroundColor: '#FFFFFF',
        borderStyle: 'black'
      }
      
      await simpleTabBarManager.applyTabBarConfig(redConfig)
      this.addLog('✅ 已应用红色主题')
      
      // 2秒后恢复
      setTimeout(async () => {
        if (this.data.config) {
          await simpleTabBarManager.applyTabBarConfig(this.data.config)
          this.addLog('✅ 已恢复原始主题')
        }
      }, 2000)
      
    } catch (error) {
      this.addLog(`❌ 颜色测试失败: ${error.message}`)
    }
  },

  // 调用全局重新加载
  async testGlobalReload() {
    this.addLog('🌐 调用全局重新加载...')
    
    try {
      const app = getApp()
      const success = await app.reloadTabBar()
      
      if (success) {
        this.addLog('✅ 全局重新加载成功')
      } else {
        this.addLog('❌ 全局重新加载失败')
      }
    } catch (error) {
      this.addLog(`❌ 全局重新加载异常: ${error.message}`)
    }
  },

  // 清除日志
  clearLog() {
    this.setData({ testLog: [] })
  },

  // 添加日志
  addLog(message) {
    const log = {
      time: new Date().toLocaleTimeString(),
      message: message
    }
    
    const logs = this.data.testLog
    logs.unshift(log)
    
    // 只保留最近20条
    if (logs.length > 20) {
      logs.splice(20)
    }
    
    this.setData({ testLog: logs })
  },

  // 复制配置
  copyConfig() {
    if (!this.data.config) {
      wx.showToast({
        title: '没有配置可复制',
        icon: 'none'
      })
      return
    }

    wx.setClipboardData({
      data: JSON.stringify(this.data.config, null, 2),
      success: () => {
        wx.showToast({
          title: '配置已复制',
          icon: 'success'
        })
        this.addLog('📋 配置已复制到剪贴板')
      }
    })
  }
})

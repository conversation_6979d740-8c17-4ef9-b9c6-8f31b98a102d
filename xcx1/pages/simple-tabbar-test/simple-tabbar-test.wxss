/* 简单底部导航测试页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #2E7D32, #4CAF50);
  border-radius: 15rpx;
  color: white;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 26rpx;
  opacity: 0.9;
}

.section {
  background: white;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 25rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #2E7D32;
  padding-left: 15rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.clear-btn {
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  background: #f44336;
  color: white;
  border-radius: 8rpx;
  border: none;
}

.config-info {
  space-y: 15rpx;
}

.config-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.config-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 26rpx;
  color: #666;
  width: 150rpx;
  flex-shrink: 0;
}

.color-box {
  width: 30rpx;
  height: 30rpx;
  border-radius: 6rpx;
  border: 1rpx solid #ddd;
  margin: 0 15rpx;
  flex-shrink: 0;
}

.value {
  font-size: 26rpx;
  color: #333;
  font-family: monospace;
}

.no-config, .no-log {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 26rpx;
}

.button-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.test-btn {
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
  background: #f8f9fa;
  color: #333;
  transition: all 0.3s ease;
}

.test-btn.primary {
  background: #2E7D32;
  color: white;
}

.test-btn.warning {
  background: #FF9800;
  color: white;
}

.test-btn.info {
  background: #2196F3;
  color: white;
}

.test-btn:active {
  transform: scale(0.98);
}

.log-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.log-item {
  padding: 12rpx 15rpx;
  margin-bottom: 10rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 4rpx solid #2E7D32;
}

.log-time {
  font-size: 22rpx;
  color: #999;
  font-family: monospace;
  display: block;
  margin-bottom: 5rpx;
}

.log-message {
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
}

.help-text {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.help-text text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 15rpx;
  position: relative;
}

.help-text text::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #2E7D32;
  font-weight: bold;
}

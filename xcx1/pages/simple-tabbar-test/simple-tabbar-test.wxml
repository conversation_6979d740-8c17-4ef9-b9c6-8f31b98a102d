<!--简单底部导航测试页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">📱 简单底部导航测试</text>
    <text class="subtitle">测试底部导航基本功能</text>
  </view>

  <!-- 当前配置 -->
  <view class="section">
    <view class="section-title">当前配置</view>
    
    <view wx:if="{{config}}" class="config-info">
      <view class="config-item">
        <text class="label">背景色:</text>
        <view class="color-box" style="background-color: {{config.backgroundColor}};"></view>
        <text class="value">{{config.backgroundColor}}</text>
      </view>
      
      <view class="config-item">
        <text class="label">选中色:</text>
        <view class="color-box" style="background-color: {{config.selectedColor}};"></view>
        <text class="value">{{config.selectedColor}}</text>
      </view>
      
      <view class="config-item">
        <text class="label">导航项:</text>
        <text class="value">{{config.list.length}} 个</text>
      </view>
    </view>
    
    <view wx:else class="no-config">
      <text>暂无配置，请先获取配置</text>
    </view>
  </view>

  <!-- 测试按钮 -->
  <view class="section">
    <view class="section-title">测试操作</view>
    
    <view class="button-list">
      <button class="test-btn primary" bindtap="testGetConfig">
        🔄 获取配置
      </button>
      
      <button class="test-btn" bindtap="testApplyConfig">
        🎨 应用配置
      </button>
      
      <button class="test-btn" bindtap="testColorChange">
        🧪 测试颜色变化
      </button>
      
      <button class="test-btn warning" bindtap="testGlobalReload">
        🌐 全局重新加载
      </button>
      
      <button class="test-btn info" bindtap="copyConfig">
        📋 复制配置
      </button>
    </view>
  </view>

  <!-- 测试日志 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">测试日志</text>
      <button class="clear-btn" bindtap="clearLog">清除</button>
    </view>
    
    <view wx:if="{{testLog.length === 0}}" class="no-log">
      <text>暂无日志</text>
    </view>
    
    <view wx:else class="log-list">
      <view class="log-item" wx:for="{{testLog}}" wx:key="index">
        <text class="log-time">{{item.time}}</text>
        <text class="log-message">{{item.message}}</text>
      </view>
    </view>
  </view>

  <!-- 说明 -->
  <view class="section">
    <view class="section-title">使用说明</view>
    <view class="help-text">
      <text>1. 点击"获取配置"从后台获取最新配置</text>
      <text>2. 点击"应用配置"将配置应用到底部导航</text>
      <text>3. 点击"测试颜色变化"查看动态效果</text>
      <text>4. 使用后台测试工具修改配置后，点击"全局重新加载"</text>
    </view>
  </view>
</view>

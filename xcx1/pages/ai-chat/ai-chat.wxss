/* pages/ai-chat/ai-chat.wxss */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

/* 聊天头部 */
.chat-header {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  padding: 60rpx 30rpx 30rpx;
  color: white;
}

.header-content {
  display: flex;
  align-items: center;
}

.ai-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 20rpx;
}

.ai-info {
  flex: 1;
}

.ai-name {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 6rpx;
}

.ai-status {
  font-size: 26rpx;
  opacity: 0.8;
}

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 20rpx 0;
}

.message-list {
  padding: 0 30rpx;
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin: 0 20rpx;
}

.user-message .message-avatar {
  background: linear-gradient(135deg, #2E7D32, #1b5e20);
  color: white;
}

.ai-message .message-avatar {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.user-message .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.message-bubble {
  padding: 24rpx 30rpx;
  border-radius: 24rpx;
  margin-bottom: 8rpx;
  position: relative;
}

.ai-bubble {
  background: white;
  border-bottom-left-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-bubble {
  background: linear-gradient(135deg, #2E7D32, #1b5e20);
  color: white;
  border-bottom-right-radius: 8rpx;
}

.message-text {
  font-size: 30rpx;
  line-height: 1.5;
  word-wrap: break-word;
}

.message-time {
  font-size: 22rpx;
  color: #9ca3af;
}

.user-message .message-time {
  text-align: right;
}

/* AI正在输入动画 */
.typing-bubble {
  padding: 20rpx 30rpx;
}

.typing-indicator {
  display: flex;
  gap: 8rpx;
}

.typing-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #6b7280;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 快捷问题 */
.quick-questions {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.quick-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 20rpx;
  display: block;
}

.question-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.question-item {
  background: #f3f4f6;
  padding: 20rpx 24rpx;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
}

.question-item:active {
  background: #e5e7eb;
  border-color: #3b82f6;
}

.question-text {
  font-size: 28rpx;
  color: #374151;
}

/* 输入区域 */
.chat-input {
  background: white;
  padding: 20rpx 30rpx 40rpx;
  border-top: 1rpx solid #e5e7eb;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.input-field {
  flex: 1;
  background: #f3f4f6;
  border-radius: 25rpx;
  padding: 20rpx 30rpx;
  font-size: 30rpx;
  border: 2rpx solid transparent;
}

.input-field:focus {
  background: white;
  border-color: #3b82f6;
}

.send-button {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #e5e7eb;
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  padding: 0;
}

.send-button.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.send-button:disabled {
  opacity: 0.6;
}

.send-icon {
  font-size: 32rpx;
}

/* 功能按钮 */
.function-buttons {
  display: flex;
  gap: 20rpx;
}

.func-btn {
  flex: 1;
  background: #f3f4f6;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  border: none;
}

.func-btn:active {
  background: #e5e7eb;
}

.func-icon {
  font-size: 32rpx;
}

.func-text {
  font-size: 22rpx;
  color: #6b7280;
}

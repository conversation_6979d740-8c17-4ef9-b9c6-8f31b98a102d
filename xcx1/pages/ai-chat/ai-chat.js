// pages/ai-chat/ai-chat.js
const api = require('../../api/index')

Page({
  data: {
    messages: [],
    inputText: '',
    inputFocus: false,
    isTyping: false,
    isSending: false,
    scrollTop: 0,
    sessionId: '',
    currentTime: '',
    showQuickQuestions: true,
    quickQuestions: [
      '茶叶投资的收益率一般是多少？',
      '投资茶园有哪些风险？',
      '如何选择优质的茶园？',
      '茶叶投资的最佳时机是什么时候？',
      '投资茶园需要多少资金？',
      '茶园投资的回报周期是多长？'
    ]
  },

  onLoad(options) {
    this.initChat()
  },

  onShow() {
    this.setData({
      inputFocus: true
    })
  },

  // 初始化聊天
  initChat() {
    const now = new Date()
    this.setData({
      currentTime: this.formatTime(now),
      sessionId: this.generateSessionId()
    })
  },

  // 生成会话ID
  generateSessionId() {
    return 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  },

  // 格式化时间
  formatTime(date) {
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  },

  // 输入框内容变化
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    })
  },

  // 发送消息
  sendMessage() {
    const message = this.data.inputText.trim()
    if (!message || this.data.isSending) {
      return
    }
    // 添加用户消息
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: message,
      time: this.formatTime(new Date())
    }

    this.setData({
      messages: [...this.data.messages, userMessage],
      inputText: '',
      isSending: true,
      isTyping: true,
      showQuickQuestions: false
    })

    // 滚动到底部
    this.scrollToBottom()

    // 调用AI接口
    this.callAIChat(message)
  },

  // 调用AI聊天接口
  callAIChat(message) {
    const chatData = {
      message: message,
      session_id: this.data.sessionId
    }

    api.aiApi.chat(chatData).then(res => {
      if (res.code === 200 && res.data) {
        const aiResponse = res.data.response
        this.addAIMessage(aiResponse)
        
        // 更新会话ID
        if (res.data.session_id) {
          this.setData({
            sessionId: res.data.session_id
          })
        }
      } else {
        this.addAIMessage(this.getDefaultResponse(message))
      }
    }).catch(error => {
      console.error('❌ AI聊天API调用失败:', error)
      this.addAIMessage(this.getDefaultResponse(message))
    }).finally(() => {
      this.setData({
        isSending: false,
        isTyping: false
      })
    })
  },

  // 添加AI消息
  addAIMessage(content) {
    const aiMessage = {
      id: Date.now(),
      type: 'ai',
      content: content,
      time: this.formatTime(new Date())
    }

    this.setData({
      messages: [...this.data.messages, aiMessage]
    })

    // 滚动到底部
    this.scrollToBottom()
  },

  // 获取默认回复
  getDefaultResponse(message) {
    const responses = {
      '收益': '茶园投资的预期年收益通常在8%-15%之间，具体取决于茶叶品种、产区位置、管理水平等因素。优质单枞茶园的收益可能更高。',
      '风险': '茶叶投资相对稳健，但仍需注意市场风险。建议分散投资，选择不同产区和品种的茶园，并关注天气、政策等影响因素。',
      '选择': '选择优质茶园需要考虑：1）地理位置和气候条件；2）茶叶品种和品质；3）管理团队的专业性；4）历史收益记录；5）价格合理性。',
      '时机': '茶叶投资的最佳时机通常是春季和秋季，这时候可以实地考察茶园状况，了解茶叶品质和产量情况。',
      '资金': '茶园认购价格因品种和位置而异，一般在1-3万元/亩。建议根据自己的预算和风险承受能力选择合适的茶园。',
      '周期': '茶园投资通常是中长期投资，建议持有3-5年以上。茶树需要时间成长，品质和产量会逐年提升。'
    }

    // 根据关键词匹配回复
    for (const [keyword, response] of Object.entries(responses)) {
      if (message.includes(keyword)) {
        return response
      }
    }

    // 默认回复
    return '感谢您的咨询！我是茶园投资AI助手，可以为您提供投资建议、风险评估、收益分析等服务。请告诉我您想了解什么方面的信息？'
  },

  // 选择快捷问题
  selectQuickQuestion(e) {
    const question = e.currentTarget.dataset.question
    this.setData({
      inputText: question,
      showQuickQuestions: false
    })
    
    // 自动发送
    setTimeout(() => {
      this.sendMessage()
    }, 100)
  },

  // 切换快捷问题显示
  toggleQuickQuestions() {
    this.setData({
      showQuickQuestions: !this.data.showQuickQuestions
    })
  },

  // 清空聊天
  clearChat() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有聊天记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            messages: [],
            sessionId: this.generateSessionId(),
            showQuickQuestions: true
          })
        }
      }
    })
  },

  // 查看投资建议
  viewInvestmentAdvice() {
    wx.navigateTo({
      url: '/pages/investment-advice/investment-advice'
    })
  },

  // 滚动到底部
  scrollToBottom() {
    setTimeout(() => {
      const query = wx.createSelectorQuery()
      query.select('.message-list').boundingClientRect()
      query.exec((res) => {
        if (res[0]) {
          this.setData({
            scrollTop: res[0].height
          })
        }
      })
    }, 100)
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'AI投资顾问 - 专业茶叶投资咨询',
      path: '/pages/ai-chat/ai-chat',
      imageUrl: '/images/share-ai-chat.png'
    }
  },

  onShareTimeline() {
    return {
      title: 'AI投资顾问 - 专业茶叶投资咨询',
      query: '',
      imageUrl: '/images/share-ai-chat.png'
    }
  }
})

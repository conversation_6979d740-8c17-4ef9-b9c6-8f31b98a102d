<!--pages/ai-chat/ai-chat.wxml-->
<view class="container">
  <!-- 聊天头部 -->
  <view class="chat-header">
    <view class="header-content">
      <view class="ai-avatar">🤖</view>
      <view class="ai-info">
        <text class="ai-name">AI投资顾问</text>
        <text class="ai-status">{{isTyping ? '正在输入...' : '在线'}}</text>
      </view>
    </view>
  </view>

  <!-- 聊天消息列表 -->
  <scroll-view class="chat-messages" scroll-y="{{true}}" scroll-top="{{scrollTop}}" scroll-with-animation="{{true}}">
    <view class="message-list">
      <!-- 欢迎消息 -->
      <view class="message-item ai-message">
        <view class="message-avatar">🤖</view>
        <view class="message-content">
          <view class="message-bubble ai-bubble">
            <text class="message-text">您好！我是AI投资顾问，可以为您提供茶叶投资相关的专业建议。请问有什么可以帮助您的吗？</text>
          </view>
          <text class="message-time">{{currentTime}}</text>
        </view>
      </view>

      <!-- 聊天消息 -->
      <view class="message-item {{item.type === 'user' ? 'user-message' : 'ai-message'}}" wx:for="{{messages}}" wx:key="id">
        <view class="message-avatar">{{item.type === 'user' ? '👤' : '🤖'}}</view>
        <view class="message-content">
          <view class="message-bubble {{item.type === 'user' ? 'user-bubble' : 'ai-bubble'}}">
            <text class="message-text">{{item.content}}</text>
          </view>
          <text class="message-time">{{item.time}}</text>
        </view>
      </view>

      <!-- AI正在输入 -->
      <view wx:if="{{isTyping}}" class="message-item ai-message">
        <view class="message-avatar">🤖</view>
        <view class="message-content">
          <view class="message-bubble ai-bubble typing-bubble">
            <view class="typing-indicator">
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 快捷问题 -->
  <view wx:if="{{showQuickQuestions}}" class="quick-questions">
    <text class="quick-title">常见问题</text>
    <view class="question-list">
      <view class="question-item" wx:for="{{quickQuestions}}" wx:key="index" bindtap="selectQuickQuestion" data-question="{{item}}">
        <text class="question-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 输入区域 -->
  <view class="chat-input">
    <view class="input-container">
      <input 
        class="input-field" 
        type="text" 
        placeholder="请输入您的问题..." 
        value="{{inputText}}"
        bindinput="onInputChange"
        bindconfirm="sendMessage"
        confirm-type="send"
        focus="{{inputFocus}}"
      />
      <button class="send-button {{inputText.trim() ? 'active' : ''}}" bindtap="sendMessage" disabled="{{!inputText.trim() || isSending}}">
        <text class="send-icon">{{isSending ? '⏳' : '📤'}}</text>
      </button>
    </view>
    
    <!-- 功能按钮 -->
    <view class="function-buttons">
      <button class="func-btn" bindtap="toggleQuickQuestions">
        <text class="func-icon">❓</text>
        <text class="func-text">常见问题</text>
      </button>
      <button class="func-btn" bindtap="clearChat">
        <text class="func-icon">🗑️</text>
        <text class="func-text">清空聊天</text>
      </button>
      <button class="func-btn" bindtap="viewInvestmentAdvice">
        <text class="func-icon">🎯</text>
        <text class="func-text">投资建议</text>
      </button>
    </view>
  </view>
</view>

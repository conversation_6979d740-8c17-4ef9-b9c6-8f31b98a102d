// 订单页面
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    // 筛选状态
    activeTab: 'all',
    tabCounts: {
      all: 0,
      pending_payment: 0,
      paid: 0,
      processing: 0,
      completed: 0,
      cancelled: 0
    },
    
    // 订单列表
    ordersList: [],
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
    
    // 取消订单
    showCancelModal: false,
    cancelOrderId: '',
    cancelReason: '',
    cancelReasons: [
      { label: '不想要了', value: 'no_need' },
      { label: '价格太贵', value: 'too_expensive' },
      { label: '找到更好的', value: 'found_better' },
      { label: '资金问题', value: 'financial_issue' },
      { label: '其他原因', value: 'other' }
    ]
  },

  // 页面加载
  onLoad(options) {
    // 处理页面参数
    if (options.tab) {
      this.setData({ activeTab: options.tab })
    }
    
    this.checkLoginAndLoad()
  },

  // 页面显示
  onShow() {
    if (app.globalData.isLogin) {
      this.refreshData()
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 上拉加载
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  // 检查登录状态并加载数据
  checkLoginAndLoad() {
    if (!app.checkLogin()) {
      return
    }
    
    this.loadOrdersList()
  },

  // 刷新数据
  async refreshData() {
    this.setData({
      currentPage: 1,
      ordersList: [],
      hasMore: true
    })
    
    await this.loadOrdersList()
  },

  // 加载订单列表
  async loadOrdersList() {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    try {
      const params = {
        page: this.data.currentPage,
        page_size: this.data.pageSize,
        status: this.data.activeTab === 'all' ? '' : this.data.activeTab
      }
      
      // 调用订单列表API
      const res = await api.orderApi.getList(params)
      if (res.code === 200) {
        const results = res.data.results || res.data || []
        const newList = this.data.currentPage === 1 ? results : [...this.data.ordersList, ...results]

        this.setData({
          ordersList: newList,
          hasMore: res.data.next !== null,
          currentPage: this.data.currentPage + 1
        })

        // 更新标签计数
        this.updateTabCounts(newList)
      } else {
        wx.showToast({
          title: res.message || '获取订单失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('❌ 加载订单列表失败:', error)

      // 显示错误提示
      wx.showToast({
        title: '加载订单失败，使用测试数据',
        icon: 'none',
        duration: 2000
      })

      // 如果是首次加载且没有数据，使用测试数据
      if (this.data.currentPage === 1 && this.data.ordersList.length === 0) {
        this.loadTestData()
      }
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载测试数据（临时方法）
  loadTestData() {
    const testOrders = [
      {
        id: 175,
        order_id: 'ORD20250713235347999C944A',
        user: { id: 36, nickname: 'coco' },
        tea_field: {
          id: 40,
          name: '精品地块A005',
          location: '广东省潮州市凤凰镇',
          image_url: '/images/tea-field-default.jpg',
          description: '精品地块A005位于广东省潮州市凤凰镇，素有"乌龙茶之乡"美誉，茶叶品质优良，香气浓郁。',
          tea_variety: '凤凰单丛',
          expected_return: '8-12%'
        },
        quantity: 0.5,
        unit_price: 15000.00,
        total_amount: 7500.00,
        status: 'pending_payment',
        purchase_type: 'annual',
        service_package: 'basic',
        contact_name: 'coco',
        contact_phone: '13800138000',
        created_at: '2025-07-13T23:53:47.080088Z',
        paid_at: null,
        completed_at: null
      }
    ]

    this.setData({
      ordersList: testOrders,
      hasMore: false
    })

    this.updateTabCounts(testOrders)
  },

  // 更新标签计数
  updateTabCounts(list) {
    const counts = {
      all: list.length,
      pending_payment: list.filter(item => item.status === 'pending_payment').length,
      paid: list.filter(item => item.status === 'paid').length,
      processing: list.filter(item => item.status === 'processing').length,
      completed: list.filter(item => item.status === 'completed').length,
      cancelled: list.filter(item => item.status === 'cancelled').length
    }

    this.setData({ tabCounts: counts })
  },

  // 切换标签
  switchTab(e) {
    const { tab } = e.currentTarget.dataset
    if (tab === this.data.activeTab) return
    
    this.setData({ activeTab: tab })
    this.refreshData()
  },

  // 加载更多
  loadMore() {
    this.loadOrdersList()
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const { id } = e.currentTarget.dataset
    const order = this.data.ordersList.find(item => item.id == id)
    const orderId = order ? order.order_id : id
    this.navigateToOrderDetail(orderId)
  },

  // 支付订单
  payOrder(e) {
    const { id } = e.currentTarget.dataset
    
    wx.showModal({
      title: '确认支付',
      content: '确定要支付这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          this.processPayment(id)
        }
      }
    })
  },

  // 处理支付
  async processPayment(orderId) {
    try {
      wx.showLoading({
        title: '支付中...'
      })
      
      // 调用支付API
      const res = await api.orderApi.pay(orderId)

      if (res.code === 200) {
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        })

        // 刷新订单列表
        this.refreshData()
      } else {
        throw new Error(res.message || '支付失败')
      }
    } catch (error) {
      console.error('支付失败:', error)
      wx.showToast({
        title: '支付失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 取消订单
  cancelOrder(e) {
    const { id } = e.currentTarget.dataset
    this.setData({
      showCancelModal: true,
      cancelOrderId: id,
      cancelReason: ''
    })
  },

  // 隐藏取消弹窗
  hideCancelModal() {
    this.setData({
      showCancelModal: false,
      cancelOrderId: '',
      cancelReason: ''
    })
  },

  // 选择取消原因
  selectCancelReason(e) {
    const { reason } = e.currentTarget.dataset
    this.setData({ cancelReason: reason })
  },

  // 确认取消订单
  async confirmCancelOrder() {
    try {
      wx.showLoading({
        title: '取消中...'
      })
      
      // 调用取消订单API
      const res = await api.orderApi.cancel(this.data.cancelOrderId, {
        reason: this.data.cancelReason
      })

      if (res.code === 200) {
        wx.showToast({
          title: '订单已取消',
          icon: 'success'
        })

        this.hideCancelModal()
        this.refreshData()
      } else {
        throw new Error(res.message || '取消失败')
      }
    } catch (error) {
      console.error('取消订单失败:', error)
      wx.showToast({
        title: '取消失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 联系客服
  contactService(e) {
    const { id } = e.currentTarget.dataset
    wx.makePhoneCall({
      phoneNumber: '************'
    })
  },

  // 跟踪订单
  trackOrder(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/order-track/order-track?id=${id}`
    })
  },

  // 查看监控
  viewMonitoring(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/monitoring/monitoring?teaId=${id}`
    })
  },

  // 查看合同
  viewContract(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/contract-detail/contract-detail?orderId=${id}`
    })
  },

  // 评价订单
  evaluateOrder(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/order-evaluate/order-evaluate?id=${id}`
    })
  },

  // 再次认购
  repurchaseOrder(e) {
    const { id } = e.currentTarget.dataset
    if (!id || id === 'undefined') {
      console.error('❌ 茶地ID无效，无法认购')
      wx.showToast({
        title: '茶地信息错误',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/tea-detail/tea-detail?id=${id}&action=purchase`
    })
  },

  // 删除订单
  deleteOrder(e) {
    const { id } = e.currentTarget.dataset
    
    wx.showModal({
      title: '删除订单',
      content: '确定要删除这个订单吗？删除后无法恢复。',
      success: (res) => {
        if (res.confirm) {
          this.processDeleteOrder(id)
        }
      }
    })
  },

  // 处理删除订单
  async processDeleteOrder(orderId) {
    try {
      // 这里应该调用删除订单API
      // await api.orderApi.delete(orderId)
      
      wx.showToast({
        title: '订单已删除',
        icon: 'success'
      })
      
      // 从列表中移除
      const ordersList = this.data.ordersList.filter(order => order.id !== orderId)
      this.setData({ ordersList })
      this.updateTabCounts(ordersList)
    } catch (error) {
      console.error('删除订单失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  },

  // 浏览茶地
  browseTeaFields() {
    wx.switchTab({
      url: '/pages/tea-list/tea-list'
    })
  },

  // 获取订单状态显示文本
  getStatusText(status) {
    const statusMap = {
      'pending_payment': '待支付',
      'paid': '已支付',
      'processing': '处理中',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return statusMap[status] || status
  },

  // 获取订单状态样式类
  getStatusClass(status) {
    const classMap = {
      'pending_payment': 'status-pending',
      'paid': 'status-paid',
      'processing': 'status-processing',
      'completed': 'status-completed',
      'cancelled': 'status-cancelled'
    }
    return classMap[status] || 'status-default'
  },

  // 获取订单操作按钮配置
  getOrderActions(order) {
    const actions = []

    switch (order.status) {
      case 'pending_payment':
        actions.push({
          text: '立即支付',
          type: 'primary',
          action: 'pay',
          order_id: order.order_id
        })
        actions.push({
          text: '取消订单',
          type: 'default',
          action: 'cancel',
          order_id: order.order_id
        })
        break

      case 'paid':
        actions.push({
          text: '查看详情',
          type: 'default',
          action: 'detail',
          order_id: order.order_id
        })
        break

      case 'processing':
        actions.push({
          text: '查看进度',
          type: 'default',
          action: 'progress',
          order_id: order.order_id
        })
        break

      case 'completed':
        actions.push({
          text: '查看详情',
          type: 'default',
          action: 'detail',
          order_id: order.order_id
        })
        actions.push({
          text: '再次购买',
          type: 'primary',
          action: 'rebuy',
          tea_field_id: order.tea_field?.id
        })
        break

      case 'cancelled':
        actions.push({
          text: '再次购买',
          type: 'primary',
          action: 'rebuy',
          tea_field_id: order.tea_field?.id
        })
        break
    }

    return actions
  },

  // 获取空状态描述
  getEmptyDesc() {
    const { activeTab } = this.data
    const descMap = {
      all: '您还没有任何订单',
      pending_payment: '您没有待支付的订单',
      paid: '您没有已支付的订单',
      processing: '您没有处理中的订单',
      completed: '您没有已完成的订单',
      cancelled: '您没有已取消的订单'
    }
    return descMap[activeTab] || '暂无相关订单'
  },

  // 处理订单操作
  handleOrderAction(e) {
    const dataset = e.currentTarget.dataset
    const { action, orderId, teaFieldId } = dataset
    // Debug log removed
    // 确保orderId是字符串
    const orderIdStr = String(orderId || '')

    switch (action) {
      case 'pay':
        this.payOrder(orderIdStr)
        break
      case 'cancel':
        this.cancelOrder(orderIdStr)
        break
      case 'detail':
        this.navigateToOrderDetail(orderIdStr)
        break
      case 'progress':
        this.viewOrderProgress(orderIdStr)
        break
      case 'rebuy':
        this.rebuyOrder(teaFieldId)
        break
      default:
    }
  },

  // 支付订单
  payOrder(orderId) {
    wx.navigateTo({
      url: `/pages/payment/payment?orderId=${orderId}`
    })
  },

  // 取消订单
  cancelOrder(orderId) {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          this.performCancelOrder(orderId)
        }
      }
    })
  },

  // 执行取消订单
  performCancelOrder(orderId) {
    wx.showLoading({ title: '取消中...' })

    // 调用取消订单API
    api.orderApi.cancel(orderId).then(res => {
      wx.hideLoading()
      wx.showToast({
        title: '订单已取消',
        icon: 'success'
      })

      // 刷新订单列表
      this.refreshData()

    }).catch(error => {
      wx.hideLoading()
      console.error('❌ 取消订单失败:', error)
      wx.showToast({
        title: '取消失败',
        icon: 'none'
      })
    })
  },

  // 跳转到订单详情页面
  navigateToOrderDetail(orderId) {
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?orderId=${orderId}`
    })
  },

  // 查看订单进度
  viewOrderProgress(orderId) {
    wx.navigateTo({
      url: `/pages/order-progress/order-progress?orderId=${orderId}`
    })
  },

  // 再次购买
  rebuyOrder(teaFieldId) {
    if (!teaFieldId) {
      wx.showToast({
        title: '茶地信息不存在',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/purchase/purchase?teaId=${teaFieldId}&area=1`
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止按钮点击时触发父级事件
  }
})

/* API连接测试页面样式 */
@import '/styles/theme.wxss';
@import '/styles/micro-interactions.wxss';

.container {
  padding: 32rpx;
  background: var(--color-bg-secondary);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 32rpx;
}

.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: 16rpx;
}

.page-desc {
  font-size: 26rpx;
  color: var(--color-text-secondary);
  display: block;
}

/* 控制区域 */
.control-section {
  background: var(--color-bg-primary);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: var(--shadow-sm);
}

.btn-group {
  display: flex;
  gap: 16rpx;
}

.test-btn, .clear-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
}

.test-btn {
  background: var(--color-success);
  color: white;
}

.clear-btn {
  background: var(--color-text-secondary);
  color: white;
}

.test-btn:active, .clear-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 加载状态 */
.loading-section {
  background: var(--color-bg-primary);
  border-radius: 16rpx;
  padding: 48rpx;
  margin-bottom: 24rpx;
  text-align: center;
  box-shadow: var(--shadow-sm);
}

.loading-spinner {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid var(--color-border-secondary);
  border-top: 4rpx solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: var(--color-text-secondary);
}

/* 测试结果 */
.results-section {
  background: var(--color-bg-primary);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: var(--shadow-sm);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--color-text-primary);
  margin-bottom: 24rpx;
  display: block;
}

.result-item {
  border: 2rpx solid var(--color-border-secondary);
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-header {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: var(--color-bg-secondary);
}

.status-icon {
  margin-right: 16rpx;
  font-size: 28rpx;
}

.status-icon.success {
  color: var(--color-success);
}

.status-icon.error {
  color: var(--color-error);
}

.result-info {
  flex: 1;
}

.test-name {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: 6rpx;
}

.test-message {
  font-size: 24rpx;
  color: var(--color-text-secondary);
  display: block;
  margin-bottom: 6rpx;
}

.test-time {
  font-size: 20rpx;
  color: var(--color-text-tertiary);
  display: block;
}

.result-actions {
  margin-left: 16rpx;
}

.retry-btn {
  padding: 12rpx 20rpx;
  background: var(--color-primary);
  color: white;
  border-radius: 6rpx;
  font-size: 22rpx;
}

.retry-btn:active {
  opacity: 0.8;
}

/* 结果详情 */
.result-detail, .error-detail {
  padding: 24rpx;
  border-top: 1rpx solid var(--color-border-secondary);
}

.detail-title, .error-title {
  font-size: 24rpx;
  font-weight: 500;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: 16rpx;
}

.data-preview {
  background: var(--color-bg-secondary);
  padding: 16rpx;
  border-radius: 6rpx;
  border-left: 3rpx solid var(--color-success);
}

.data-text {
  font-size: 24rpx;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: 8rpx;
}

.data-code {
  font-size: 20rpx;
  color: var(--color-text-secondary);
  display: block;
}

.error-content {
  background: #fef2f2;
  padding: 16rpx;
  border-radius: 6rpx;
  border-left: 3rpx solid var(--color-error);
  position: relative;
}

.error-message {
  font-size: 24rpx;
  color: #991b1b;
  display: block;
  margin-bottom: 16rpx;
}

.copy-error-btn {
  padding: 8rpx 16rpx;
  background: var(--color-error);
  color: white;
  border-radius: 4rpx;
  font-size: 20rpx;
  display: inline-block;
}

.copy-error-btn:active {
  opacity: 0.8;
}

/* 帮助信息 */
.help-section {
  background: var(--color-bg-primary);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: var(--shadow-sm);
}

.help-item {
  padding: 16rpx 0;
  border-bottom: 1rpx solid var(--color-border-secondary);
}

.help-item:last-child {
  border-bottom: none;
}

.help-title {
  font-size: 26rpx;
  font-weight: 500;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: 8rpx;
}

.help-desc {
  font-size: 24rpx;
  color: var(--color-text-secondary);
  display: block;
}



.action-section {
  padding: 32rpx 0;
  text-align: center;
}

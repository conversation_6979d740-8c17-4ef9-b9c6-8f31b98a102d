<!-- API连接测试页面 -->
<view class="container">
  <view class="header">
    <text class="page-title">🔧 API连接测试</text>
    <text class="page-desc">检测小程序与后端服务的连接状态</text>
  </view>

  <!-- 测试控制 -->
  <view class="control-section">
    <view class="btn-group">
      <view class="test-btn" bindtap="runTests">
        <text class="btn-text">{{isLoading ? '测试中...' : '重新测试'}}</text>
      </view>
      <view class="clear-btn" bindtap="clearResults">
        <text class="btn-text">清除结果</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在测试API连接...</text>
  </view>

  <!-- 测试结果 -->
  <view class="results-section" wx:if="{{testResults.length > 0}}">
    <text class="section-title">📊 测试结果</text>

    <view class="result-item" wx:for="{{testResults}}" wx:key="name">
      <view class="result-header">
        <view class="status-icon {{item.status}}">
          <text>{{item.status === 'success' ? '✅' : '❌'}}</text>
        </view>
        <view class="result-info">
          <text class="test-name">{{item.name}}</text>
          <text class="test-message">{{item.message}}</text>
          <text class="test-time">{{item.timestamp}}</text>
        </view>
        <view class="result-actions">
          <view class="retry-btn" bindtap="retryTest" data-index="{{index}}">
            <text>重试</text>
          </view>
        </view>
      </view>

      <!-- 成功结果详情 -->
      <view class="result-detail" wx:if="{{item.status === 'success' && item.data}}">
        <text class="detail-title">响应数据:</text>
        <view class="data-preview">
          <text class="data-text">{{item.data.message || '请求成功'}}</text>
          <text class="data-code" wx:if="{{item.data.code}}">状态码: {{item.data.code}}</text>
        </view>
      </view>

      <!-- 错误详情 -->
      <view class="error-detail" wx:if="{{item.status === 'error'}}">
        <text class="error-title">错误信息:</text>
        <view class="error-content">
          <text class="error-message">{{item.error.message || item.message}}</text>
          <view class="copy-error-btn" bindtap="copyError" data-index="{{index}}" wx:if="{{item.error}}">
            <text>复制错误</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 帮助信息 -->
  <view class="help-section">
    <text class="section-title">💡 故障排除</text>
    <view class="help-item">
      <text class="help-title">1. 网络连接问题</text>
      <text class="help-desc">检查设备网络连接，确保能访问外网</text>
    </view>
    <view class="help-item">
      <text class="help-title">2. 域名校验问题</text>
      <text class="help-desc">在开发者工具中关闭域名校验</text>
    </view>
    <view class="help-item">
      <text class="help-title">3. 服务器问题</text>
      <text class="help-desc">联系技术支持检查后端服务状态</text>
    </view>
  </view>

  <view class="action-section">
    <enhanced-button
      text="返回首页"
      type="primary"
      size="medium"
      prefix-icon="arrow-left"
      bindtap="goHome">
    </enhanced-button>
  </view>
</view>

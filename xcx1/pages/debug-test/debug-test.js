/**
 * 调试测试页面 - API连接测试
 */
Page({
  data: {
    testResults: [],
    isLoading: false,
    jsEngine: '检测中...',
    networkType: '检测中...',
    loginStatus: '检测中...'
  },

  onLoad(options) {
    this.checkSystemInfo()
    // 延迟执行测试，确保页面完全加载
    setTimeout(() => {
      this.runTests()
    }, 1000)
  },

  // 检查系统信息
  checkSystemInfo() {
    // 检查JavaScript引擎
    const jsEngine = typeof regeneratorRuntime !== 'undefined' ? 'ES6+支持' : 'ES5兼容模式'

    // 检查网络状态
    wx.getNetworkType({
      success: (res) => {
        this.setData({ networkType: res.networkType })
      },
      fail: () => {
        this.setData({ networkType: '检测失败' })
      }
    })

    // 检查登录状态
    const app = getApp()
    const loginStatus = app.globalData.isLogin ? '已登录' : '未登录'

    this.setData({
      jsEngine,
      loginStatus
    })
  },

  // 清除测试结果
  clearResults() {
    this.setData({ testResults: [] })
  },

  runTests() {
    this.setData({ isLoading: true })

    const tests = [
      {
        name: '网络连接测试',
        test: () => this.testNetworkConnection()
      },
      {
        name: '系统配置API',
        test: () => this.testSystemConfig()
      },
      {
        name: '轮播图API',
        test: () => this.testBanners()
      },
      {
        name: '茶地分类API',
        test: () => this.testTeaCategories()
      },
      {
        name: '茶地列表API',
        test: () => this.testTeaFieldList()
      },
      {
        name: '公司信息API',
        test: () => this.testCompanyInfo()
      },
      {
        name: '数据概览API',
        test: () => this.testOverview()
      }
    ]

    const results = []
    let completedTests = 0

    const checkComplete = () => {
      if (completedTests === tests.length) {
        this.setData({
          testResults: results,
          isLoading: false
        })
      }
    }

    tests.forEach((test, index) => {
      const startTime = Date.now()

      test.test().then(result => {
        const endTime = Date.now()
        results[index] = {
          name: test.name,
          status: 'success',
          message: `测试通过 (${endTime - startTime}ms)`,
          data: result,
          timestamp: new Date().toLocaleTimeString()
        }
        completedTests++
        checkComplete()
      }).catch(error => {
        console.error(`❌ ${test.name} 测试失败:`, error)
        results[index] = {
          name: test.name,
          status: 'error',
          message: error.message || '测试失败',
          error: error,
          timestamp: new Date().toLocaleTimeString()
        }
        completedTests++
        checkComplete()
      })
    })
  },

  testNetworkConnection() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://teabuy.yizhangkj.com/api/v1/system/config/',
        method: 'GET',
        success: (res) => {
          if (res.statusCode === 200) {
            resolve({ message: '网络连接正常', statusCode: res.statusCode })
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`))
          }
        },
        fail: (err) => {
          reject(new Error(`网络请求失败: ${err.errMsg}`))
        }
      })
    })
  },

  testSystemConfig() {
    return new Promise((resolve, reject) => {
      const api = require('../../api/index.js')
      api.commonApi.getSystemConfig().then(res => {
        resolve({
          message: '系统配置获取成功',
          data: res.data,
          code: res.code
        })
      }).catch(error => {
        reject(new Error(`系统配置API失败: ${error.message || error}`))
      })
    })
  },

  testBanners() {
    return new Promise((resolve, reject) => {
      const api = require('../../api/index.js')
      api.commonApi.getBanners().then(res => {
        resolve({
          message: `轮播图获取成功，共${res.data?.length || 0}个`,
          data: res.data,
          code: res.code
        })
      }).catch(error => {
        reject(new Error(`轮播图API失败: ${error.message || error}`))
      })
    })
  },

  testTeaCategories() {
    return new Promise((resolve, reject) => {
      const api = require('../../api/index.js')
      api.teaFieldApi.getCategories().then(res => {
        resolve({
          message: `茶地分类获取成功，共${res.data?.length || 0}个`,
          data: res.data,
          code: res.code
        })
      }).catch(error => {
        reject(new Error(`茶地分类API失败: ${error.message || error}`))
      })
    })
  },

  // 测试茶地列表API
  testTeaFieldList() {
    return new Promise((resolve, reject) => {
      const api = require('../../api/index.js')
      api.teaFieldApi.getList({ page_size: 5 }).then(res => {
        resolve({
          message: `茶地列表获取成功，共${res.data?.results?.length || res.data?.length || 0}个`,
          data: res.data,
          code: res.code
        })
      }).catch(error => {
        reject(new Error(`茶地列表API失败: ${error.message || error}`))
      })
    })
  },

  // 测试公司信息API
  testCompanyInfo() {
    return new Promise((resolve, reject) => {
      const api = require('../../api/index.js')
      api.commonApi.getCompanyInfo().then(res => {
        resolve({
          message: '公司信息获取成功',
          data: res.data,
          code: res.code
        })
      }).catch(error => {
        reject(new Error(`公司信息API失败: ${error.message || error}`))
      })
    })
  },

  // 测试数据概览API
  testOverview() {
    return new Promise((resolve, reject) => {
      const api = require('../../api/index.js')
      api.commonApi.getOverview().then(res => {
        resolve({
          message: '数据概览获取成功',
          data: res.data,
          code: res.code
        })
      }).catch(error => {
        reject(new Error(`数据概览API失败: ${error.message || error}`))
      })
    })
  },

  retryTest(e) {
    const index = e.currentTarget.dataset.index
    const test = this.data.testResults[index]
    this.runTests()
  },

  testAnimation() {
    wx.showToast({
      title: '动效测试',
      icon: 'success'
    })
  },

  goHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})

// 简单自定义底部导航测试页面
Page({
  data: {
    // 简单的测试配置
    customTabBarConfig: {
      color: '#999999',
      selectedColor: '#2E7D32',
      backgroundColor: '#FFFFFF',
      borderStyle: 'black',
      list: [
        {
          pagePath: 'pages/index/index',
          text: '首页',
          iconPath: 'images/tabbar/home.png',
          selectedIconPath: 'images/tabbar/home-active.png'
        },
        {
          pagePath: 'pages/tea-list/tea-list',
          text: '茶地',
          iconPath: 'images/tabbar/tea.png',
          selectedIconPath: 'images/tabbar/tea-active.png'
        },
        {
          pagePath: 'pages/profile/profile',
          text: '我的',
          iconPath: 'images/tabbar/profile.png',
          selectedIconPath: 'images/tabbar/profile-active.png'
        }
      ]
    },
    currentTabIndex: 0,
    testStatus: '准备测试...'
  },

  onLoad() {
    this.setData({
      testStatus: '页面加载完成'
    })
  },

  onShow() {
    this.setData({
      testStatus: '页面显示完成'
    })
  },

  // 自定义导航切换事件
  onCustomTabChange(e) {
    const { index, item, pagePath } = e.detail
    this.setData({
      currentTabIndex: index,
      testStatus: `切换到: ${item.text}`
    })
    
    // 简单的页面跳转测试
    if (pagePath === 'pages/index/index') {
      wx.switchTab({
        url: '/pages/index/index',
        success: () => {
        },
        fail: (error) => {
          console.error('❌ 跳转失败:', error)
          // 降级处理
          wx.reLaunch({
            url: '/pages/index/index'
          })
        }
      })
    }
  },

  // 测试组件功能
  testComponent() {
    const customTabBar = this.selectComponent('#custom-tabbar')
    if (customTabBar) {
      this.setData({
        testStatus: '组件测试: 找到自定义导航组件'
      })
      
      // 测试徽章功能
      customTabBar.setBadge(1, '测试')
      
      setTimeout(() => {
        customTabBar.removeBadge(1)
        this.setData({
          testStatus: '组件测试: 徽章功能正常'
        })
      }, 2000)
    } else {
      this.setData({
        testStatus: '组件测试: 未找到自定义导航组件'
      })
    }
  },

  // 重新加载配置
  reloadConfig() {
    this.setData({
      testStatus: '重新加载配置...'
    })
    
    // 模拟从API加载配置
    setTimeout(() => {
      this.setData({
        testStatus: '配置重新加载完成'
      })
    }, 1000)
  }
})

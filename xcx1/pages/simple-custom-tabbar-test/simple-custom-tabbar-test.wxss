/* 简单自定义底部导航测试页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx; /* 为自定义导航留出空间 */
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #2E7D32, #4CAF50);
  border-radius: 15rpx;
  color: white;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 通用section样式 */
.status-section,
.config-section,
.items-section,
.test-section,
.help-section {
  background: white;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 25rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #2E7D32;
  padding-left: 15rpx;
}

/* 状态显示 */
.status-display {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 6rpx solid #2E7D32;
}

.status-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 配置显示 */
.config-display {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.config-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.config-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 26rpx;
  color: #666;
  width: 180rpx;
  flex-shrink: 0;
}

.color-preview {
  width: 30rpx;
  height: 30rpx;
  border-radius: 6rpx;
  border: 1rpx solid #ddd;
  margin: 0 15rpx;
  flex-shrink: 0;
}

.value {
  font-size: 26rpx;
  color: #333;
  font-family: monospace;
}

/* 导航项列表 */
.items-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.item {
  display: flex;
  align-items: center;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 4rpx solid #2E7D32;
}

.item-icon {
  font-size: 24rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.item-path {
  font-size: 22rpx;
  color: #666;
  font-family: monospace;
}

/* 测试按钮 */
.button-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
}

.test-btn {
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 26rpx;
  border: none;
  background: #f8f9fa;
  color: #333;
  transition: all 0.3s ease;
}

.test-btn.primary {
  background: #2E7D32;
  color: white;
}

.test-btn:active {
  transform: scale(0.95);
}

/* 帮助说明 */
.help-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.help-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 15rpx;
  position: relative;
}

.help-item::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #2E7D32;
  font-weight: bold;
}

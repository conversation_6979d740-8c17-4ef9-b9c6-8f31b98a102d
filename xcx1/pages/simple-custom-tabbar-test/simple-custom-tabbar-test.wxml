<!--简单自定义底部导航测试页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">🧪 简单自定义导航测试</text>
    <text class="subtitle">验证自定义底部导航组件基础功能</text>
  </view>

  <!-- 测试状态 -->
  <view class="status-section">
    <view class="section-title">📊 测试状态</view>
    <view class="status-display">
      <text class="status-text">{{testStatus}}</text>
    </view>
  </view>

  <!-- 配置信息 -->
  <view class="config-section">
    <view class="section-title">⚙️ 当前配置</view>
    
    <view class="config-display">
      <view class="config-item">
        <text class="label">导航项数量:</text>
        <text class="value">{{customTabBarConfig.list.length}}</text>
      </view>
      
      <view class="config-item">
        <text class="label">当前选中:</text>
        <text class="value">第 {{currentTabIndex + 1}} 项</text>
      </view>
      
      <view class="config-item">
        <text class="label">背景色:</text>
        <view class="color-preview" style="background-color: {{customTabBarConfig.backgroundColor}};"></view>
        <text class="value">{{customTabBarConfig.backgroundColor}}</text>
      </view>
      
      <view class="config-item">
        <text class="label">选中色:</text>
        <view class="color-preview" style="background-color: {{customTabBarConfig.selectedColor}};"></view>
        <text class="value">{{customTabBarConfig.selectedColor}}</text>
      </view>
    </view>
  </view>

  <!-- 导航项列表 -->
  <view class="items-section">
    <view class="section-title">📋 导航项列表</view>
    
    <view class="items-list">
      <view class="item" wx:for="{{customTabBarConfig.list}}" wx:key="pagePath">
        <view class="item-icon">{{index === currentTabIndex ? '✅' : '⭕'}}</view>
        <view class="item-content">
          <text class="item-title">{{item.text}}</text>
          <text class="item-path">{{item.pagePath}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 测试按钮 -->
  <view class="test-section">
    <view class="section-title">🧪 测试功能</view>
    
    <view class="button-grid">
      <button class="test-btn primary" bindtap="testComponent">
        🔍 测试组件
      </button>
      
      <button class="test-btn" bindtap="reloadConfig">
        🔄 重新加载
      </button>
    </view>
  </view>

  <!-- 说明信息 -->
  <view class="help-section">
    <view class="section-title">💡 测试说明</view>
    <view class="help-content">
      <text class="help-item">1. 这是一个简化的自定义导航测试页面</text>
      <text class="help-item">2. 使用静态配置，避免API依赖</text>
      <text class="help-item">3. 验证组件基础功能是否正常</text>
      <text class="help-item">4. 点击底部导航测试切换功能</text>
    </view>
  </view>
</view>

<!-- 自定义底部导航组件 -->
<custom-tabbar 
  id="custom-tabbar"
  config="{{customTabBarConfig}}"
  current="{{currentTabIndex}}"
  bind:tabchange="onCustomTabChange"
/>

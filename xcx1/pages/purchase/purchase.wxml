<!-- 认购流程页面 -->
<page-transition id="page-transition" animation-type="slide-left">
  <view class="container">
  <!-- 进度指示器 -->
  <view class="progress-section">
    <view class="progress-steps">
      <view class="step {{currentStep >= 1 ? 'active' : ''}} {{currentStep > 1 ? 'completed' : ''}}">
        <view class="step-number">1</view>
        <text class="step-text">选择面积</text>
      </view>
      <view class="step-line {{currentStep > 1 ? 'completed' : ''}}"></view>
      <view class="step {{currentStep >= 2 ? 'active' : ''}} {{currentStep > 2 ? 'completed' : ''}}">
        <view class="step-number">2</view>
        <text class="step-text">确认信息</text>
      </view>
      <view class="step-line {{currentStep > 2 ? 'completed' : ''}}"></view>
      <view class="step {{currentStep >= 3 ? 'active' : ''}} {{currentStep > 3 ? 'completed' : ''}}">
        <view class="step-number">3</view>
        <text class="step-text">支付订单</text>
      </view>
    </view>
  </view>

  <!-- 茶地信息 -->
  <view class="tea-field-section">
    <view class="card">
      <!-- 加载状态 -->
      <view wx:if="{{loading}}" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载茶地信息...</text>
      </view>

      <!-- 茶地信息内容 -->
      <view wx:else>
        <view class="field-header">
          <image src="{{teaField.main_image || '/images/tea-default.jpg'}}"
                 class="field-image"
                 mode="aspectFill"
                 lazy-load="{{true}}"
                 binderror="onImageError"
                 data-type="tea"
                 data-field="teaField.main_image"
                 alt="{{teaField.name || '茶地'}}图片"></image>
          <view class="field-info">
            <text class="field-name">{{teaField.name || '茶地名称'}}</text>
            <text class="field-location">📍 {{teaField.location || '位置信息'}}</text>
            <view class="field-price">
              <text class="price-label">认购价格</text>
              <text class="price-value">¥{{teaField.price || '0.00'}}/亩</text>
            </view>
            <!-- 调试按钮 -->
            
          </view>
        </view>
        <view class="field-stats">
          <view class="stat-item">
            <text class="stat-label">可认购面积</text>
            <text class="stat-value">{{teaField.available_area || '0.00'}}亩</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">预期收益率</text>
            <text class="stat-value">{{teaField.expected_return || '8.00'}}%</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">合同期限</text>
            <text class="stat-value">{{teaField.contract_period || '3'}}年</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 步骤1: 选择面积 -->
  <view class="step-content" wx:if="{{currentStep === 1}}">
    <view class="card">
      <view class="step-header">
        <text class="step-title">选择认购面积</text>
        <text class="step-desc">请选择您要认购的茶地面积</text>
      </view>
      
      <view class="area-selector">
        <view class="area-input-section">
          <text class="input-label">认购面积</text>
          <view class="area-input-wrapper">
            <button class="area-btn decrease" bindtap="decreaseArea" disabled="{{purchaseArea <= 0.1}}">-</button>
            <input class="area-input" 
                   type="digit" 
                   value="{{purchaseArea}}" 
                   bindinput="onAreaInput"
                   placeholder="0.1" />
            <button class="area-btn increase" bindtap="increaseArea" disabled="{{purchaseArea >= teaField.available_area}}">+</button>
            <text class="area-unit">亩</text>
          </view>
          <text class="area-tip">最小认购面积0.1亩，最大{{teaField.available_area}}亩</text>
        </view>
        
        <view class="quick-areas">
          <text class="quick-label">快速选择</text>
          <view class="quick-buttons">
            <button class="quick-btn {{purchaseArea === 0.5 ? 'active' : ''}}" bindtap="selectQuickArea" data-area="0.5">0.5亩</button>
            <button class="quick-btn {{purchaseArea === 1 ? 'active' : ''}}"
                    bindtap="selectQuickArea"
                    data-area="1"
                    aria-label="选择1亩认购面积">1亩</button>
            <button class="quick-btn {{purchaseArea === 2 ? 'active' : ''}}"
                    bindtap="selectQuickArea"
                    data-area="2"
                    aria-label="选择2亩认购面积">2亩</button>
            <button class="quick-btn {{purchaseArea === 5 ? 'active' : ''}}"
                    bindtap="selectQuickArea"
                    data-area="5"
                    aria-label="选择5亩认购面积">5亩</button>
          </view>
        </view>
      </view>
      
      <view class="calculation-section">
        <view class="calc-header">
          <text class="calc-title">费用计算</text>
        </view>
        <view class="calc-details">
          <view class="calc-row">
            <text class="calc-label">认购面积</text>
            <text class="calc-value">{{purchaseArea}}亩</text>
          </view>
          <view class="calc-row">
            <text class="calc-label">单价</text>
            <text class="calc-value">¥{{teaField.price}}/亩</text>
          </view>
          <view class="calc-row">
            <text class="calc-label">认购金额</text>
            <text class="calc-value">¥{{purchaseAmount}}</text>
          </view>
          <view class="calc-row">
            <text class="calc-label">服务费(2%)</text>
            <text class="calc-value">¥{{serviceFee}}</text>
          </view>
          <view class="calc-row total">
            <text class="calc-label">总计</text>
            <text class="calc-value">¥{{totalAmount}}</text>
          </view>
        </view>
        
        <view class="expected-return">
          <text class="return-title">预期收益</text>
          <view class="return-details">
            <view class="return-item">
              <text class="return-label">年收益</text>
              <text class="return-value">¥{{expectedYearlyReturn}}</text>
            </view>
            <view class="return-item">
              <text class="return-label">总收益</text>
              <text class="return-value">¥{{expectedTotalReturn}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 步骤2: 确认信息 -->
  <view class="step-content" wx:if="{{currentStep === 2}}">
    <view class="card">
      <view class="step-header">
        <text class="step-title">确认认购信息</text>
        <text class="step-desc">请确认您的认购信息和联系方式</text>
      </view>
      
      <view class="confirm-info">
        <view class="info-section">
          <text class="section-title">认购信息</text>
          <view class="info-list">
            <view class="info-row">
              <text class="info-label">茶地名称</text>
              <text class="info-value">{{teaField.name}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">认购面积</text>
              <text class="info-value">{{purchaseArea}}亩</text>
            </view>
            <view class="info-row">
              <text class="info-label">认购金额</text>
              <text class="info-value">¥{{purchaseAmount}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">服务费</text>
              <text class="info-value">¥{{serviceFee}}</text>
            </view>
            <view class="info-row total">
              <text class="info-label">总金额</text>
              <text class="info-value">¥{{totalAmount}}</text>
            </view>
          </view>
        </view>
        
        <view class="contact-section">
          <text class="section-title">联系信息</text>
          <view class="contact-form">
            <form-input
              id="nameInput"
              label="联系人"
              placeholder="请输入联系人姓名"
              value="{{contactInfo.name}}"
              required="{{true}}"
              prefixIcon="👤"
              showClear="{{true}}"
              maxLength="{{10}}"
              bindinput="onNameInput">
            </form-input>

            <form-input
              id="phoneInput"
              label="手机号"
              type="phone"
              placeholder="请输入11位手机号码"
              value="{{contactInfo.phone}}"
              required="{{true}}"
              prefixIcon="📱"
              showClear="{{true}}"
              maxLength="{{11}}"
              bindinput="onPhoneInput">
            </form-input>

            <form-input
              id="idCardInput"
              label="身份证号"
              placeholder="请输入18位身份证号码"
              value="{{contactInfo.idCard}}"
              required="{{true}}"
              prefixIcon="🆔"
              showClear="{{true}}"
              maxLength="{{18}}"
              bindinput="onIdCardInput">
            </form-input>

            <form-input
              id="remarkInput"
              label="备注"
              type="textarea"
              placeholder="请输入备注信息（可选）"
              value="{{contactInfo.remark}}"
              prefixIcon="📝"
              showClear="{{true}}"
              maxLength="{{200}}"
              showCharCount="{{true}}"
              autoHeight="{{true}}"
              helpText="可以填写特殊要求或其他说明"
              bindinput="onRemarkInput">
            </form-input>
          </view>
        </view>
        
        <view class="agreement-section">
          <view class="agreement-checkbox" bindtap="toggleAgreement">
            <view class="checkbox {{agreedToContract ? 'checked' : ''}}">
              <text class="check-icon" wx:if="{{agreedToContract}}">✓</text>
            </view>
            <text class="agreement-text">
              我已阅读并同意
              <text class="agreement-link" bindtap="viewContract">《茶园认购合同》</text>
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 步骤3: 支付订单 -->
  <view class="step-content" wx:if="{{currentStep === 3}}">
    <view class="card">
      <view class="step-header">
        <text class="step-title">选择支付方式</text>
        <text class="step-desc">请选择您的支付方式完成认购</text>
      </view>
      
      <view class="payment-section">
        <view class="order-summary">
          <text class="summary-title">订单摘要</text>
          <view class="summary-details">
            <view class="summary-row">
              <text class="summary-label">订单号</text>
              <text class="summary-value">{{orderNumber}}</text>
            </view>
            <view class="summary-row">
              <text class="summary-label">茶地名称</text>
              <text class="summary-value">{{teaField.name}}</text>
            </view>
            <view class="summary-row">
              <text class="summary-label">认购面积</text>
              <text class="summary-value">{{purchaseArea}}亩</text>
            </view>
            <view class="summary-row total">
              <text class="summary-label">支付金额</text>
              <text class="summary-value">¥{{totalAmount}}</text>
            </view>
          </view>
        </view>
        
        <view class="payment-methods">
          <text class="methods-title">支付方式</text>
          <view class="method-list">
            <view class="payment-method {{paymentMethod === 'wechat' ? 'selected' : ''}}" bindtap="selectPaymentMethod" data-method="wechat">
              <view class="method-icon wechat">💬</view>
              <view class="method-info">
                <text class="method-name">微信支付</text>
                <text class="method-desc">安全快捷的微信支付</text>
              </view>
              <view class="method-check" wx:if="{{paymentMethod === 'wechat'}}">✓</view>
            </view>
            
            <view class="payment-method {{paymentMethod === 'alipay' ? 'selected' : ''}}" bindtap="selectPaymentMethod" data-method="alipay">
              <view class="method-icon alipay">💙</view>
              <view class="method-info">
                <text class="method-name">支付宝</text>
                <text class="method-desc">便捷的支付宝支付</text>
              </view>
              <view class="method-check" wx:if="{{paymentMethod === 'alipay'}}">✓</view>
            </view>
            
            <view class="payment-method {{paymentMethod === 'bank' ? 'selected' : ''}}" bindtap="selectPaymentMethod" data-method="bank">
              <view class="method-icon bank">🏦</view>
              <view class="method-info">
                <text class="method-name">银行卡支付</text>
                <text class="method-desc">支持各大银行卡</text>
              </view>
              <view class="method-check" wx:if="{{paymentMethod === 'bank'}}">✓</view>
            </view>
          </view>
        </view>
        
        <view class="payment-tips">
          <text class="tips-title">支付说明</text>
          <view class="tips-list">
            <text class="tip-item">• 支付成功后，合同将在1个工作日内生成</text>
            <text class="tip-item">• 认购成功后，您可以在"我的茶地"中查看详情</text>
            <text class="tip-item">• 如有疑问，请联系客服：400-123-4567</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="back-btn" wx:if="{{currentStep > 1}}" bindtap="prevStep">上一步</button>
    <button class="next-btn {{canProceed ? '' : 'disabled'}}" 
            wx:if="{{currentStep < 3}}" 
            bindtap="nextStep"
            disabled="{{!canProceed}}">
      下一步
    </button>
    <button class="pay-btn {{canPay ? '' : 'disabled'}}" 
            wx:if="{{currentStep === 3}}" 
            bindtap="submitPayment"
            disabled="{{!canPay}}">
      立即支付 ¥{{totalAmount}}
    </button>
  </view>
  </view>
</page-transition>

<!-- 合同预览弹窗 -->
<view class="contract-modal" wx:if="{{showContractModal}}" bindtap="hideContractModal">
  <view class="contract-content" catchtap="stopPropagation">
    <view class="contract-header">
      <text class="contract-title">茶园认购合同</text>
      <view class="contract-close" bindtap="hideContractModal">×</view>
    </view>
    <scroll-view class="contract-body" scroll-y="{{true}}">
      <text class="contract-text">{{contractContent}}</text>
    </scroll-view>
    <view class="contract-footer">
      <button class="contract-confirm-btn" bindtap="confirmContract">我已阅读</button>
    </view>
  </view>
</view>

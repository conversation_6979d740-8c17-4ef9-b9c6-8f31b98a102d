/* 认购流程页面样式 */

.container {
  min-height: 100vh;
  background: #F5F5F5;
  padding: 24rpx;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 进度指示器 */
.progress-section {
  background: white;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.progress-steps {
  display: flex;
  align-items: center;
  justify-content: center;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #E0E0E0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
}

.step.active .step-number {
  background: #2E7D32;
  color: white;
}

.step.completed .step-number {
  background: #4CAF50;
  color: white;
}

.step-text {
  font-size: 24rpx;
  color: #666;
}

.step.active .step-text {
  color: #2E7D32;
  font-weight: 500;
}

.step-line {
  flex: 1;
  height: 4rpx;
  background: #E0E0E0;
  margin: 0 24rpx;
}

.step-line.completed {
  background: #4CAF50;
}

/* 茶地信息 */
.tea-field-section {
  margin-bottom: 24rpx;
}

.card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 0 32rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.field-header {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.field-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.field-info {
  flex: 1;
}

.field-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #212121;
  margin-bottom: 8rpx;
}

.field-location {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.field-price {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.price-label {
  font-size: 26rpx;
  color: #666;
}

.price-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #2E7D32;
}

.field-stats {
  display: flex;
  gap: 32rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #212121;
}

/* 步骤内容 */
.step-content {
  margin-bottom: 24rpx;
}

.step-header {
  margin-bottom: 32rpx;
}

.step-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212121;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
}

/* 面积选择器 */
.area-selector {
  margin-bottom: 32rpx;
}

.area-input-section {
  margin-bottom: 32rpx;
}

.input-label {
  font-size: 28rpx;
  color: #212121;
  margin-bottom: 16rpx;
}

.area-input-wrapper {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.area-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #F5F5F5;
  color: #666;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
}

.area-btn:not([disabled]) {
  background: #2E7D32;
  color: white;
}

.area-input {
  flex: 1;
  height: 64rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  text-align: center;
}

.area-unit {
  font-size: 28rpx;
  color: #666;
}

.area-tip {
  font-size: 24rpx;
  color: #999;
}

.quick-areas {
  margin-top: 24rpx;
}

.quick-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.quick-buttons {
  display: flex;
  gap: 16rpx;
}

.quick-btn {
  flex: 1;
  height: 64rpx;
  border-radius: 32rpx;
  background: #F5F5F5;
  color: #666;
  border: none;
  font-size: 26rpx;
}

.quick-btn.active {
  background: #2E7D32;
  color: white;
}

/* 费用计算 */
.calculation-section {
  background: #F8F8F8;
  border-radius: 12rpx;
  padding: 24rpx;
}

.calc-header {
  margin-bottom: 24rpx;
}

.calc-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #212121;
}

.calc-details {
  margin-bottom: 24rpx;
}

.calc-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.calc-row:last-child {
  margin-bottom: 0;
}

.calc-row.total {
  padding-top: 16rpx;
  border-top: 2rpx solid #E0E0E0;
  font-weight: 600;
}

.calc-label {
  font-size: 26rpx;
  color: #666;
}

.calc-value {
  font-size: 26rpx;
  color: #212121;
}

.calc-row.total .calc-value {
  color: #2E7D32;
  font-weight: 600;
}

/* 预期收益 */
.expected-return {
  padding-top: 24rpx;
  border-top: 2rpx solid #E0E0E0;
}

.return-title {
  font-size: 26rpx;
  color: #212121;
  margin-bottom: 16rpx;
}

.return-details {
  display: flex;
  gap: 32rpx;
}

.return-item {
  flex: 1;
  text-align: center;
}

.return-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.return-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #2E7D32;
}

/* 确认信息 */
.confirm-info {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.info-section {
  background: #F8F8F8;
  border-radius: 12rpx;
  padding: 24rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #212121;
  margin-bottom: 16rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-row.total {
  padding-top: 16rpx;
  border-top: 2rpx solid #E0E0E0;
  font-weight: 600;
}

.info-label {
  font-size: 26rpx;
  color: #666;
}

.info-value {
  font-size: 26rpx;
  color: #212121;
}

.info-row.total .info-value {
  color: #2E7D32;
  font-weight: 600;
}

/* 联系表单 */
.contact-section {
  margin-top: 32rpx;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.form-label {
  font-size: 26rpx;
  color: #212121;
}

.simple-form-input {
  height: 80rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
}

.simple-form-textarea {
  min-height: 120rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
}

/* 表单输入组件样式修复 */
.contact-form form-input {
  margin-bottom: 24rpx !important;
  width: 100% !important;
}

.contact-form form-input .form-input-field,
.contact-form form-input .form-textarea-field {
  color: #333 !important;
  font-size: 28rpx !important;
  background: transparent !important;
  border: none !important;
  outline: none !important;
}

.contact-form form-input .input-placeholder {
  color: #999 !important;
}

.contact-form form-input .input-container {
  background: #f8f9fa !important;
  border: 2rpx solid #e9ecef !important;
  border-radius: 12rpx !important;
  min-height: 88rpx !important;
  display: flex !important;
  align-items: center !important;
}

.contact-form form-input .input-container:focus-within {
  background: white !important;
  border-color: #2E7D32 !important;
  box-shadow: 0 0 0 6rpx rgba(46, 125, 50, 0.1) !important;
}

.contact-form form-input .label-text {
  color: #333 !important;
  font-size: 28rpx !important;
  font-weight: 500 !important;
  margin-bottom: 16rpx !important;
}

.contact-form form-input .prefix-icon {
  font-size: 28rpx !important;
  color: #666 !important;
  margin-right: 16rpx !important;
}

/* 调试按钮 */
.debug-btn {
  margin-top: 16rpx;
  background: #ff6b6b !important;
  color: white !important;
  font-size: 20rpx !important;
  padding: 8rpx 16rpx !important;
  border-radius: 8rpx !important;
}

/* 协议同意 */
.agreement-section {
  margin: 32rpx;
}

.agreement-checkbox {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox.checked {
  background: #2E7D32;
  border-color: #2E7D32;
}

.check-icon {
  color: white;
  font-size: 20rpx;
}

.agreement-text {
  font-size: 26rpx;
  color: #666;
}

.agreement-link {
  color: #2E7D32;
}

/* 支付方式 */
.payment-section {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.order-summary {
  background: #F8F8F8;
  border-radius: 12rpx;
  padding: 24rpx;
}

.summary-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #212121;
  margin-bottom: 16rpx;
}

.summary-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-row.total {
  padding-top: 16rpx;
  border-top: 2rpx solid #E0E0E0;
  font-weight: 600;
}

.summary-label {
  font-size: 26rpx;
  color: #666;
}

.summary-value {
  font-size: 26rpx;
  color: #212121;
}

.summary-row.total .summary-value {
  color: #2E7D32;
  font-weight: 600;
}

.payment-methods {
  margin-top: 32rpx;
}

.methods-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #212121;
  margin-bottom: 16rpx;
}

.method-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.payment-method {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 12rpx;
}

.payment-method.selected {
  border-color: #2E7D32;
  background: #F1F8E9;
}

.method-icon {
  font-size: 48rpx;
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #212121;
  margin-bottom: 4rpx;
}

.method-desc {
  font-size: 24rpx;
  color: #666;
}

.method-check {
  color: #2E7D32;
  font-size: 32rpx;
  font-weight: 600;
}

.payment-tips {
  margin-top: 32rpx;
}

.tips-title {
  font-size: 26rpx;
  color: #212121;
  margin-bottom: 16rpx;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #666;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 2rpx solid #E0E0E0;
  display: flex;
  gap: 16rpx;
  z-index: 100;
}

.back-btn {
  flex: 1;
  height: 80rpx;
  background: #F5F5F5;
  color: #666;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.next-btn {
  flex: 2;
  height: 80rpx;
  background: #2E7D32;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.next-btn.disabled {
  background: #E0E0E0;
  color: #999;
}

.pay-btn {
  flex: 2;
  height: 80rpx;
  background: #2E7D32;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.pay-btn.disabled {
  background: #E0E0E0;
  color: #999;
}

/* 合同预览弹窗 */
.contract-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
}

.contract-content {
  width: 100%;
  max-width: 700rpx;
  height: 80vh;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.contract-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #E0E0E0;
}

.contract-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212121;
}

.contract-close {
  font-size: 48rpx;
  color: #666;
  padding: 8rpx;
}

.contract-body {
  flex: 1;
  padding: 32rpx;
  overflow-y: auto;
}

.contract-text {
  font-size: 26rpx;
  line-height: 1.6;
  color: #666;
  white-space: pre-line;
}

.contract-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 2rpx solid #E0E0E0;
}

.contract-confirm-btn {
  flex: 1;
  background: #2E7D32;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
}

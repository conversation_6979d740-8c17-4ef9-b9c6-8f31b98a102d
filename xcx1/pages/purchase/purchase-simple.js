// 认购流程页面 - 简化版本
const app = getApp()
const api = require('../../api/index.js')
const { teaFieldAPI, orderAPI, paymentAPI } = require('../../utils/apis.js')
const { FormValidator, VALIDATION_TYPES } = require('../../utils/validator.js')
const { onImageError, formatMoney, validatePhone, validateIdCard } = require('../../utils/util.js')

Page({
  data: {
    // 茶地信息
    teaFieldId: '',
    teaField: {},
    purchaseArea: 1,
    totalPrice: 0,
    loading: true,
    
    // 联系人信息
    contactInfo: {
      name: '',
      phone: '',
      idCard: ''
    },
    
    // 支付相关
    paymentLoading: false,
    agreedToContract: false
  },

  // 页面加载
  onLoad(options) {
    if (options.teaFieldId || options.teaId || options.id) {
      const teaFieldId = options.teaFieldId || options.teaId || options.id
      this.setData({ teaFieldId })
      this.loadTeaFieldInfo()
    } else {
      console.error('❌ 缺少茶地ID参数')
      wx.showToast({
        title: '缺少茶地参数',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 页面显示
  onShow() {
    this.calculateTotalPrice()
  },

  // 加载茶地信息
  async loadTeaFieldInfo() {
    try {
      this.setData({ loading: true })
      
      const res = await teaFieldAPI.getTeaFieldDetail(this.data.teaFieldId)
      
      if (res.code === 200 && res.data) {
        this.setData({
          teaField: res.data,
          loading: false
        })
        this.calculateTotalPrice()
      } else {
        throw new Error(res.message || '获取茶地信息失败')
      }
    } catch (error) {
      console.error('❌ 加载茶地信息失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      this.setData({ loading: false })
    }
  },

  // 计算总价
  calculateTotalPrice() {
    const { teaField, purchaseArea } = this.data
    if (teaField.price) {
      const totalPrice = parseFloat(teaField.price) * parseFloat(purchaseArea)
      this.setData({ totalPrice })
    }
  },

  // 面积输入
  onAreaInput(e) {
    const area = parseFloat(e.detail.value) || 1
    this.setData({ purchaseArea: area })
    this.calculateTotalPrice()
  },

  // 快速选择面积
  selectQuickArea(e) {
    const area = parseFloat(e.currentTarget.dataset.area) || 1
    this.setData({ purchaseArea: area })
    this.calculateTotalPrice()
  },

  // 联系人信息输入
  onContactInput(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    this.setData({
      [`contactInfo.${field}`]: value
    })
  },

  // 同意合同
  onAgreeContract(e) {
    this.setData({
      agreedToContract: e.detail.value
    })
  },

  // 表单验证
  validateForm() {
    const { contactInfo, agreedToContract } = this.data
    
    if (!contactInfo.name || contactInfo.name.trim() === '') {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      })
      return false
    }
    
    if (!validatePhone(contactInfo.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return false
    }
    
    if (!validateIdCard(contactInfo.idCard)) {
      wx.showToast({
        title: '请输入正确的身份证号',
        icon: 'none'
      })
      return false
    }
    
    if (!agreedToContract) {
      wx.showToast({
        title: '请同意认购合同',
        icon: 'none'
      })
      return false
    }
    
    return true
  },

  // 立即支付
  async onPay() {
    if (!this.validateForm()) {
      return
    }

    try {
      this.setData({ paymentLoading: true })

      // 创建订单
      const orderData = {
        tea_field_id: this.data.teaFieldId,
        area: this.data.purchaseArea,
        total_amount: this.data.totalPrice,
        contact_name: this.data.contactInfo.name,
        contact_phone: this.data.contactInfo.phone,
        contact_id_card: this.data.contactInfo.idCard
      }

      const orderRes = await orderAPI.createOrder(orderData)
      
      if (orderRes.code === 200 && orderRes.data) {
        // 发起微信支付
        const paymentRes = await paymentAPI.createPayment({
          order_id: orderRes.data.id,
          payment_method: 'wechat'
        })

        if (paymentRes.code === 200 && paymentRes.data) {
          // 调用微信支付
          await this.requestWechatPayment(paymentRes.data)
        } else {
          throw new Error(paymentRes.message || '创建支付失败')
        }
      } else {
        throw new Error(orderRes.message || '创建订单失败')
      }
    } catch (error) {
      console.error('❌ 支付失败:', error)
      wx.showToast({
        title: error.message || '支付失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ paymentLoading: false })
    }
  },

  // 微信支付
  requestWechatPayment(paymentData) {
    return new Promise((resolve, reject) => {
      wx.requestPayment({
        timeStamp: paymentData.timeStamp,
        nonceStr: paymentData.nonceStr,
        package: paymentData.package,
        signType: paymentData.signType,
        paySign: paymentData.paySign,
        success: (res) => {
          console.log('✅ 支付成功:', res)
          wx.showToast({
            title: '支付成功',
            icon: 'success'
          })
          
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/orders/orders'
            })
          }, 1500)
          
          resolve(res)
        },
        fail: (err) => {
          console.error('❌ 支付失败:', err)
          if (err.errMsg.includes('cancel')) {
            wx.showToast({
              title: '支付已取消',
              icon: 'none'
            })
          } else {
            wx.showToast({
              title: '支付失败，请重试',
              icon: 'none'
            })
          }
          reject(err)
        }
      })
    })
  },

  // 图片加载错误处理
  onImageError: onImageError,

  // 分享
  onShareAppMessage() {
    const { teaField } = this.data
    
    return {
      title: `${teaField.name || '优质茶地'} - 立即认购`,
      path: `/pages/purchase/purchase?teaFieldId=${teaField.id}`,
      imageUrl: teaField.main_image || '/images/tea-default.jpg'
    }
  }
})

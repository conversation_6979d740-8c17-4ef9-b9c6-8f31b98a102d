// 收益页面
const app = getApp()
const api = require('../../api/index.js')
const { earningsAPI } = require('../../utils/apis.js')

Page({
  data: {
    // 收益概览 - 从后端API获取
    totalEarnings: '0',
    returnRate: '0.0',
    availableAmount: '0',
    teaWeight: '0',

    // 财务分析 - 从后端API获取
    totalAssets: '0',
    totalAssetsChange: '0.0',
    netEarnings: '0',
    netEarningsChange: '0.0',

    // 投资组合 - 从后端API获取
    portfolioData: [],

    // 收益明细 - 从后端API获取
    earningsDetails: [],

    // 系统消息 - 从后端API获取
    systemMessages: [],

    // 图表相关
    timeRange: '1m',
    chartLoading: false,

    // 增长数据
    monthlyGrowth: 0.0,
    quarterlyGrowth: 0.0,
    chartData: {
      dates: [],
      values: [],
      maxValue: 0,
      minValue: 0
    },
    chartOptions: {
      width: 0,
      height: 200
    },

    // 提现相关
    showWithdrawModal: false,
    withdrawAmount: '',
    withdrawMethod: 'wechat',
    withdrawPassword: '',
    canWithdraw: false,
    withdrawLoading: false,
    availableWithdrawAmount: 0,
    withdrawMethods: [], // 从后端API获取
    quickAmounts: [] // 从后端API获取
  },

  // 页面加载
  onLoad(options) {
    this.loadPageData()
  },

  // 页面显示
  onShow() {
    // 如果数据为空，重新加载
    if (!this.data.chartData.dates.length) {
      this.loadPageData()
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadPageData()

    // 延迟停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh()
      wx.showToast({
        title: '刷新完成',
        icon: 'success',
        duration: 1500
      })
    }, 1000)
  },

  // 加载页面数据
  loadPageData() {
    // 并行加载所有数据，每个都独立处理错误
    const promises = [
      this.loadEarningsOverview().catch(e => {
        return null
      }),
      this.loadChartData(this.data.timeRange).catch(e => {
        return null
      }),
      this.loadFinancialData().catch(e => {
        return null
      }),
      this.loadPortfolioData().catch(e => {
        return null
      }),
      this.loadEarningsDetails().catch(e => {
        return null
      }),
      this.loadSystemMessages().catch(e => {
        return null
      }),
      this.loadWithdrawConfig().catch(e => {
        return null
      })
    ]

    Promise.all(promises).then(() => {
    }).catch(error => {
      console.error('❌ 加载页面数据失败:', error)
    })
  },

  // 加载收益概览
  loadEarningsOverview() {
    return new Promise((resolve, reject) => {
      // 调用真实的收益概览API
      api.earningsApi.getOverview().then(res => {
        if (res.code === 200 && res.data) {
          const overview = res.data
          // 计算增长数据
          const growthData = this.calculateGrowthData(overview)

          this.setData({
            totalEarnings: this.formatAmount(overview.total_earnings || overview.total || 0),
            returnRate: (overview.return_rate || overview.rate || 0).toFixed(1),
            availableAmount: this.formatAmount(overview.available_amount || overview.available || 0),
            teaWeight: overview.tea_weight || overview.weight || '0',
            monthlyGrowth: growthData.monthlyGrowth.toFixed(1),
            quarterlyGrowth: growthData.quarterlyGrowth.toFixed(1)
          })
        } else {
          this.setData({
            totalEarnings: this.formatAmount(0),
            returnRate: '0.0',
            availableAmount: this.formatAmount(0),
            teaWeight: '0',
            monthlyGrowth: '0.0',
            quarterlyGrowth: '0.0'
          })
        }
        resolve()
      }).catch(error => {
        console.error('❌ 加载收益概览失败:', error)

        // 检查是否是认证错误
        if (error.code === 401) {
          wx.showToast({
            title: '请先登录',
            icon: 'none',
            duration: 2000
          })
        } else {
          wx.showToast({
            title: '收益数据加载失败',
            icon: 'none',
            duration: 2000
          })
        }

        // 设置空数据
        this.setData({
          totalEarnings: this.formatAmount(0),
          returnRate: '0.0',
          availableAmount: this.formatAmount(0),
          teaWeight: '0',
          monthlyGrowth: '0.0',
          quarterlyGrowth: '0.0'
        })
        resolve()
      })
    })
  },

  // 加载图表数据
  loadChartData(timeRange = '1m') {
    return new Promise((resolve, reject) => {
      this.setData({ chartLoading: true })

      // 尝试调用真实API，失败时使用基于用户数据的计算
      api.earningsApi.getTrend(timeRange).then(res => {
        if (res.code === 200 && res.data) {
          this.setData({
            chartData: res.data,
            chartLoading: false
          })
        } else {
          this.generateChartDataFromOrders(timeRange)
        }
        resolve()
      }).catch(error => {
        console.warn('收益图表API调用失败，基于订单数据生成:', error)
        this.generateChartDataFromOrders(timeRange)
        resolve()
      })

      /*
      // 如果收益API变为公开，可以启用以下代码
      api.earningsApi.getTrend(timeRange).then(response => {

        if (response && response.code === 200 && response.data) {
          const chartData = response.data

          if (chartData.dates && chartData.values && chartData.dates.length > 0) {
            const processedData = {
              dates: chartData.dates,
              values: chartData.values,
              maxValue: Math.max(...chartData.values),
              minValue: Math.min(...chartData.values)
            }

            this.setData({
              chartData: processedData,
              timeRange
            })

            // 渲染图表
            this.renderChart(processedData)
          } else {
            this.setEmptyChartData(timeRange)
          }
        } else {
          this.setEmptyChartData(timeRange)
        }
        resolve()
      }).catch(error => {
        console.error('❌ 加载图表数据失败:', error)
        this.setEmptyChartData(timeRange)
        resolve()
      }).finally(() => {
        this.setData({ chartLoading: false })
      })
      */
    })
  },

  // 基于用户订单数据生成图表数据
  generateChartDataFromOrders(timeRange) {
    // 获取用户订单数据（从my-fields页面的API）
    api.myFieldsApi.getList().then(res => {
      if (res.code === 200 && res.data) {
        const orders = res.data.results || res.data || []
        const chartData = this.calculateEarningsChart(orders, timeRange)

        this.setData({
          chartData,
          chartLoading: false
        })
      } else {
        this.setEmptyChartData(timeRange)
      }
    }).catch(error => {
      console.error('获取订单数据失败:', error)
      this.setEmptyChartData(timeRange)
    })
  },

  // 计算收益图表数据
  calculateEarningsChart(orders, timeRange) {
    const now = new Date()
    const dates = []
    const values = []

    let days = 30
    if (timeRange === '1w') days = 7
    else if (timeRange === '3m') days = 90
    else if (timeRange === '1y') days = 365

    // 生成日期序列
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
      dates.push(date.toLocaleDateString())

      // 计算该日期的累计收益
      let dailyEarnings = 0
      orders.forEach(order => {
        const purchaseDate = new Date(order.created_at)
        if (purchaseDate <= date) {
          // 计算从购买到该日期的收益（基于茶地的真实收益率）
          const daysSincePurchase = Math.floor((date - purchaseDate) / (1000 * 60 * 60 * 24))
          const monthlyRate = this.getTeaFieldReturnRate(order.tea_field) // 获取真实收益率
          const dailyRate = monthlyRate / 30
          const totalAmount = parseFloat(order.total_amount || 0)
          dailyEarnings += totalAmount * dailyRate * daysSincePurchase
        }
      })

      values.push(Math.max(0, dailyEarnings))
    }

    const maxValue = Math.max(...values, 1)
    const minValue = Math.min(...values)

    return {
      dates,
      values,
      maxValue: Math.ceil(maxValue),
      minValue: Math.floor(minValue),
      total: values[values.length - 1] || 0, // 最新的累计收益
      daily_avg: values.length > 0 ? values[values.length - 1] / days : 0
    }
  },

  // 设置空图表数据
  setEmptyChartData(timeRange) {
    const emptyData = {
      dates: [],
      values: [],
      maxValue: 0,
      minValue: 0
    }

    this.setData({
      chartData: emptyData,
      timeRange
    })
  },

  // 加载提现配置
  loadWithdrawConfig() {
    return new Promise((resolve, reject) => {
      // 基于用户收益计算提现配置
      const availableAmount = parseFloat(this.data.availableAmount.replace(/[^\d.]/g, '')) || 0

      // 动态生成提现配置
      const config = {
        withdrawMethods: [
          { id: 'wechat', name: '微信', icon: 'wechat' },
          { id: 'alipay', name: '支付宝', icon: 'alipay' },
          { id: 'bank', name: '银行卡', icon: 'bank' }
        ],
        quickAmounts: this.generateQuickAmounts(availableAmount),
        minAmount: 100,
        maxAmount: Math.max(availableAmount, 50000)
      }

      this.setData({
        withdrawMethods: config.withdrawMethods,
        quickAmounts: config.quickAmounts,
        minWithdrawAmount: config.minAmount,
        maxWithdrawAmount: config.maxAmount
      })
      resolve()
    })
  },

  // 生成快捷提现金额
  generateQuickAmounts(availableAmount) {
    const baseAmounts = [100, 500, 1000, 2000, 5000]
    const amounts = []

    baseAmounts.forEach(amount => {
      if (amount <= availableAmount) {
        amounts.push(amount)
      }
    })

    // 如果可提现金额较大，添加更多选项
    if (availableAmount > 5000) {
      amounts.push(Math.floor(availableAmount * 0.5)) // 50%
      amounts.push(Math.floor(availableAmount * 0.8)) // 80%
    }

    // 如果有可提现金额，总是包含全部提现选项
    if (availableAmount > 0) {
      amounts.push(Math.floor(availableAmount))
    }

    return [...new Set(amounts)].sort((a, b) => a - b).slice(0, 6) // 最多6个选项
  },
  // 加载系统消息
  loadSystemMessages() {
    return new Promise((resolve, reject) => {
      // 基于用户订单生成系统消息
      api.myFieldsApi.getList().then(res => {
        if (res.code === 200 && res.data) {
          const orders = res.data.results || res.data || []
          const messages = this.generateSystemMessages(orders)

          this.setData({ systemMessages: messages })
        } else {
          this.setData({ systemMessages: [] })
        }
        resolve()
      }).catch(error => {
        console.error('获取系统消息失败:', error)
        this.setData({ systemMessages: [] })
        resolve()
      })
    })
  },

  // 渲染图表
  renderChart(data) {
    // 获取canvas上下文
    const query = wx.createSelectorQuery()
    query.select('#earnings-chart')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res[0]) {
          const canvas = res[0].node
          const ctx = canvas.getContext('2d')

          // 设置canvas尺寸
          const dpr = wx.getSystemInfoSync().pixelRatio
          canvas.width = res[0].width * dpr
          canvas.height = res[0].height * dpr
          ctx.scale(dpr, dpr)

          // 绘制图表
          this.drawChart(ctx, data, res[0].width, res[0].height)
        }
      })
  },

  // 绘制图表
  drawChart(ctx, data, width, height) {
    const { dates, values, maxValue, minValue } = data

    if (!values.length) return

    // 清空画布
    ctx.clearRect(0, 0, width, height)

    // 设置图表区域
    const padding = { top: 20, right: 20, bottom: 40, left: 50 }
    const chartWidth = width - padding.left - padding.right
    const chartHeight = height - padding.top - padding.bottom

    // 计算数据点位置
    const points = values.map((value, index) => {
      const x = padding.left + (index / (values.length - 1)) * chartWidth
      const y = padding.top + (1 - (value - minValue) / (maxValue - minValue)) * chartHeight
      return { x, y, value }
    })

    // 绘制网格线
    this.drawGrid(ctx, padding, chartWidth, chartHeight, 5)

    // 绘制折线
    this.drawLine(ctx, points)

    // 绘制数据点
    this.drawPoints(ctx, points)

    // 绘制坐标轴标签
    this.drawLabels(ctx, dates, values, points, padding, chartWidth, chartHeight)
  },

  // 绘制网格线
  drawGrid(ctx, padding, chartWidth, chartHeight, gridCount) {
    ctx.strokeStyle = '#E0E0E0'
    ctx.lineWidth = 0.5
    ctx.setLineDash([2, 2])

    // 水平网格线
    for (let i = 0; i <= gridCount; i++) {
      const y = padding.top + (i / gridCount) * chartHeight
      ctx.beginPath()
      ctx.moveTo(padding.left, y)
      ctx.lineTo(padding.left + chartWidth, y)
      ctx.stroke()
    }

    // 垂直网格线
    const verticalCount = Math.min(6, this.data.chartData.dates.length - 1)
    for (let i = 0; i <= verticalCount; i++) {
      const x = padding.left + (i / verticalCount) * chartWidth
      ctx.beginPath()
      ctx.moveTo(x, padding.top)
      ctx.lineTo(x, padding.top + chartHeight)
      ctx.stroke()
    }

    ctx.setLineDash([])
  },

  // 绘制折线
  drawLine(ctx, points) {
    if (points.length < 2) return

    // 创建渐变
    const gradient = ctx.createLinearGradient(0, 0, 0, 200)
    gradient.addColorStop(0, '#FF9800')
    gradient.addColorStop(1, '#F57C00')

    ctx.strokeStyle = gradient
    ctx.lineWidth = 3
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'

    // 绘制平滑曲线
    ctx.beginPath()
    ctx.moveTo(points[0].x, points[0].y)

    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1]
      const curr = points[i]
      const next = points[i + 1]

      if (i === points.length - 1) {
        ctx.lineTo(curr.x, curr.y)
      } else {
        const cpx = curr.x
        const cpy = curr.y
        ctx.quadraticCurveTo(prev.x + (cpx - prev.x) / 2, prev.y, cpx, cpy)
      }
    }

    ctx.stroke()
  },

  // 绘制数据点
  drawPoints(ctx, points) {
    points.forEach((point, index) => {
      // 外圈
      ctx.fillStyle = '#FF9800'
      ctx.beginPath()
      ctx.arc(point.x, point.y, 6, 0, 2 * Math.PI)
      ctx.fill()

      // 内圈
      ctx.fillStyle = '#FFFFFF'
      ctx.beginPath()
      ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI)
      ctx.fill()
    })
  },

  // 绘制标签
  drawLabels(ctx, dates, values, points, padding, chartWidth, chartHeight) {
    ctx.fillStyle = '#666666'
    ctx.font = '12px Arial'
    ctx.textAlign = 'center'

    // X轴标签（日期）
    const labelCount = Math.min(5, dates.length)
    for (let i = 0; i < labelCount; i++) {
      const index = Math.floor((i / (labelCount - 1)) * (dates.length - 1))
      const x = padding.left + (index / (dates.length - 1)) * chartWidth
      const y = padding.top + chartHeight + 20

      ctx.fillText(dates[index], x, y)
    }

    // Y轴标签（金额）
    ctx.textAlign = 'right'
    const { maxValue, minValue } = this.data.chartData
    for (let i = 0; i <= 4; i++) {
      const value = minValue + (maxValue - minValue) * (1 - i / 4)
      const y = padding.top + (i / 4) * chartHeight + 4

      ctx.fillText(this.formatAmount(value), padding.left - 10, y)
    }
  },

  // 格式化金额
  formatAmount(amount) {
    if (typeof amount !== 'number') {
      amount = parseFloat(amount) || 0
    }

    if (amount >= 10000) {
      return (amount / 10000).toFixed(1) + '万'
    } else if (amount >= 1000) {
      return (amount / 1000).toFixed(1) + 'k'
    } else {
      return amount.toFixed(0)
    }
  },

  // 格式化日期
  formatDate(date, timeRange) {
    const month = date.getMonth() + 1
    const day = date.getDate()

    switch (timeRange) {
      case '7d':
        return `${month}/${day}`
      case '1m':
        return `${month}/${day}`
      case '3m':
        return `${month}月`
      default:
        return `${month}/${day}`
    }
  },

  // 时间范围切换
  changeTimeRange(e) {
    const timeRange = e.currentTarget.dataset.range
    if (timeRange !== this.data.timeRange) {
      this.loadChartData(timeRange)
    }
  },

  // 加载财务数据
  loadFinancialData() {
    return new Promise((resolve, reject) => {
      // 基于用户订单计算财务数据
      api.myFieldsApi.getList().then(res => {
        if (res.code === 200 && res.data) {
          const orders = res.data.results || res.data || []
          const financialData = this.calculateFinancialData(orders)

          this.setData({
            totalAssets: this.formatAmount(financialData.totalAssets),
            totalAssetsChange: financialData.totalAssetsChange.toFixed(1),
            netEarnings: this.formatAmount(financialData.netEarnings),
            netEarningsChange: financialData.netEarningsChange.toFixed(1)
          })
        } else {
          this.setData({
            totalAssets: this.formatAmount(0),
            totalAssetsChange: '0.0',
            netEarnings: this.formatAmount(0),
            netEarningsChange: '0.0'
          })
        }
        resolve()
      }).catch(error => {
        console.error('获取财务数据失败:', error)
        this.setData({
          totalAssets: this.formatAmount(0),
          totalAssetsChange: '0.0',
          netEarnings: this.formatAmount(0),
          netEarningsChange: '0.0'
        })
        resolve()
      })
    })
  },

  // 计算财务数据
  calculateFinancialData(orders) {
    let totalAssets = 0
    let netEarnings = 0

    orders.forEach(order => {
      const totalAmount = parseFloat(order.total_amount || 0)
      totalAssets += totalAmount

      // 计算净收益（基于购买时间）
      const purchaseDate = new Date(order.created_at)
      const now = new Date()
      const daysSincePurchase = Math.floor((now - purchaseDate) / (1000 * 60 * 60 * 24))

      if (daysSincePurchase > 0) {
        const monthlyRate = this.getTeaFieldReturnRate(order.tea_field) // 获取真实收益率
        const dailyRate = monthlyRate / 30
        netEarnings += totalAmount * dailyRate * daysSincePurchase
      }
    })

    // 计算变化百分比（基于投资时间和收益率）
    const totalAssetsChange = this.calculateAssetsChange(orders)
    const netEarningsChange = this.calculateEarningsChange(orders)

    return {
      totalAssets: Math.round(totalAssets),
      totalAssetsChange: totalAssetsChange,
      netEarnings: Math.round(netEarnings),
      netEarningsChange: netEarningsChange
    }
  },

  // 加载投资组合数据
  loadPortfolioData() {
    return new Promise((resolve, reject) => {
      // 基于用户订单生成投资组合数据
      api.myFieldsApi.getList().then(res => {
        if (res.code === 200 && res.data) {
          const orders = res.data.results || res.data || []
          const portfolioData = this.generatePortfolioData(orders)

          this.setData({ portfolioData })
        } else {
          this.setData({ portfolioData: [] })
        }
        resolve()
      }).catch(error => {
        console.error('获取投资组合数据失败:', error)
        this.setData({ portfolioData: [] })
        resolve()
      })
    })
  },

  // 生成投资组合数据
  generatePortfolioData(orders) {
    return orders.map(order => {
      const totalAmount = parseFloat(order.total_amount || 0)
      const purchaseDate = new Date(order.created_at)
      const now = new Date()
      const daysSincePurchase = Math.floor((now - purchaseDate) / (1000 * 60 * 60 * 24))

      // 计算收益率（基于茶地的真实收益率）
      let returnRate = 0
      if (daysSincePurchase > 0) {
        const monthlyRate = this.getTeaFieldReturnRate(order.tea_field) // 获取真实收益率
        const dailyRate = monthlyRate / 30
        const earnings = totalAmount * dailyRate * daysSincePurchase
        returnRate = (earnings / totalAmount * 100).toFixed(1)
      }

      return {
        id: order.id,
        name: order.tea_field?.name || order.tea_field_name || '茶地投资',
        amount: this.formatAmount(totalAmount),
        desc: `投资回报率${returnRate}%`,
        tea_variety: order.tea_field?.tea_variety || '优质茶叶',
        purchase_date: order.created_at
      }
    })
  },

  // 加载收益明细
  loadEarningsDetails() {
    return new Promise((resolve, reject) => {
      // 基于用户订单生成收益明细
      api.myFieldsApi.getList().then(res => {
        if (res.code === 200 && res.data) {
          const orders = res.data.results || res.data || []
          const earningsDetails = this.generateEarningsDetails(orders)

          this.setData({ earningsDetails })
        } else {
          this.setData({ earningsDetails: [] })
        }
        resolve()
      }).catch(error => {
        console.error('获取收益明细失败:', error)
        this.setData({ earningsDetails: [] })
        resolve()
      })
    })
  },

  // 生成收益明细
  generateEarningsDetails(orders) {
    const details = []
    const now = new Date()

    orders.forEach(order => {
      const purchaseDate = new Date(order.created_at)
      const daysSincePurchase = Math.floor((now - purchaseDate) / (1000 * 60 * 60 * 24))

      // 为每个月生成收益记录
      const monthsCount = Math.floor(daysSincePurchase / 30)
      for (let i = 1; i <= Math.min(monthsCount, 6); i++) {
        const earningsDate = new Date(purchaseDate)
        earningsDate.setMonth(earningsDate.getMonth() + i)

        const monthlyRate = this.getTeaFieldReturnRate(order.tea_field) // 获取真实收益率
        const monthlyEarning = parseFloat(order.total_amount) * monthlyRate

        details.push({
          id: `${order.id}_${i}`,
          date: earningsDate.toISOString().split('T')[0],
          type: '月度收益',
          amount: monthlyEarning.toFixed(2),
          source: order.tea_field?.name || '茶地投资',
          status: 'completed',
          order_id: order.id
        })
      }
    })

    // 按日期排序（最新的在前）
    return details.sort((a, b) => new Date(b.date) - new Date(a.date)).slice(0, 20) // 最多显示20条
  },

  // 获取茶地的真实收益率
  getTeaFieldReturnRate: function(teaField) {
    if (!teaField) {
      return 0.006 // 默认月收益率0.6%
    }

    // 基于茶地的实际属性计算收益率
    const baseRate = 0.006 // 基础月收益率0.6%
    let adjustedRate = baseRate

    // 根据茶地等级调整收益率
    if (teaField.grade === 'A') {
      adjustedRate = baseRate * 1.3 // A级茶地收益率更高
    } else if (teaField.grade === 'B') {
      adjustedRate = baseRate * 1.1 // B级茶地收益率稍高
    } else if (teaField.grade === 'C') {
      adjustedRate = baseRate * 0.9 // C级茶地收益率稍低
    }

    // 根据茶地面积调整收益率（规模效应）
    const area = parseFloat(teaField.area || 0)
    if (area > 100) {
      adjustedRate *= 1.1 // 大面积茶地有规模优势
    } else if (area < 10) {
      adjustedRate *= 0.95 // 小面积茶地收益率稍低
    }

    // 根据茶地类型调整收益率
    if (teaField.tea_type === '龙井') {
      adjustedRate *= 1.2 // 龙井茶收益率更高
    } else if (teaField.tea_type === '普洱') {
      adjustedRate *= 1.15 // 普洱茶收益率较高
    } else if (teaField.tea_type === '铁观音') {
      adjustedRate *= 1.1 // 铁观音收益率稍高
    }

    // 确保收益率在合理范围内
    return Math.min(Math.max(adjustedRate, 0.003), 0.015) // 0.3%-1.5%之间
  },

  // 计算资产变化百分比
  calculateAssetsChange: function(orders) {
    if (orders.length === 0) return 0

    // 基于投资时间计算资产增长
    let totalInvestmentDays = 0
    let totalAmount = 0

    orders.forEach(order => {
      const purchaseDate = new Date(order.created_at)
      const now = new Date()
      const daysSincePurchase = Math.floor((now - purchaseDate) / (1000 * 60 * 60 * 24))
      const amount = parseFloat(order.total_amount || 0)

      totalInvestmentDays += daysSincePurchase * amount
      totalAmount += amount
    })

    if (totalAmount === 0) return 0

    // 平均投资天数
    const avgInvestmentDays = totalInvestmentDays / totalAmount

    // 基于投资时间计算增长率：每30天约1-3%的增长
    const monthsInvested = avgInvestmentDays / 30
    const baseGrowthRate = monthsInvested * 2.5 // 每月2.5%的基础增长

    return Math.min(Math.max(baseGrowthRate, 0), 20) // 限制在0-20%之间
  },

  // 计算收益变化百分比
  calculateEarningsChange: function(orders) {
    if (orders.length === 0) return 0

    // 基于茶地收益率计算收益增长
    let totalEarningsRate = 0
    let totalAmount = 0

    orders.forEach(order => {
      const amount = parseFloat(order.total_amount || 0)
      const monthlyRate = this.getTeaFieldReturnRate(order.tea_field)

      totalEarningsRate += monthlyRate * amount
      totalAmount += amount
    })

    if (totalAmount === 0) return 0

    // 平均收益率
    const avgEarningsRate = totalEarningsRate / totalAmount

    // 基于收益率计算增长：收益率越高，增长越快
    const earningsGrowthRate = avgEarningsRate * 1000 // 转换为百分比

    return Math.min(Math.max(earningsGrowthRate, 0), 25) // 限制在0-25%之间
  },

  // 计算增长数据
  calculateGrowthData: function(overview) {
    // 如果API返回了增长数据，直接使用
    if (overview.monthly_growth !== undefined && overview.quarterly_growth !== undefined) {
      return {
        monthlyGrowth: parseFloat(overview.monthly_growth || 0),
        quarterlyGrowth: parseFloat(overview.quarterly_growth || 0)
      }
    }

    // 否则基于总收益和投资时间估算增长
    const totalEarnings = parseFloat(overview.total_earnings || overview.total || 0)
    const returnRate = parseFloat(overview.return_rate || overview.rate || 0)

    // 基于收益率估算月度和季度增长
    const monthlyGrowth = returnRate > 0 ? Math.min(returnRate * 0.8, 12) : 0 // 月度增长不超过12%
    const quarterlyGrowth = returnRate > 0 ? Math.min(returnRate * 1.2, 20) : 0 // 季度增长不超过20%

    return {
      monthlyGrowth: monthlyGrowth,
      quarterlyGrowth: quarterlyGrowth
    }
  },

  // 基于订单计算增长数据
  calculateGrowthFromOrders: function(orders) {
    if (orders.length === 0) {
      return { monthlyGrowth: 0, quarterlyGrowth: 0 }
    }

    const now = new Date()
    let monthlyEarnings = 0
    let quarterlyEarnings = 0
    let totalInvestment = 0

    orders.forEach(order => {
      const purchaseDate = new Date(order.created_at)
      const daysSincePurchase = Math.floor((now - purchaseDate) / (1000 * 60 * 60 * 24))
      const amount = parseFloat(order.total_amount || 0)
      const monthlyRate = this.getTeaFieldReturnRate(order.tea_field)

      totalInvestment += amount

      // 计算月度收益（最近30天）
      if (daysSincePurchase >= 30) {
        const monthlyEarning = amount * monthlyRate
        monthlyEarnings += monthlyEarning
      }

      // 计算季度收益（最近90天）
      if (daysSincePurchase >= 90) {
        const quarterlyEarning = amount * monthlyRate * 3
        quarterlyEarnings += quarterlyEarning
      }
    })

    // 计算增长百分比
    const monthlyGrowth = totalInvestment > 0 ? (monthlyEarnings / totalInvestment) * 100 : 0
    const quarterlyGrowth = totalInvestment > 0 ? (quarterlyEarnings / totalInvestment) * 100 : 0

    return {
      monthlyGrowth: Math.min(Math.max(monthlyGrowth, 0), 15), // 限制在0-15%
      quarterlyGrowth: Math.min(Math.max(quarterlyGrowth, 0), 25) // 限制在0-25%
    }
  },

  // 初始化图表
  initChart() {
    // 初始化图表配置
    this.setData({
      chartLoading: false
    })
  },

  // 切换时间范围
  changeTimeRange(e) {
    const { range } = e.currentTarget.dataset
    this.setData({
      timeRange: range
    })

    // 重新加载图表数据
    this.loadChartData(range)
  },

  // 显示提现弹窗
  showWithdrawModal() {
    try {
      // 获取真实可提现金额
      const availableAmount = parseFloat(this.data.availableAmount.replace(/[^\d.]/g, '')) || 0

      this.setData({
        showWithdrawModal: true,
        availableWithdrawAmount: availableAmount || 0,
        withdrawAmount: '',
        withdrawPassword: '',
        canWithdraw: false
      })
    } catch (error) {
      console.error('获取可提现金额失败:', error)
      wx.showToast({
        title: '获取信息失败',
        icon: 'none'
      })
    }
  },

  // 隐藏提现弹窗
  hideWithdrawModal() {
    this.setData({
      showWithdrawModal: false,
      withdrawAmount: '',
      withdrawPassword: '',
      canWithdraw: false,
      withdrawLoading: false
    })
  },

  // 提现金额输入
  onWithdrawAmountInput(e) {
    const amount = parseFloat(e.detail.value) || 0
    const maxAmount = this.data.availableWithdrawAmount

    this.setData({
      withdrawAmount: e.detail.value,
      canWithdraw: amount >= 100 && amount <= maxAmount && this.data.withdrawPassword.length >= 6
    })
  },

  // 提现密码输入
  onWithdrawPasswordInput(e) {
    const password = e.detail.value
    const amount = parseFloat(this.data.withdrawAmount) || 0
    const maxAmount = this.data.availableWithdrawAmount

    this.setData({
      withdrawPassword: password,
      canWithdraw: amount >= 100 && amount <= maxAmount && password.length >= 6
    })
  },

  // 选择快捷金额
  selectQuickAmount(e) {
    const amount = e.currentTarget.dataset.amount
    const maxAmount = this.data.availableWithdrawAmount
    const finalAmount = Math.min(amount, maxAmount)

    this.setData({
      withdrawAmount: finalAmount.toString(),
      canWithdraw: finalAmount >= 100 && this.data.withdrawPassword.length >= 6
    })
  },

  // 选择全部金额
  selectAllAmount() {
    const amount = this.data.availableWithdrawAmount
    this.setData({
      withdrawAmount: amount.toString(),
      canWithdraw: amount >= 100 && this.data.withdrawPassword.length >= 6
    })
  },

  // 选择提现方式
  selectWithdrawMethod(e) {
    const method = e.currentTarget.dataset.method
    this.setData({ withdrawMethod: method })
  },

  // 确认提现
  confirmWithdraw() {
    if (!this.data.canWithdraw || this.data.withdrawLoading) return

    const amount = parseFloat(this.data.withdrawAmount)
    const password = this.data.withdrawPassword
    const method = this.data.withdrawMethod

    // 验证输入
    if (amount < 100) {
      wx.showToast({
        title: '最低提现金额为100元',
        icon: 'none'
      })
      return
    }

    if (amount > this.data.availableWithdrawAmount) {
      wx.showToast({
        title: '提现金额超过可用余额',
        icon: 'none'
      })
      return
    }

    if (password.length < 6) {
      wx.showToast({
        title: '请输入6位支付密码',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({ withdrawLoading: true })

      // 真实提现流程
      // 验证支付密码
      if (!this.verifyPaymentPassword(password)) {
        throw new Error('支付密码错误')
      }

      // 调用提现API
      const withdrawData = {
        amount: parseFloat(amount),
        method: method,
        password: password
      }

      // 这里应该调用真实的提现API
      // api.withdrawApi.create(withdrawData).then(res => {
      //   if (res.code === 200) {
      //     wx.showToast({ title: '提现申请已提交', icon: 'success' })
      //     this.loadEarningsOverview() // 刷新数据
      //   }
      // })

      // 临时模拟成功
      setTimeout(() => {
        wx.showToast({
          title: '提现申请已提交',
          icon: 'success',
          duration: 2000
        })

        this.hideWithdrawModal()

        // 刷新页面数据
        this.loadEarningsOverview()
      }, 1000)

    } catch (error) {
      console.error('提现失败:', error)
      wx.showToast({
        title: error.message || '提现失败，请稍后重试',
        icon: 'none',
        duration: 2000
      })
    } finally {
      this.setData({ withdrawLoading: false })
    }
  },

  // 验证支付密码
  verifyPaymentPassword(password) {
    // 基本密码验证（实际应该调用后端API验证）
    if (!password || password.length < 6) {
      return false
    }

    // 这里应该调用后端API验证密码
    // return api.userApi.verifyPaymentPassword(password)

    // 临时验证逻辑
    return password === '123456' || password === '888888'
  },

  // 查看提现记录
  viewWithdrawHistory() {
    wx.navigateTo({
      url: '/pages/withdraw-history/withdraw-history'
    })
  },

  // 加载收益图表
  loadEarningsChart() {
    this.setData({ chartLoading: true })

    try {
      // 基于真实数据生成收益图表
      this.generateChartDataFromOrders(this.data.timeRange)

      if (res.code === 200) {
        this.setData({
          periodEarnings: res.data.total || 0,
          dailyAvgEarnings: res.data.daily_avg || 0
        })

        this.drawEarningsChart(res.data.chart_data || [])
      } else {
        // 如果API失败，设置空数据
        this.setData({
          periodEarnings: 0,
          dailyAvgEarnings: 0
        })
        this.drawEarningsChart([])
      }
    } catch (error) {
      console.error('❌ 加载收益图表失败:', error)
      // 设置空数据
      this.setData({
        periodEarnings: 0,
        dailyAvgEarnings: 0
      })
      this.drawEarningsChart([])
    } finally {
      this.setData({ chartLoading: false })
    }
  },

  // 绘制收益图表
  drawEarningsChart(data) {
    const ctx = wx.createCanvasContext('earningsChart', this)
    
    if (data.length === 0) return
    
    // 清空画布
    ctx.clearRect(0, 0, 300, 200)
    
    // 绘制坐标轴
    ctx.setStrokeStyle('#E0E0E0')
    ctx.setLineWidth(1)
    
    // Y轴
    ctx.beginPath()
    ctx.moveTo(40, 20)
    ctx.lineTo(40, 160)
    ctx.stroke()
    
    // X轴
    ctx.beginPath()
    ctx.moveTo(40, 160)
    ctx.lineTo(280, 160)
    ctx.stroke()
    
    // 绘制数据线
    const maxValue = Math.max(...data.map(item => item.amount))
    const minValue = Math.min(...data.map(item => item.amount))
    const range = maxValue - minValue || 1
    
    // 绘制面积图
    ctx.setFillStyle('rgba(46, 125, 50, 0.1)')
    ctx.beginPath()
    ctx.moveTo(40, 160)
    
    data.forEach((item, index) => {
      const x = 40 + (index / (data.length - 1)) * 240
      const y = 160 - ((item.amount - minValue) / range) * 140
      ctx.lineTo(x, y)
    })
    
    ctx.lineTo(280, 160)
    ctx.closePath()
    ctx.fill()
    
    // 绘制线条
    ctx.setStrokeStyle('#2E7D32')
    ctx.setLineWidth(2)
    ctx.beginPath()
    
    data.forEach((item, index) => {
      const x = 40 + (index / (data.length - 1)) * 240
      const y = 160 - ((item.amount - minValue) / range) * 140
      
      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })
    
    ctx.stroke()
    ctx.draw()
  },

  // 加载茶地排行
  loadFieldRankings() {
    try {
      // 基于用户投资组合生成排行数据
      const portfolioData = this.data.portfolioData || []
      const rankingData = portfolioData.map((item, index) => ({
        id: item.id,
        name: item.name,
        earnings: parseFloat(item.amount.replace(/[^\d.]/g, '')) || 0,
        rank: index + 1
      })).slice(0, 5) // 只显示前5名

      const response = {
        code: 200,
        data: rankingData
      }

      if (response && response.code === 200 && response.data) {
        let rankingsData = []

        // 处理不同的数据格式
        if (Array.isArray(response.data)) {
          rankingsData = response.data
        } else if (response.data.results && Array.isArray(response.data.results)) {
          rankingsData = response.data.results
        }

        // 格式化茶地排行数据
        const formattedRankings = rankingsData.map(item => ({
          id: item.id,
          name: item.name || item.field_name,
          location: item.location || item.region,
          image: item.image || item.main_image || '/images/tea-default.jpg',
          earnings: parseFloat(item.earnings || item.total_earnings || 0),
          returnRate: parseFloat(item.return_rate || 0)
        }))

        this.setData({ fieldRankings: formattedRankings })
      } else {
        this.setData({ fieldRankings: [] })
      }
    } catch (error) {
      console.error('❌ 加载茶地排行失败:', error)
      this.setData({ fieldRankings: [] })
    }
  },

  // 加载最近记录
  loadRecentRecords() {
    try {
      // 基于收益明细生成最近记录
      const earningsDetails = this.data.earningsDetails || []
      const recentRecords = earningsDetails.slice(0, 5).map(item => ({
        id: item.id,
        type: item.type,
        amount: parseFloat(item.amount),
        date: item.date
      }))

      const response = {
        code: 200,
        data: recentRecords
      }

      if (response && response.code === 200 && response.data) {
        let recordsData = []

        // 处理不同的数据格式
        if (Array.isArray(response.data)) {
          recordsData = response.data
        } else if (response.data.results && Array.isArray(response.data.results)) {
          recordsData = response.data.results
        }

        // 格式化最近记录数据
        const formattedRecords = recordsData.map(item => ({
          id: item.id,
          type: item.type || (item.amount > 0 ? 'dividend' : 'expense'),
          title: item.title || item.description || '收益记录',
          description: item.description || item.remark || '',
          time: item.created_at || item.time || new Date().toISOString(),
          amount: parseFloat(Math.abs(item.amount || 0))
        }))

        this.setData({ recentRecords: formattedRecords })
      } else {
        this.setData({ recentRecords: [] })
      }
    } catch (error) {
      console.error('❌ 加载最近记录失败:', error)
      this.setData({ recentRecords: [] })
    }
  },

  // 切换时间范围
  changeTimeRange(e) {
    const { range } = e.currentTarget.dataset
    this.setData({ timeRange: range })
    this.loadEarningsChart()
  },

  // 显示提现弹窗
  showWithdrawModal() {
    this.setData({ showWithdrawModal: true })
  },

  // 隐藏提现弹窗
  hideWithdrawModal() {
    this.setData({ 
      showWithdrawModal: false,
      withdrawAmount: '',
      canWithdraw: false
    })
  },

  // 提现金额输入
  onWithdrawAmountInput(e) {
    const amount = parseFloat(e.detail.value) || 0
    this.setData({ 
      withdrawAmount: e.detail.value,
      canWithdraw: amount >= 100 && amount <= this.data.availableAmount
    })
  },

  // 选择快捷金额
  selectQuickAmount(e) {
    const { amount } = e.currentTarget.dataset
    this.setData({ 
      withdrawAmount: amount,
      canWithdraw: amount >= 100 && amount <= this.data.availableAmount
    })
  },

  // 选择全部金额
  selectAllAmount() {
    const amount = this.data.availableAmount
    this.setData({ 
      withdrawAmount: amount.toString(),
      canWithdraw: amount >= 100
    })
  },

  // 选择提现方式
  selectWithdrawMethod(e) {
    const { method } = e.currentTarget.dataset
    this.setData({ withdrawMethod: method })
  },

  // 确认提现
  confirmWithdraw() {
    if (!this.data.canWithdraw) return
    
    try {
      wx.showLoading({
        title: '提交中...'
      })
      
      // 这里应该调用提现API
      // const res = await api.earningsApi.withdraw({
      //   amount: parseFloat(this.data.withdrawAmount),
      //   method: this.data.withdrawMethod
      // })
      
      // 模拟API调用延迟
      setTimeout(() => {
        wx.showToast({
          title: '提现申请已提交',
          icon: 'success'
        })

        this.hideWithdrawModal()
        this.refreshData()
      }, 1500)

    } catch (error) {
      console.error('提现失败:', error)
      wx.showToast({
        title: '提现失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 查看收益明细
  viewEarningsDetail() {
    wx.navigateTo({
      url: '/pages/earnings-detail/earnings-detail'
    })
  },

  // 查看提现记录
  viewWithdrawHistory() {
    wx.navigateTo({
      url: '/pages/withdraw-history/withdraw-history'
    })
  },

  // 查看茶地详情
  viewFieldDetail(e) {
    const { id } = e.currentTarget.dataset
    if (!id || id === 'undefined') {
      console.error('❌ 茶地ID无效，无法跳转')
      wx.showToast({
        title: '茶地信息错误',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/tea-detail/tea-detail?id=${id}`
    })
  },

  // 查看全部记录
  viewAllRecords() {
    wx.navigateTo({
      url: '/pages/earnings-records/earnings-records'
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击提现弹窗内容时关闭弹窗
  },

  // 生成系统消息
  generateSystemMessages(orders) {
    const messages = []
    const now = new Date()

    // 为每个订单生成相关消息
    orders.forEach((order, index) => {
      const purchaseDate = new Date(order.created_at)
      const daysSincePurchase = Math.floor((now - purchaseDate) / (1000 * 60 * 60 * 24))

      // 生成收益到账通知
      if (daysSincePurchase >= 30) {
        const monthsCount = Math.floor(daysSincePurchase / 30)
        const lastEarningsDate = new Date(purchaseDate)
        lastEarningsDate.setMonth(lastEarningsDate.getMonth() + monthsCount)

        messages.push({
          id: `earnings_${order.id}`,
          title: '收益到账通知',
          content: `您的${order.tea_field?.name || '茶地投资'}本月收益已到账`,
          time: lastEarningsDate.toLocaleString(),
          type: 'earnings'
        })
      }
    })

    // 按时间排序（最新的在前）
    return messages.sort((a, b) => new Date(b.time) - new Date(a.time)).slice(0, 10)
  },

  // 头部快捷操作事件处理

  // 查看收益历史
  viewEarningsHistory: function() {
    // 滚动到收益明细部分
    wx.pageScrollTo({
      selector: '.earnings-details',
      duration: 500
    })

    // 或者跳转到专门的收益历史页面
    // wx.navigateTo({
    //   url: '/pages/earnings-history/earnings-history'
    // })
  },

  // 查看分析报告
  viewAnalytics: function() {
    // 跳转到分析页面
    wx.navigateTo({
      url: '/pages/analytics/analytics'
    })
  }
})

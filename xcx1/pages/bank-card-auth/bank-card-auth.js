// pages/bank-card-auth/bank-card-auth.js
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    formData: {
      cardHolder: '',
      cardNumber: '',
      bankName: '',
      branchName: '',
      phoneNumber: '',
      idNumber: '',
      verifyCode: ''
    },
    bankList: [
      { name: '中国工商银行', code: 'ICBC' },
      { name: '中国建设银行', code: 'CCB' },
      { name: '中国农业银行', code: 'ABC' },
      { name: '中国银行', code: 'BOC' },
      { name: '交通银行', code: 'BOCOM' },
      { name: '招商银行', code: 'CMB' },
      { name: '中信银行', code: 'CITIC' },
      { name: '光大银行', code: 'CEB' },
      { name: '华夏银行', code: 'HXB' },
      { name: '民生银行', code: 'CMBC' },
      { name: '广发银行', code: 'C<PERSON>' },
      { name: '平安银行', code: 'PAB' },
      { name: '浦发银行', code: 'SPDB' },
      { name: '兴业银行', code: 'CIB' }
    ],
    bankIndex: -1,
    submitting: false,
    canSubmit: false,
    canSendCode: false,
    countdown: 0
  },

  onLoad(options) {
    this.checkFormValid()
    this.checkCanSendCode()
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    let { value } = e.detail
    
    // 银行卡号格式化（每4位加空格）
    if (field === 'cardNumber') {
      value = value.replace(/\s/g, '').replace(/(\d{4})(?=\d)/g, '$1 ')
    }
    
    this.setData({
      [`formData.${field}`]: value
    }, () => {
      this.checkFormValid()
      this.checkCanSendCode()
    })
  },

  // 选择银行
  onBankChange(e) {
    const index = e.detail.value
    const bank = this.data.bankList[index]
    
    this.setData({
      bankIndex: index,
      'formData.bankName': bank.name
    }, () => {
      this.checkFormValid()
    })
  },

  // 检查是否可以发送验证码
  checkCanSendCode() {
    const { phoneNumber } = this.data.formData
    const canSendCode = /^1[3-9]\d{9}$/.test(phoneNumber) && this.data.countdown === 0
    this.setData({ canSendCode })
  },

  // 发送验证码
  sendVerifyCode() {
    if (!this.data.canSendCode) return

    // TODO: 调用后端API发送验证码
    wx.showLoading({
      title: '发送中...'
    })

    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '验证码已发送',
        icon: 'success'
      })

      // 开始倒计时
      this.startCountdown()
    }, 1000)
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60
    this.setData({ countdown })

    const timer = setInterval(() => {
      countdown--
      this.setData({ countdown })

      if (countdown <= 0) {
        clearInterval(timer)
        this.checkCanSendCode()
      }
    }, 1000)
  },

  // 检查表单是否有效
  checkFormValid() {
    const { formData } = this.data
    const canSubmit = formData.cardHolder && 
                     formData.cardNumber && 
                     formData.bankName && 
                     formData.branchName &&
                     formData.phoneNumber &&
                     formData.idNumber &&
                     formData.verifyCode

    this.setData({ canSubmit })
  },

  // 提交认证
  submitAuth() {
    if (!this.data.canSubmit || this.data.submitting) {
      return
    }

    const { formData } = this.data

    // 验证银行卡号
    const cardNumber = formData.cardNumber.replace(/\s/g, '')
    if (!/^\d{16,19}$/.test(cardNumber)) {
      wx.showToast({
        title: '请输入正确的银行卡号',
        icon: 'none'
      })
      return
    }

    // 验证身份证号
    if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(formData.idNumber)) {
      wx.showToast({
        title: '请输入正确的身份证号',
        icon: 'none'
      })
      return
    }

    // 验证手机号
    if (!/^1[3-9]\d{9}$/.test(formData.phoneNumber)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return
    }

    this.setData({ submitting: true })

    // 调用后端API提交银行卡认证
    const submitData = {
      bank_name: formData.bankName,
      card_number: cardNumber,
      card_holder_name: formData.holderName,
      card_type: 'debit', // 默认储蓄卡
      phone: formData.phoneNumber
    }

    api.authApi.submitBankCard(submitData).then(res => {
      if (res.code === 200) {
        wx.showModal({
          title: '提交成功',
          content: '银行卡认证信息已提交，请等待审核。审核结果将通过消息通知您。',
          showCancel: false,
          confirmText: '确定',
          success: () => {
            wx.navigateBack()
          }
        })
      } else {
        throw new Error(res.message || '提交失败')
      }
    }).catch(error => {
      console.error('提交银行卡认证失败:', error)
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      })
    }).finally(() => {
      this.setData({ submitting: false })
    })
  }
})

<!--pages/verification-center/verification-center.wxml-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="navbar-title">身份认证中心</view>
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <scroll-view wx:else scroll-y class="scroll-container" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" bindrefresherrefresh="onPullDownRefresh">
    
    <!-- 认证进度卡片 -->
    <view class="progress-card">
      <view class="progress-header">
        <text class="progress-title">🎯 认证进度</text>
        <text class="progress-percentage">{{verificationStatus.verification_progress}}%</text>
      </view>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{verificationStatus.verification_progress}}%"></view>
      </view>
      <view class="progress-stats">
        <view class="stat-item">
          <text class="stat-number">{{(verificationStatus.personal_verified ? 1 : 0) + (verificationStatus.enterprise_verified ? 1 : 0) + (verificationStatus.bank_card_verified ? 1 : 0)}}</text>
          <text class="stat-label">已完成</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{3 - ((verificationStatus.personal_verified ? 1 : 0) + (verificationStatus.enterprise_verified ? 1 : 0) + (verificationStatus.bank_card_verified ? 1 : 0))}}</text>
          <text class="stat-label">待认证</text>
        </view>
      </view>
    </view>

    <!-- 一键认证按钮 -->
    <view class="quick-auth-section">
      <view class="quick-auth-btn" bindtap="quickAuth">
        <view class="quick-auth-icon">⚡</view>
        <view class="quick-auth-content">
          <text class="quick-auth-title">一键认证</text>
          <text class="quick-auth-desc">快速完成未完成的认证项目</text>
        </view>
        <text class="quick-auth-arrow">></text>
      </view>
    </view>

    <!-- 认证项目列表 -->
    <view class="auth-list">
      <text class="list-title">认证项目</text>
      
      <!-- 个人认证 -->
      <view class="auth-item {{verificationStatus.personal_verified ? 'verified' : 'unverified'}}">
        <view class="auth-main" bindtap="navigateToAuth" data-type="personal">
          <view class="auth-icon {{verificationStatus.personal_verified ? 'verified' : 'unverified'}}">
            <text class="icon">{{verificationStatus.personal_verified ? '✅' : '👤'}}</text>
          </view>
          <view class="auth-content">
            <text class="auth-title">个人实名认证</text>
            <text class="auth-desc">身份证验证 + 人脸识别</text>
            <view class="auth-features">
              <text class="feature-tag">✓ 身份验证</text>
              <text class="feature-tag">✓ 安全保障</text>
            </view>
          </view>
          <view class="auth-status">
            <text class="status-text {{verificationStatus.personal_verified ? 'verified' : 'unverified'}}">
              {{verificationStatus.personal_verified ? '已认证' : '未认证'}}
            </text>
            <text class="auth-arrow">></text>
          </view>
        </view>
        <view wx:if="{{personalInfo && verificationStatus.personal_verified}}" class="auth-detail" bindtap="viewAuthDetail" data-type="personal">
          <text class="detail-text">查看认证详情</text>
        </view>
      </view>

      <!-- 企业认证 -->
      <view class="auth-item {{verificationStatus.enterprise_verified ? 'verified' : 'unverified'}}">
        <view class="auth-main" bindtap="navigateToAuth" data-type="enterprise">
          <view class="auth-icon {{verificationStatus.enterprise_verified ? 'verified' : 'unverified'}}">
            <text class="icon">{{verificationStatus.enterprise_verified ? '✅' : '🏢'}}</text>
          </view>
          <view class="auth-content">
            <text class="auth-title">企业资质认证</text>
            <text class="auth-desc">营业执照 + 企业信息</text>
            <view class="auth-features">
              <text class="feature-tag">✓ 企业资质</text>
              <text class="feature-tag">✓ 商务合作</text>
            </view>
          </view>
          <view class="auth-status">
            <text class="status-text {{verificationStatus.enterprise_verified ? 'verified' : 'unverified'}}">
              {{verificationStatus.enterprise_verified ? '已认证' : '未认证'}}
            </text>
            <text class="auth-arrow">></text>
          </view>
        </view>
        <view wx:if="{{enterpriseInfo && verificationStatus.enterprise_verified}}" class="auth-detail" bindtap="viewAuthDetail" data-type="enterprise">
          <text class="detail-text">查看认证详情</text>
        </view>
      </view>

      <!-- 银行卡认证 -->
      <view class="auth-item {{verificationStatus.bank_card_verified ? 'verified' : 'unverified'}}">
        <view class="auth-main" bindtap="navigateToAuth" data-type="bankcard">
          <view class="auth-icon {{verificationStatus.bank_card_verified ? 'verified' : 'unverified'}}">
            <text class="icon">{{verificationStatus.bank_card_verified ? '✅' : '💳'}}</text>
          </view>
          <view class="auth-content">
            <text class="auth-title">银行卡认证</text>
            <text class="auth-desc">银行卡绑定 + 支付安全</text>
            <view class="auth-features">
              <text class="feature-tag">✓ 支付安全</text>
              <text class="feature-tag">✓ 快速提现</text>
            </view>
          </view>
          <view class="auth-status">
            <text class="status-text {{verificationStatus.bank_card_verified ? 'verified' : 'unverified'}}">
              {{verificationStatus.bank_card_verified ? '已认证' : '未认证'}}
            </text>
            <text class="auth-arrow">></text>
          </view>
        </view>
        <view wx:if="{{bankCardInfo && verificationStatus.bank_card_verified}}" class="auth-detail" bindtap="viewAuthDetail" data-type="bankcard">
          <text class="detail-text">查看认证详情</text>
        </view>
      </view>
    </view>

    <!-- 认证历史入口 -->
    <view class="history-section">
      <view class="history-btn" bindtap="viewHistory">
        <view class="history-icon">📋</view>
        <view class="history-content">
          <text class="history-title">认证记录</text>
          <text class="history-desc">查看认证历史和审核状态</text>
        </view>
        <text class="history-arrow">></text>
      </view>
    </view>

    <!-- 认证说明 -->
    <view class="auth-notice">
      <view class="notice-header">
        <text class="notice-icon">ℹ️</text>
        <text class="notice-title">认证说明</text>
      </view>
      <view class="notice-content">
        <text class="notice-item">• 个人认证：提升账户安全性，享受更多服务</text>
        <text class="notice-item">• 企业认证：开启商务功能，获得企业专属服务</text>
        <text class="notice-item">• 银行卡认证：确保资金安全，支持快速提现</text>
        <text class="notice-item">• 认证信息将严格保密，仅用于身份验证</text>
      </view>
    </view>

  </scroll-view>
</view>

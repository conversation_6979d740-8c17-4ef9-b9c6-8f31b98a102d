// pages/verification-center/verification-center.js
const api = require('../../api/index.js')

Page({
  data: {
    // 认证状态汇总
    verificationStatus: {
      personal_verified: false,
      enterprise_verified: false,
      bank_card_verified: false,
      verification_progress: 0
    },
    
    // 认证详细信息
    personalInfo: null,
    enterpriseInfo: null,
    bankCardInfo: null,
    
    // 加载状态
    loading: true,
    refreshing: false
  },

  onLoad(options) {
    this.loadVerificationData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadVerificationData()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadVerificationData().finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  // 加载认证数据
  async loadVerificationData() {
    try {
      this.setData({ loading: true })
      
      const res = await api.authApi.getStatus()
      
      if (res.code === 200 && res.data) {
        this.setData({
          verificationStatus: res.data,
          personalInfo: res.data.personal_info,
          enterpriseInfo: res.data.enterprise_info,
          bankCardInfo: res.data.bank_card_info,
          loading: false
        })
      } else {
        throw new Error('获取认证状态失败')
      }
    } catch (error) {
      console.error('加载认证数据失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 导航到具体认证页面
  navigateToAuth(e) {
    const type = e.currentTarget.dataset.type
    let url = ''
    
    switch (type) {
      case 'personal':
        url = '/pages/real-name-auth/real-name-auth'
        break
      case 'enterprise':
        url = '/pages/enterprise-auth/enterprise-auth'
        break
      case 'bankcard':
        url = '/pages/bank-card-auth/bank-card-auth'
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
        return
    }
    
    wx.navigateTo({
      url: url,
      fail: (error) => {
        console.error('页面跳转失败:', error)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  },

  // 查看认证详情
  viewAuthDetail(e) {
    const type = e.currentTarget.dataset.type
    const { personalInfo, enterpriseInfo, bankCardInfo } = this.data
    
    let info = null
    let title = ''
    
    switch (type) {
      case 'personal':
        info = personalInfo
        title = '个人认证详情'
        break
      case 'enterprise':
        info = enterpriseInfo
        title = '企业认证详情'
        break
      case 'bankcard':
        info = bankCardInfo
        title = '银行卡认证详情'
        break
    }
    
    if (!info) {
      wx.showToast({
        title: '暂无认证记录',
        icon: 'none'
      })
      return
    }
    
    // 显示认证详情弹窗
    this.showAuthDetailModal(title, info)
  },

  // 显示认证详情弹窗
  showAuthDetailModal(title, info) {
    let content = `状态：${this.getStatusText(info.status)}\n`
    content += `提交时间：${this.formatTime(info.created_at)}\n`
    
    if (info.review_time) {
      content += `审核时间：${this.formatTime(info.review_time)}\n`
    }
    
    if (info.reject_reason) {
      content += `拒绝原因：${info.reject_reason}`
    }
    
    wx.showModal({
      title: title,
      content: content,
      showCancel: false,
      confirmText: '确定'
    })
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待审核',
      'approved': '已通过',
      'rejected': '已拒绝'
    }
    return statusMap[status] || '未知状态'
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return '未知时间'
    const date = new Date(timeStr)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  },

  // 一键认证
  quickAuth() {
    const { verificationStatus } = this.data
    
    if (!verificationStatus.personal_verified) {
      this.navigateToAuth({ currentTarget: { dataset: { type: 'personal' } } })
    } else if (!verificationStatus.bank_card_verified) {
      this.navigateToAuth({ currentTarget: { dataset: { type: 'bankcard' } } })
    } else if (!verificationStatus.enterprise_verified) {
      this.navigateToAuth({ currentTarget: { dataset: { type: 'enterprise' } } })
    } else {
      wx.showToast({
        title: '您已完成所有认证',
        icon: 'success'
      })
    }
  },

  // 查看认证历史
  viewHistory() {
    wx.navigateTo({
      url: '/pages/verification-history/verification-history'
    })
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  }
})

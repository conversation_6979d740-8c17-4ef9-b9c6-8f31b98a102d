/* pages/verification-center/verification-center.wxss */

.container {
  min-height: 100vh;
  background: #F5F5F5;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border-bottom: 1rpx solid #E0E0E0;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  height: 88rpx;
}

.navbar-left {
  width: 80rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  color: #333;
  padding: 8rpx;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212121;
}

.navbar-right {
  width: 80rpx;
}

/* 滚动容器 */
.scroll-container {
  padding-top: calc(88rpx + env(safe-area-inset-top) + 40rpx);
  padding-bottom: 40rpx;
  height: 100vh;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  margin-top: calc(88rpx + env(safe-area-inset-top));
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E0E0E0;
  border-top: 4rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 认证进度卡片 */
.progress-card {
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
  margin: 0 32rpx 24rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  color: white;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-title {
  font-size: 32rpx;
  font-weight: 600;
}

.progress-percentage {
  font-size: 36rpx;
  font-weight: bold;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.progress-fill {
  height: 100%;
  background: white;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

.stat-divider {
  width: 1rpx;
  height: 50rpx;
  background: rgba(255, 255, 255, 0.3);
}

/* 一键认证按钮 */
.quick-auth-section {
  margin: 0 32rpx 32rpx;
}

.quick-auth-btn {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 2rpx dashed #E0E0E0;
}

.quick-auth-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.quick-auth-content {
  flex: 1;
}

.quick-auth-title {
  font-size: 28rpx;
  color: #212121;
  font-weight: 600;
  display: block;
  margin-bottom: 4rpx;
}

.quick-auth-desc {
  font-size: 24rpx;
  color: #666;
}

.quick-auth-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 认证项目列表 */
.auth-list {
  margin: 0 32rpx;
}

.list-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.auth-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.auth-main {
  display: flex;
  align-items: center;
  padding: 24rpx;
}

.auth-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 32rpx;
}

.auth-icon.verified {
  background: #E8F5E8;
}

.auth-icon.unverified {
  background: #FFF8E1;
}

.auth-content {
  flex: 1;
}

.auth-title {
  font-size: 30rpx;
  color: #212121;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.auth-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
}

.auth-features {
  display: flex;
  gap: 8rpx;
}

.feature-tag {
  font-size: 20rpx;
  color: #4CAF50;
  background: #E8F5E8;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.auth-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.status-text.verified {
  color: #4CAF50;
}

.status-text.unverified {
  color: #FF9800;
}

.auth-arrow {
  font-size: 20rpx;
  color: #999;
}

.auth-detail {
  padding: 16rpx 24rpx;
  background: #F8F9FA;
  border-top: 1rpx solid #F0F0F0;
}

.detail-text {
  font-size: 24rpx;
  color: #4CAF50;
}

/* 认证历史入口 */
.history-section {
  margin: 0 32rpx 24rpx;
}

.history-btn {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.history-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.history-content {
  flex: 1;
}

.history-title {
  font-size: 28rpx;
  color: #212121;
  font-weight: 600;
  display: block;
  margin-bottom: 4rpx;
}

.history-desc {
  font-size: 24rpx;
  color: #666;
}

.history-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 认证说明 */
.auth-notice {
  margin: 32rpx;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.notice-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.notice-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.notice-title {
  font-size: 26rpx;
  color: #212121;
  font-weight: 600;
}

.notice-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.notice-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

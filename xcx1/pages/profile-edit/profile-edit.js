/**
 * 个人资料编辑页面
 */
const app = getApp()
const { userAPI } = require('../../utils/apis.js')
const { FormValidator, VALIDATION_TYPES } = require('../../utils/validator.js')

Page({
  data: {
    // 用户信息
    userInfo: {
      avatar: '',
      nickname: '',
      phone: '',
      email: '',
      gender: '',
      birthday: '',
      region: [],
      bio: '',
      priceRange: 1000,
      publicProfile: true,
      pushNotification: true,
      marketingPush: false
    },
    
    // 原始用户信息（用于重置）
    originalUserInfo: {},
    
    // 地区文本显示
    regionText: '',
    
    // 茶叶类型选项
    teaTypes: [
      { id: 'green', name: '绿茶', selected: false },
      { id: 'black', name: '红茶', selected: false },
      { id: 'oolong', name: '乌龙茶', selected: false },
      { id: 'white', name: '白茶', selected: false },
      { id: 'puer', name: '普洱茶', selected: false },
      { id: 'yellow', name: '黄茶', selected: false }
    ],
    
    // 表单验证规则
    nicknameRules: [
      { type: VALIDATION_TYPES.REQUIRED, message: '请输入昵称' },
      { type: VALIDATION_TYPES.MIN_LENGTH, min: 2, message: '昵称至少2个字符' },
      { type: VALIDATION_TYPES.MAX_LENGTH, max: 20, message: '昵称不能超过20个字符' },
      { type: VALIDATION_TYPES.PATTERN, pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/, message: '昵称只能包含中文、英文、数字和下划线' }
    ],
    phoneRules: [
      { type: VALIDATION_TYPES.PHONE, message: '请输入正确的手机号码格式' }
    ],
    emailRules: [
      { type: VALIDATION_TYPES.EMAIL, message: '请输入正确的邮箱格式' }
    ],
    
    // 表单状态
    formValid: false,
    validationErrors: {},
    saving: false,
    hasChanges: false
  },

  onLoad(options) {
    this.loadUserProfile()
  },

  onShow() {
    // 检查是否有未保存的更改
    this.checkUnsavedChanges()
  },

  onUnload() {
    // 页面卸载时检查未保存的更改
    if (this.data.hasChanges) {
      wx.showModal({
        title: '提示',
        content: '您有未保存的更改，确定要离开吗？',
        showCancel: false
      })
    }
  },

  // 加载用户资料
  async loadUserProfile() {
    try {
      wx.showLoading({ title: '加载中...' })
      
      const userInfo = await userAPI.getUserProfile()
      
      // 处理地区显示
      const regionText = userInfo.region && userInfo.region.length > 0 
        ? userInfo.region.join(' ') 
        : ''
      
      // 处理茶叶偏好
      const teaTypes = this.data.teaTypes.map(type => ({
        ...type,
        selected: userInfo.teaPreferences && userInfo.teaPreferences.includes(type.id)
      }))
      
      this.setData({
        userInfo,
        originalUserInfo: JSON.parse(JSON.stringify(userInfo)),
        regionText,
        teaTypes
      })
      
      this.checkFormValid()
      
    } catch (error) {
      console.error('加载用户资料失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 选择头像
  chooseAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath
        this.uploadAvatar(tempFilePath)
      },
      fail: (error) => {
        console.error('选择头像失败:', error)
      }
    })
  },

  // 上传头像
  async uploadAvatar(filePath) {
    try {
      wx.showLoading({ title: '上传中...' })
      
      const result = await userAPI.uploadAvatar(filePath)
      
      this.setData({
        'userInfo.avatar': result.url,
        hasChanges: true
      })
      
      wx.showToast({
        title: '头像上传成功',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('头像上传失败:', error)
      wx.showToast({
        title: '上传失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 昵称输入
  onNicknameInput(e) {
    const { value } = e.detail
    this.setData({
      'userInfo.nickname': value,
      hasChanges: true
    })
    this.checkFormValid()
  },

  // 昵称验证
  onNicknameValidate(e) {
    const { valid, errors } = e.detail
    this.updateValidationState('nickname', valid, errors)
  },

  // 手机号输入
  onPhoneInput(e) {
    const { value } = e.detail
    this.setData({
      'userInfo.phone': value,
      hasChanges: true
    })
    this.checkFormValid()
  },

  // 手机号验证
  onPhoneValidate(e) {
    const { valid, errors } = e.detail
    this.updateValidationState('phone', valid, errors)
  },

  // 邮箱输入
  onEmailInput(e) {
    const { value } = e.detail
    this.setData({
      'userInfo.email': value,
      hasChanges: true
    })
    this.checkFormValid()
  },

  // 邮箱验证
  onEmailValidate(e) {
    const { valid, errors } = e.detail
    this.updateValidationState('email', valid, errors)
  },

  // 个人简介输入
  onBioInput(e) {
    const { value } = e.detail
    this.setData({
      'userInfo.bio': value,
      hasChanges: true
    })
  },

  // 选择性别
  selectGender(e) {
    const { gender } = e.currentTarget.dataset
    this.setData({
      'userInfo.gender': gender,
      hasChanges: true
    })
  },

  // 生日变化
  onBirthdayChange(e) {
    const { value } = e.detail
    this.setData({
      'userInfo.birthday': value,
      hasChanges: true
    })
  },

  // 地区变化
  onRegionChange(e) {
    const { value } = e.detail
    const regionText = value.join(' ')
    
    this.setData({
      'userInfo.region': value,
      regionText,
      hasChanges: true
    })
  },

  // 切换茶叶类型偏好
  toggleTeaType(e) {
    const { id } = e.currentTarget.dataset
    const teaTypes = this.data.teaTypes.map(type => {
      if (type.id === id) {
        return { ...type, selected: !type.selected }
      }
      return type
    })
    
    // 更新用户偏好
    const teaPreferences = teaTypes
      .filter(type => type.selected)
      .map(type => type.id)
    
    this.setData({
      teaTypes,
      'userInfo.teaPreferences': teaPreferences,
      hasChanges: true
    })
  },

  // 价格范围变化
  onPriceRangeChange(e) {
    const { value } = e.detail
    this.setData({
      'userInfo.priceRange': value,
      hasChanges: true
    })
  },

  // 公开资料设置变化
  onPublicProfileChange(e) {
    const { value } = e.detail
    this.setData({
      'userInfo.publicProfile': value,
      hasChanges: true
    })
  },

  // 推送通知设置变化
  onPushNotificationChange(e) {
    const { value } = e.detail
    this.setData({
      'userInfo.pushNotification': value,
      hasChanges: true
    })
  },

  // 营销推送设置变化
  onMarketingPushChange(e) {
    const { value } = e.detail
    this.setData({
      'userInfo.marketingPush': value,
      hasChanges: true
    })
  },

  // 更新验证状态
  updateValidationState(field, valid, errors) {
    const validationErrors = { ...this.data.validationErrors }
    
    if (valid) {
      delete validationErrors[field]
    } else {
      validationErrors[field] = errors
    }
    
    this.setData({ validationErrors })
    this.checkFormValid()
  },

  // 检查表单是否有效
  checkFormValid() {
    const { userInfo, validationErrors } = this.data
    
    // 检查必填字段
    const hasRequiredFields = userInfo.nickname && userInfo.nickname.trim().length > 0
    
    // 检查验证错误
    const hasErrors = Object.keys(validationErrors).length > 0
    
    const formValid = hasRequiredFields && !hasErrors
    
    this.setData({ formValid })
  },

  // 检查未保存的更改
  checkUnsavedChanges() {
    const hasChanges = JSON.stringify(this.data.userInfo) !== JSON.stringify(this.data.originalUserInfo)
    this.setData({ hasChanges })
  },

  // 保存资料
  async saveProfile() {
    if (!this.data.formValid || this.data.saving) {
      return
    }

    try {
      this.setData({ saving: true })
      
      await userAPI.updateUserProfile(this.data.userInfo)
      
      // 更新原始数据
      this.setData({
        originalUserInfo: JSON.parse(JSON.stringify(this.data.userInfo)),
        hasChanges: false
      })
      
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
      
      // 更新全局用户信息
      app.globalData.userInfo = { ...this.data.userInfo }
      
    } catch (error) {
      console.error('保存资料失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    } finally {
      this.setData({ saving: false })
    }
  },

  // 重置表单
  resetForm() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有修改吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            userInfo: JSON.parse(JSON.stringify(this.data.originalUserInfo)),
            hasChanges: false,
            validationErrors: {}
          })
          
          // 重置茶叶偏好
          const teaTypes = this.data.teaTypes.map(type => ({
            ...type,
            selected: this.data.originalUserInfo.teaPreferences && 
                     this.data.originalUserInfo.teaPreferences.includes(type.id)
          }))
          
          this.setData({ teaTypes })
          
          wx.showToast({
            title: '已重置',
            icon: 'success'
          })
        }
      }
    })
  },

  // 修改密码
  changePassword() {
    wx.navigateTo({
      url: '/pages/change-password/change-password'
    })
  },

  // 绑定手机号
  bindPhone() {
    wx.navigateTo({
      url: '/pages/bind-phone/bind-phone'
    })
  },

  // 绑定邮箱
  bindEmail() {
    wx.navigateTo({
      url: '/pages/bind-email/bind-email'
    })
  },

  // 注销账户
  deleteAccount() {
    wx.showModal({
      title: '危险操作',
      content: '注销账户将永久删除您的所有数据，此操作不可恢复。确定要继续吗？',
      confirmText: '确定注销',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.confirmDeleteAccount()
        }
      }
    })
  },

  // 确认注销账户
  async confirmDeleteAccount() {
    try {
      wx.showLoading({ title: '处理中...' })
      
      await userAPI.deleteAccount()
      
      wx.showToast({
        title: '账户已注销',
        icon: 'success'
      })
      
      // 清除本地数据并返回登录页
      app.globalData.userInfo = null
      wx.clearStorageSync()
      
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/login/login'
        })
      }, 2000)
      
    } catch (error) {
      console.error('注销账户失败:', error)
      wx.showToast({
        title: '注销失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  }
})

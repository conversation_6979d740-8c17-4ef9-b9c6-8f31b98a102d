// 帮助中心页面
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    // 搜索相关
    searchKeyword: '',
    searchResults: [],
    
    // 热门问题
    hotQuestions: [],

    // 帮助分类
    helpCategories: [],
    
    // 所有问题数据（用于搜索）
    allQuestions: []
  },

  // 页面加载
  onLoad(options) {
    this.loadHelpData()
  },

  // 页面显示
  onShow() {
  },

  // 加载帮助数据
  async loadHelpData() {
    try {
      // 并行加载热门问题和分类
      const [hotRes, categoriesRes, allRes] = await Promise.all([
        api.helpApi.getHotQuestions().catch(() => ({ data: [] })),
        api.helpApi.getCategories().catch(() => ({ data: [] })),
        api.helpApi.getAllQuestions().catch(() => ({ data: [] }))
      ])

      // 更新热门问题
      if (hotRes.data && hotRes.data.length > 0) {
        this.setData({ hotQuestions: hotRes.data })
      } else {
        // 使用默认热门问题
        this.setDefaultHotQuestions()
      }

      // 更新分类
      if (categoriesRes.data && categoriesRes.data.length > 0) {
        this.setData({ helpCategories: categoriesRes.data })
      } else {
        // 使用默认分类
        this.setDefaultCategories()
      }

      // 更新所有问题（用于搜索）
      if (allRes.data && allRes.data.length > 0) {
        this.setData({ allQuestions: allRes.data })
      } else {
        // 使用默认问题数据
        this.setDefaultAllQuestions()
      }
    } catch (error) {
      console.error('加载帮助数据失败:', error)
      // 加载失败时使用默认数据
      this.setDefaultHotQuestions()
      this.setDefaultCategories()
      this.setDefaultAllQuestions()
    }
  },

  // 设置默认热门问题
  setDefaultHotQuestions() {
    this.setData({
      hotQuestions: [
        { id: 1, question: '如何注册和登录账户？' },
        { id: 2, question: '茶地认购流程是什么？' },
        { id: 3, question: '如何查看我的收益？' },
        { id: 4, question: '支付方式有哪些？' },
        { id: 5, question: '如何联系客服？' }
      ]
    })
  },

  // 设置默认分类
  setDefaultCategories() {
    this.setData({
      helpCategories: [
        { id: 1, name: '账户相关', icon: '👤', count: 8 },
        { id: 2, name: '认购流程', icon: '🛒', count: 12 },
        { id: 3, name: '收益查看', icon: '💰', count: 6 },
        { id: 4, name: '支付问题', icon: '💳', count: 10 },
        { id: 5, name: '技术支持', icon: '🔧', count: 5 },
        { id: 6, name: '其他问题', icon: '❓', count: 7 }
      ]
    })
  },

  // 设置默认所有问题
  setDefaultAllQuestions() {
    this.setData({
      allQuestions: [
        { id: 1, question: '如何注册和登录账户？', category: '账户相关' },
        { id: 2, question: '茶地认购流程是什么？', category: '认购流程' },
        { id: 3, question: '如何查看我的收益？', category: '收益查看' },
        { id: 4, question: '支付方式有哪些？', category: '支付问题' },
        { id: 5, question: '如何联系客服？', category: '技术支持' },
        { id: 6, question: '忘记密码怎么办？', category: '账户相关' },
        { id: 7, question: '认购后如何查看茶地信息？', category: '认购流程' },
        { id: 8, question: '收益什么时候到账？', category: '收益查看' },
        { id: 9, question: '支付失败怎么办？', category: '支付问题' },
        { id: 10, question: '如何修改个人信息？', category: '账户相关' }
      ]
    })
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({ searchKeyword: keyword })
    
    if (keyword.trim()) {
      this.performSearch(keyword)
    } else {
      this.setData({ searchResults: [] })
    }
  },

  // 执行搜索
  performSearch(keyword) {
    const results = this.data.allQuestions.filter(item => 
      item.question.toLowerCase().includes(keyword.toLowerCase()) ||
      item.category.toLowerCase().includes(keyword.toLowerCase())
    )
    
    this.setData({ searchResults: results })
  },

  // 搜索确认
  searchHelp() {
    if (this.data.searchKeyword.trim()) {
      this.performSearch(this.data.searchKeyword)
    }
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchKeyword: '',
      searchResults: []
    })
  },

  // 查看问题详情
  viewQuestion(e) {
    const { id } = e.currentTarget.dataset
    const question = this.data.allQuestions.find(q => q.id == id)

    if (question) {
      wx.showModal({
        title: question.question,
        content: this.getAnswerById(id),
        showCancel: false,
        confirmText: '知道了'
      })
    }
  },

  // 根据ID获取答案
  getAnswerById(id) {
    const answers = {
      1: '您可以使用微信快速登录，首次登录会自动创建账户。登录后请完善个人信息以获得更好的服务体验。',
      2: '1. 浏览茶地列表选择心仪茶地\n2. 查看茶地详情和投资信息\n3. 选择认购面积\n4. 填写联系信息\n5. 完成支付\n6. 获得认购凭证',
      3: '在"我的"页面点击"收益记录"可查看详细收益情况，包括分红记录、收益趋势等信息。',
      4: '支持微信支付、支付宝支付等多种支付方式，安全便捷。',
      5: '您可以通过以下方式联系客服：\n• 拨打客服电话：************\n• 发送邮件：<EMAIL>\n• 在线客服（开发中）',
      6: '如果忘记密码，请联系客服重置密码。我们会通过手机验证码的方式帮您重新设置。',
      7: '认购成功后，在"我的茶地"页面可以查看您认购的所有茶地信息，包括位置、面积、收益等。',
      8: '收益会根据茶叶收获情况定期发放，具体时间请关注系统通知。',
      9: '如果支付失败，请检查网络连接和支付账户余额，或尝试其他支付方式。如仍有问题请联系客服。',
      10: '在"设置"页面点击"个人资料"可以修改昵称、头像、联系方式等个人信息。'
    }
    return answers[id] || '暂无详细答案，请联系客服获取帮助。'
  },

  // 查看分类
  viewCategory(e) {
    const { id } = e.currentTarget.dataset
    const category = this.data.helpCategories.find(c => c.id == id)

    if (category) {
      const categoryQuestions = this.data.allQuestions.filter(q => q.category === category.name)

      wx.showActionSheet({
        itemList: categoryQuestions.map(q => q.question),
        success: (res) => {
          const selectedQuestion = categoryQuestions[res.tapIndex]
          if (selectedQuestion) {
            this.viewQuestion({ currentTarget: { dataset: { id: selectedQuestion.id } } })
          }
        }
      })
    }
  },

  // 联系客服
  contactService() {
    wx.showActionSheet({
      itemList: ['在线客服', '拨打电话'],
      success: (res) => {
        if (res.tapIndex === 0) {
          wx.showToast({
            title: '在线客服开发中',
            icon: 'none'
          })
        } else if (res.tapIndex === 1) {
          this.callService()
        }
      }
    })
  },

  // 拨打客服电话
  callService() {
    wx.makePhoneCall({
      phoneNumber: '************'
    })
  },

  // 意见反馈
  feedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    })
  },

  // 查看使用教程
  viewTutorial() {
    wx.showModal({
      title: '使用教程',
      content: '📖 新手入门指南\n\n1. 注册登录：使用微信快速登录\n2. 浏览茶地：查看茶地详情和投资信息\n3. 认购茶地：选择面积完成支付\n4. 监控收益：实时查看茶地状况\n\n更多详细教程请关注我们的公众号或联系客服。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 复制邮箱
  copyEmail() {
    wx.setClipboardData({
      data: '<EMAIL>',
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success'
        })
      }
    })
  },

  // 查看位置
  viewLocation() {
    wx.openLocation({
      latitude: 30.2741,
      longitude: 120.1551,
      name: '两山茶业有限公司',
      address: '浙江省杭州市西湖区茶叶大厦'
    })
  }
})

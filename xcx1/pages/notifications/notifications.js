// 消息通知页面
const app = getApp()
const { NotificationManager, MESSAGE_TYPES, MESSAGE_PRIORITY } = require('../../utils/notification.js')
const { LoadingManager, PageLoadingMixin } = require('../../utils/loading.js')
const api = require('../../api/index.js')

Page(Object.assign({}, PageLoadingMixin, {
  data: {
    notifications: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    unreadCount: 0,
    totalCount: 0,
    currentTab: 'all',
    refreshing: false
  },

  // 页面加载
  onLoad(options) {
    // 初始化消息管理器
    NotificationManager.init()

    // 监听消息变化
    NotificationManager.addListener(this.onMessageChange.bind(this))

    this.loadNotifications()
  },

  // 页面显示
  onShow() {
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ page: 1, hasMore: true })
    this.loadNotifications(true).finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadNotifications()
    }
  },

  // 加载通知列表
  loadNotifications(refresh = false) {
    if (this.data.loading && !refresh) return

    this.setData({
      loading: true,
      refreshing: refresh
    })

    try {
      // 使用本地消息管理器获取数据
      const { currentTab, page, pageSize } = this.data

      const options = {
        limit: pageSize,
        offset: refresh ? 0 : (page - 1) * pageSize
      }

      // 根据当前标签页筛选
      if (currentTab !== 'all') {
        if (currentTab === 'unread') {
          options.unreadOnly = true
        } else {
          options.type = currentTab
        }
      }

      const result = NotificationManager.getMessages(options)

      // 格式化消息数据
      const formattedMessages = result.messages.map(msg => ({
        ...msg,
        timeText: this.formatTime(msg.timestamp),
        iconText: this.getMessageIcon(msg.type),
        typeText: this.getMessageTypeText(msg.type),
        priorityText: this.getPriorityText(msg.priority)
      }))

      if (refresh) {
        this.setData({
          notifications: formattedMessages,
          page: 1,
          hasMore: formattedMessages.length >= pageSize
        })
      } else {
        this.setData({
          notifications: [...this.data.notifications, ...formattedMessages],
          page: this.data.page + 1,
          hasMore: formattedMessages.length >= pageSize
        })
      }

      this.setData({
        unreadCount: result.unreadCount,
        totalCount: result.total
      })

    } catch (error) {
      console.error('加载消息失败:', error)

      // 如果是首次加载失败，创建一些示例消息
      if (this.data.notifications.length === 0) {
        this.createSampleMessages()
      }

      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      })
    }
  },

  // 消息变化监听
  onMessageChange(event, data) {
    // 刷新消息列表
    this.loadNotifications(true)
  },

  // 创建示例消息
  createSampleMessages() {
    const sampleMessages = [
      {
        type: MESSAGE_TYPES.SYSTEM,
        title: '欢迎使用茶管家',
        content: '感谢您使用茶管家小程序，开始您的茶园投资之旅吧！',
        priority: MESSAGE_PRIORITY.NORMAL,
        persistent: true
      },
      {
        type: MESSAGE_TYPES.ORDER,
        title: '订单状态更新',
        content: '您的茶地认购订单已确认，请及时查看订单详情。',
        priority: MESSAGE_PRIORITY.HIGH,
        data: { url: '/pages/orders/orders' }
      },
      {
        type: MESSAGE_TYPES.EARNINGS,
        title: '收益到账通知',
        content: '恭喜您获得本月茶园收益 ¥128.50，已自动转入您的账户。',
        priority: MESSAGE_PRIORITY.NORMAL,
        data: { url: '/pages/analytics/analytics' }
      }
    ]

    sampleMessages.forEach(msg => {
      NotificationManager.sendLocalNotification({
        ...msg,
        showToast: false
      })
    })

    // 重新加载
    this.loadNotifications(true)
  },

  // 格式化时间
  formatTime(timestamp) {
    const now = Date.now()
    const diff = now - timestamp

    if (diff < 60000) {
      return '刚刚'
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`
    } else if (diff < 604800000) {
      return `${Math.floor(diff / 86400000)}天前`
    } else {
      const date = new Date(timestamp)
      return `${date.getMonth() + 1}/${date.getDate()}`
    }
  },

  // 获取消息图标
  getMessageIcon(type) {
    const icons = {
      [MESSAGE_TYPES.SYSTEM]: '⚙️',
      [MESSAGE_TYPES.ORDER]: '📋',
      [MESSAGE_TYPES.PAYMENT]: '💳',
      [MESSAGE_TYPES.TEA_FIELD]: '🌱',
      [MESSAGE_TYPES.EARNINGS]: '💰',
      [MESSAGE_TYPES.PROMOTION]: '🎉',
      [MESSAGE_TYPES.MAINTENANCE]: '🔧'
    }
    return icons[type] || '📨'
  },

  // 获取消息类型文本
  getMessageTypeText(type) {
    const texts = {
      [MESSAGE_TYPES.SYSTEM]: '系统消息',
      [MESSAGE_TYPES.ORDER]: '订单消息',
      [MESSAGE_TYPES.PAYMENT]: '支付消息',
      [MESSAGE_TYPES.TEA_FIELD]: '茶地消息',
      [MESSAGE_TYPES.EARNINGS]: '收益消息',
      [MESSAGE_TYPES.PROMOTION]: '推广消息',
      [MESSAGE_TYPES.MAINTENANCE]: '维护消息'
    }
    return texts[type] || '未知类型'
  },

  // 获取优先级文本
  getPriorityText(priority) {
    const texts = {
      [MESSAGE_PRIORITY.LOW]: '低',
      [MESSAGE_PRIORITY.NORMAL]: '普通',
      [MESSAGE_PRIORITY.HIGH]: '重要',
      [MESSAGE_PRIORITY.URGENT]: '紧急'
    }
    return texts[priority] || '普通'
  },

  // 查看通知详情
  viewNotificationDetail(e) {
    const { id } = e.currentTarget.dataset
    const notification = this.data.notifications.find(item => item.id === id)
    
    if (notification && !notification.is_read) {
      // 标记为已读
      this.markAsRead(id)
    }

    wx.showModal({
      title: notification.title,
      content: notification.content,
      showCancel: false
    })
  },

  // 标记为已读
  markAsRead(id) {
    const notifications = this.data.notifications.map(item => {
      if (item.id === id) {
        return { ...item, is_read: true }
      }
      return item
    })

    const unreadCount = notifications.filter(item => !item.is_read).length

    this.setData({ 
      notifications,
      unreadCount
    })
  },

  // 全部标记为已读
  markAllAsRead() {
    try {
      // 使用本地消息管理器标记所有消息为已读
      NotificationManager.markAllAsRead()

      // 更新本地状态
      const notifications = this.data.notifications.map(item => ({
        ...item,
        is_read: true
      }))

      this.setData({
        notifications,
        unreadCount: 0
      })

      wx.showToast({
        title: '已全部标记为已读',
        icon: 'success'
      })
    } catch (error) {
      console.error('标记已读失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  // 删除通知
  deleteNotification(e) {
    const { id } = e.currentTarget.dataset

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条通知吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            // 使用本地消息管理器删除消息
            NotificationManager.deleteMessage(id)

            // 更新本地状态
            const notifications = this.data.notifications.filter(item => item.id !== id)
            const unreadCount = notifications.filter(item => !item.is_read).length

            this.setData({
              notifications,
              unreadCount
            })

            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
          } catch (error) {
            console.error('删除通知失败:', error)
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 清空所有通知
  clearAllNotifications() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有通知吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            notifications: [],
            unreadCount: 0
          })

          wx.showToast({
            title: '清空成功',
            icon: 'success'
          })
        }
      }
    })
  }
}))

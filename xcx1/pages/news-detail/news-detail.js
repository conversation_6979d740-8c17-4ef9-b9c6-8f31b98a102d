// 新闻详情页面
const api = require('../../api/index.js')

Page({
  data: {
    newsId: null,
    news: null,
    loading: true,
    error: null
  },

  // 页面加载
  onLoad(options) {
    const newsId = options.id
    if (!newsId) {
      console.error('❌ 缺少新闻ID参数')
      this.setData({ 
        error: '缺少新闻ID参数',
        loading: false 
      })
      return
    }

    this.setData({ newsId })
    this.loadNewsDetail()
  },

  // 页面显示
  onShow() {
  },

  // 加载新闻详情
  loadNewsDetail() {
    this.setData({ loading: true, error: null })

    // 调用新闻详情API
    api.newsApi.getDetail(this.data.newsId).then(res => {
      if (res.code === 200 && res.data) {
        this.setData({
          news: res.data,
          loading: false
        })
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: res.data.title.length > 10 ? res.data.title.substring(0, 10) + '...' : res.data.title
        })
      } else {
        this.setData({
          error: res.message || '新闻不存在',
          loading: false
        })
      }
    }).catch(error => {
      console.error('❌ 新闻详情API调用失败:', error)
      this.setData({
        error: '加载失败，请重试',
        loading: false
      })
    })
  },

  // 重新加载
  onRetry() {
    this.loadNewsDetail()
  },

  // 分享
  onShareAppMessage() {
    const news = this.data.news
    if (news) {
      return {
        title: news.title,
        path: `/pages/news-detail/news-detail?id=${news.id}`,
        imageUrl: news.image_url || ''
      }
    }
    return {
      title: '茶园资讯',
      path: `/pages/news-detail/news-detail?id=${this.data.newsId}`
    }
  },

  // 返回首页
  goHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 查看更多新闻
  viewMoreNews() {
    wx.navigateTo({
      url: '/pages/news-list/news-list'
    })
  }
})

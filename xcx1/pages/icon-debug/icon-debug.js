// 图标调试页面
Page({
  data: {
    // 测试图标路径
    iconPaths: [
      'static/images/tabbar/home.png',
      'static/images/tabbar/home-active.png',
      'static/images/tabbar/tea.png',
      'static/images/tabbar/tea-active.png',
      'static/images/tabbar/monitor.png',
      'static/images/tabbar/monitor-active.png',
      'static/images/tabbar/chart.png',
      'static/images/tabbar/chart-active.png',
      'static/images/tabbar/profile.png',
      'static/images/tabbar/profile-active.png'
    ],
    
    // 测试结果
    testResults: [],
    
    // API配置
    apiConfig: null
  },

  onLoad() {
    this.testIconPaths()
    this.loadApiConfig()
  },

  // 测试图标路径
  testIconPaths() {
    const results = this.data.iconPaths.map(path => ({
      path: path,
      status: 'testing',
      error: null
    }))
    
    this.setData({ testResults: results })
  },

  // 图标加载成功
  onImageLoad(e) {
    const path = e.currentTarget.dataset.path
    const index = e.currentTarget.dataset.index
    const results = this.data.testResults
    if (results[index]) {
      results[index].status = 'success'
      this.setData({ testResults: results })
    }
  },

  // 图标加载失败
  onImageError(e) {
    const path = e.currentTarget.dataset.path
    const index = e.currentTarget.dataset.index
    
    console.error(`❌ 图标加载失败: ${path}`)
    
    const results = this.data.testResults
    if (results[index]) {
      results[index].status = 'error'
      results[index].error = '图标文件无法加载'
      this.setData({ testResults: results })
    }
  },

  // 加载API配置
  async loadApiConfig() {
    try {
      const result = await new Promise((resolve, reject) => {
        wx.request({
          url: 'https://teabuy.yizhangkj.com/api/v1/page-decoration/tabbar/config/',
          method: 'GET',
          header: {
            'Cache-Control': 'no-cache'
          },
          success: (res) => {
            if (res.data && res.data.code === 200) {
              resolve(res.data.data.config)
            } else {
              reject(new Error(res.data.message || 'API调用失败'))
            }
          },
          fail: reject
        })
      })
      // Debug log removed
      
      this.setData({ apiConfig: result })
      
    } catch (error) {
      console.error('❌ API配置加载失败:', error)
      wx.showToast({
        title: 'API加载失败',
        icon: 'error'
      })
    }
  },

  // 重新测试
  retestIcons() {
    this.testIconPaths()
  },

  // 重新加载API
  reloadApi() {
    this.setData({ apiConfig: null })
    this.loadApiConfig()
  },

  // 测试自定义导航组件
  testCustomTabBar() {
    const customTabBar = this.selectComponent('#custom-tabbar')
    if (customTabBar) {
      // 重新加载配置
      customTabBar.reloadConfig().then(() => {
        wx.showToast({
          title: '组件测试完成',
          icon: 'success'
        })
      }).catch((error) => {
        console.error('❌ 自定义导航配置重新加载失败:', error)
        wx.showToast({
          title: '组件测试失败',
          icon: 'error'
        })
      })
    } else {
      console.error('❌ 未找到自定义导航组件')
      wx.showToast({
        title: '未找到组件',
        icon: 'error'
      })
    }
  },

  // 查看详细信息
  showDetails() {
    const successCount = this.data.testResults.filter(r => r.status === 'success').length
    const errorCount = this.data.testResults.filter(r => r.status === 'error').length
    const testingCount = this.data.testResults.filter(r => r.status === 'testing').length
    
    const apiItems = this.data.apiConfig ? this.data.apiConfig.list.length : 0
    
    const details = `
图标测试结果:
• 成功: ${successCount}
• 失败: ${errorCount}  
• 测试中: ${testingCount}
• 总计: ${this.data.testResults.length}

API配置:
• 导航项数量: ${apiItems}
• 配置状态: ${this.data.apiConfig ? '已加载' : '未加载'}

建议:
${errorCount > 0 ? '• 检查失败的图标文件是否存在\n• 确认图标路径是否正确' : '• 所有图标测试通过'}
${!this.data.apiConfig ? '• 检查API接口是否正常' : '• API配置正常'}
    `.trim()

    wx.showModal({
      title: '调试详情',
      content: details,
      showCancel: false,
      confirmText: '确定'
    })
  }
})

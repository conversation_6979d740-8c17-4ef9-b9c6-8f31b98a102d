// 发票管理页面
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    invoices: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },

  // 页面加载
  onLoad(options) {
    this.loadInvoices()
  },

  // 页面显示
  onShow() {
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ page: 1, hasMore: true })
    this.loadInvoices(true).finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadInvoices()
    }
  },

  // 加载发票列表
  async loadInvoices(refresh = false) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      // 调用发票列表API
      const params = {
        page: refresh ? 1 : this.data.currentPage,
        page_size: this.data.pageSize
      }

      const res = await api.invoiceApi.getList(params)

      if (res.code === 200) {
        const newInvoices = refresh ? res.data.results : [...this.data.invoices, ...res.data.results]

        this.setData({
          invoices: newInvoices,
          hasMore: res.data.next !== null,
          currentPage: refresh ? 2 : this.data.currentPage + 1
        })
      } else {
        // 使用默认数据
        this.setData({
          invoices: [],
          hasMore: false
        })
      }
    } catch (error) {
      console.error('加载发票列表失败:', error)

      // 使用默认数据
      if (refresh || this.data.invoices.length === 0) {
        this.setData({
          invoices: [
            {
              id: 'demo_001',
              invoice_number: 'FP202401001',
              order_id: 'ORD202401001',
              amount: 5000,
              status: 'pending',
              type: 'normal',
              created_at: new Date().toISOString(),
              title: '茶地认购发票'
            }
          ],
          hasMore: false
        })
      }

      wx.showToast({
        title: '网络错误，显示示例数据',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 申请开票
  applyInvoice() {
    wx.showToast({
      title: '申请开票功能开发中',
      icon: 'none'
    })
  },

  // 下载发票
  downloadInvoice(e) {
    const { id } = e.currentTarget.dataset
    wx.showToast({
      title: '下载功能开发中',
      icon: 'none'
    })
  },

  // 查看发票详情
  viewInvoiceDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.showToast({
      title: '发票详情功能开发中',
      icon: 'none'
    })
  }
})

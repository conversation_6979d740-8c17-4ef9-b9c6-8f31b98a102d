// 备份原始analytics.js文件
const api = require('../../api/index.js')

Page({
  data: {
    // 页面数据
    loading: true,
    
    // 概览数据
    overviewData: {
      revenue: 0,
      revenue_trend: 0,
      production: 0,
      production_trend: 0,
      quality_score: 0,
      quality_trend: 0,
      efficiency: 0,
      efficiency_trend: 0
    },

    // 收益图表数据
    revenueChartData: [],
    revenueChartLoading: false,

    // 产量数据
    productionData: {
      current_month: 0,
      monthly: [],
      spring: 0,
      spring_percent: 0,
      summer: 0,
      summer_percent: 0,
      autumn: 0,
      autumn_percent: 0
    },

    // 环境数据
    environmentData: {
      temperature: 0,
      temperature_status: 'unknown',
      humidity: 0,
      humidity_status: 'unknown',
      light: 0,
      light_status: 'unknown',
      ph: 0,
      ph_status: 'unknown',
      soil_moisture: 0,
      soil_status: 'unknown',
      weather: '未知',
      air_quality: 0,
      air_quality_status: 'unknown'
    },

    // 市场数据
    marketData: {
      price_trend: 'stable',
      price_change: '0%',
      market_share: 0,
      demand_level: 'low',
      competition_level: 'unknown'
    },

    // 建议数据
    suggestions: []
  },

  onLoad: function(options) {
    this.initPage()
  },

  onShow: function() {
    this.refreshData()
  },

  onPullDownRefresh: function() {
    this.refreshData()
  },

  // 初始化页面
  initPage: function() {
    this.loadPageData()
  },

  // 刷新数据
  refreshData: function() {
    this.loadPageData()
  },

  // 加载页面数据
  loadPageData: function() {
    this.setData({ loading: true })

    // 延迟停止下拉刷新
    setTimeout(function() {
      wx.stopPullDownRefresh()
    }, 1000)

    const promises = [
      this.loadOverviewData().catch(function(e) {
        return null
      }),
      this.loadRevenueChart().catch(function(e) {
        return null
      })
    ]

    const self = this
    Promise.all(promises).then(function() {
      self.setData({ loading: false })
    }).catch(function(error) {
      console.error('❌ 加载分析数据失败:', error)
      self.setData({ loading: false })
    })
  },

  // 加载概览数据
  loadOverviewData: function() {
    const self = this
    return new Promise(function(resolve, reject) {
      // 调用真实的分析概览API
      api.analyticsApi.getOverview().then(function(res) {
        if (res.code === 200 && res.data) {
          const data = res.data
          self.setData({
            overviewData: {
              revenue: parseFloat(data.total_earnings || 0),
              revenue_trend: self.parsePercentage(data.earnings_change),
              production: parseFloat(data.total_yield || 0),
              production_trend: self.parsePercentage(data.yield_change),
              quality_score: self.getQualityScore(data.avg_quality),
              quality_trend: self.parsePercentage(data.quality_change),
              efficiency: self.calculateEfficiency(data),
              efficiency_trend: self.parsePercentage(data.quality_change)
            }
          })
        } else {
          self.setEmptyOverviewData()
        }
        resolve()
      }).catch(function(error) {
        console.error('❌ 分析概览API调用失败:', error)
        self.setEmptyOverviewData()
        resolve()
      })
    })
  },

  // 加载收益图表
  loadRevenueChart: function() {
    const self = this
    return new Promise(function(resolve, reject) {
      // 调用真实的收益图表API
      api.analyticsApi.getEarnings().then(function(res) {
        if (res.code === 200 && res.data) {
          const chartData = self.processRevenueChartData(res.data)
          self.setData({ revenueChartData: chartData })
          self.drawRevenueChart()
        } else {
          self.setEmptyChartData()
        }
        self.setData({ revenueChartLoading: false })
        resolve()
      }).catch(function(error) {
        console.error('❌ 收益图表API调用失败:', error)
        self.setEmptyChartData()
        self.setData({ revenueChartLoading: false })
        resolve()
      })
    })
  },

  // 辅助方法
  parsePercentage: function(str) {
    if (!str) return 0
    const match = str.toString().match(/([+-]?\d+\.?\d*)/)
    return match ? parseFloat(match[1]) : 0
  },

  getQualityScore: function(quality) {
    const qualityMap = {
      '优质': 95,
      '良好': 85,
      '一般': 75,
      '较差': 65
    }
    return qualityMap[quality] || 80
  },

  calculateEfficiency: function(data) {
    const teaCount = parseInt(data.tea_count || 0)
    const totalYield = parseFloat(data.total_yield || 0)
    
    if (teaCount === 0) return 0
    
    const avgYieldPerField = totalYield / teaCount
    const maxExpectedYield = 50
    
    return Math.min(95, Math.max(60, (avgYieldPerField / maxExpectedYield) * 100))
  },

  setEmptyOverviewData: function() {
    this.setData({
      overviewData: {
        revenue: 0,
        revenue_trend: 0,
        production: 0,
        production_trend: 0,
        quality_score: 0,
        quality_trend: 0,
        efficiency: 0,
        efficiency_trend: 0
      }
    })
  },

  processRevenueChartData: function(data) {
    if (data.chart_data) {
      return data.chart_data
    }
    
    const months = ['1月', '2月', '3月', '4月', '5月', '6月']
    const totalEarnings = parseFloat(data.total_earnings || 0)
    const monthlyAvg = totalEarnings / 6
    
    const result = []
    for (let i = 0; i < months.length; i++) {
      const variance = (Math.random() - 0.5) * 0.3
      const actual = Math.round(monthlyAvg * (1 + variance))
      const expected = Math.round(monthlyAvg * (1 + (i * 0.05)))
      
      result.push({
        month: months[i],
        actual: Math.max(0, actual),
        expected: Math.max(0, expected)
      })
    }
    
    return result
  },

  setEmptyChartData: function() {
    const emptyData = []
    const months = ['1月', '2月', '3月', '4月', '5月', '6月']
    
    for (let i = 0; i < months.length; i++) {
      emptyData.push({
        month: months[i],
        actual: 0,
        expected: 0
      })
    }
    
    this.setData({ revenueChartData: emptyData })
    this.drawRevenueChart()
  },

  drawRevenueChart: function() {
    // 简化的图表绘制逻辑
  }
})

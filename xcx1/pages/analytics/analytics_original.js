// 分析页面
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    // 时间范围
    timeRange: 'month',

    // 页面状态
    showLoginTip: false,

    // 概览数据
    overviewData: {
      revenue: 0,
      revenue_trend: 0,
      production: 0,
      production_trend: 0,
      quality_score: 0,
      quality_trend: 0,
      efficiency: 0,
      efficiency_trend: 0
    },
    
    // 收益图表
    revenueChartType: 'line',
    revenueChartLoading: false,
    revenueChartData: [],
    
    // 产量数据
    productionData: {
      current_month: 0,
      monthly: [],
      spring: 0,
      spring_percent: 0,
      summer: 0,
      summer_percent: 0,
      autumn: 0,
      autumn_percent: 0
    },

    // 性能指标数据
    performanceData: {
      quality: 0,
      water_efficiency: 0,
      light_efficiency: 0
    },
    
    // 环境数据
    environmentData: {
      temperature: { score: 0, impact: 'good', impact_text: '', description: '' },
      humidity: { score: 0, impact: 'good', impact_text: '', description: '' },
      light: { score: 0, impact: 'good', impact_text: '', description: '' },
      soil: { score: 0, impact: 'good', impact_text: '', description: '' }
    },
    
    // 市场数据
    marketData: {
      current_price: 0,
      price_trend: 0,
      expected_price: 0,
      update_time: '',
      forecast: []
    },
    
    // 建议列表
    suggestions: []
  },

  // 页面加载
  onLoad(options) {
    try {
      this.loadAllData()
    } catch (error) {
      console.error('❌ 页面初始化失败:', error)
    }
  },

  // 页面显示
  onShow() {
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadAllData()

    // 延迟停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 加载所有数据
  loadAllData() {
    // 并行加载所有数据，每个都独立处理错误
    const promises = [
      this.loadOverviewData().catch(e => {
        return null
      }),
      this.loadRevenueChart().catch(e => {
        return null
      }),
      this.loadProductionData().catch(e => {
        return null
      }),
      this.loadEnvironmentData().catch(e => {
        return null
      }),
      this.loadMarketData().catch(e => {
        return null
      }),
      this.loadSuggestions().catch(e => {
        return null
      })
    ]

    Promise.all(promises).then(() => {
    }).catch(error => {
      console.error('❌ 加载分析数据失败:', error)
    })
  },

  // 加载概览数据
  loadOverviewData() {
    return new Promise((resolve, reject) => {
      // 调用真实的分析概览API
      api.analyticsApi.getOverview().then(res => {
        if (res.code === 200 && res.data) {
          const data = res.data
          this.setData({
            overviewData: {
              revenue: parseFloat(data.total_earnings || 0),
              revenue_trend: this.parsePercentage(data.earnings_change),
              production: parseFloat(data.total_yield || 0),
              production_trend: this.parsePercentage(data.yield_change),
              quality_score: this.getQualityScore(data.avg_quality),
              quality_trend: this.parsePercentage(data.quality_change),
              efficiency: this.calculateEfficiency(data),
              efficiency_trend: this.parsePercentage(data.quality_change)
            }
          })
        } else {
          this.setEmptyOverviewData()
        }
        resolve()
      }).catch(error => {
        console.error('❌ 分析概览API调用失败:', error)
        this.setEmptyOverviewData()
        resolve()
      })
    })
  },

  // 设置空概览数据
  setEmptyOverviewData() {
    this.setData({
      overviewData: {
        revenue: 0,
        revenue_trend: 0,
        production: 0,
        production_trend: 0,
        quality_score: 0,
        quality_trend: 0,
        efficiency: 0,
        efficiency_trend: 0
      }
    })
  },

  // 解析百分比字符串
  parsePercentage(str) {
    if (!str) return 0
    const match = str.toString().match(/([+-]?\d+\.?\d*)/)
    return match ? parseFloat(match[1]) : 0
  },

  // 获取品质评分
  getQualityScore(quality) {
    const qualityMap = {
      '优质': 95,
      '良好': 85,
      '一般': 75,
      '较差': 65
    }
    return qualityMap[quality] || 80
  },

  // 计算效率指标
  calculateEfficiency(data) {
    const teaCount = parseInt(data.tea_count || 0)
    const totalYield = parseFloat(data.total_yield || 0)

    if (teaCount === 0) return 0

    // 基于茶地数量和产量计算效率
    const avgYieldPerField = totalYield / teaCount
    const maxExpectedYield = 50 // 假设每个茶地最大产量50kg

    return Math.min(95, Math.max(60, (avgYieldPerField / maxExpectedYield) * 100))
  },

  // 加载收益图表
  loadRevenueChart() {
    return new Promise((resolve, reject) => {
      this.setData({ revenueChartLoading: true })

      // 调用真实的收益图表API
      api.analyticsApi.getEarnings().then(res => {
        if (res.code === 200 && res.data) {
          const chartData = this.processRevenueChartData(res.data)
          this.setData({ revenueChartData: chartData })
          this.drawRevenueChart()
        } else {
          this.setEmptyChartData()
        }
        this.setData({ revenueChartLoading: false })
        resolve()
      }).catch(error => {
        console.error('❌ 收益图表API调用失败:', error)
        this.setEmptyChartData()
        this.setData({ revenueChartLoading: false })
        resolve()
      })
    })
  },

  // 处理收益图表数据
  processRevenueChartData(data) {
    // 如果后端返回的是图表数据
    if (data.chart_data) {
      return data.chart_data
    }

    // 如果后端返回的是统计数据，生成图表数据
    const months = ['1月', '2月', '3月', '4月', '5月', '6月']
    const totalEarnings = parseFloat(data.total_earnings || 0)
    const monthlyAvg = totalEarnings / 6

    return months.map((month, index) => {
      const variance = (Math.random() - 0.5) * 0.3 // ±15%的随机变化
      const actual = Math.round(monthlyAvg * (1 + variance))
      const expected = Math.round(monthlyAvg * (1 + (index * 0.05))) // 逐月增长5%

      return {
        month,
        actual: Math.max(0, actual),
        expected: Math.max(0, expected)
      }
    })
  },

  // 设置空图表数据
  setEmptyChartData() {
    const emptyData = [
      { month: '1月', actual: 0, expected: 0 },
      { month: '2月', actual: 0, expected: 0 },
      { month: '3月', actual: 0, expected: 0 },
      { month: '4月', actual: 0, expected: 0 },
      { month: '5月', actual: 0, expected: 0 },
      { month: '6月', actual: 0, expected: 0 }
    ]

    this.setData({ revenueChartData: emptyData })
    this.drawRevenueChart()
  },

  // 绘制收益图表
  drawRevenueChart() {
    const ctx = wx.createCanvasContext('revenueChart', this)
    const revenueChartData = this.data.revenueChartData
    
    if (revenueChartData.length === 0) return
    
    // 清空画布
    ctx.clearRect(0, 0, 300, 200)
    
    // 绘制坐标轴
    ctx.setStrokeStyle('#E0E0E0')
    ctx.setLineWidth(1)
    
    // Y轴
    ctx.beginPath()
    ctx.moveTo(40, 20)
    ctx.lineTo(40, 160)
    ctx.stroke()
    
    // X轴
    ctx.beginPath()
    ctx.moveTo(40, 160)
    ctx.lineTo(280, 160)
    ctx.stroke()
    
    // 绘制数据线
    if (this.data.revenueChartType === 'line') {
      this.drawLineChart(ctx, revenueChartData)
    } else {
      this.drawBarChart(ctx, revenueChartData)
    }
    
    ctx.draw()
  },

  // 绘制折线图
  drawLineChart(ctx, data) {
    const values = []
    data.forEach(item => {
      values.push(item.actual, item.expected)
    })
    const maxValue = Math.max.apply(Math, values)
    const minValue = Math.min.apply(Math, values)
    const range = maxValue - minValue || 1
    
    // 绘制实际收益线
    ctx.setStrokeStyle('#2E7D32')
    ctx.setLineWidth(2)
    ctx.beginPath()
    
    data.forEach((item, index) => {
      const x = 40 + (index / (data.length - 1)) * 240
      const y = 160 - ((item.actual - minValue) / range) * 140
      
      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })
    ctx.stroke()
    
    // 绘制预期收益线
    ctx.setStrokeStyle('#4CAF50')
    ctx.setLineWidth(2)
    ctx.setLineDash([5, 5])
    ctx.beginPath()
    
    data.forEach((item, index) => {
      const x = 40 + (index / (data.length - 1)) * 240
      const y = 160 - ((item.expected - minValue) / range) * 140
      
      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })
    ctx.stroke()
    ctx.setLineDash([])
  },

  // 绘制柱状图
  drawBarChart(ctx, data) {
    const values = []
    data.forEach(item => {
      values.push(item.actual, item.expected)
    })
    const maxValue = Math.max.apply(Math, values)
    const barWidth = 240 / data.length / 2
    
    data.forEach((item, index) => {
      const x = 40 + (index / data.length) * 240
      
      // 实际收益柱
      const actualHeight = (item.actual / maxValue) * 140
      ctx.setFillStyle('#2E7D32')
      ctx.fillRect(x, 160 - actualHeight, barWidth - 2, actualHeight)
      
      // 预期收益柱
      const expectedHeight = (item.expected / maxValue) * 140
      ctx.setFillStyle('#4CAF50')
      ctx.fillRect(x + barWidth, 160 - expectedHeight, barWidth - 2, expectedHeight)
    })
  },

  // 加载产量数据
  loadProductionData() {
    return new Promise((resolve, reject) => {
      // 调用真实的产量数据API
      api.analyticsApi.getProduction().then(res => {
        if (res.code === 200 && res.data) {
          const productionData = this.processProductionData(res.data)
          this.setData({
            productionData: productionData
          })
        } else {
          this.setEmptyProductionData()
        }
        resolve()
      }).catch(error => {
        console.error('❌ 产量数据API调用失败:', error)
        this.setEmptyProductionData()
        resolve()
      })
    })
  },

  // 加载环境数据
  loadEnvironmentData() {
    return new Promise((resolve, reject) => {
      // 调用真实的环境数据API
      api.analyticsApi.getEnvironment().then(res => {
        if (res.code === 200 && res.data) {
          const envData = this.processEnvironmentData(res.data)
          this.setData({
            environmentData: envData
          })
        } else {
          this.setEmptyEnvironmentData()
        }
        resolve()
      }).catch(error => {
        console.error('❌ 环境数据API调用失败:', error)
        this.setEmptyEnvironmentData()
        resolve()
      })

    })
  },

  // 处理环境数据
  processEnvironmentData(data) {
    return {
      temperature: parseFloat(data.avg_temperature || 22.5),
      temperature_status: this.getEnvironmentStatus(data.avg_temperature, 20, 25),
      humidity: parseFloat(data.avg_humidity || 65),
      humidity_status: this.getEnvironmentStatus(data.avg_humidity, 60, 70),
      light: parseFloat(data.avg_light || 35000),
      light_status: this.getEnvironmentStatus(data.avg_light, 30000, 40000),
      ph: parseFloat(data.avg_ph || 6.8),
      ph_status: this.getEnvironmentStatus(data.avg_ph, 6.5, 7.0),
      soil_moisture: this.calculateSoilMoisture(data),
      soil_status: 'good',
      weather: data.weather || '多云',
      air_quality: data.air_quality || 85,
      air_quality_status: this.getAirQualityStatus(data.air_quality || 85)
    }
  },

  // 设置空环境数据
  setEmptyEnvironmentData() {
    this.setData({
      environmentData: {
        temperature: 0,
        temperature_status: 'unknown',
        humidity: 0,
        humidity_status: 'unknown',
        light: 0,
        light_status: 'unknown',
        ph: 0,
        ph_status: 'unknown',
        soil_moisture: 0,
        soil_status: 'unknown',
        weather: '未知',
        air_quality: 0,
        air_quality_status: 'unknown'
      }
    })
  },

  // 获取环境状态
  getEnvironmentStatus(value, minGood, maxGood) {
    if (!value) return 'unknown'
    if (value >= minGood && value <= maxGood) return 'good'
    if (value < minGood * 0.8 || value > maxGood * 1.2) return 'bad'
    return 'warning'
  },

  // 获取空气质量状态
  getAirQualityStatus(value) {
    if (value >= 80) return 'good'
    if (value >= 60) return 'warning'
    return 'bad'
  },

  // 计算土壤湿度
  calculateSoilMoisture(data) {
    // 基于湿度和其他因素计算土壤湿度
    const humidity = parseFloat(data.avg_humidity || 65)
    return Math.round(humidity * 0.8) // 简化计算
  },

  // 加载市场数据
  loadMarketData() {
    return new Promise((resolve, reject) => {
      // 调用真实的市场数据API
      api.analyticsApi.getMarketData().then(res => {
        if (res.code === 200 && res.data) {
          const marketData = this.processMarketData(res.data)
          this.setData({
            marketData: marketData
          })
        } else {
          this.setEmptyMarketData()
        }
        resolve()
      }).catch(error => {
        console.error('❌ 市场数据API调用失败:', error)
        this.setEmptyMarketData()
        resolve()
      })
    })
  },

  // 加载建议
  loadSuggestions() {
    return new Promise((resolve, reject) => {
      // 基于用户数据生成智能建议
      this.generateIntelligentSuggestions().then(suggestions => {
        this.setData({
          suggestions: suggestions
        })
        resolve()
      }).catch(error => {
        console.error('❌ 生成建议失败:', error)
        this.setEmptySuggestions()
        resolve()
      })
    })
  },

  // 处理市场数据
  processMarketData(data) {
    // 基于用户统计数据生成市场分析
    const teaCount = parseInt(data.tea_count || 0)
    const totalEarnings = parseFloat(data.total_earnings || 0)

    return {
      price_trend: totalEarnings > 1000 ? 'up' : 'stable',
      price_change: this.calculatePriceChange(totalEarnings),
      market_share: this.calculateMarketShare(teaCount),
      demand_level: this.getDemandLevel(teaCount),
      competition_level: 'medium'
    }
  },

  // 设置空市场数据
  setEmptyMarketData() {
    this.setData({
      marketData: {
        price_trend: 'stable',
        price_change: '0%',
        market_share: 0,
        demand_level: 'low',
        competition_level: 'unknown'
      }
    })
  },

  // 计算价格变化
  calculatePriceChange(earnings) {
    if (earnings > 5000) return '+15.2%'
    if (earnings > 2000) return '****%'
    if (earnings > 500) return '****%'
    return '0%'
  },

  // 计算市场份额
  calculateMarketShare(teaCount) {
    return Math.min(25, Math.max(1, teaCount * 2.5))
  },

  // 获取需求水平
  getDemandLevel(teaCount) {
    if (teaCount >= 5) return 'high'
    if (teaCount >= 2) return 'medium'
    return 'low'
  },

  // 生成智能建议
  generateIntelligentSuggestions() {
    return new Promise((resolve) => {
      // 基于用户数据生成个性化建议
      const overviewData = this.data.overviewData || {}
      const environmentData = this.data.environmentData || {}
      const suggestions = []

      // 基于收益情况的建议
      if (overviewData.revenue < 1000) {
        suggestions.push({
          type: 'investment',
          title: '增加投资规模',
          description: '考虑增加茶地认购数量，提高整体收益水平',
          priority: 'high'
        })
      }

      // 基于环境数据的建议
      if (environmentData.temperature > 25) {
        suggestions.push({
          type: 'environment',
          title: '注意温度控制',
          description: '当前温度偏高，建议增加遮阴措施',
          priority: 'medium'
        })
      }

      // 基于产量的建议
      if (overviewData.production < 100) {
        suggestions.push({
          type: 'production',
          title: '优化管理措施',
          description: '建议加强茶园管理，提高茶叶产量',
          priority: 'medium'
        })
      }

      // 通用建议
      suggestions.push({
        type: 'general',
        title: '定期查看分析报告',
        description: '建议每周查看一次分析数据，及时调整投资策略',
        priority: 'low'
      })

      resolve(suggestions)
    })
  },

  // 设置空建议
  setEmptySuggestions() {
    this.setData({
      suggestions: []
    })
  },

  // 处理产量数据
  processProductionData(data) {
    const totalYield = parseFloat(data.total_yield || 0)
    const months = ['1月', '2月', '3月', '4月', '5月', '6月']
    const monthlyAvg = totalYield / 6

    // 生成月度产量数据
    const monthly = months.map((month, index) => {
      const variance = (Math.random() - 0.5) * 0.2 // ±10%的随机变化
      const value = Math.round(monthlyAvg * (1 + variance))
      return {
        month,
        value: Math.max(0, value)
      }
    })

    // 计算季节产量分布
    const springYield = Math.round(totalYield * 0.45) // 春茶45%
    const summerYield = Math.round(totalYield * 0.35) // 夏茶35%
    const autumnYield = Math.round(totalYield * 0.20) // 秋茶20%

    return {
      current_month: monthly[5]?.value || 0, // 当前月（6月）
      monthly: monthly,
      spring: springYield,
      spring_percent: 45,
      summer: summerYield,
      summer_percent: 35,
      autumn: autumnYield,
      autumn_percent: 20
    }
  },

  // 设置空产量数据
  setEmptyProductionData() {
    this.setData({
      productionData: {
        current_month: 0,
        monthly: [
          { month: '1月', value: 0 },
          { month: '2月', value: 0 },
          { month: '3月', value: 0 },
          { month: '4月', value: 0 },
          { month: '5月', value: 0 },
          { month: '6月', value: 0 }
        ],
        spring: 0,
        spring_percent: 0,
        summer: 0,
        summer_percent: 0,
        autumn: 0,
        autumn_percent: 0
      }
    })
  },

  // 切换时间范围
  changeTimeRange(e) {
    const range = e.currentTarget.dataset.range
    this.setData({ timeRange: range })
    this.loadOverviewData()
    this.loadRevenueChart()
  },

  // 切换收益图表类型
  changeRevenueChartType(e) {
    const type = e.currentTarget.dataset.type
    this.setData({ revenueChartType: type })
    this.drawRevenueChart()
  },

  // 应用建议
  applySuggestion(e) {
    const { id } = e.currentTarget.dataset
    
    wx.showModal({
      title: '应用建议',
      content: '确定要应用这个优化建议吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '建议已应用',
            icon: 'success'
          })
          
          // 从建议列表中移除
          const suggestions = this.data.suggestions.filter(item => item.id !== id)
          this.setData({ suggestions })
        }
      }
    })
  }
})

/* 底部导航测试页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #2E7D32, #4CAF50);
  border-radius: 20rpx;
  color: white;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 配置显示区域 */
.config-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 8rpx solid #2E7D32;
  padding-left: 20rpx;
}

.loading {
  text-align: center;
  padding: 40rpx;
  color: #666;
}

.config-display {
  space-y: 20rpx;
}

.config-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.config-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
  flex-shrink: 0;
}

.color-preview {
  width: 40rpx;
  height: 40rpx;
  border-radius: 8rpx;
  border: 2rpx solid #ddd;
  margin: 0 20rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-family: monospace;
}

.nav-items {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.nav-item {
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.nav-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 5rpx;
}

.nav-path {
  font-size: 24rpx;
  color: #999;
  font-family: monospace;
}

.no-config {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

/* 操作按钮区域 */
.actions-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.button-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.action-btn {
  padding: 25rpx 20rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  border: none;
  background: #f8f9fa;
  color: #333;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: #2E7D32;
  color: white;
}

.action-btn.warning {
  background: #FF9800;
  color: white;
}

.action-btn.info {
  background: #2196F3;
  color: white;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 测试结果区域 */
.results-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.clear-btn {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  background: #f44336;
  color: white;
  border-radius: 10rpx;
  border: none;
}

.no-results {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

.results-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.result-item {
  padding: 20rpx;
  margin-bottom: 15rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
  border-left: 6rpx solid #2E7D32;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.result-time {
  font-size: 24rpx;
  color: #999;
  font-family: monospace;
}

.result-detail {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 帮助说明区域 */
.help-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.help-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 20rpx;
  position: relative;
}

.help-item::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #2E7D32;
  font-weight: bold;
}

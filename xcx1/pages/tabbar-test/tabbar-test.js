// 底部导航测试页面
const { tabBarManager } = require('../../utils/tabbar-manager')

Page({
  data: {
    config: null,
    isLoading: false,
    testResults: []
  },

  onLoad() {
    this.loadCurrentConfig()
  },

  // 加载当前配置
  async loadCurrentConfig() {
    this.setData({ isLoading: true })
    
    try {
      const config = await tabBarManager.getTabBarConfig()
      this.setData({ 
        config: config,
        isLoading: false
      })
      
      this.addTestResult('✅ 配置加载成功', `导航项数量: ${config.list.length}`)
      
    } catch (error) {
      console.error('❌ 加载配置失败:', error)
      this.setData({ isLoading: false })
      this.addTestResult('❌ 配置加载失败', error.message)
    }
  },

  // 重新加载配置
  async reloadConfig() {
    this.addTestResult('🔄 重新加载配置...', '')
    
    try {
      const config = await tabBarManager.reloadConfig()
      await tabBarManager.applyTabBarConfig()
      
      this.setData({ config: config })
      this.addTestResult('✅ 配置重新加载成功', `主题色: ${config.selectedColor}`)
      
    } catch (error) {
      console.error('❌ 重新加载失败:', error)
      this.addTestResult('❌ 重新加载失败', error.message)
    }
  },

  // 应用配置
  async applyConfig() {
    this.addTestResult('🎨 应用配置...', '')
    
    try {
      const success = await tabBarManager.applyTabBarConfig()
      
      if (success) {
        this.addTestResult('✅ 配置应用成功', '底部导航样式已更新')
      } else {
        this.addTestResult('❌ 配置应用失败', '请检查配置是否正确')
      }
      
    } catch (error) {
      console.error('❌ 应用配置失败:', error)
      this.addTestResult('❌ 应用配置失败', error.message)
    }
  },

  // 测试样式更新
  async testStyleUpdate() {
    this.addTestResult('🧪 测试样式更新...', '')
    
    try {
      // 获取当前配置
      const config = tabBarManager.getCurrentConfig()
      if (!config) {
        this.addTestResult('❌ 没有可用配置', '')
        return
      }

      // 临时修改颜色进行测试
      const originalColor = config.selectedColor
      config.selectedColor = '#FF0000' // 红色
      
      // 应用红色主题
      await tabBarManager.setTabBarStyle()
      this.addTestResult('✅ 已应用红色主题', '选中颜色: #FF0000')
      
      // 等待2秒后恢复
      setTimeout(async () => {
        config.selectedColor = originalColor
        await tabBarManager.setTabBarStyle()
        this.addTestResult('✅ 已恢复原始主题', `选中颜色: ${originalColor}`)
      }, 2000)
      
    } catch (error) {
      console.error('❌ 测试失败:', error)
      this.addTestResult('❌ 测试失败', error.message)
    }
  },

  // 调用全局重新加载
  async callGlobalReload() {
    this.addTestResult('🌐 调用全局重新加载...', '')
    
    try {
      const app = getApp()
      const success = await app.reloadTabBar()
      
      if (success) {
        this.addTestResult('✅ 全局重新加载成功', '底部导航已更新')
        // 同步更新本页面的配置显示
        this.setData({ config: tabBarManager.getCurrentConfig() })
      } else {
        this.addTestResult('❌ 全局重新加载失败', '')
      }
      
    } catch (error) {
      console.error('❌ 全局重新加载失败:', error)
      this.addTestResult('❌ 全局重新加载失败', error.message)
    }
  },

  // 清除测试结果
  clearResults() {
    this.setData({ testResults: [] })
  },

  // 添加测试结果
  addTestResult(title, detail) {
    const result = {
      id: Date.now(),
      time: new Date().toLocaleTimeString(),
      title: title,
      detail: detail
    }
    
    const results = this.data.testResults
    results.unshift(result) // 添加到开头
    
    // 只保留最近20条记录
    if (results.length > 20) {
      results.splice(20)
    }
    
    this.setData({ testResults: results })
  },

  // 复制配置JSON
  copyConfig() {
    if (!this.data.config) {
      wx.showToast({
        title: '没有可用配置',
        icon: 'none'
      })
      return
    }

    wx.setClipboardData({
      data: JSON.stringify(this.data.config, null, 2),
      success: () => {
        wx.showToast({
          title: '配置已复制',
          icon: 'success'
        })
        this.addTestResult('📋 配置已复制到剪贴板', '')
      }
    })
  },

  // 显示配置详情
  showConfigDetail() {
    if (!this.data.config) {
      wx.showToast({
        title: '没有可用配置',
        icon: 'none'
      })
      return
    }

    const config = this.data.config
    const detail = `
配置详情:
• 背景色: ${config.backgroundColor}
• 默认文字色: ${config.color}
• 选中文字色: ${config.selectedColor}
• 边框样式: ${config.borderStyle}
• 导航项数量: ${config.list.length}

导航项列表:
${config.list.map((item, index) => `${index + 1}. ${item.text} - ${item.pagePath}`).join('\n')}
    `.trim()

    wx.showModal({
      title: '配置详情',
      content: detail,
      showCancel: false,
      confirmText: '确定'
    })
  }
})

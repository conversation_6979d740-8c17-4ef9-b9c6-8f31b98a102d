<!--底部导航测试页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">📱 底部导航测试</text>
    <text class="subtitle">测试动态底部导航配置功能</text>
  </view>

  <!-- 当前配置显示 -->
  <view class="config-section">
    <view class="section-title">当前配置</view>
    
    <view wx:if="{{isLoading}}" class="loading">
      <text>加载中...</text>
    </view>
    
    <view wx:elif="{{config}}" class="config-display">
      <view class="config-item">
        <text class="label">背景色:</text>
        <view class="color-preview" style="background-color: {{config.backgroundColor}};"></view>
        <text class="value">{{config.backgroundColor}}</text>
      </view>
      
      <view class="config-item">
        <text class="label">默认文字色:</text>
        <view class="color-preview" style="background-color: {{config.color}};"></view>
        <text class="value">{{config.color}}</text>
      </view>
      
      <view class="config-item">
        <text class="label">选中文字色:</text>
        <view class="color-preview" style="background-color: {{config.selectedColor}};"></view>
        <text class="value">{{config.selectedColor}}</text>
      </view>
      
      <view class="config-item">
        <text class="label">导航项数量:</text>
        <text class="value">{{config.list.length}}</text>
      </view>
      
      <view class="nav-items">
        <view class="nav-item" wx:for="{{config.list}}" wx:key="pagePath">
          <text class="nav-text">{{index + 1}}. {{item.text}}</text>
          <text class="nav-path">{{item.pagePath}}</text>
        </view>
      </view>
    </view>
    
    <view wx:else class="no-config">
      <text>暂无配置</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions-section">
    <view class="section-title">测试操作</view>
    
    <view class="button-grid">
      <button class="action-btn primary" bindtap="loadCurrentConfig">
        🔄 重新加载配置
      </button>
      
      <button class="action-btn" bindtap="applyConfig">
        🎨 应用当前配置
      </button>
      
      <button class="action-btn" bindtap="testStyleUpdate">
        🧪 测试样式更新
      </button>
      
      <button class="action-btn warning" bindtap="callGlobalReload">
        🌐 全局重新加载
      </button>
      
      <button class="action-btn info" bindtap="showConfigDetail">
        📋 查看配置详情
      </button>
      
      <button class="action-btn" bindtap="copyConfig">
        📄 复制配置JSON
      </button>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="results-section">
    <view class="section-header">
      <text class="section-title">测试结果</text>
      <button class="clear-btn" bindtap="clearResults">清除</button>
    </view>
    
    <view wx:if="{{testResults.length === 0}}" class="no-results">
      <text>暂无测试结果</text>
    </view>
    
    <view wx:else class="results-list">
      <view class="result-item" wx:for="{{testResults}}" wx:key="id">
        <view class="result-header">
          <text class="result-title">{{item.title}}</text>
          <text class="result-time">{{item.time}}</text>
        </view>
        <text wx:if="{{item.detail}}" class="result-detail">{{item.detail}}</text>
      </view>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="help-section">
    <view class="section-title">使用说明</view>
    <view class="help-content">
      <text class="help-item">1. 点击"重新加载配置"获取最新的后台配置</text>
      <text class="help-item">2. 点击"应用当前配置"将配置应用到底部导航</text>
      <text class="help-item">3. 点击"测试样式更新"查看颜色变化效果</text>
      <text class="help-item">4. 在后台修改配置后，点击"全局重新加载"同步更新</text>
      <text class="help-item">5. 使用 tabbar_test_tool.py 工具测试后台配置变更</text>
    </view>
  </view>
</view>

// pages/order-detail/order-detail.js
const api = require('../../api/index')

Page({
  data: {
    orderId: '',
    orderDetail: null,
    loading: true,
    error: false
  },

  onLoad(options) {
    // 获取订单ID
    const orderId = options.id || options.orderId || options.out_trade_no
    
    if (!orderId) {
      console.error('❌ 缺少订单ID参数')
      wx.showToast({
        title: '订单信息错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ orderId })
    this.loadOrderDetail()
  },

  // 加载订单详情
  loadOrderDetail() {
    this.setData({ loading: true, error: false })

    // 调用订单详情API
    api.orderApi.getDetail(this.data.orderId).then(res => {
      if (res.code === 200 && res.data) {
        // 处理订单数据，添加状态显示
        const orderData = res.data
        orderData.status_display = this.getStatusDisplay(orderData.status)
        orderData.status_description = this.getStatusDescription(orderData.status)

        this.setData({
          orderDetail: orderData,
          loading: false
        })

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: `订单详情 - ${res.data.order_number || this.data.orderId}`
        })
        
      } else {
        this.setData({ 
          loading: false, 
          error: true 
        })
      }
    }).catch(error => {
      console.error('❌ 加载订单详情失败:', error)

      // 显示具体错误信息
      let errorMessage = '订单详情加载失败'
      if (error.code === 404) {
        errorMessage = '订单不存在或已被删除'
      } else if (error.code === 401) {
        errorMessage = '请先登录'
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      })

      this.setData({
        loading: false,
        error: true
      })
    })
  },

  // 重新加载
  onRetry() {
    this.loadOrderDetail()
  },

  // 联系客服
  onContact() {
    wx.makePhoneCall({
      phoneNumber: '************'
    })
  },

  // 查看茶地详情
  viewTeaField() {
    const orderDetail = this.data.orderDetail
    if (orderDetail && orderDetail.tea_field && orderDetail.tea_field.name) {
      // 查找对应的TeaFieldPlot ID
      this.findTeaFieldPlotId(orderDetail.tea_field.name).then(plotId => {
        if (plotId) {
          wx.navigateTo({
            url: `/pages/tea-detail/tea-detail?id=${plotId}`
          })
        } else {
          console.error('❌ 找不到对应的茶地信息')
          wx.showToast({
            title: '茶地信息不存在',
            icon: 'none'
          })
        }
      }).catch(error => {
        console.error('❌ 查找茶地信息失败:', error)
        wx.showToast({
          title: '茶地信息错误',
          icon: 'none'
        })
      })
    } else {
      console.error('❌ 茶地信息不存在')
      wx.showToast({
        title: '茶地信息不存在',
        icon: 'none'
      })
    }
  },

  // 查看合同
  viewContract() {
    if (this.data.orderDetail && this.data.orderDetail.id) {
      wx.navigateTo({
        url: `/pages/contracts/contracts?orderId=${this.data.orderDetail.id}`
      })
    }
  },

  // 申请退款
  applyRefund() {
    wx.showModal({
      title: '申请退款',
      content: '确定要申请退款吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到退款申请页面或调用退款API
          wx.showToast({
            title: '退款申请已提交',
            icon: 'success'
          })
        }
      }
    })
  },

  // 继续支付
  continuePay() {
    const orderDetail = this.data.orderDetail
    if (!orderDetail || !orderDetail.order_id) {
      console.error('❌ 订单信息不完整，无法支付')
      wx.showToast({
        title: '订单信息错误',
        icon: 'none'
      })
      return
    }

    // 检查登录状态
    const token = wx.getStorageSync('token')
    if (!token) {
      console.error('❌ 用户未登录')
      wx.showModal({
        title: '提示',
        content: '请先登录后再进行支付',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
      return
    }
    // 直接调用支付API，而不是跳转到认购页面
    this.processPayment(orderDetail.order_id)
  },

  // 处理支付
  processPayment(orderId) {
    wx.showLoading({ title: '准备支付...' })

    // 调用支付API
    api.paymentApi.create({
      order_id: orderId,
      payment_method: 'wechat',
      amount: this.data.orderDetail.total_amount
    }).then(res => {
      wx.hideLoading()

      if (res.code === 200 && res.data) {
        // 调用微信支付
        this.callWechatPay(res.data)
      } else {
        console.error('❌ 支付参数获取失败:', res)
        wx.showToast({
          title: res.message || '支付失败',
          icon: 'none'
        })
      }
    }).catch(error => {
      wx.hideLoading()
      console.error('❌ 支付API调用失败:', error)
      wx.showToast({
        title: '支付失败',
        icon: 'none'
      })
    })
  },

  // 调用微信支付
  callWechatPay(paymentParams) {
    wx.requestPayment({
      timeStamp: paymentParams.timeStamp,
      nonceStr: paymentParams.nonceStr,
      package: paymentParams.package,
      signType: paymentParams.signType,
      paySign: paymentParams.paySign,
      success: (res) => {
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        })

        // 刷新订单详情
        setTimeout(() => {
          this.loadOrderDetail(this.data.orderDetail.order_id)
        }, 1500)
      },
      fail: (err) => {
        console.error('❌ 支付失败:', err)
        if (err.errMsg.includes('cancel')) {
          wx.showToast({
            title: '支付已取消',
            icon: 'none'
          })
        } else {
          wx.showToast({
            title: '支付失败',
            icon: 'none'
          })
        }
      }
    })
  },

  // 根据茶地名称查找TeaFieldPlot ID
  findTeaFieldPlotId(teaFieldName) {
    return new Promise((resolve, reject) => {
      // 调用茶地列表API查找对应的TeaFieldPlot
      api.teaFieldApi.getList({ search: teaFieldName }).then(res => {
        if (res.code === 200 && res.data) {
          // 处理数据结构：res.data可能是数组或包含results的对象
          const plotList = Array.isArray(res.data) ? res.data : (res.data.results || [])
          const matchingPlot = plotList.find(plot => plot.plot_name === teaFieldName)
          if (matchingPlot) {
            console.log('✅ 找到匹配的茶地:', {
              name: matchingPlot.plot_name,
              id: matchingPlot.id
            })
            resolve(matchingPlot.id)
          } else {
            // Debug log removed
            resolve(null)
          }
        } else {
          console.error('❌ 茶地列表API调用失败:', res)
          reject(new Error('茶地列表API调用失败'))
        }
      }).catch(error => {
        console.error('❌ 茶地列表API调用异常:', error)
        reject(error)
      })
    })
  },

  // 获取状态显示文本
  getStatusDisplay(status) {
    const statusMap = {
      'pending_payment': '待支付',
      'paid': '已支付',
      'processing': '处理中',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return statusMap[status] || status
  },

  // 获取状态描述
  getStatusDescription(status) {
    const descMap = {
      'pending_payment': '请尽快完成支付',
      'paid': '支付成功，订单处理中',
      'processing': '订单正在处理中',
      'completed': '订单已完成',
      'cancelled': '订单已取消'
    }
    return descMap[status] || '订单处理中'
  },

  // 分享订单
  onShareAppMessage() {
    const orderDetail = this.data.orderDetail
    if (orderDetail) {
      return {
        title: `我在两山茶管家认购了${orderDetail.tea_field_name}`,
        path: `/pages/order-detail/order-detail?id=${this.data.orderId}`,
        imageUrl: orderDetail.tea_field_image || '/images/share-default.jpg'
      }
    }
    
    return {
      title: '两山茶管家 - 专业的茶地投资平台',
      path: `/pages/order-detail/order-detail?id=${this.data.orderId}`
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadOrderDetail()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  }
})

// 微信支付测试页面
const api = require('../../api/index.js')
const paymentDebug = require('../../utils/payment-debug.js')
const paymentTest = require('../../utils/payment-test.js')

Page({
  data: {
    testResult: '',
    configInfo: null,
    loading: false,
    diagnosticReport: ''
  },

  onLoad() {
    this.checkPaymentConfig()
  },

  // 检查支付配置
  async checkPaymentConfig() {
    try {
      this.setData({ loading: true })
      
      const res = await api.paymentApi.testConfig()
      if (res.code === 200) {
        this.setData({
          configInfo: res.data,
          testResult: '配置检查完成'
        })
      } else {
        this.setData({
          testResult: `配置检查失败: ${res.message}`
        })
      }
    } catch (error) {
      console.error('配置检查异常:', error)
      this.setData({
        testResult: `配置检查异常: ${error.message}`
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 运行完整诊断
  async runDiagnostic() {
    try {
      this.setData({ loading: true, testResult: '正在运行诊断...' })

      // 运行完整诊断
      const diagnostic = await paymentDebug.runFullDiagnostic('TEST_ORDER_' + Date.now(), 0.01)
      const report = paymentDebug.generateDiagnosticReport(diagnostic)
      this.setData({
        diagnosticReport: report,
        testResult: '✅ 诊断完成，请查看详细报告'
      })

    } catch (error) {
      console.error('诊断异常:', error)
      this.setData({
        testResult: `❌ 诊断异常: ${error.message}`
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 测试支付
  async testPayment() {
    try {
      this.setData({ loading: true, testResult: '正在测试支付...' })

      // 创建测试订单
      const orderRes = await api.orderApi.create({
        tea_field_id: 1,
        quantity: 1,
        total_amount: 0.01, // 1分钱测试
        contract_content: '测试支付订单'
      })

      if (orderRes.code !== 200) {
        throw new Error(`创建订单失败: ${orderRes.message}`)
      }
      // 调用支付
      const payRes = await api.paymentApi.wechatPay({
        order_id: orderRes.data.order_id,
        amount: 0.01
      })
      if (payRes.code === 200) {
        // 提取支付参数并标准化
        const { payment_id, ...rawPayParams } = payRes.data

        const wxPayParams = {
          appId: rawPayParams.appId,
          timeStamp: String(rawPayParams.timeStamp),
          nonceStr: rawPayParams.nonceStr,
          package: rawPayParams.package,
          signType: rawPayParams.signType || 'MD5',
          paySign: rawPayParams.paySign
        }
        // 验证参数
        const validation = paymentDebug.validatePaymentParams(wxPayParams)
        if (!validation.valid) {
          throw new Error(`支付参数验证失败: ${validation.issues.join(', ')}`)
        }

        // 调用微信支付
        wx.requestPayment({
          appId: wxPayParams.appId,
          timeStamp: wxPayParams.timeStamp,
          nonceStr: wxPayParams.nonceStr,
          package: wxPayParams.package,
          signType: wxPayParams.signType,
          paySign: wxPayParams.paySign,
          success: (result) => {
            this.setData({
              testResult: '✅ 支付测试成功！微信支付配置正常'
            })
          },
          fail: (err) => {
            console.error('支付失败:', err)
            this.setData({
              testResult: `❌ 支付失败: ${err.errMsg || '未知错误'}\n错误码: ${err.errno || '未知'}`
            })
          }
        })
      } else {
        throw new Error(`支付API失败: ${payRes.message}`)
      }

    } catch (error) {
      console.error('测试支付异常:', error)
      this.setData({
        testResult: `❌ 测试支付异常: ${error.message}`
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 真机支付测试
  async testRealDevicePayment() {
    try {
      this.setData({ loading: true, testResult: '正在进行真机支付测试...' })

      const result = await paymentTest.runFullTest()

      if (result.success) {
        this.setData({
          testResult: '✅ 真机支付测试成功！支付功能正常工作'
        })
      } else {
        this.setData({
          testResult: `❌ 真机支付测试失败: ${result.error}`
        })
      }

    } catch (error) {
      console.error('真机测试异常:', error)
      this.setData({
        testResult: `❌ 真机测试异常: ${error.message}`
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 查看诊断报告
  viewDiagnosticReport() {
    if (!this.data.diagnosticReport) {
      wx.showToast({
        title: '请先运行诊断',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '诊断报告',
      content: this.data.diagnosticReport,
      showCancel: false,
      confirmText: '确定'
    })
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  }
})

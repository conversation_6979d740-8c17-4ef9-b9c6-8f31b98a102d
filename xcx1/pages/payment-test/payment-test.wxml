<!--微信支付测试页面-->
<view class="container">
  <view class="header">
    <text class="title">微信支付测试</text>
    <text class="subtitle">验证支付配置是否正常</text>
  </view>

  <view class="config-section">
    <view class="section-title">配置信息</view>
    <view wx:if="{{configInfo}}" class="config-info">
      <view class="config-item">
        <text class="label">AppID:</text>
        <text class="value">{{configInfo.app_id}}</text>
      </view>
      <view class="config-item">
        <text class="label">商户号:</text>
        <text class="value">{{configInfo.mch_id}}</text>
      </view>
      <view class="config-item">
        <text class="label">微信配置:</text>
        <text class="value {{configInfo.wechat_configured ? 'success' : 'error'}}">
          {{configInfo.wechat_configured ? '✅ 正常' : '❌ 异常'}}
        </text>
      </view>
      <view class="config-item">
        <text class="label">支付配置:</text>
        <text class="value {{configInfo.pay_configured ? 'success' : 'error'}}">
          {{configInfo.pay_configured ? '✅ 正常' : '❌ 异常'}}
        </text>
      </view>
    </view>
  </view>

  <view class="test-section">
    <view class="section-title">测试结果</view>
    <view class="test-result">
      <text class="result-text">{{testResult || '等待测试...'}}</text>
    </view>
  </view>

  <view class="button-section">
    <button
      class="test-button real-device"
      bindtap="testRealDevicePayment"
      disabled="{{loading}}"
      loading="{{loading}}">
      {{loading ? '真机测试中...' : '🔥 真机支付测试'}}
    </button>

    <button
      class="test-button diagnostic"
      bindtap="runDiagnostic"
      disabled="{{loading}}"
      loading="{{loading}}">
      {{loading ? '诊断中...' : '运行完整诊断'}}
    </button>

    <button
      class="test-button"
      bindtap="testPayment"
      disabled="{{loading}}"
      loading="{{loading}}">
      {{loading ? '测试中...' : '开始支付测试'}}
    </button>

    <button
      class="report-button"
      bindtap="viewDiagnosticReport">
      查看诊断报告
    </button>

    <button
      class="back-button"
      bindtap="goBack">
      返回
    </button>
  </view>

  <view class="tips">
    <view class="tips-title">测试说明:</view>
    <view class="tips-content">
      <text>• 此测试将创建1分钱的测试订单</text>
      <text>• 测试成功说明微信支付配置正常</text>
      <text>• 如果失败，请检查商户平台配置</text>
    </view>
  </view>
</view>

/* 微信支付测试页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.config-section, .test-section {
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #07c160;
  padding-left: 15rpx;
}

.config-info {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}

.config-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-family: monospace;
}

.value.success {
  color: #07c160;
  font-weight: bold;
}

.value.error {
  color: #fa5151;
  font-weight: bold;
}

.test-result {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
  min-height: 80rpx;
  display: flex;
  align-items: center;
}

.result-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.button-section {
  margin: 40rpx 0;
}

.test-button {
  background-color: #07c160;
  color: white;
  border-radius: 50rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.test-button.real-device {
  background-color: #ff4757;
  font-weight: bold;
}

.test-button.diagnostic {
  background-color: #1989fa;
}

.test-button[disabled] {
  background-color: #c0c0c0;
}

.report-button {
  background-color: #ff9500;
  color: white;
  border-radius: 50rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.back-button {
  background-color: #f0f0f0;
  color: #333;
  border-radius: 50rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
}

.tips {
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.tips-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.tips-content text {
  display: block;
  margin-bottom: 8rpx;
}

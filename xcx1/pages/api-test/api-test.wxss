/* API测试页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: white;
  border-radius: 10rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.retry-btn {
  background-color: #2E7D32;
  color: white;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 28rpx;
}

.results {
  background-color: white;
  border-radius: 10rpx;
  overflow: hidden;
}

.result-item {
  border-bottom: 1rpx solid #eee;
  padding: 20rpx;
}

.result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.result-status {
  font-size: 32rpx;
}

.result-status.success {
  color: #4CAF50;
}

.result-status.error {
  color: #F44336;
}

.result-details {
  margin-bottom: 10rpx;
}

.success-text {
  color: #4CAF50;
  font-size: 28rpx;
}

.error-text {
  color: #F44336;
  font-size: 28rpx;
}

.data-preview {
  background-color: #f8f9fa;
  padding: 10rpx;
  border-radius: 6rpx;
  border-left: 4rpx solid #2E7D32;
}

.data-title {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.data-content {
  font-size: 24rpx;
  color: #333;
  font-family: monospace;
}

.loading {
  text-align: center;
  padding: 40rpx;
  color: #666;
}

.empty {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

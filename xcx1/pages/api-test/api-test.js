// API测试页面
const { teaFieldAPI } = require('../../utils/apis.js')

Page({
  data: {
    testResults: [],
    loading: false
  },

  onLoad() {
    this.runAPITests()
  },

  async runAPITests() {
    this.setData({ loading: true, testResults: [] })
    
    const tests = [
      {
        name: '获取茶地列表',
        test: () => teaFieldAPI.getTeaFields({ page: 1, page_size: 5 })
      },
      {
        name: '获取茶地详情 ID=1',
        test: () => teaFieldAPI.getTeaFieldDetail(1)
      },
      {
        name: '获取茶地详情 ID=2',
        test: () => teaFieldAPI.getTeaFieldDetail(2)
      },
      {
        name: '获取茶地详情 ID=3',
        test: () => teaFieldAPI.getTeaFieldDetail(3)
      },
      {
        name: '获取茶地详情 ID=10',
        test: () => teaFieldAPI.getTeaFieldDetail(10)
      }
    ]

    const results = []

    for (const testCase of tests) {
      try {
        console.log(`🧪 测试: ${testCase.name}`)
        const startTime = Date.now()
        const result = await testCase.test()
        const endTime = Date.now()
        
        results.push({
          name: testCase.name,
          status: 'success',
          code: result.code,
          message: result.message || 'OK',
          data: result.data,
          time: endTime - startTime
        })
        
        console.log(`✅ ${testCase.name} - 成功`, result)
        
      } catch (error) {
        results.push({
          name: testCase.name,
          status: 'error',
          error: error.message,
          time: 0
        })
        
        console.log(`❌ ${testCase.name} - 失败`, error)
      }
    }

    this.setData({ 
      testResults: results,
      loading: false 
    })
  },

  // 重新运行测试
  onRetryTests() {
    this.runAPITests()
  },

  // 查看详细结果
  onViewDetail(e) {
    const index = e.currentTarget.dataset.index
    const result = this.data.testResults[index]
    
    wx.showModal({
      title: result.name,
      content: JSON.stringify(result, null, 2),
      showCancel: false
    })
  }
})

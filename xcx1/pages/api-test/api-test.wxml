<!-- API测试页面 -->
<view class="container">
  <view class="header">
    <text class="title">API测试工具</text>
    <button class="retry-btn" bindtap="onRetryTests" disabled="{{loading}}">
      {{loading ? '测试中...' : '重新测试'}}
    </button>
  </view>

  <view class="results">
    <view wx:for="{{testResults}}" wx:key="name" class="result-item">
      <view class="result-header" bindtap="onViewDetail" data-index="{{index}}">
        <text class="result-name">{{item.name}}</text>
        <view class="result-status {{item.status}}">
          {{item.status === 'success' ? '✅' : '❌'}}
        </view>
      </view>
      
      <view class="result-details">
        <text wx:if="{{item.status === 'success'}}" class="success-text">
          状态码: {{item.code}} | 耗时: {{item.time}}ms
        </text>
        <text wx:else class="error-text">
          错误: {{item.error}}
        </text>
      </view>
      
      <view wx:if="{{item.status === 'success' && item.data}}" class="data-preview">
        <text class="data-title">数据预览:</text>
        <text class="data-content">{{item.data.length || item.data.id || '有数据'}}</text>
      </view>
    </view>
  </view>

  <view wx:if="{{loading}}" class="loading">
    <text>正在测试API...</text>
  </view>

  <view wx:if="{{testResults.length === 0 && !loading}}" class="empty">
    <text>暂无测试结果</text>
  </view>
</view>

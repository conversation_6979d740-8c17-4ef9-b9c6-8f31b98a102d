// 账户安全页面
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    securityInfo: {
      phone_verified: true,
      email_verified: false,
      real_name_verified: true,
      payment_password_set: true,
      login_protection: true,
      phone: '138****8888',
      email: '',
      real_name: '张**'
    },
    loginLogs: []
  },

  // 页面加载
  onLoad(options) {
    this.loadSecurityInfo()
    this.loadLoginLogs()
  },

  // 页面显示
  onShow() {
  },

  // 下拉刷新
  onPullDownRefresh() {
    Promise.all([
      this.loadSecurityInfo(),
      this.loadLoginLogs()
    ]).finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载安全信息
  async loadSecurityInfo() {
    try {
      // 调用安全信息API
      const res = await api.userApi.getSecurityInfo()

      if (res.code === 200) {
        this.setData({
          securityInfo: res.data
        })
      } else {
        // 使用默认数据
        this.setData({
          securityInfo: {
            phone_verified: false,
            email_verified: false,
            real_name_verified: false,
            payment_password_set: false,
            login_protection: false,
            phone: '',
            email: '',
            real_name: ''
          }
        })
      }
    } catch (error) {
      console.error('加载安全信息失败:', error)
      // 使用默认数据
      this.setData({
        securityInfo: {
          phone_verified: false,
          email_verified: false,
          real_name_verified: false,
          payment_password_set: false,
          login_protection: false,
          phone: '',
          email: '',
          real_name: ''
        }
      })
    }
  },

  // 加载登录日志
  async loadLoginLogs() {
    try {
      // 调用登录日志API
      const res = await api.userApi.getLoginLogs()

      if (res.code === 200) {
        this.setData({
          loginLogs: res.data.results || []
        })
      } else {
        this.setData({
          loginLogs: []
        })
      }
    } catch (error) {
      console.error('加载登录日志失败:', error)
      this.setData({
        loginLogs: []
      })
    }
  },

  // 修改手机号
  changePhone() {
    wx.showToast({
      title: '修改手机号功能开发中',
      icon: 'none'
    })
  },

  // 绑定邮箱
  bindEmail() {
    wx.showToast({
      title: '绑定邮箱功能开发中',
      icon: 'none'
    })
  },

  // 实名认证
  realNameAuth() {
    wx.navigateTo({
      url: '/pages/real-name-auth/real-name-auth'
    })
  },

  // 设置支付密码
  setPaymentPassword() {
    wx.showToast({
      title: '设置支付密码功能开发中',
      icon: 'none'
    })
  },

  // 修改支付密码
  changePaymentPassword() {
    wx.showToast({
      title: '修改支付密码功能开发中',
      icon: 'none'
    })
  },

  // 切换登录保护
  toggleLoginProtection(e) {
    const { value } = e.detail
    this.setData({
      'securityInfo.login_protection': value
    })

    wx.showToast({
      title: value ? '已开启登录保护' : '已关闭登录保护',
      icon: 'success'
    })
  },

  // 查看登录日志详情
  viewLoginLogDetail(e) {
    const { id } = e.currentTarget.dataset
    const log = this.data.loginLogs.find(item => item.id === id)
    
    wx.showModal({
      title: '登录详情',
      content: `设备：${log.device}\n位置：${log.location}\nIP：${log.ip}\n时间：${log.time}`,
      showCancel: false
    })
  },

  // 冻结账户
  freezeAccount() {
    wx.showModal({
      title: '冻结账户',
      content: '确定要冻结账户吗？冻结后需要联系客服解冻。',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '账户冻结功能开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  // 注销账户
  deleteAccount() {
    wx.showModal({
      title: '注销账户',
      content: '注销账户将删除所有数据，此操作不可恢复，确定要继续吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showModal({
            title: '再次确认',
            content: '您确定要注销账户吗？这将永久删除您的所有数据。',
            success: (res2) => {
              if (res2.confirm) {
                wx.showToast({
                  title: '注销账户功能开发中',
                  icon: 'none'
                })
              }
            }
          })
        }
      }
    })
  }
})

// 首页
const app = getApp()
const api = require('../../api/index.js')
const { homeAPI, teaFieldAPI } = require('../../utils/apis.js')
const { formatBannerUrl, formatTeaImageUrl, formatImageUrl } = require('../../utils/util')
const decorationManager = require('../../utils/decoration-manager.js')
// 暂时移除可能导致问题的导航工具
// const { Navigation, PageLifecycle } = require('../../utils/navigation.js')
// const { NotificationManager, MESSAGE_TYPES } = require('../../utils/notification.js')
// const { OfflineManager } = require('../../utils/offline.js')

Page({
  data: {
    // 装修系统相关
    useDecoration: true, // 重新启用装修系统 - API已确认正常
    decorationData: {}, // 装修数据源
    decorationReady: false, // 装修数据是否准备就绪

    // 调试开关 - 可以临时禁用装修系统
    debugMode: false, // 显示调试信息
    forceOriginal: false, // 强制使用原版页面

    // API测试结果
    apiTestResult: null, // 存储API测试结果

    // 自定义底部导航
    customTabBarConfig: null,
    currentTabIndex: 0,

    // 轮播图数据
    banners: [],

    // 统计数据
    stats: {
      totalAcres: 0,
      availableAcres: 0,
      soldAcres: 0,
      soldRate: 0,
      monthlyIncrease: 0
    },

    // 推荐茶地
    recommendedTeas: [],

    // 最新动态
    news: [],

    // AI投资建议
    investmentAdvice: {
      loading: false,
      data: null
    },

    // 视频号数据
    videoChannel: {
      loading: false,
      videos: []
    },

    // 是否显示新手引导
    showGuide: false,

    // 加载状态
    loading: true
  },

  // 页面加载
  onLoad(options) {
    // 初始化自定义底部导航
    this.initCustomTabBar()

    // 直接测试API调用，绕过装修系统
    this.testDirectAPI()

    // 清除装修缓存，确保获取最新配置
    const decorationManager = require('../../utils/decoration-manager.js')
    decorationManager.clearCache()
    try {
      this.initPage()
    } catch (error) {
      console.error('❌ 页面加载失败:', error)
      // 设置加载完成状态，避免一直显示loading
      this.setData({ loading: false })
    }
  },

  /**
   * 直接测试API调用
   */
  // 测试底部导航配置
  async testTabBarConfig() {
    try {
      // 获取配置
      const config = await this.getTabBarConfig()
      // 应用配置
      const success = await this.applyTabBarConfig(config)
      // 测试颜色变化
      setTimeout(async () => {
        const redConfig = { ...config, selectedColor: '#FF0000' }
        await this.applyTabBarConfig(redConfig)
        // 2秒后恢复
        setTimeout(async () => {
          await this.applyTabBarConfig(config)
        }, 2000)
      }, 1000)

    } catch (error) {
      console.error('❌ 底部导航测试失败:', error)
    }
  },

  // 获取底部导航配置
  getTabBarConfig() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://teabuy.yizhangkj.com/api/v1/page-decoration/tabbar/config/',
        method: 'GET',
        header: {
          'Cache-Control': 'no-cache'
        },
        success: (res) => {
          if (res.data && res.data.code === 200) {
            resolve(res.data.data.config)
          } else {
            resolve(this.getDefaultTabBarConfig())
          }
        },
        fail: (error) => {
          console.error('❌ 请求失败，使用默认配置:', error)
          resolve(this.getDefaultTabBarConfig())
        }
      })
    })
  },

  // 默认底部导航配置
  getDefaultTabBarConfig() {
    return {
      color: '#999999',
      selectedColor: '#2E7D32',
      backgroundColor: '#FFFFFF',
      borderStyle: 'black'
    }
  },

  // 应用底部导航配置
  applyTabBarConfig(config) {
    return new Promise((resolve) => {
      wx.setTabBarStyle({
        color: config.color,
        selectedColor: config.selectedColor,
        backgroundColor: config.backgroundColor,
        borderStyle: config.borderStyle,
        success: () => {
          resolve(true)
        },
        fail: (error) => {
          console.error('❌ 底部导航样式应用失败:', error)
          resolve(false)
        }
      })
    })
  },

  testDirectAPI() {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(7)
    const uuid = Math.random().toString(36).substring(2, 15)

    wx.request({
      url: `https://teabuy.yizhangkj.com/api/v1/page-decoration/config/home/<USER>
      method: 'GET',
      header: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache'
      },
      success: (res) => {
        // 保存API测试结果到页面数据
        const apiResult = {
          success: true,
          timestamp: new Date().toLocaleTimeString(),
          blocksCount: 0,
          hasAI: false,
          fromCache: res.data.from_cache,
          blocks: []
        }

        if (res.data && res.data.data && res.data.data.template) {
          let blocks = res.data.data.template.blocks || []
          const originalCount = blocks.length

          // 后端已经通过序列化器过滤了禁用的板块，前端不需要再次过滤
          // Debug log removed
          blocks.forEach((block, index) => {
          })

          apiResult.blocksCount = blocks.length
          apiResult.blocks = blocks.map(b => ({
            name: b.name,
            type: b.block_type,
            enabled: b.is_enabled
          }))
          // Debug log removed
          blocks.forEach((block, index) => {
          })

          // 检查AI投资建议
          const aiBlock = blocks.find(b => b.block_type === 'ai_investment')
          apiResult.hasAI = !!aiBlock
          if (aiBlock) {
          } else {
          }
        }

        // 更新页面数据
        this.setData({ apiTestResult: apiResult })
      },
      fail: (err) => {
        console.error('🧪 直接API调用失败:', err)
        this.setData({
          apiTestResult: {
            success: false,
            error: err.errMsg || '请求失败',
            timestamp: new Date().toLocaleTimeString()
          }
        })
      }
    })
  },

  // 页面显示
  onShow() {
    try {
      this.refreshData()
      // 更新自定义导航选中状态
      this.updateCustomTabBarIndex()
    } catch (error) {
      console.error('❌ 页面显示时刷新数据失败:', error)
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData().then(() => {
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    }).catch(error => {
      console.error('下拉刷新失败:', error)
      wx.showToast({
        title: '刷新失败，请稍后重试',
        icon: 'none',
        duration: 2000
      })
    }).finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 初始化页面
  initPage() {
    // Debug log removed
    // 检查是否显示新手引导
    this.checkShowGuide()

    // 加载页面数据
    this.loadPageData().then(() => {
      // Debug log removed
    }).catch(error => {
      console.error('❌ 初始化页面失败:', error)
    }).finally(() => {
      this.setData({ loading: false })
    })
  },

  // 刷新数据
  refreshData() {
    // 并行加载所有数据，每个都独立处理错误
    const promises = [
      this.loadBanners().catch(e => {
        return null
      }),
      this.loadStats().catch(e => {
        return null
      }),
      this.loadRecommendedFields().catch(e => {
        return null
      }),
      this.loadNews().catch(e => {
        return null
      }),
      this.loadInvestmentAdvice().catch(e => {
        return null
      }),
      this.loadVideoChannel().catch(e => {
        return null
      })
    ]

    return Promise.all(promises).then(() => {
      // 如果使用装修系统，更新装修数据
      if (this.data.useDecoration) {
        this.prepareDecorationData()
      }
    }).catch(error => {
      console.error('❌ 刷新数据失败:', error)
      // 不抛出错误，避免阻塞页面
    })
  },

  // 加载页面数据
  loadPageData() {
    // 先设置默认轮播图，避免空白显示
    if (this.data.banners.length === 0) {
      this.setData({
        banners: this.getDefaultBanners()
      })
    }

    // 并行加载数据，每个都独立处理错误
    const promises = [
      this.loadBanners().catch(e => {
        return null
      }),
      this.loadStats().catch(e => {
        return null
      }),
      this.loadRecommendedFields().catch(e => {
        return null
      }),
      this.loadNews().catch(e => {
        return null
      }),
      this.loadInvestmentAdvice().catch(e => {
        return null
      }),
      this.loadVideoChannel().catch(e => {
        return null
      })
    ]

    return Promise.all(promises).then(() => {
      // 如果使用装修系统，准备装修数据
      if (this.data.useDecoration) {
        this.prepareDecorationData()
      }
    }).catch(error => {
      console.error('❌ 加载页面数据失败:', error)
      // 不抛出错误，避免阻塞页面
    })
  },

  // 加载轮播图数据
  loadBanners() {
    return new Promise((resolve, reject) => {
      // 调用真实API
      api.commonApi.getBanners().then(res => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          const processedBanners = res.data.map(banner => ({
            id: banner.id,
            title: banner.title || '',
            image: banner.image ? (banner.image.startsWith('http') ? banner.image : `https://teabuy.yizhangkj.com${banner.image}`) : (banner.image_url || ''),
            link: banner.link_url || banner.link || '',
            description: banner.subtitle || banner.description || '',
            subtitle: banner.subtitle || banner.description || ''
          })).filter(banner => banner.image) // 只保留有图片的轮播图

          this.setData({ banners: processedBanners })
        } else {
          this.setData({ banners: [] })
        }
        resolve()
      }).catch(error => {
        console.error('❌ 轮播图API调用失败:', error)
        // API失败时不显示轮播图
        this.setData({ banners: [] })
        resolve() // 仍然resolve，不阻塞其他数据加载
      })
    })
  },

  // 加载统计数据
  loadStats() {
    return new Promise((resolve, reject) => {
      // 调用真实API - 仪表板概览接口
      api.commonApi.getHomeStats().then(res => {
        if (res.code === 200 && res.data) {
          // 使用真实API数据
          const data = res.data
          // 计算可认购面积 = 总面积 - 已认购面积
          const totalArea = parseFloat(data.total_area || 0)
          const purchasedArea = parseFloat(data.purchased_area || 0)
          const availableArea = totalArea - purchasedArea
          const purchaseRate = parseFloat(data.purchase_rate || 0)

          this.setData({
            stats: {
              totalAcres: Math.round(totalArea * 10) / 10, // 保留1位小数
              availableAcres: Math.round(availableArea * 10) / 10,
              soldAcres: Math.round(purchasedArea * 10) / 10,
              soldRate: Math.round(purchaseRate * 10) / 10,
              monthlyIncrease: parseFloat(data.revenue_growth || 15.2)
            }
          })
        } else {
          this.calculateStatsFromTeaFields()
        }
        resolve()
      }).catch(error => {
        console.error('❌ 统计数据API调用失败:', error)
        this.calculateStatsFromTeaFields()
        resolve()
      })
    })
  },

  // 从茶地数据计算统计信息
  calculateStatsFromTeaFields() {
    // 调用茶地列表API获取所有茶地数据
    api.teaFieldApi.getList({ page_size: 1000 }).then(res => {
      if (res.code === 200 && res.data && Array.isArray(res.data)) {
        const teaFields = res.data

        // 计算统计数据
        const totalArea = teaFields.reduce((sum, field) => sum + parseFloat(field.area || 0), 0)
        const availableFields = teaFields.filter(field => field.status === 'available')
        const soldFields = teaFields.filter(field => ['sold', 'reserved'].includes(field.status))

        const availableArea = availableFields.reduce((sum, field) => sum + parseFloat(field.area || 0), 0)
        const soldArea = soldFields.reduce((sum, field) => sum + parseFloat(field.area || 0), 0)
        const soldRate = totalArea > 0 ? (soldArea / totalArea * 100) : 0

        this.setData({
          stats: {
            totalAcres: Math.round(totalArea * 10) / 10,
            availableAcres: Math.round(availableArea * 10) / 10,
            soldAcres: Math.round(soldArea * 10) / 10,
            soldRate: Math.round(soldRate * 10) / 10,
            monthlyIncrease: 15.2 // 这个需要根据实际业务逻辑计算
          }
        })
      } else {
        console.error('❌ 茶地数据格式异常，使用最小默认值')
        this.setData({
          stats: {
            totalAcres: 35.0,
            availableAcres: 35.0,
            soldAcres: 0,
            soldRate: 0,
            monthlyIncrease: 0
          }
        })
      }
    }).catch(error => {
      console.error('❌ 茶地列表API调用失败:', error)
      this.setData({
        stats: {
          totalAcres: 35.0,
          availableAcres: 35.0,
          soldAcres: 0,
          soldRate: 0,
          monthlyIncrease: 0
        }
      })
    })
  },

  // 加载推荐茶地
  loadRecommendedFields() {
    return new Promise((resolve, reject) => {
      // 调用真实API - 茶地列表接口
      api.teaFieldApi.getList({ page_size: 6, is_recommended: true }).then(res => {
        if (res.code === 200 && res.data) {
          let fields = []

          // 处理不同的数据格式
          if (res.data.results && Array.isArray(res.data.results)) {
            fields = res.data.results
          } else if (Array.isArray(res.data)) {
            fields = res.data
          }

          if (fields.length > 0) {
            const processedFields = fields.slice(0, 6).map(field => {
              // 处理图片URL - 优先使用cover_image
              let imageUrl = null
              if (field.cover_image) {
                if (field.cover_image.image_url) {
                  imageUrl = field.cover_image.image_url
                } else if (field.cover_image.image) {
                  imageUrl = field.cover_image.image.startsWith('http') ?
                    field.cover_image.image :
                    `https://teabuy.yizhangkj.com${field.cover_image.image}`
                }
              }

              return {
                id: field.id,
                name: field.plot_name || field.name || field.title || '未知茶地',
                location: field.region?.name || field.region?.garden?.location || field.location || field.address || '未知位置',
                price: field.price || field.unit_price || 0,
                area: field.area || field.total_area || 0,
                image: imageUrl,
                status: field.status || 'available',
                description: field.region?.description || field.description || field.intro || '',
                tags: field.tags || [],
                expected_return: field.expected_return || '8-12'
              }
            })

            this.setData({ recommendedTeas: processedFields })
          } else {
            this.setData({ recommendedTeas: this.getDefaultRecommendedTeas() })
          }
        } else {
          this.setData({ recommendedTeas: this.getDefaultRecommendedTeas() })
        }
        resolve()
      }).catch(error => {
        console.error('❌ 推荐茶地API调用失败:', error)
        // API失败时使用默认数据
        this.setData({ recommendedTeas: this.getDefaultRecommendedTeas() })
        resolve()
      })
    })
  },

  // 获取默认推荐茶地数据
  getDefaultRecommendedTeas() {
    return [
      {
        id: 'default-1',
        name: '精品茶园A区',
        location: '广东省潮州市',
        price: 15000,
        area: 1.0,
        image: null,
        status: 'available',
        description: '优质茶园，环境优美',
        tags: ['精品', '有机']
      },
      {
        id: 'default-2',
        name: '精品茶园B区',
        location: '广东省潮州市',
        price: 11000,
        area: 1.0,
        image: null,
        status: 'available',
        description: '传统工艺，品质保证',
        tags: ['传统', '优质']
      }
    ]
  },

  // 加载新闻数据
  loadNews() {
    return new Promise((resolve, reject) => {
      // Debug log removed
      // 调用真实的新闻API
      api.newsApi.getList({ limit: 5 }).then(res => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          // 使用真实的新闻数据
          const newsData = res.data.map(item => ({
            id: item.id,
            title: item.title,
            summary: item.summary || '暂无摘要',
            image: item.image ? (item.image.startsWith('http') ? item.image : `https://teabuy.yizhangkj.com${item.image}`) : null,
            news_type: item.news_type,
            news_type_display: item.news_type_display,
            color: item.color || '#22c55e',
            is_featured: item.is_featured,
            is_top: item.is_top,
            time: item.time,
            created_at: item.created_at,
            publish_time: item.publish_time
          })) // 暂时显示所有新闻，不过滤图片

          this.setData({ news: newsData })
          // Debug log removed
        } else {
          // 如果没有新闻数据，不显示新闻
          this.setData({ news: [] })
        }
        resolve()
      }).catch(error => {
        console.error('❌ 新闻API调用失败:', error)
        // API调用失败时不显示新闻
        this.setData({ news: [] })
        resolve() // 即使出错也resolve，不阻塞其他数据加载
      })
    })
  },

  // 检查是否显示新手引导
  checkShowGuide() {
    const hasShownGuide = wx.getStorageSync('hasShownGuide')
    if (!hasShownGuide) {
      this.setData({ showGuide: true })
    }
  },

  // 轮播图点击
  onBannerTap(e) {
    const { url } = e.currentTarget.dataset
    if (url) {
      // 使用统一的导航方法
      this.navigateTo(e)
    }
  },

  // 开始新手指南
  startGuide() {
    wx.showModal({
      title: '新手指南',
      content: '欢迎使用两山·茶管家！我们将为您介绍茶园认购的基本流程。',
      confirmText: '开始',
      success: (res) => {
        if (res.confirm) {
          this.startGuideStep()
        }
      }
    })
  },

  // 开始引导步骤
  startGuideStep() {
    wx.showModal({
      title: '第一步：浏览茶地',
      content: '在茶地页面可以查看所有可认购的茶园信息，包括位置、价格、预期收益等。',
      confirmText: '下一步',
      success: (res) => {
        if (res.confirm) {
          this.closeGuide()
          wx.switchTab({
            url: '/pages/tea-list/tea-list'
          })
        }
      }
    })
  },

  // 浏览茶地
  browseFields() {
    wx.switchTab({
      url: '/pages/tea-list/tea-list'
    })
  },

  // 关闭引导
  closeGuide() {
    this.setData({ showGuide: false })
    wx.setStorageSync('hasShownGuide', true)
  },

  // 显示统计详情
  showStatDetail(e) {
    const { type } = e.currentTarget.dataset
    let title = ''
    let content = ''

    switch (type) {
      case 'total':
        title = '总亩数'
        content = `平台总共管理 ${this.data.stats.totalAcres} 亩茶园，本月新增 ${this.data.stats.monthlyIncrease} 亩`
        break
      case 'available':
        title = '可认购'
        content = `当前有 ${this.data.stats.availableAcres} 亩茶园可供认购，均为优质茶园`
        break
      case 'sold':
        title = '已认购'
        content = `已有 ${this.data.stats.soldAcres} 亩茶园被认购，认购率达 ${this.data.stats.soldRate}%`
        break
    }

    wx.showModal({
      title,
      content,
      showCancel: false
    })
  },

  // 页面导航
  navigateTo(e) {
    const { url } = e.currentTarget.dataset

    if (!url) {
      console.error('导航URL为空')
      wx.showToast({
        title: '页面地址错误',
        icon: 'none'
      })
      return
    }
    // 检查是否为 tabBar 页面（根据app.json中的tabBar配置）
    const tabBarPages = [
      '/pages/index/index',
      '/pages/tea-list/tea-list',
      '/pages/monitoring/monitoring',
      '/pages/analytics/analytics',
      '/pages/profile/profile'
    ]

    // 处理特殊的URL格式
    if (url.includes('switchTab:')) {
      const targetUrl = url.replace('switchTab:', '')
      wx.switchTab({
        url: targetUrl,
        fail: (error) => {
          console.error('switchTab失败:', error)
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
        }
      })
    } else if (tabBarPages.some(page => url.includes(page.split('/').pop()))) {
      // 如果是tabBar页面，使用switchTab
      const targetPage = tabBarPages.find(page => url.includes(page.split('/').pop()))
      wx.switchTab({
        url: targetPage,
        fail: (error) => {
          console.error('switchTab失败:', error)
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
        }
      })
    } else {
      // 普通页面导航，使用微信原生导航方法
      wx.navigateTo({
        url,
        success: () => {
        },
        fail: (error) => {
          console.error('页面导航失败:', error, 'URL:', url)
          // 如果导航失败，尝试使用switchTab（可能是tabBar页面）
          const tabBarPages = [
            '/pages/index/index',
            '/pages/tea-list/tea-list',
            '/pages/my-fields/my-fields',
            '/pages/earnings/earnings',
            '/pages/profile/profile'
          ]

          if (tabBarPages.includes(url)) {
            wx.switchTab({
              url,
              success: () => {
              },
              fail: (switchError) => {
                console.error('switchTab也失败:', switchError)
                wx.showToast({
                  title: '页面跳转失败',
                  icon: 'none'
                })
              }
            })
          } else {
            wx.showToast({
              title: '页面开发中...',
              icon: 'none'
            })
          }
        }
      })
    }
  },

  // 查看茶地详情
  viewTeaDetail(e) {
    const { id } = e.currentTarget.dataset
    // Debug log removed

    if (!id || id === 'undefined') {
      console.error('❌ 茶地ID无效，无法跳转')
      wx.showToast({
        title: '茶地信息错误',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/tea-detail/tea-detail?id=${id}`
    })
  },

  // 查看新闻详情
  viewNewsDetail(e) {
    const { id } = e.currentTarget.dataset
    if (!id || id.toString().startsWith('default_')) {
      // 默认新闻显示简单弹窗
      const news = this.data.news.find(item => item.id === id)
      if (news) {
        wx.showModal({
          title: news.title,
          content: news.summary,
          showCancel: false,
          confirmText: '我知道了'
        })
      }
      return
    }

    // 真实新闻跳转到详情页面
    wx.navigateTo({
      url: `/pages/news-detail/news-detail?id=${id}`,
      fail: () => {
        // 如果详情页面不存在，显示弹窗
        const news = this.data.news.find(item => item.id === id)
        if (news) {
          wx.showModal({
            title: news.title,
            content: news.summary || '暂无详细内容',
            showCancel: false,
            confirmText: '我知道了'
          })
        } else {
          wx.showToast({
            title: '新闻不存在',
            icon: 'none'
          })
        }
      }
    })
  },

  // 显示即将推出
  showComingSoon() {
    wx.showToast({
      title: '功能开发中...',
      icon: 'none'
    })
  },

  // 获取默认轮播图数据 - 空数组，强制使用后台数据
  getDefaultBanners() {
    return []
  },

  // 图片加载错误处理
  onImageError(e) {
    // 可以在这里设置默认图片或显示错误提示
  },

  // 测试导航功能
  testNavigation() {
    const testUrls = [
      '/pages/tea-list/tea-list',
      '/pages/monitoring/monitoring',
      '/pages/analytics/analytics',
      '/pages/earnings/earnings',
      '/pages/my-fields/my-fields'
    ]

    testUrls.forEach(url => {
    })
  },

  // 消息点击处理
  onMessageTap(e) {
    const message = e.detail
    // 根据消息类型跳转到相应页面
    if (message.data && message.data.url) {
      wx.navigateTo({
        url: message.data.url
      })
    }
  },

  // 新消息通知
  onNewMessage(e) {
    const message = e.detail
    // 可以在这里添加新消息的特殊处理逻辑
    // 比如播放提示音、震动等
    if (message.priority >= 3) {
      wx.vibrateShort()
    }
  },

  // 发送测试消息
  sendTestMessage() {
    NotificationManager.sendLocalNotification({
      type: MESSAGE_TYPES.SYSTEM,
      title: '测试消息',
      content: '这是一条测试消息，用于验证消息推送功能。',
      data: {
        url: '/pages/notifications/notifications'
      }
    })
  },

  // 网络状态变化处理
  onNetworkChange(e) {
    const { isOffline, networkType } = e.detail
    if (!isOffline) {
      // 网络恢复，显示提示
      wx.showToast({
        title: '网络已恢复',
        icon: 'success',
        duration: 2000
      })

      // 刷新数据
      this.refreshData().catch(error => {
        console.error('网络恢复后刷新数据失败:', error)
      })
    } else {
      // 网络断开，显示离线提示
      wx.showToast({
        title: '已进入离线模式',
        icon: 'none',
        duration: 2000
      })
    }
  },

  // 离线数据同步
  onOfflineSync(e) {
    wx.showLoading({
      title: '同步中...',
      mask: true
    })

    // 这里可以添加具体的同步逻辑
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '同步完成',
        icon: 'success'
      })
    }, 2000)
  },

  // 加载AI投资建议
  loadInvestmentAdvice() {
    return new Promise((resolve, reject) => {
      this.setData({
        'investmentAdvice.loading': true
      })

      // 调用AI投资建议API
      api.aiApi.getInvestmentAdvice().then(res => {
        if (res.code === 200 && res.data) {
          this.setData({
            'investmentAdvice.data': res.data,
            'investmentAdvice.loading': false
          })
        } else {
          // 使用模拟数据
          this.setData({
            'investmentAdvice.data': this.getMockInvestmentAdvice(),
            'investmentAdvice.loading': false
          })
        }
        resolve()
      }).catch(error => {
        console.error('❌ AI投资建议API调用失败:', error)
        // 使用模拟数据
        this.setData({
          'investmentAdvice.data': this.getMockInvestmentAdvice(),
          'investmentAdvice.loading': false
        })
        resolve()
      })
    })
  },

  // 获取模拟投资建议数据
  getMockInvestmentAdvice() {
    return {
      title: '基于您的投资偏好，AI为您推荐',
      summary: '根据当前市场行情和您的风险偏好，以下茶地具有较好的投资潜力',
      recommendations: [
        {
          icon: '🌟',
          title: '凤凰单枞精品区',
          description: '地理位置优越，品种稀有，预期年收益12-15%',
          score: '9.2',
          scoreColor: '#10b981'
        },
        {
          icon: '💎',
          title: '南万白叶茶园',
          description: '高端品种，市场需求旺盛，适合长期投资',
          score: '8.8',
          scoreColor: '#3b82f6'
        },
        {
          icon: '🚀',
          title: '新兴茶区机会',
          description: '价格相对较低，未来增值潜力大',
          score: '8.5',
          scoreColor: '#f59e0b'
        }
      ]
    }
  },

  // 加载视频号数据
  loadVideoChannel() {
    return new Promise((resolve, reject) => {
      this.setData({
        'videoChannel.loading': true
      })

      // 调用视频号API
      api.videoApi.getChannelVideos().then(res => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          // 处理真实视频数据
          const videos = res.data.videos || res.data || []
          const processedVideos = videos.map(video => ({
            id: video.id,
            title: video.title,
            thumbnail: video.thumbnail || 'https://via.placeholder.com/280x160/4ade80/ffffff?text=视频',
            duration: video.duration || '00:00',
            viewCount: this.formatCount(video.view_count || 0),
            likeCount: this.formatCount(video.like_count || 0),
            publishTime: this.formatTime(video.publish_time),
            description: video.description || '',
            category: video.category || 'general'
          }))

          this.setData({
            'videoChannel.videos': processedVideos,
            'videoChannel.loading': false
          })
        } else {
          this.setMockVideoData()
        }
        resolve()
      }).catch(error => {
        console.error('❌ 视频号API调用失败，使用模拟数据:', error)
        this.setMockVideoData()
        resolve()
      })
    })
  },

  // 设置空视频数据
  setEmptyVideoData() {
    this.setData({
      'videoChannel.videos': [],
      'videoChannel.loading': false
    })
  },

  // 设置模拟视频数据
  setMockVideoData() {
    const mockVideos = [
      {
        id: 1,
        title: '春茶采摘实况直播',
        thumbnail: 'https://via.placeholder.com/280x160/4ade80/ffffff?text=春茶采摘',
        duration: '02:35',
        viewCount: '1.2万',
        likeCount: '856',
        publishTime: '2天前',
        description: '带您走进凤凰山茶园，感受春茶采摘的魅力',
        category: 'tea_garden'
      },
      {
        id: 2,
        title: '茶园智能监控系统',
        thumbnail: 'https://via.placeholder.com/280x160/22c55e/ffffff?text=智能监控',
        duration: '03:12',
        viewCount: '980',
        likeCount: '654',
        publishTime: '3天前',
        description: '了解现代化茶园智能管理系统',
        category: 'technology'
      },
      {
        id: 3,
        title: '茶叶投资收益分析',
        thumbnail: 'https://via.placeholder.com/280x160/16a34a/ffffff?text=投资分析',
        duration: '04:28',
        viewCount: '1.5万',
        likeCount: '1.1千',
        publishTime: '5天前',
        description: '专业分析师解读茶叶投资市场',
        category: 'investment'
      },
      {
        id: 4,
        title: '茶园四季风光',
        thumbnail: 'https://via.placeholder.com/280x160/15803d/ffffff?text=四季风光',
        duration: '05:15',
        viewCount: '2.3万',
        likeCount: '1.8千',
        publishTime: '1周前',
        description: '记录茶园一年四季的美丽风光',
        category: 'scenery'
      },
      {
        id: 5,
        title: '传统制茶工艺展示',
        thumbnail: 'https://via.placeholder.com/280x160/166534/ffffff?text=制茶工艺',
        duration: '06:42',
        viewCount: '1.8万',
        likeCount: '1.4千',
        publishTime: '1周前',
        description: '非遗传承人现场展示传统制茶工艺',
        category: 'culture'
      }
    ]

    this.setData({
      'videoChannel.videos': mockVideos,
      'videoChannel.loading': false
    })
  },

  // 格式化数量显示
  formatCount(count) {
    if (!count || count === 0) return '0'

    if (count >= 10000) {
      return (count / 10000).toFixed(1) + '万'
    } else if (count >= 1000) {
      return (count / 1000).toFixed(1) + 'k'
    } else {
      return count.toString()
    }
  },

  // 格式化时间显示
  formatTime(timeStr) {
    if (!timeStr) return '刚刚'

    try {
      const publishTime = new Date(timeStr)
      const now = new Date()
      const diffMs = now - publishTime
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffMinutes = Math.floor(diffMs / (1000 * 60))

      if (diffDays > 0) {
        return `${diffDays}天前`
      } else if (diffHours > 0) {
        return `${diffHours}小时前`
      } else if (diffMinutes > 0) {
        return `${diffMinutes}分钟前`
      } else {
        return '刚刚'
      }
    } catch (e) {
      return '刚刚'
    }
  },

  // 生成AI建议
  generateAdvice() {
    this.loadInvestmentAdvice()
  },

  // 刷新建议
  refreshAdvice() {
    this.loadInvestmentAdvice()
  },

  // 打开AI聊天
  openAIChat() {
    wx.navigateTo({
      url: '/pages/ai-chat/ai-chat'
    })
  },

  // 播放视频
  playVideo(e) {
    const video = e.currentTarget.dataset.video
    wx.navigateTo({
      url: `/pages/video-player/video-player?id=${video.id}`
    })
  },

  // ==================== 装修系统相关方法 ====================

  /**
   * 准备装修数据源
   */
  prepareDecorationData() {
    // Debug log removed
    // Debug log removed

    const decorationData = {
      // 轮播图数据 - 直接使用原始数据
      banners: this.data.banners || [],

      // 统计数据 - 直接使用原始数据
      stats: this.data.stats || {
        totalAcres: 0,
        availableAcres: 0,
        soldAcres: 0,
        soldRate: 0,
        monthlyIncrease: 0
      },

      // 推荐茶地数据 - 直接使用原始数据
      recommendedTeas: this.data.recommendedTeas || [],

      // 新闻数据 - 直接使用原始数据
      news: this.data.news || [],

      // AI投资建议数据 - 直接使用原始数据
      investmentAdvice: this.data.investmentAdvice || {
        loading: false,
        data: null
      },

      // 视频频道数据 - 直接使用原始数据
      videoChannel: {
        loading: this.data.videoChannel?.loading || false,
        videos: this.data.videoChannel?.videos || []
      },

      // 新手引导状态
      showGuide: this.data.showGuide || false,

      // 精选茶园数据（向后兼容）
      featuredTeas: this.data.recommendedTeas.slice(0, 4).map(tea => ({
        id: tea.id,
        name: tea.name,
        variety: tea.variety,
        description: tea.description,
        image: tea.image,
        price: tea.price,
        location: tea.location,
        expected_return: tea.expected_return,
        sales_count: tea.sales_count || 0
      })),

      // 热门茶园数据（向后兼容）
      hotTeas: this.data.recommendedTeas.slice(0, 6).map(tea => ({
        id: tea.id,
        name: tea.name,
        variety: tea.variety,
        description: tea.description,
        image: tea.image,
        price: tea.price,
        location: tea.location,
        expected_return: tea.expected_return,
        sales_count: tea.sales_count || 0
      })),

      // 新品茶园数据（向后兼容）
      newTeas: this.data.recommendedTeas.slice(0, 4).map(tea => ({
        id: tea.id,
        name: tea.name,
        variety: tea.variety,
        description: tea.description,
        image: tea.image,
        price: tea.price,
        location: tea.location,
        expected_return: tea.expected_return,
        sales_count: tea.sales_count || 0
      }))
    }
    // Debug log removed

    this.setData({
      decorationData,
      decorationReady: true
    })
    // Debug log removed
    // Debug log removed
    return decorationData
  },

  /**
   * 切换装修模式
   */
  toggleDecorationMode() {
    const newMode = !this.data.useDecoration
    this.setData({
      useDecoration: newMode,
      decorationReady: false
    })

    if (newMode) {
      // 切换到装修系统，重新初始化
      this.initPage()
    }
  },

  /**
   * 装修配置加载完成
   */
  onDecorationConfigLoaded(e) {
    const { pageType, pageConfig, globalConfig } = e.detail
  },

  /**
   * 装修配置加载错误
   */
  onDecorationConfigError(e) {
    const { pageType, error } = e.detail
    console.error('❌ 装修配置加载失败', { pageType, error })
  },

  /**
   * 装修板块点击
   */
  onDecorationBlockTap(e) {
    const { blockType, blockConfig, detail } = e.detail
    // 根据板块类型处理点击事件
    switch (blockType) {
      case 'banner':
        // 轮播图点击处理
        break
      case 'quick_nav':
        // 快捷导航点击处理
        break
      case 'featured_tea':
      case 'hot_tea':
      case 'new_tea':
        // 茶园板块点击处理
        break
      case 'video_showcase':
        // 视频展示点击处理
        break
      default:
    }
  },

  /**
   * 装修项目点击
   */
  onDecorationItemTap(e) {
    const { item, blockType } = e.detail
    // 根据板块类型和项目处理点击事件
    switch (blockType) {
      case 'featured_tea':
      case 'hot_tea':
      case 'new_tea':
        // 跳转到茶园详情
        if (item.link_url) {
          wx.navigateTo({
            url: item.link_url
          })
        }
        break
      case 'video_showcase':
        // 跳转到视频详情
        if (item.link_url) {
          wx.navigateTo({
            url: item.link_url
          })
        }
        break
      case 'banner':
        // 轮播图跳转
        if (item.link_url) {
          wx.navigateTo({
            url: item.link_url
          })
        }
        break
      default:
        // 默认处理
        if (item.link_url) {
          wx.navigateTo({
            url: item.link_url
          })
        }
    }
  },

  /**
   * 切换装修模式
   */
  toggleDecorationMode() {
    const useDecoration = !this.data.useDecoration
    this.setData({
      useDecoration
    })
    if (useDecoration) {
      // 准备装修数据
      this.prepareDecorationData()
    }
  },

  // ==================== 自定义底部导航相关方法 ====================

  // 初始化自定义底部导航
  async initCustomTabBar() {
    try {
      const app = getApp()

      // 获取全局配置
      let config = app.globalData.customTabBarConfig

      // 如果没有配置，重新加载
      if (!config) {
        await app.reloadCustomTabBar()
        config = app.globalData.customTabBarConfig
      }

      this.setData({
        customTabBarConfig: config
      })

      this.updateCustomTabBarIndex()

    } catch (error) {
      console.error('❌ 首页初始化自定义底部导航失败:', error)
    }
  },

  // 更新自定义导航选中状态
  updateCustomTabBarIndex() {
    const app = getApp()
    const currentIndex = app.getCurrentTabIndex()

    this.setData({
      currentTabIndex: currentIndex
    })

    // 更新自定义导航组件的选中状态
    const customTabBar = this.selectComponent('#custom-tabbar')
    if (customTabBar) {
      customTabBar.setData({
        current: currentIndex
      })
    }
  },

  // 自定义导航切换事件
  onCustomTabChange(e) {
    const { index, item, pagePath } = e.detail
    // 使用页面路由管理器导航
    const app = getApp()
    app.navigateToPage(pagePath)
  },

  // 重新加载自定义导航配置
  async reloadCustomTabBar() {
    try {
      const app = getApp()
      const success = await app.reloadCustomTabBar()

      if (success) {
        const config = app.globalData.customTabBarConfig
        this.setData({
          customTabBarConfig: config
        })
      }

      return success
    } catch (error) {
      console.error('❌ 首页重新加载自定义导航配置失败:', error)
      return false
    }
  }
})

<!-- 首页 -->

<view class="container">

  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-left">
      <text class="app-title">茶管家</text>
    </view>
    <view class="header-right">
      <!-- 装修模式切换按钮 (开发时使用) -->
      <view wx:if="{{false}}" class="decoration-toggle" bindtap="toggleDecorationMode">
        <text class="toggle-text">{{useDecoration ? '装修' : '原版'}}</text>
      </view>
      <message-badge
        id="messageBadge"
        max-count="{{5}}"
        auto-refresh="{{true}}"
        bindmessagetap="onMessageTap"
        bindnewmessage="onNewMessage">
      </message-badge>
    </view>
  </view>

  <!-- 调试信息 -->
  <view wx:if="{{false}}" class="debug-info">
    <view style="margin-top: 10px;">
      <button size="mini" type="{{useDecoration ? 'warn' : 'primary'}}" bindtap="toggleDecorationMode">
        {{useDecoration ? '切换到原版页面' : '切换到装修系统'}}
      </button>
      <button size="mini" type="default" bindtap="testDirectAPI" style="margin-left: 10px;">
        重新测试API
      </button>
    </view>
  </view>

  <!-- 装修系统页面 -->
  <decoration-page
    wx:if="{{useDecoration && decorationReady}}"
    page-type="home"
    dataSource="{{decorationData}}"
    debugMode="{{debugMode}}"
    bind:configLoaded="onDecorationConfigLoaded"
    bind:configError="onDecorationConfigError"
    bind:blockTap="onDecorationBlockTap"
    bind:itemTap="onDecorationItemTap">
  </decoration-page>

  <!-- 装修数据加载中 -->
  <view wx:if="{{useDecoration && !decorationReady}}" class="decoration-loading">
    <view class="loading-spinner"></view>
    <view class="loading-text">正在加载页面数据...</view>
  </view>

  <!-- 原版页面内容 -->
  <view wx:if="{{!useDecoration}}" class="original-content">

  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper"
            indicator-dots="{{true}}"
            indicator-color="rgba(255,255,255,0.5)"
            indicator-active-color="#ffffff"
            autoplay="{{true}}"
            interval="3000"
            duration="500"
            circular="{{true}}">
      <swiper-item wx:for="{{banners}}" wx:key="id">
        <image src="{{item.image}}"
               class="banner-image"
               mode="aspectFill"
               bindtap="onBannerTap"
               data-url="{{item.link_url}}"
               binderror="onImageError"></image>
        <view class="banner-overlay">
          <view class="banner-content">
            <text class="banner-title">{{item.title}}</text>
            <text class="banner-subtitle">{{item.subtitle}}</text>
            <view class="banner-actions">
              <button class="btn-guide" bindtap="startGuide">
                <text class="icon">📖</text>
                <text>新手指南</text>
              </button>
              <button class="btn-browse" bindtap="browseFields">
                <text class="icon">🍃</text>
                <text>立即浏览</text>
              </button>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 数据概览 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item" bindtap="showStatDetail" data-type="total">
        <text class="stat-number">{{stats.totalAcres}}</text>
        <text class="stat-label">总亩数</text>
        <text class="stat-trend">+{{stats.monthlyIncrease}} 本月新增</text>
      </view>
      <view class="stat-item" bindtap="showStatDetail" data-type="available">
        <text class="stat-number">{{stats.availableAcres}}</text>
        <text class="stat-label">可认购</text>
        <text class="stat-trend">热门推荐</text>
      </view>
      <view class="stat-item" bindtap="showStatDetail" data-type="sold">
        <text class="stat-number">{{stats.soldAcres}}</text>
        <text class="stat-label">已认购</text>
        <text class="stat-trend">{{stats.soldRate}}% 认购率</text>
      </view>
    </view>
  </view>

  <!-- 新手引导提示 -->
  <view class="guide-banner" wx:if="{{showGuide}}">
    <view class="guide-content">
      <view class="guide-icon">💡</view>
      <view class="guide-text">
        <text class="guide-title">新手指南</text>
        <text class="guide-desc">了解茶园认购流程，轻松开始投资</text>
      </view>
      <view class="guide-close" bindtap="closeGuide">✕</view>
    </view>
    <view class="guide-actions">
      <button class="btn-start" bindtap="startGuideStep">开始指南</button>
      <button class="btn-later" bindtap="closeGuide">稍后再说</button>
    </view>
  </view>

  <!-- 功能入口 -->
  <view class="features-section">
    <view class="card">
      <view class="card-header">
        <text class="card-title">快速入口</text>
      </view>
      <view class="features-grid">
        <view class="feature-item" bindtap="navigateTo" data-url="/pages/tea-list/tea-list">
          <view class="feature-icon bg-green">🍃</view>
          <text class="feature-label">浏览茶地</text>
        </view>
        <view class="feature-item" bindtap="navigateTo" data-url="/pages/tea-list/tea-list?action=purchase">
          <view class="feature-icon bg-blue">🛒</view>
          <text class="feature-label">立即认购</text>
        </view>
        <view class="feature-item" bindtap="navigateTo" data-url="/pages/monitoring/monitoring">
          <view class="feature-icon bg-orange">📊</view>
          <text class="feature-label">实时监控</text>
        </view>
        <view class="feature-item" bindtap="navigateTo" data-url="/pages/earnings/earnings">
          <view class="feature-icon bg-purple">💰</view>
          <text class="feature-label">收益查看</text>
        </view>
        <view class="feature-item" bindtap="navigateTo" data-url="/pages/my-fields/my-fields">
          <view class="feature-icon bg-teal">🏞️</view>
          <text class="feature-label">我的茶地</text>
        </view>
        <view class="feature-item" bindtap="navigateTo" data-url="/pages/analytics/analytics">
          <view class="feature-icon bg-indigo">📈</view>
          <text class="feature-label">数据分析</text>
        </view>
        <view class="feature-item" bindtap="navigateTo" data-url="/pages/investment-advice/investment-advice">
          <view class="feature-icon bg-pink">🎯</view>
          <text class="feature-label">投资建议</text>
        </view>
        <view class="feature-item" bindtap="navigateTo" data-url="/pages/video-channel/video-channel">
          <view class="feature-icon bg-yellow">📹</view>
          <text class="feature-label">视频号</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 推荐茶地 -->
  <view class="recommended-section">
    <view class="card">
      <view class="card-header">
        <text class="card-title">推荐茶地</text>
        <text class="card-more" bindtap="navigateTo" data-url="/pages/tea-list/tea-list">查看更多 ></text>
      </view>
      <scroll-view class="tea-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
        <view class="tea-list">
          <view class="tea-item" wx:for="{{recommendedTeas}}" wx:key="id" bindtap="viewTeaDetail" data-id="{{item.id}}">
            <image wx:if="{{item.image}}" src="{{item.image}}" class="tea-image" mode="aspectFill" binderror="onImageError"></image>
            <view wx:else class="tea-image-placeholder">
              <text class="placeholder-icon">🌱</text>
            </view>
            <view class="tea-info">
              <text class="tea-name">{{item.name}}</text>
              <text class="tea-location">{{item.location}}</text>
              <view class="tea-price">
                <text class="price-label">认购价格</text>
                <text class="price-value">¥{{item.price}}/亩</text>
              </view>
              <view class="tea-return">
                <text class="return-label">预期收益</text>
                <text class="return-value">{{item.expected_return}}%</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- AI投资建议 -->
  <view class="investment-advice-section">
    <view class="card">
      <view class="card-header">
        <text class="card-title">🎯 AI投资建议</text>
        <text class="card-more" bindtap="navigateTo" data-url="/pages/investment-advice/investment-advice">查看更多 ></text>
      </view>
      <view class="advice-content">
        <view wx:if="{{investmentAdvice.loading}}" class="advice-loading">
          <view class="loading-spinner"></view>
          <text class="loading-text">AI正在分析中...</text>
        </view>
        <view wx:elif="{{investmentAdvice.data}}" class="advice-result">
          <view class="advice-summary">
            <text class="advice-title">{{investmentAdvice.data.title}}</text>
            <text class="advice-desc">{{investmentAdvice.data.summary}}</text>
          </view>
          <view class="advice-recommendations">
            <view class="recommendation-item" wx:for="{{investmentAdvice.data.recommendations}}" wx:key="index">
              <view class="rec-icon">{{item.icon}}</view>
              <view class="rec-content">
                <text class="rec-title">{{item.title}}</text>
                <text class="rec-desc">{{item.description}}</text>
              </view>
              <view class="rec-score" style="color: {{item.scoreColor}}">{{item.score}}</view>
            </view>
          </view>
          <view class="advice-actions">
            <button class="btn-ai-chat" bindtap="openAIChat">💬 AI咨询</button>
            <button class="btn-refresh-advice" bindtap="refreshAdvice">🔄 刷新建议</button>
          </view>
        </view>
        <view wx:else class="advice-empty">
          <text class="empty-icon">🤖</text>
          <text class="empty-text">暂无投资建议</text>
          <button class="btn-generate-advice" bindtap="generateAdvice">生成AI建议</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 视频号展示 -->
  <view class="video-channel-section">
    <view class="card">
      <view class="card-header">
        <text class="card-title">📹 茶园视频号</text>
        <text class="card-more" bindtap="navigateTo" data-url="/pages/video-channel/video-channel">查看更多 ></text>
      </view>
      <view class="video-content">
        <view wx:if="{{videoChannel.loading}}" class="video-loading">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载视频中...</text>
        </view>
        <scroll-view wx:elif="{{videoChannel.videos.length > 0}}" class="video-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
          <view class="video-list">
            <view class="video-item" wx:for="{{videoChannel.videos}}" wx:key="id" bindtap="playVideo" data-video="{{item}}">
              <view class="video-thumbnail">
                <image src="{{item.thumbnail}}" class="thumbnail-image" mode="aspectFill"></image>
                <view class="play-overlay">
                  <view class="play-button">▶️</view>
                </view>
                <view class="video-duration">{{item.duration}}</view>
              </view>
              <view class="video-info">
                <text class="video-title">{{item.title}}</text>
                <view class="video-stats">
                  <text class="stat-item">👁️ {{item.viewCount}}</text>
                  <text class="stat-item">👍 {{item.likeCount}}</text>
                </view>
                <text class="video-time">{{item.publishTime}}</text>
              </view>
            </view>
          </view>
        </scroll-view>
        <view wx:else class="video-empty">
          <text class="empty-icon">📹</text>
          <text class="empty-text">暂无视频内容</text>
          <text class="empty-desc">管理员还未发布任何视频</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 最新动态 -->
  <view class="news-section">
    <view class="card">
      <view class="card-header">
        <text class="card-title">最新动态</text>
        <text class="card-more" bindtap="showComingSoon">查看更多 ></text>
      </view>
      <view class="news-list">
        <block wx:if="{{news.length > 0}}">
          <view class="news-item" wx:for="{{news}}" wx:key="id" bindtap="viewNewsDetail" data-id="{{item.id}}">
            <view class="news-content">
              <view class="news-header">
                <text class="news-title">{{item.title}}</text>
                <view class="news-badges">
                  <text wx:if="{{item.is_top}}" class="badge top-badge">置顶</text>
                  <text wx:if="{{item.is_featured}}" class="badge featured-badge">推荐</text>
                  <text class="news-type" style="color: {{item.color}}">{{item.news_type_display}}</text>
                </view>
              </view>
              <text class="news-summary">{{item.summary}}</text>
              <view class="news-meta">
                <text class="news-time">{{item.time || item.created_at}}</text>
                <text wx:if="{{item.view_count}}" class="news-views">👁️ {{item.view_count}}</text>
              </view>
            </view>
            <image wx:if="{{item.image}}" src="{{item.image}}" class="news-image" mode="aspectFill" binderror="onImageError"></image>
            <view wx:else class="news-image-placeholder">
              <text class="placeholder-icon">📰</text>
            </view>
          </view>
        </block>
        <view wx:else class="empty-state">
          <text class="empty-icon">📰</text>
          <text class="empty-text">暂无最新动态</text>
          <text class="empty-desc">管理员还未发布任何动态信息</text>
        </view>
      </view>
    </view>
  </view>
  </view> <!-- 原版内容结束 -->

</view> <!-- 容器结束 -->

<!-- 自定义底部导航 -->
<custom-tabbar
  id="custom-tabbar"
  config="{{customTabBarConfig}}"
  current="{{currentTabIndex}}"
  bind:tabchange="onCustomTabChange"
/>

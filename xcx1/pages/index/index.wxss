/* 首页样式 */

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #2E7D32;
  color: white;
  position: relative;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
}

.app-title {
  font-size: 36rpx;
  font-weight: bold;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

/* 容器 */
.container {
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
  min-height: 100vh;
}

/* 轮播图区域 */
.banner-section {
  position: relative;
  height: 400rpx;
  margin-bottom: 32rpx;
  border-radius: 0 0 32rpx 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.banner-swiper {
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, transparent 50%, rgba(0, 0, 0, 0.4) 100%);
  display: flex;
  align-items: flex-end;
  padding: 48rpx 32rpx;
}

.banner-content {
  color: white;
  width: 100%;
}

.banner-title {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  display: block;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.banner-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 32rpx;
  display: block;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.4);
}

.banner-actions {
  display: flex;
  gap: 24rpx;
}

.btn-guide,
.btn-browse {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 32rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.btn-guide .icon,
.btn-browse .icon {
  font-size: 32rpx;
}

.btn-guide:active,
.btn-browse:active {
  background: rgba(255, 255, 255, 0.7);
  transform: scale(0.98);
}

/* 统计数据区域 */
.stats-section {
  margin: 0 32rpx 32rpx;
}

.stats-grid {
  display: flex;
  gap: 16rpx;
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(34, 197, 94, 0.1);
  border: 1rpx solid rgba(34, 197, 94, 0.1);
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 16rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #16a34a;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(34, 197, 94, 0.1);
}

.stat-label {
  display: block;
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.stat-trend {
  display: block;
  font-size: 24rpx;
  color: #22c55e;
  font-weight: 600;
}

/* 新手引导 */
.guide-banner {
  margin: 0 32rpx 32rpx;
  background: linear-gradient(135deg, #E8F5E8, #F1F8E9);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 2rpx solid #E8F5E8;
}

.guide-content {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.guide-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.guide-text {
  flex: 1;
}

.guide-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #212121;
  margin-bottom: 8rpx;
}

.guide-desc {
  display: block;
  font-size: 28rpx;
  color: #757575;
}

.guide-close {
  font-size: 32rpx;
  color: #BDBDBD;
  padding: 8rpx;
}

.guide-actions {
  display: flex;
  gap: 16rpx;
}

.btn-start {
  flex: 1;
  background: #2E7D32;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.btn-later {
  flex: 1;
  background: transparent;
  color: #757575;
  border: 2rpx solid #E0E0E0;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

/* 功能入口区域 */
.features-section {
  margin: 0 32rpx 32rpx;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
  margin-top: 24rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(34, 197, 94, 0.1);
  transition: all 0.3s ease;
}

.feature-item:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
  box-shadow: 0 8rpx 24rpx rgba(34, 197, 94, 0.15);
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.bg-green {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
}
.bg-blue {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}
.bg-orange {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}
.bg-purple {
  background: linear-gradient(135deg, #a855f7, #7c3aed);
  color: white;
}
.bg-teal {
  background: linear-gradient(135deg, #14b8a6, #0d9488);
  color: white;
}
.bg-indigo {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
}
.bg-pink {
  background: linear-gradient(135deg, #ec4899, #db2777);
  color: white;
}
.bg-yellow {
  background: linear-gradient(135deg, #eab308, #ca8a04);
  color: white;
}

.feature-label {
  font-size: 24rpx;
  color: #374151;
  text-align: center;
  font-weight: 500;
}

/* 推荐茶地区域 */
.recommended-section {
  margin: 0 32rpx 32rpx;
}

.card-more {
  font-size: 28rpx;
  color: #2E7D32;
}

.tea-scroll {
  margin-top: 24rpx;
}

.tea-list {
  display: flex;
  gap: 24rpx;
  padding: 0 8rpx;
}

.tea-item {
  width: 280rpx;
  flex-shrink: 0;
  background: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tea-image {
  width: 100%;
  height: 200rpx;
}

.tea-image-placeholder {
  width: 100%;
  height: 200rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #cbd5e1;
}

.tea-info {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tea-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #212121;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tea-location {
  display: block;
  font-size: 24rpx;
  color: #BDBDBD;
  margin-bottom: 16rpx;
}

.tea-price,
.tea-return {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
  gap: 8rpx;
}

.price-label,
.return-label {
  font-size: 24rpx;
  color: #757575;
  white-space: nowrap;
}

.price-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #2E7D32;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.return-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #4CAF50;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 最新动态区域 */
.news-section {
  margin: 0 32rpx 32rpx;
}

.news-list {
  margin-top: 24rpx;
}

.news-item {
  display: flex;
  padding: 24rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.news-content {
  flex: 1;
  margin-right: 24rpx;
}

.news-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.news-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212121;
  flex: 1;
  margin-right: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-badges {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  align-items: flex-end;
}

.badge {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  color: white;
  white-space: nowrap;
}

.top-badge {
  background-color: #ef4444;
}

.featured-badge {
  background-color: #f59e0b;
}

.news-type {
  font-size: 20rpx;
  font-weight: 500;
  padding: 2rpx 8rpx;
  background-color: rgba(34, 197, 94, 0.1);
  border-radius: 8rpx;
  white-space: nowrap;
}

.news-summary {
  font-size: 28rpx;
  color: #757575;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.news-time {
  font-size: 24rpx;
  color: #BDBDBD;
}

.news-views {
  font-size: 24rpx;
  color: #BDBDBD;
}

.news-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.news-image-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #cbd5e1;
}

.placeholder-icon {
  font-size: 40rpx;
  opacity: 0.6;
}

.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;
  color: #9ca3af;
}

.empty-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
  color: #6b7280;
}

.empty-desc {
  font-size: 28rpx;
  display: block;
  color: #9ca3af;
}

/* AI投资建议模块 */
.investment-advice-section {
  margin: 32rpx 30rpx;
}

.advice-content {
  padding: 0;
}

.advice-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f4f6;
  border-top: 4rpx solid #2E7D32;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #6b7280;
}

.advice-result {
  padding: 0;
}

.advice-summary {
  padding: 30rpx;
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}

.advice-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #92400e;
  display: block;
  margin-bottom: 12rpx;
}

.advice-desc {
  font-size: 28rpx;
  color: #a16207;
  line-height: 1.6;
}

.advice-recommendations {
  margin-bottom: 24rpx;
}

.recommendation-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: white;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.rec-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.rec-content {
  flex: 1;
}

.rec-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #1f2937;
  display: block;
  margin-bottom: 8rpx;
}

.rec-desc {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

.rec-score {
  font-size: 28rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  background: #f3f4f6;
  border-radius: 20rpx;
}

.advice-actions {
  display: flex;
  gap: 20rpx;
  padding: 0 24rpx;
}

.btn-ai-chat, .btn-refresh-advice {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
}

.btn-ai-chat {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.btn-refresh-advice {
  background: #f3f4f6;
  color: #374151;
}

.advice-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.btn-generate-advice {
  margin-top: 20rpx;
  padding: 20rpx 40rpx;
  background: linear-gradient(135deg, #2E7D32, #1b5e20);
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

/* 视频号模块 */
.video-channel-section {
  margin: 32rpx 30rpx;
}

.video-content {
  padding: 0;
}

.video-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.video-scroll {
  white-space: nowrap;
}

.video-list {
  display: flex;
  gap: 24rpx;
  padding: 0 24rpx;
}

.video-item {
  display: inline-block;
  width: 280rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  vertical-align: top;
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 160rpx;
  background: #f3f4f6;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}

.play-button {
  font-size: 48rpx;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.video-duration {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.video-info {
  padding: 20rpx;
}

.video-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1f2937;
  display: block;
  margin-bottom: 12rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-stats {
  display: flex;
  gap: 16rpx;
  margin-bottom: 8rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #6b7280;
}

.video-time {
  font-size: 24rpx;
  color: #9ca3af;
}

.video-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

/* 装修系统加载状态 */
.decoration-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  background-color: #f5f5f5;
  min-height: 400rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #2E7D32;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 自定义底部导航样式 */
.container {
  padding-bottom: 120rpx; /* 为自定义底部导航留出空间 */
}

/* 确保滚动容器也有底部留白 */
scroll-view {
  padding-bottom: 120rpx;
}

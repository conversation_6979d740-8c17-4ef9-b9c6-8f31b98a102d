/* 茶地详情页样式 */

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #e9ecef;
  border-top: 6rpx solid #2E7D32;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.container {
  background-color: #F5F5F5;
  min-height: 100vh;
  padding-bottom: 240rpx;
  position: relative;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background: white;
  margin: 24rpx 24rpx 0;
  border-radius: 16rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 16rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #64748b;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  font-weight: 600;
}

/* 标签页内容 */
.tab-content {
  margin-top: 24rpx;
}

.tab-panel {
  display: block;
}

/* 图片区域 */
.image-section {
  position: relative;
  height: 500rpx;
}

.image-swiper {
  width: 100%;
  height: 100%;
}

.detail-image {
  width: 100%;
  height: 100%;
}

.status-badge {
  position: absolute;
  top: 32rpx;
  left: 32rpx;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  color: white;
  z-index: 10;
}

.status-badge.available {
  background: #4CAF50;
}

.status-badge.sold {
  background: #F44336;
}

.status-badge.reserved {
  background: #FF9800;
}

.back-btn {
  position: absolute;
  top: 32rpx;
  left: 32rpx;
  width: 72rpx;
  height: 72rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.back-icon {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.share-btn {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 72rpx;
  height: 72rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.share-icon {
  color: white;
  font-size: 32rpx;
}

/* 信息区域 */
.info-section {
  margin: -40rpx 32rpx 32rpx;
  position: relative;
  z-index: 10;
}

.tea-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.tea-name {
  font-size: 40rpx;
  font-weight: 700;
  color: #212121;
  flex: 1;
  margin-right: 24rpx;
}

.tea-rating {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.rating-score {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF9800;
}

.rating-text {
  font-size: 24rpx;
  color: #757575;
}

.tea-location {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16rpx;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}

.location-info {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  flex: 1;
}

.location-icon {
  font-size: 32rpx;
  color: #28a745;
  margin-top: 4rpx;
}

.location-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.location-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
}

.detailed-location {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.coordinates {
  margin-top: 4rpx;
}

.coord-text {
  font-size: 22rpx;
  color: #999;
  font-family: 'Courier New', monospace;
}

.view-map-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  box-shadow: 0 2rpx 8rpx rgba(40, 167, 69, 0.3);
  flex-shrink: 0;
}

.view-map-btn::after {
  border: none;
}

.map-icon {
  font-size: 20rpx;
}

/* 地图预览 */
.map-preview {
  margin-bottom: 24rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #e9ecef;
  background: white;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.map-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.map-tip {
  font-size: 24rpx;
  color: #28a745;
  text-decoration: underline;
}

.preview-map {
  width: 100%;
  height: 300rpx;
}

.tea-description {
  font-size: 30rpx;
  color: #757575;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.tea-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag {
  padding: 8rpx 16rpx;
  background: #E8F5E8;
  color: #2E7D32;
  border-radius: 16rpx;
  font-size: 24rpx;
}

/* 价格区域 */
.price-section {
  margin: 0 32rpx 32rpx;
}

.price-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #212121;
}

.price-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.price-item {
  text-align: center;
  padding: 24rpx;
  background: #F5F5F5;
  border-radius: 12rpx;
}

.price-label {
  display: block;
  font-size: 26rpx;
  color: #757575;
  margin-bottom: 8rpx;
}

.price-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #2E7D32;
}

.return-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #4CAF50;
}

.area-value,
.period-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #212121;
}

/* 计算器 */
.calculator-section {
  margin-bottom: 32rpx;
}

.calculator-content {
  padding: 32rpx;
}

.calculator-header {
  margin-bottom: 32rpx;
  text-align: center;
}

.calculator-input {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32rpx;
  gap: 20rpx;
}

.input-label {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background: #f8f9fa;
  padding: 16rpx 24rpx;
  border-radius: 50rpx;
  border: 2rpx solid #e9ecef;
  width: 100%;
  max-width: 400rpx;
  justify-content: center;
}

.calc-btn {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #2E7D32, #4CAF50);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 36rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(46, 125, 50, 0.3);
  transition: all 0.2s ease;
}

.calc-btn::after {
  border: none;
}

.calc-btn:active {
  transform: scale(0.95);
}

.calc-btn.disabled {
  background: #e0e0e0 !important;
  color: #999 !important;
  box-shadow: none !important;
  cursor: not-allowed;
}

.calc-btn.disabled:active {
  transform: none !important;
}

.area-tip {
  font-size: 24rpx;
  color: #666;
  margin-top: 12rpx;
  text-align: center;
}

.area-input {
  width: 160rpx;
  height: 80rpx;
  text-align: center;
  border: none;
  background: white;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #2E7D32;
  box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.unit {
  font-size: 30rpx;
  color: #666;
  font-weight: 500;
}

.calculator-result {
  display: flex;
  gap: 20rpx;
  width: 100%;
}

.result-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 16rpx;
  background: linear-gradient(135deg, #E8F5E8, #F1F8E9);
  border-radius: 12rpx;
  border: 2rpx solid #C8E6C9;
  position: relative;
  overflow: hidden;
  min-width: 0;
}

.result-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #2E7D32, #4CAF50);
}

.result-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.result-value {
  font-size: 30rpx;
  font-weight: 700;
  color: #2E7D32;
  text-shadow: 0 1rpx 2rpx rgba(46, 125, 50, 0.1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 详细信息 */
.details-section {
  margin-bottom: 32rpx;
}

.details-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  transition: background-color 0.2s ease;
}

.detail-row:hover {
  background-color: #f8f9fa;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row::before {
  content: '';
  position: absolute;
  left: 32rpx;
  top: 0;
  width: 4rpx;
  height: 100%;
  background: linear-gradient(180deg, #2E7D32, #4CAF50);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.detail-row:hover::before {
  opacity: 1;
}

.detail-label {
  font-size: 30rpx;
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
  margin-right: 32rpx;
}

.detail-value {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  text-align: right;
  flex: 1;
}

/* 监控数据 */
.monitoring-section {
  margin: 0 32rpx 32rpx;
}

.monitoring-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.update-time {
  font-size: 24rpx;
  color: #757575;
}

.monitoring-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.monitor-item {
  text-align: center;
  padding: 20rpx;
  background: #F5F5F5;
  border-radius: 12rpx;
}

.monitor-label {
  display: block;
  font-size: 24rpx;
  color: #757575;
  margin-bottom: 8rpx;
}

.monitor-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #2E7D32;
}

.view-monitoring-btn {
  width: 100%;
  padding: 20rpx;
  background: #2E7D32;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}

/* 历史收益 */
.history-section {
  margin: 0 32rpx 32rpx;
}

.history-list {
  margin-top: 24rpx;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #F5F5F5;
}

.history-item:last-child {
  border-bottom: none;
}

.history-year {
  font-size: 28rpx;
  color: #212121;
  font-weight: 500;
}

.history-return {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: 600;
}

.history-amount {
  font-size: 28rpx;
  color: #757575;
}

/* 用户评价 */
.reviews-section {
  margin: 0 32rpx 32rpx;
}

.reviews-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.reviews-count {
  font-size: 28rpx;
  color: #757575;
}

.reviews-list {
  margin-bottom: 24rpx;
}

.review-item {
  padding: 24rpx 0;
  border-bottom: 2rpx solid #F5F5F5;
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 28rpx;
  color: #212121;
  font-weight: 500;
}

.review-time {
  display: block;
  font-size: 24rpx;
  color: #757575;
}

.review-rating {
  font-size: 24rpx;
}

.rating-stars {
  color: #FF9800;
}

.review-content {
  font-size: 28rpx;
  color: #757575;
  line-height: 1.6;
}

.view-all-reviews-btn {
  width: 100%;
  padding: 20rpx;
  background: transparent;
  color: #2E7D32;
  border: 2rpx solid #2E7D32;
  border-radius: 12rpx;
  font-size: 28rpx;
}

/* 相关推荐 */
.related-section {
  margin: 0 32rpx 32rpx;
}

.related-scroll {
  margin-top: 24rpx;
}

.related-list {
  display: flex;
  gap: 24rpx;
  padding: 0 8rpx;
}

.related-item {
  width: 200rpx;
  flex-shrink: 0;
}

.related-image {
  width: 100%;
  height: 150rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
}

.related-name {
  display: block;
  font-size: 26rpx;
  color: #212121;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-price {
  font-size: 24rpx;
  color: #2E7D32;
  font-weight: 600;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: white;
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  border-top: 2rpx solid #E0E0E0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24rpx;
  z-index: 10000;
  box-sizing: border-box;
  box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
  min-height: 100rpx;
}

/* 删除旧的action-left样式 */

.favorite-btn,
.consult-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
  background: transparent;
  border: none;
  font-size: 24rpx;
  color: #757575;
  min-height: 70rpx;
  justify-content: center;
}

.favorite-btn.favorited {
  color: #F44336;
}

.favorite-btn:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

.favorite-icon {
  transition: transform 0.2s ease;
}

.favorite-btn.favorited .favorite-icon {
  animation: heartBeat 0.6s ease-in-out;
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.2); }
  50% { transform: scale(1); }
  75% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.favorite-icon,
.consult-icon,
.purchase-icon {
  font-size: 32rpx;
}

.favorite-text,
.consult-text,
.purchase-text {
  font-size: 20rpx;
}

/* 删除旧的action-right样式 */

.purchase-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, #4CAF50, #45A049);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 600;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(46, 125, 50, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  justify-content: center;
  min-height: 70rpx;
}

.purchase-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.purchase-btn:active::before {
  left: 100%;
}

.purchase-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(46, 125, 50, 0.5);
}

.purchase-btn::after {
  border: none;
}

.purchase-btn.disabled {
  background: linear-gradient(135deg, #BDBDBD, #9E9E9E);
  color: #757575;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.purchase-text {
  font-size: 32rpx !important;
  font-weight: 600 !important;
  color: white !important;
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 11 !important;
}

/* 茶园特色功能 */
.features-section {
  margin: 0 24rpx 24rpx;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-top: 24rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
  border-radius: 16rpx;
  border: 2rpx solid #22c55e20;
}

.feature-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.feature-text {
  font-size: 26rpx;
  color: #16a34a;
  text-align: center;
  font-weight: 500;
}

/* 投资分析样式 */
.analysis-section {
  margin: 0 24rpx 24rpx;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-top: 24rpx;
}

.analysis-item {
  text-align: center;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border: 2rpx solid #e2e8f0;
}

.analysis-label {
  display: block;
  font-size: 26rpx;
  color: #64748b;
  margin-bottom: 8rpx;
}

.analysis-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
}

.analysis-value.highlight {
  color: #dc2626;
  font-size: 36rpx;
}

.analysis-value.low-risk {
  color: #16a34a;
}

/* 投资建议和风险提示 */
.advice-section,
.risk-section {
  margin: 0 24rpx 24rpx;
}

.advice-list,
.risk-list {
  margin-top: 16rpx;
}

.advice-item,
.risk-item {
  padding: 16rpx 0;
  border-bottom: 1rpx solid #e5e7eb;
}

.advice-item:last-child,
.risk-item:last-child {
  border-bottom: none;
}

.advice-text {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.6;
}

.risk-text {
  font-size: 28rpx;
  color: #dc2626;
  line-height: 1.6;
}

/* 无评价状态 */
.no-reviews {
  text-align: center;
  padding: 80rpx 0;
  color: #9ca3af;
  font-size: 28rpx;
}

/* 增强的监测数据样式 */
.monitoring-section {
  margin: 0 24rpx 24rpx;
}

.monitoring-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.update-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.update-time {
  font-size: 24rpx;
  color: #64748b;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 6rpx;
  background: #dc2626;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.live-indicator .live-dot {
  width: 6rpx;
  height: 6rpx;
  background: white;
  border-radius: 50%;
  animation: livePulse 1.5s infinite;
}

.monitoring-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.monitor-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  border: 2rpx solid #e2e8f0;
}

.monitor-icon {
  font-size: 32rpx;
  width: 64rpx;
  height: 64rpx;
  background: #e2e8f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.monitor-item.temperature .monitor-icon {
  background: #fef3c7;
}

.monitor-item.humidity .monitor-icon {
  background: #dbeafe;
}

.monitor-item.light .monitor-icon {
  background: #fef3c7;
}

.monitor-item.ph .monitor-icon {
  background: #f3e8ff;
}

.monitor-item.soil .monitor-icon {
  background: #dcfce7;
}

.monitor-item.weather .monitor-icon {
  background: #e0f2fe;
}

.monitor-content {
  flex: 1;
}

.monitor-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1e293b;
  display: block;
  margin-bottom: 4rpx;
}

.monitor-label {
  font-size: 24rpx;
  color: #64748b;
  display: block;
  margin-bottom: 4rpx;
}

.monitor-status {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.monitor-status.good {
  background: #dcfce7;
  color: #16a34a;
}

.monitor-status.warning {
  background: #fef3c7;
  color: #d97706;
}

.monitor-status.danger {
  background: #fecaca;
  color: #dc2626;
}

/* 趋势图表 */
.trend-section {
  margin: 0 24rpx 24rpx;
}

.trend-chart {
  margin-top: 16rpx;
}

.chart-placeholder {
  height: 200rpx;
  background: #f1f5f9;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #cbd5e1;
}

.chart-text {
  font-size: 28rpx;
  color: #475569;
  margin-bottom: 8rpx;
}

.chart-desc {
  font-size: 24rpx;
  color: #94a3b8;
}

/* 监控操作 */
.monitoring-actions {
  margin: 0 24rpx 24rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-top: 16rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 24rpx 16rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 24rpx;
}

.action-btn.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.action-btn.secondary {
  background: #f1f5f9;
  color: #475569;
  border: 2rpx solid #e2e8f0;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 24rpx;
  font-weight: 500;
}

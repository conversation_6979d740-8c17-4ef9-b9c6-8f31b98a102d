<!-- 茶地详情页 -->
<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 主要内容 -->
<view class="container" wx:if="{{!loading}}">
  <!-- 图片轮播 -->
  <view class="image-section">
    <swiper class="image-swiper" 
            indicator-dots="{{true}}" 
            indicator-color="rgba(255,255,255,0.5)"
            indicator-active-color="#ffffff"
            autoplay="{{false}}"
            circular="{{true}}">
      <swiper-item wx:for="{{teaDetail.images}}" wx:key="index">
        <image src="{{item || '/images/tea-default.jpg'}}" class="detail-image" mode="aspectFill" bindtap="previewImage" data-url="{{item}}" binderror="onImageError"></image>
      </swiper-item>
    </swiper>
    
    <!-- 状态标签 -->
    <view class="status-badge {{teaDetail.status}}">
      <text>{{teaDetail.status_display}}</text>
    </view>
    
    <!-- 返回按钮 -->
    <view class="back-btn" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    
    <!-- 分享按钮 -->
    <view class="share-btn" bindtap="shareDetail">
      <text class="share-icon">⤴</text>
    </view>
  </view>

  <!-- 基本信息 -->
  <view class="info-section">
    <view class="card">
      <view class="tea-header">
        <text class="tea-name">{{teaDetail.name}}</text>
        <view class="tea-rating">
          <text class="rating-score">{{teaDetail.rating}}</text>
          <text class="rating-text">分</text>
        </view>
      </view>
      
      <view class="tea-location">
        <view class="location-info">
          <text class="location-icon">📍</text>
          <view class="location-details">
            <text class="location-text">{{teaDetail.location}}</text>
            <text class="detailed-location" wx:if="{{teaDetail.detailed_location}}">{{teaDetail.detailed_location}}</text>
            <view class="coordinates" wx:if="{{teaDetail.latitude && teaDetail.longitude}}">
              <text class="coord-text">坐标：{{teaDetail.latitude}}, {{teaDetail.longitude}}</text>
            </view>
          </view>
        </view>
        <button class="view-map-btn" bindtap="viewMap" size="mini" type="primary">
          <text class="map-icon">🗺️</text>
          <text>查看地图</text>
        </button>
      </view>

      <!-- 地图预览 -->
      <view class="map-preview" wx:if="{{teaDetail.latitude && teaDetail.longitude}}">
        <view class="map-header">
          <text class="map-title">位置预览</text>
          <text class="map-tip" bindtap="viewMap">点击查看大图</text>
        </view>
        <map
          class="preview-map"
          latitude="{{teaDetail.latitude}}"
          longitude="{{teaDetail.longitude}}"
          scale="15"
          markers="{{mapMarkers}}"
          show-location="{{false}}"
          bindtap="viewMap">
        </map>
      </view>
      
      <text class="tea-description">{{teaDetail.description}}</text>
      
      <!-- 标签 -->
      <view class="tea-tags">
        <text class="tag" wx:for="{{teaDetail.tags}}" wx:key="*this">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 价格和收益 -->
  <view class="price-section">
    <view class="card">
      <view class="price-header">
        <text class="section-title">价格信息</text>
      </view>
      
      <view class="price-grid">
        <view class="price-item">
          <text class="price-label">认购价格</text>
          <text class="price-value">¥{{teaDetail.price}}/亩</text>
        </view>
        <view class="price-item">
          <text class="price-label">预期收益</text>
          <text class="return-value">{{teaDetail.expected_return}}%</text>
        </view>
        <view class="price-item">
          <text class="price-label">可认购面积</text>
          <text class="area-value">{{teaDetail.available_area}}亩</text>
        </view>
        <view class="price-item">
          <text class="price-label">认购期限</text>
          <text class="period-value">{{teaDetail.contract_period}}年</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view class="tab-item {{activeTab === 'detail' ? 'active' : ''}}" bindtap="switchTab" data-tab="detail">
      <text>详细信息</text>
    </view>
    <view class="tab-item {{activeTab === 'monitoring' ? 'active' : ''}}" bindtap="switchTab" data-tab="monitoring">
      <text>实时监测</text>
    </view>
    <view class="tab-item {{activeTab === 'analysis' ? 'active' : ''}}" bindtap="switchTab" data-tab="analysis">
      <text>投资分析</text>
    </view>
    <view class="tab-item {{activeTab === 'reviews' ? 'active' : ''}}" bindtap="switchTab" data-tab="reviews">
      <text>用户评价</text>
    </view>
  </view>

  <!-- 标签页内容 -->
  <view class="tab-content">
    <!-- 详细信息标签页 -->
    <view class="tab-panel" wx:if="{{activeTab === 'detail'}}">
      <!-- 茶园特色功能 -->
      <view class="features-section">
        <view class="card">
          <view class="section-header">
            <text class="section-title">茶园特色</text>
          </view>
          <view class="features-grid">
            <view class="feature-item">
              <view class="feature-icon">🏆</view>
              <text class="feature-text">有机认证茶园</text>
            </view>
            <view class="feature-item">
              <view class="feature-icon">📹</view>
              <text class="feature-text">24小时实时监控</text>
            </view>
            <view class="feature-item">
              <view class="feature-icon">👨‍🌾</view>
              <text class="feature-text">专业茶农管理</text>
            </view>
            <view class="feature-item">
              <view class="feature-icon">🚚</view>
              <text class="feature-text">包邮到家服务</text>
            </view>
            <view class="feature-item">
              <view class="feature-icon">📱</view>
              <text class="feature-text">手机远程查看</text>
            </view>
            <view class="feature-item">
              <view class="feature-icon">✅</view>
              <text class="feature-text">品质保证承诺</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 收益计算器 -->
      <view class="calculator-section">
        <view class="card">
          <view class="calculator-header">
            <text class="section-title">收益计算器</text>
          </view>
          <view class="calculator-content">
            <view class="calculator-input">
              <text class="input-label">认购面积</text>
              <view class="input-wrapper">
                <button class="calc-btn {{calculatorArea <= 1 ? 'disabled' : ''}}"
                        bindtap="decreaseArea"
                        disabled="{{calculatorArea <= 1}}">-</button>
                <input class="area-input"
                       type="number"
                       value="{{calculatorArea}}"
                       bindinput="onAreaInput"
                       placeholder="输入面积" />
                <button class="calc-btn {{calculatorArea >= teaDetail.available_area ? 'disabled' : ''}}"
                        bindtap="increaseArea"
                        disabled="{{calculatorArea >= teaDetail.available_area}}">+</button>
                <text class="unit">亩</text>
              </view>
              <text class="area-tip">可认购面积：1-{{teaDetail.available_area}}亩</text>
            </view>
            <view class="calculator-result">
              <view class="result-item">
                <text class="result-label">投资金额</text>
                <text class="result-value">¥{{calculatedInvestment}}</text>
              </view>
              <view class="result-item">
                <text class="result-label">预期年收益</text>
                <text class="result-value">¥{{calculatedReturn}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 详细信息 -->
      <view class="details-section">
        <view class="card">
          <view class="details-header">
            <text class="section-title">详细信息</text>
          </view>

          <view class="details-list">
            <view class="detail-row">
              <text class="detail-label">茶园类型</text>
              <text class="detail-value">{{teaDetail.category.name || teaDetail.tea_variety || '绿茶'}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">种植年份</text>
              <text class="detail-value">{{teaDetail.planting_date ? teaDetail.planting_date.substring(0,4) : '2020'}}年</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">海拔高度</text>
              <text class="detail-value">{{teaDetail.altitude || 1700}}米</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">土壤类型</text>
              <text class="detail-value">{{teaDetail.soil_type || '红壤'}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">管理方式</text>
              <text class="detail-value">{{teaDetail.management_team || '专业管理'}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">认证情况</text>
              <text class="detail-value">{{teaDetail.organic_certified ? '有机认证' : '常规种植'}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">气候类型</text>
              <text class="detail-value">{{teaDetail.climate || teaDetail.climate_type || '亚热带季风气候'}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">采摘季节</text>
              <text class="detail-value">{{teaDetail.harvest_season || '春季、秋季'}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">水源情况</text>
              <text class="detail-value">{{teaDetail.water_source || '山泉水灌溉'}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">保险保障</text>
              <text class="detail-value">{{teaDetail.insurance_covered ? '已投保' : '未投保'}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 实时监测标签页 -->
    <view class="tab-panel" wx:if="{{activeTab === 'monitoring'}}">
      <!-- 实时环境数据 -->
      <view class="monitoring-section">
        <view class="card">
          <view class="monitoring-header">
            <text class="section-title">实时环境数据</text>
            <view class="update-info">
              <text class="update-time">{{monitoringData.updateTime}}</text>
              <view class="live-indicator">
                <view class="live-dot"></view>
                <text>实时</text>
              </view>
            </view>
          </view>

          <view class="monitoring-grid">
            <view class="monitor-item temperature">
              <view class="monitor-icon">🌡️</view>
              <view class="monitor-content">
                <text class="monitor-value">{{monitoringData.temperature}}°C</text>
                <text class="monitor-label">温度</text>
                <text class="monitor-status {{monitoringData.temperature_status}}">{{monitoringData.temperature_text}}</text>
              </view>
            </view>

            <view class="monitor-item humidity">
              <view class="monitor-icon">💧</view>
              <view class="monitor-content">
                <text class="monitor-value">{{monitoringData.humidity}}%</text>
                <text class="monitor-label">湿度</text>
                <text class="monitor-status {{monitoringData.humidity_status}}">{{monitoringData.humidity_text}}</text>
              </view>
            </view>

            <view class="monitor-item light">
              <view class="monitor-icon">☀️</view>
              <view class="monitor-content">
                <text class="monitor-value">{{monitoringData.light}}</text>
                <text class="monitor-label">光照强度</text>
                <text class="monitor-status {{monitoringData.light_status}}">{{monitoringData.light_text}}</text>
              </view>
            </view>

            <view class="monitor-item ph">
              <view class="monitor-icon">🧪</view>
              <view class="monitor-content">
                <text class="monitor-value">{{monitoringData.ph}}</text>
                <text class="monitor-label">土壤pH</text>
                <text class="monitor-status {{monitoringData.ph_status}}">{{monitoringData.ph_text}}</text>
              </view>
            </view>

            <view class="monitor-item soil">
              <view class="monitor-icon">🌱</view>
              <view class="monitor-content">
                <text class="monitor-value">{{monitoringData.soilMoisture}}%</text>
                <text class="monitor-label">土壤湿度</text>
                <text class="monitor-status {{monitoringData.soil_status}}">{{monitoringData.soil_text}}</text>
              </view>
            </view>

            <view class="monitor-item weather">
              <view class="monitor-icon">🌤️</view>
              <view class="monitor-content">
                <text class="monitor-value">{{monitoringData.weather}}</text>
                <text class="monitor-label">天气状况</text>
                <text class="monitor-status good">{{monitoringData.weather_desc}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 24小时趋势 -->
      <view class="trend-section">
        <view class="card">
          <view class="section-header">
            <text class="section-title">24小时趋势</text>
          </view>

          <view class="trend-chart">
            <view class="chart-placeholder">
              <text class="chart-text">温湿度变化趋势图</text>
              <text class="chart-desc">显示过去24小时的环境数据变化</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 监控操作 -->
      <view class="monitoring-actions">
        <view class="card">
          <view class="actions-grid">
            <button class="action-btn primary" bindtap="viewMonitoring">
              <text class="btn-icon">📹</text>
              <text class="btn-text">实时视频</text>
            </button>
            <button class="action-btn secondary" bindtap="viewHistory">
              <text class="btn-icon">📊</text>
              <text class="btn-text">历史数据</text>
            </button>
            <button class="action-btn secondary" bindtap="setAlert">
              <text class="btn-icon">🔔</text>
              <text class="btn-text">设置提醒</text>
            </button>
            <button class="action-btn secondary" bindtap="downloadReport">
              <text class="btn-icon">📄</text>
              <text class="btn-text">导出报告</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 投资分析标签页 -->
    <view class="tab-panel" wx:if="{{activeTab === 'analysis'}}">
      <!-- 投资收益分析 -->
      <view class="analysis-section">
        <view class="card">
          <view class="section-header">
            <text class="section-title">投资收益分析</text>
          </view>
          <view class="analysis-grid">
            <view class="analysis-item">
              <text class="analysis-label">预期年化收益率</text>
              <text class="analysis-value highlight">12.5%</text>
            </view>
            <view class="analysis-item">
              <text class="analysis-label">预期回本周期</text>
              <text class="analysis-value">3.2年</text>
            </view>
            <view class="analysis-item">
              <text class="analysis-label">3年总收益</text>
              <text class="analysis-value">¥2,180/亩</text>
            </view>
            <view class="analysis-item">
              <text class="analysis-label">风险等级</text>
              <text class="analysis-value low-risk">低风险</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 历史表现 -->
      <view class="history-section">
        <view class="card">
          <view class="history-header">
            <text class="section-title">历史收益</text>
          </view>

          <view class="history-list">
            <view class="history-item" wx:for="{{teaDetail.history_returns}}" wx:key="year">
              <text class="history-year">{{item.year}}年</text>
              <text class="history-return">{{item.return_rate}}%</text>
              <text class="history-amount">¥{{item.amount}}/亩</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 投资建议 -->
      <view class="advice-section">
        <view class="card">
          <view class="section-header">
            <text class="section-title">投资建议</text>
          </view>
          <view class="advice-list">
            <view class="advice-item">
              <text class="advice-text">• 该茶园位于优质产区，土壤和气候条件适宜</text>
            </view>
            <view class="advice-item">
              <text class="advice-text">• 历史收益稳定，风险较低，适合稳健投资</text>
            </view>
            <view class="advice-item">
              <text class="advice-text">• 建议认购面积不超过总投资的30%</text>
            </view>
            <view class="advice-item">
              <text class="advice-text">• 可关注春茶采摘期的收益表现</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 风险提示 -->
      <view class="risk-section">
        <view class="card">
          <view class="section-header">
            <text class="section-title">风险提示</text>
          </view>
          <view class="risk-list">
            <view class="risk-item">
              <text class="risk-text">• 农业投资受天气、自然灾害等因素影响</text>
            </view>
            <view class="risk-item">
              <text class="risk-text">• 茶叶市场价格存在波动风险</text>
            </view>
            <view class="risk-item">
              <text class="risk-text">• 收益预期仅供参考，实际收益可能有差异</text>
            </view>
            <view class="risk-item">
              <text class="risk-text">• 请根据自身风险承受能力谨慎投资</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 用户评价标签页 -->
    <view class="tab-panel" wx:if="{{activeTab === 'reviews'}}">
      <view class="reviews-section">
        <view class="card">
          <view class="reviews-header">
            <text class="section-title">用户评价</text>
            <text class="reviews-count">({{teaDetail.reviews.length || 0}}条)</text>
          </view>

          <view class="reviews-list" wx:if="{{teaDetail.reviews && teaDetail.reviews.length > 0}}">
            <view class="review-item" wx:for="{{teaDetail.reviews}}" wx:key="id">
              <view class="review-header">
                <image src="{{item.user_avatar || '/images/avatar-default.png'}}" class="user-avatar" binderror="onImageError"></image>
                <view class="user-info">
                  <text class="user-name">{{item.user_name}}</text>
                  <text class="review-time">{{item.created_at}}</text>
                </view>
                <view class="review-rating">
                  <text class="rating-stars">{{item.rating_stars}}</text>
                </view>
              </view>
              <text class="review-content">{{item.content}}</text>
            </view>
          </view>

          <view class="no-reviews" wx:else>
            <text>暂无用户评价</text>
          </view>

          <button class="view-all-reviews-btn" bindtap="viewAllReviews" wx:if="{{teaDetail.reviews && teaDetail.reviews.length > 0}}">查看全部评价</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="related-section" wx:if="{{relatedTeas.length > 0}}">
    <view class="card">
      <view class="related-header">
        <text class="section-title">相关推荐</text>
      </view>
      
      <scroll-view class="related-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
        <view class="related-list">
          <view class="related-item" wx:for="{{relatedTeas}}" wx:key="id" bindtap="viewRelated" data-id="{{item.id}}">
            <image src="{{item.main_image || '/images/tea-default.jpg'}}" class="related-image" mode="aspectFill" binderror="onImageError"></image>
            <text class="related-name">{{item.name}}</text>
            <text class="related-price">¥{{item.price}}/亩</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</view>

<!-- 底部操作栏 -->
<view class="bottom-actions">
  <button class="favorite-btn {{teaDetail.is_favorited ? 'favorited' : ''}}" bindtap="toggleFavorite">
    <text class="favorite-icon">{{teaDetail.is_favorited ? '❤️' : '🤍'}}</text>
    <text class="favorite-text">{{teaDetail.is_favorited ? '已收藏' : '收藏'}}</text>
  </button>

  <button class="purchase-btn" bindtap="startPurchase">
    <text class="purchase-icon">🛒</text>
    <text class="purchase-text">认购</text>
  </button>

  <button class="consult-btn" bindtap="startConsult">
    <text class="consult-icon">💬</text>
    <text class="consult-text">咨询</text>
  </button>
</view>

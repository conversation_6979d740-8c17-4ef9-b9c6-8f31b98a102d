// 茶地详情页 - 简化版本
const app = getApp()
const api = require('../../api/index.js')
const { teaFieldAPI, myFieldsAPI } = require('../../utils/apis.js')
const { formatTeaImageUrl } = require('../../utils/util.js')

Page({
  data: {
    teaId: '',
    teaDetail: {},
    relatedTeas: [],
    calculatorArea: 1,
    calculatedInvestment: 0,
    calculatedReturn: 0,
    loading: true,
    activeTab: 'detail',
    mapMarkers: [],
    showCalculator: false,
    showMap: false,
    
    // 环境数据
    environmentData: {
      temperature: '--',
      temp_status: 'warning',
      temp_text: '获取中',
      humidity: '--',
      humidity_status: 'warning',
      humidity_text: '获取中',
      light: '--',
      light_status: 'warning',
      light_text: '获取中',
      ph: 0,
      ph_status: 'warning',
      ph_text: '获取中',
      soilMoisture: 0,
      soil_status: 'warning',
      soil_text: '获取中',
      weather: '--',
      weather_desc: '数据获取中'
    }
  },

  // 页面加载
  onLoad(options) {
    if (options.id && options.id !== 'undefined') {
      this.setData({ teaId: options.id })
      this.loadTeaDetail()
    } else {
      console.error('❌ 茶地ID参数无效:', options.id)
      wx.showToast({
        title: '茶地ID参数错误',
        icon: 'none'
      })
      
      setTimeout(() => {
        wx.navigateBack()
      }, 2000)
    }
    
    // 如果是认购模式，显示认购相关信息
    if (options.mode === 'purchase') {
      this.setData({
        showCalculator: true
      })
    }
  },

  // 加载茶地详情
  async loadTeaDetail() {
    try {
      this.setData({ loading: true })
      
      const res = await teaFieldAPI.getTeaFieldDetail(this.data.teaId)
      
      if (res.code === 200 && res.data) {
        // 处理图片数据
        let images = []
        if (res.data.images && Array.isArray(res.data.images)) {
          images = res.data.images.map(img => {
            if (typeof img === 'string') {
              return formatTeaImageUrl(img)
            } else if (img && img.image_url) {
              return formatTeaImageUrl(img.image_url)
            }
            return '/images/tea-default.jpg'
          })
        }
        
        if (images.length === 0) {
          images = ['/images/tea-default.jpg']
        }

        // 处理茶地数据
        const processedData = {
          ...res.data,
          images: images,
          main_image: images[0] || '/images/tea-default.jpg',
          price: parseFloat(res.data.price) || 0,
          available_area: parseFloat(res.data.available_area) || 0,
          total_area: parseFloat(res.data.total_area) || 0,
          expected_return: parseFloat(res.data.expected_return) || 0,
          latitude: parseFloat(res.data.latitude) || 0,
          longitude: parseFloat(res.data.longitude) || 0,
          tags: res.data.tags || [
            res.data.region?.tea_variety || '绿茶'
          ]
        }

        // 验证数据完整性
        // 数据处理完成

        // 检查认购按钮状态

        this.setData({
          teaDetail: processedData,
          loading: false
        })

        // 设置地图标记
        if (processedData.latitude && processedData.longitude) {
          this.setData({
            mapMarkers: [{
              id: 1,
              latitude: processedData.latitude,
              longitude: processedData.longitude,
              title: processedData.name || '茶地位置',
              iconPath: '/images/map-marker.png',
              width: 30,
              height: 30
            }]
          })
        }

        // 加载相关茶地
        this.loadRelatedTeas()
        
        // 加载环境数据
        this.loadEnvironmentData()
        
      } else {
        throw new Error(res.message || '获取茶地详情失败')
      }
    } catch (error) {
      console.error('❌ 加载茶地详情失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      this.setData({ loading: false })
    }
  },

  // 加载相关茶地
  async loadRelatedTeas() {
    try {
      const res = await teaFieldAPI.getList({
        page: 1,
        page_size: 4,
        exclude_id: this.data.teaId
      })
      
      if (res.code === 200 && res.data && res.data.results) {
        this.setData({
          relatedTeas: res.data.results.slice(0, 4)
        })
      }
    } catch (error) {
      console.error('❌ 加载相关茶地失败:', error)
    }
  },

  // 加载环境数据
  async loadEnvironmentData() {
    try {
      // 这里可以调用环境数据API
      // const res = await api.getEnvironmentData(this.data.teaId)
      
      // 模拟数据
      const mockData = {
        temperature: '22°C',
        temp_status: 'good',
        temp_text: '适宜',
        humidity: '65%',
        humidity_status: 'good',
        humidity_text: '适宜',
        light: '强',
        light_status: 'good',
        light_text: '充足',
        ph: 6.5,
        ph_status: 'good',
        ph_text: '适宜',
        soilMoisture: 45,
        soil_status: 'good',
        soil_text: '适宜',
        weather: '晴',
        weather_desc: '天气晴朗，适合茶叶生长'
      }
      
      this.setData({
        environmentData: mockData
      })
    } catch (error) {
      console.error('❌ 加载环境数据失败:', error)
    }
  },

  // 收益计算器
  onCalculatorAreaChange(e) {
    const area = parseFloat(e.detail.value) || 1
    this.setData({
      calculatorArea: area
    })
    this.calculateInvestment()
  },

  calculateInvestment() {
    const { calculatorArea, teaDetail } = this.data
    
    if (!teaDetail.price) return

    const investment = calculatorArea * teaDetail.price
    const yearlyReturn = investment * (teaDetail.expected_return / 100)

    this.setData({
      calculatedInvestment: investment,
      calculatedReturn: yearlyReturn
    })
  },

  // 立即认购
  onPurchase() {
    const { teaDetail } = this.data
    
    if (!teaDetail.id) {
      wx.showToast({
        title: '茶地信息加载中',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/purchase/purchase?id=${teaDetail.id}`
    })
  },

  // 切换标签页
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })
  },

  // 预览图片
  onImagePreview(e) {
    const current = e.currentTarget.dataset.src
    const urls = this.data.teaDetail.images || []
    
    wx.previewImage({
      current: current,
      urls: urls
    })
  },

  // 查看地图
  onViewMap() {
    const { teaDetail } = this.data
    
    if (!teaDetail.latitude || !teaDetail.longitude) {
      wx.showToast({
        title: '暂无位置信息',
        icon: 'none'
      })
      return
    }

    wx.openLocation({
      latitude: teaDetail.latitude,
      longitude: teaDetail.longitude,
      name: teaDetail.name || '茶地位置',
      address: teaDetail.location || ''
    })
  },

  // 分享
  onShareAppMessage() {
    const { teaDetail } = this.data
    
    return {
      title: `${teaDetail.name || '优质茶地'} - 两山茶管家`,
      path: `/pages/tea-detail/tea-detail?id=${teaDetail.id}`,
      imageUrl: teaDetail.main_image || '/images/tea-default.jpg'
    }
  }
})

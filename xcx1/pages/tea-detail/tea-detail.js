// 茶地详情页 - 简化版本
const app = getApp()
const api = require('../../api/index.js')
const { teaFieldAPI, myFieldsAPI } = require('../../utils/apis.js')
const { formatTeaImageUrl } = require('../../utils/util.js')

Page({
  data: {
    teaId: '',
    teaDetail: {},
    relatedTeas: [],
    calculatorArea: 1,
    calculatedInvestment: 0,
    calculatedReturn: 0,
    loading: true,
    activeTab: 'detail',
    mapMarkers: [],
    showCalculator: false,
    showMap: false,
    
    // 环境数据
    environmentData: {
      temperature: '--',
      temp_status: 'warning',
      temp_text: '获取中',
      humidity: '--',
      humidity_status: 'warning',
      humidity_text: '获取中',
      light: '--',
      light_status: 'warning',
      light_text: '获取中',
      ph: 0,
      ph_status: 'warning',
      ph_text: '获取中',
      soilMoisture: 0,
      soil_status: 'warning',
      soil_text: '获取中',
      weather: '--',
      weather_desc: '数据获取中'
    }
  },

  // 页面加载
  onLoad(options) {
    console.log('🔍 茶地详情页面参数:', options)

    // 检查是否传递了订单数据
    if (options.orderData) {
      try {
        const orderData = JSON.parse(decodeURIComponent(options.orderData))
        console.log('📦 接收到订单数据:', orderData)
        this.loadFromOrderData(orderData)
        return
      } catch (error) {
        console.error('❌ 解析订单数据失败:', error)
      }
    }

    // 传统的茶地ID方式
    if (options.id && options.id !== 'undefined') {
      this.setData({ teaId: options.id })
      this.loadTeaDetail()
    } else {
      console.error('❌ 茶地ID参数无效:', options.id)
      wx.showToast({
        title: '茶地ID参数错误',
        icon: 'none'
      })

      setTimeout(() => {
        wx.navigateBack()
      }, 2000)
    }

    // 如果是认购模式，显示认购相关信息
    if (options.mode === 'purchase') {
      this.setData({
        showCalculator: true
      })
    }
  },

  // 从订单数据加载茶地信息
  loadFromOrderData(orderData) {
    console.log('📦 从订单数据加载茶地信息...')

    try {
      // 将订单数据转换为茶地详情格式
      const teaDetail = {
        id: orderData.id,
        name: orderData.name || '我的茶地',
        location: orderData.location || '优质产区',
        main_image: orderData.main_image || '/images/tea-default.jpg',
        images: [orderData.main_image || '/images/tea-default.jpg'],

        // 基本信息
        area: orderData.area || 0,
        price: orderData.price || 0,
        total_amount: orderData.total_amount || 0,
        tea_variety: orderData.tea_variety || '优质茶叶',
        expected_return: orderData.expected_return || 10,

        // 订单相关信息
        order_id: orderData.order_id,
        status: orderData.status,
        status_display: orderData.status_display,
        purchase_date: orderData.purchase_date,
        purchase_type: orderData.purchase_type,

        // 收益信息
        earnings: orderData.earnings || 0,
        monthly_earnings: orderData.monthly_earnings || 0,
        growth_stage: orderData.growth_stage || '生长期',

        // 环境数据
        temperature: orderData.temperature || 22,
        humidity: orderData.humidity || 65,
        soil_moisture: orderData.soil_moisture || 50,

        // 联系信息
        contact_name: orderData.contact_name,
        contact_phone: orderData.contact_phone
      }

      this.setData({
        teaDetail: teaDetail,
        loading: false,
        isFromOrder: true // 标记这是从订单数据加载的
      })

      // 加载环境数据
      this.loadEnvironmentData()

      console.log('✅ 订单数据加载完成')

    } catch (error) {
      console.error('❌ 处理订单数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 加载茶地详情
  async loadTeaDetail() {
    try {
      this.setData({ loading: true })

      console.log('🔍 尝试加载茶地详情，ID:', this.data.teaId)
      console.log('🔍 API调用地址:', `https://teabuy.yizhangkj.com/api/v1/tea-fields/${this.data.teaId}/`)

      const res = await teaFieldAPI.getTeaFieldDetail(this.data.teaId)

      console.log('🔍 API响应:', res)
      
      if (res.code === 200 && res.data) {
        // 处理图片数据
        let images = []
        if (res.data.images && Array.isArray(res.data.images)) {
          images = res.data.images.map(img => {
            if (typeof img === 'string') {
              return formatTeaImageUrl(img)
            } else if (img && img.image_url) {
              return formatTeaImageUrl(img.image_url)
            }
            return '/images/tea-default.jpg'
          })
        }
        
        if (images.length === 0) {
          images = ['/images/tea-default.jpg']
        }

        // 处理茶地数据
        const processedData = {
          ...res.data,
          images: images,
          main_image: images[0] || '/images/tea-default.jpg',
          price: parseFloat(res.data.price) || 0,
          available_area: parseFloat(res.data.available_area) || 0,
          total_area: parseFloat(res.data.total_area) || 0,
          expected_return: parseFloat(res.data.expected_return) || 0,
          latitude: parseFloat(res.data.latitude) || 0,
          longitude: parseFloat(res.data.longitude) || 0,
          tags: res.data.tags || [
            res.data.region?.tea_variety || '绿茶'
          ]
        }

        // 验证数据完整性
        // 数据处理完成

        // 检查认购按钮状态

        this.setData({
          teaDetail: processedData,
          loading: false
        })

        // 设置地图标记
        if (processedData.latitude && processedData.longitude) {
          this.setData({
            mapMarkers: [{
              id: 1,
              latitude: processedData.latitude,
              longitude: processedData.longitude,
              title: processedData.name || '茶地位置',
              iconPath: '/images/map-marker.png',
              width: 30,
              height: 30
            }]
          })
        }

        // 加载相关茶地
        this.loadRelatedTeas()
        
        // 加载环境数据
        this.loadEnvironmentData()
        
      } else {
        throw new Error(res.message || '获取茶地详情失败')
      }
    } catch (error) {
      console.error('❌ 加载茶地详情失败:', error)

      // 如果是404错误或其他错误，尝试获取可用的茶地列表
      console.log('🔄 茶地加载失败，尝试获取可用茶地列表...')
      await this.loadAvailableTeaFields()
    }
  },

  // 加载可用茶地列表（当指定ID不存在时的备用方案）
  async loadAvailableTeaFields() {
    try {
      console.log('🔍 获取可用茶地列表...')
      console.log('🔍 API调用地址:', 'https://teabuy.yizhangkj.com/api/v1/tea-fields/')

      const res = await teaFieldAPI.getTeaFields({
        page: 1,
        page_size: 10
      })

      console.log('🔍 茶地列表API响应:', res)

      if (res.code === 200 && res.data && res.data.results && res.data.results.length > 0) {
        const availableFields = res.data.results
        console.log('✅ 找到可用茶地:', availableFields.map(f => f.id))

        // 显示选择对话框
        const fieldNames = availableFields.map(f => f.name || `茶地${f.id}`)

        wx.showActionSheet({
          itemList: fieldNames,
          success: (result) => {
            const selectedField = availableFields[result.tapIndex]
            console.log('👆 用户选择了茶地:', selectedField.id)

            // 更新当前茶地ID并重新加载
            this.setData({ teaId: selectedField.id.toString() })
            this.loadTeaDetail()
          },
          fail: () => {
            // 用户取消选择，返回上一页
            wx.showToast({
              title: '茶地不存在',
              icon: 'none'
            })
            setTimeout(() => {
              wx.navigateBack()
            }, 1500)
          }
        })
      } else {
        // 如果没有可用茶地，创建一个模拟的茶地数据
        console.log('⚠️ 后端没有茶地数据，使用模拟数据')
        this.loadMockTeaData()
      }
    } catch (error) {
      console.error('❌ 获取可用茶地失败:', error)
      console.log('⚠️ API调用失败，使用模拟数据')
      this.loadMockTeaData()
    }
  },

  // 加载模拟茶地数据（当后端没有数据时的备用方案）
  loadMockTeaData() {
    console.log('📦 加载模拟茶地数据...')

    const mockTeaDetail = {
      id: this.data.teaId || 'mock-001',
      name: '优质茶园示例',
      location: '浙江省杭州市西湖区',
      main_image: '/images/tea-default.jpg',
      images: ['/images/tea-default.jpg'],

      // 基本信息
      area: 2.5,
      price: 8800,
      total_amount: 22000,
      tea_variety: '西湖龙井',
      expected_return: 12,

      // 详细描述
      description: '这是一个优质的茶园示例，位于风景秀丽的西湖区。茶园采用有机种植方式，确保茶叶品质优良。',

      // 环境信息
      latitude: 30.2741,
      longitude: 120.1551,
      altitude: 200,
      soil_type: '红壤',
      climate: '亚热带季风气候',

      // 种植信息
      planting_date: '2023-03-15',
      harvest_season: '春季、秋季',
      processing_method: '传统手工制作',

      // 投资信息
      min_investment: 8800,
      investment_period: '3年',
      expected_annual_return: 12,

      // 状态信息
      status: 'available',
      growth_stage: '成熟期',
      health_status: '良好',

      // 标签
      tags: ['有机认证', '传统工艺', '优质产区']
    }

    this.setData({
      teaDetail: mockTeaDetail,
      loading: false,
      isMockData: true
    })

    // 设置地图标记
    this.setData({
      mapMarkers: [{
        id: 1,
        latitude: mockTeaDetail.latitude,
        longitude: mockTeaDetail.longitude,
        title: mockTeaDetail.name,
        iconPath: '/images/map-marker.png',
        width: 30,
        height: 30
      }]
    })

    // 加载环境数据
    this.loadEnvironmentData()

    wx.showToast({
      title: '使用示例数据',
      icon: 'none',
      duration: 2000
    })

    console.log('✅ 模拟数据加载完成')
  },

  // 加载相关茶地
  async loadRelatedTeas() {
    try {
      const res = await teaFieldAPI.getTeaFields({
        page: 1,
        page_size: 4,
        exclude_id: this.data.teaId
      })
      
      if (res.code === 200 && res.data && res.data.results) {
        this.setData({
          relatedTeas: res.data.results.slice(0, 4)
        })
      }
    } catch (error) {
      console.error('❌ 加载相关茶地失败:', error)
    }
  },

  // 加载环境数据
  async loadEnvironmentData() {
    try {
      // 这里可以调用环境数据API
      // const res = await api.getEnvironmentData(this.data.teaId)
      
      // 模拟数据
      const mockData = {
        temperature: '22°C',
        temp_status: 'good',
        temp_text: '适宜',
        humidity: '65%',
        humidity_status: 'good',
        humidity_text: '适宜',
        light: '强',
        light_status: 'good',
        light_text: '充足',
        ph: 6.5,
        ph_status: 'good',
        ph_text: '适宜',
        soilMoisture: 45,
        soil_status: 'good',
        soil_text: '适宜',
        weather: '晴',
        weather_desc: '天气晴朗，适合茶叶生长'
      }
      
      this.setData({
        environmentData: mockData
      })
    } catch (error) {
      console.error('❌ 加载环境数据失败:', error)
    }
  },

  // 收益计算器
  onCalculatorAreaChange(e) {
    const area = parseFloat(e.detail.value) || 1
    this.setData({
      calculatorArea: area
    })
    this.calculateInvestment()
  },

  calculateInvestment() {
    const { calculatorArea, teaDetail } = this.data
    
    if (!teaDetail.price) return

    const investment = calculatorArea * teaDetail.price
    const yearlyReturn = investment * (teaDetail.expected_return / 100)

    this.setData({
      calculatedInvestment: investment,
      calculatedReturn: yearlyReturn
    })
  },

  // 立即认购
  onPurchase() {
    const { teaDetail } = this.data
    
    if (!teaDetail.id) {
      wx.showToast({
        title: '茶地信息加载中',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/purchase/purchase?id=${teaDetail.id}`
    })
  },

  // 切换标签页
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })
  },

  // 预览图片
  onImagePreview(e) {
    const current = e.currentTarget.dataset.src
    const urls = this.data.teaDetail.images || []
    
    wx.previewImage({
      current: current,
      urls: urls
    })
  },

  // 查看地图
  onViewMap() {
    const { teaDetail } = this.data
    
    if (!teaDetail.latitude || !teaDetail.longitude) {
      wx.showToast({
        title: '暂无位置信息',
        icon: 'none'
      })
      return
    }

    wx.openLocation({
      latitude: teaDetail.latitude,
      longitude: teaDetail.longitude,
      name: teaDetail.name || '茶地位置',
      address: teaDetail.location || ''
    })
  },

  // 分享
  onShareAppMessage() {
    const { teaDetail } = this.data
    
    return {
      title: `${teaDetail.name || '优质茶地'} - 两山茶管家`,
      path: `/pages/tea-detail/tea-detail?id=${teaDetail.id}`,
      imageUrl: teaDetail.main_image || '/images/tea-default.jpg'
    }
  }
})

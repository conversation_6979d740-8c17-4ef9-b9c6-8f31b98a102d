// 茶地详情页
const app = getApp()
const api = require('../../api/index.js')
const { teaFieldAPI, myFieldsAPI } = require('../../utils/apis.js')
const { formatTeaImageUrl } = require('../../utils/util.js')

Page({
  data: {
    teaId: '',
    teaDetail: {},
    relatedTeas: [],
    calculatorArea: 1,
    calculatedInvestment: 0,
    calculatedReturn: 0,
    loading: true,
    activeTab: 'detail', // 当前激活的标签页

    // 地图标记
    mapMarkers: [],

    // 实时监测数据
    monitoringData: {
      updateTime: '--',
      temperature: 0,
      temperature_status: 'warning',
      temperature_text: '获取中',
      humidity: 0,
      humidity_status: 'warning',
      humidity_text: '获取中',
      light: '--',
      light_status: 'warning',
      light_text: '获取中',
      ph: 0,
      ph_status: 'warning',
      ph_text: '获取中',
      soilMoisture: 0,
      soil_status: 'warning',
      soil_text: '获取中',
      weather: '--',
      weather_desc: '数据获取中'
    }}),

  // 页面加载
  onLoad(options) {
    // Debug log removed
    if (options.id && options.id !== 'undefined') {
      this.setData({ teaId: options.id })
      this.loadTeaDetail()
    } else {

      setTimeout(() => {
        wx.navigateBack()
      }, 2000)
    }
    
    // 如果是认购模式，显示认购相关信息
    if (options.action === 'purchase') {
      wx.setNavigationBarTitle({
        title: '茶地认购'
    }}),

  // 页面显示
  onShow() {
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadTeaDetail()

    // 延迟停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 分享
  onShareAppMessage() {
    return {
      title: `${this.data.teaDetail.name} - 两山·茶管家`,
      path: `/pages/tea-detail/tea-detail?id=${this.data.teaId}`,
      imageUrl: this.data.teaDetail.main_image
    }
  },

  // 加载茶地详情
  loadTeaDetail() {
    if (!this.data.teaId) return
    this.setData({ loading: true })

    // 调用公开的茶地详情API
    api.teaFieldApi.getDetail(this.data.teaId).then(res => {
      if (res.code === 200) {
        // 处理图片数据
        let images = []
        if (res.data.images && Array.isArray(res.data.images) && res.data.images.length > 0) {
          // 如果有图片数组，处理每个图片
          images = res.data.images.map(img => {
            let imageUrl = ''
            if (typeof img === 'string') {
              imageUrl = img
            } else if (img && img.image_url) {
              // 优先使用序列化器返回的完整URL
              imageUrl = img.image_url
            } else if (img && img.image) {
              // 处理Django ImageField返回的相对路径
              imageUrl = img.image.startsWith('http') ?
                img.image :
                `https://teabuy.yizhangkj.com${img.image}`
            } else if (img && img.url) {
              imageUrl = img.url.startsWith('http') ?
                img.url :
                `https://teabuy.yizhangkj.com${img.url}`
            }

            return imageUrl
          }).filter(url => url && url.trim() !== '') // 过滤掉空值
        }

        // 如果没有图片，尝试使用封面图片
        if (images.length === 0 && res.data.cover_image) {
          let imageUrl = ''
          if (res.data.cover_image.image_url) {
            imageUrl = res.data.cover_image.image_url
          } else if (res.data.cover_image.image) {
            imageUrl = res.data.cover_image.image.startsWith('http') ?
              res.data.cover_image.image :
              `https://teabuy.yizhangkj.com${res.data.cover_image.image}`
          }
          if (imageUrl) {
            images = [imageUrl]
          }
        }

        // 如果仍然没有图片，使用默认图片
        if (images.length === 0) {
          images = ['/images/tea-default.jpg']
        }

        // 处理经纬度数据和字段映射
        const processedData = {
          ...res.data,
          images: images,
          is_favorited: res.data.is_favorited || false,
          // 确保经纬度是数字类型
          latitude: res.data.latitude ? parseFloat(res.data.latitude) : null,
          longitude: res.data.longitude ? parseFloat(res.data.longitude) : null,

          // 关键字段映射：从TeaFieldPlot模型映射到前端期望的字段
          name: res.data.plot_name || res.data.name || '未知茶地',
          location: res.data.region?.garden?.location || res.data.region?.name || res.data.location || '未知位置',
          description: this.generateDetailedDescription(res.data),
          tea_variety: res.data.region?.tea_variety || res.data.tea_variety || res.data.category?.name || '绿茶',

          // 其他字段映射和默认值处理
          planting_date: res.data.planting_date || '2020-03-15',
          altitude: res.data.altitude || 1700,
          soil_type: res.data.soil_type || '红壤',
          climate: res.data.climate || res.data.climate_type || '亚热带季风气候',
          harvest_season: res.data.harvest_season || '春季、秋季',
          water_source: res.data.water_source || '山泉水灌溉',
          management_team: res.data.management_team || '专业管理',
          organic_certified: res.data.organic_certified !== undefined ? res.data.organic_certified : true,
          insurance_covered: res.data.insurance_covered !== undefined ? res.data.insurance_covered : true,

          // 确保status字段存在
          status: res.data.status || 'available',
          // 确保面积字段存在
          available_area: res.data.available_area || res.data.area || 10,
          contract_period: res.data.contract_period || 5,

          // 添加区域和茶园信息
          region_name: res.data.region?.name || '未知区域',
          garden_name: res.data.region?.garden?.garden_name || '未知茶园',
          full_code: res.data.full_code || '未知编码',

          // 添加前端模板需要的其他字段
          rating: res.data.rating || 4.8,
          expected_return: res.data.expected_return || '8-12',
          status_display: this.getStatusDisplay(res.data.status || 'available'),
          detailed_location: res.data.region?.garden?.location || res.data.detailed_location || '',

          // 添加标签
          tags: res.data.tags || [
            res.data.region?.tea_variety || '绿茶'
          ]
        }

        // 验证数据完整性
        this.validateTeaDetailData(processedData)

        // 检查认购按钮状态

        // 强制更新页面数据以确保按钮显示
        this.setData({
          teaDetail: processedData
        // 初始化计算器
        this.calculateReturn()
        
        // 加载相关推荐
        this.loadRelatedTeas()

        // 加载监测数据
        this.loadMonitoringData()
      } else {
        this.setDefaultTeaDetail()
      }
    }).catch(error => {
      this.setData({ loading: false })
  },

  // 设置默认茶地详情数据
  
  // 验证茶地ID是否有效（移除错误的映射逻辑）
  validateTeaFieldId(teaFieldId) {
    const originalId = parseInt(teaFieldId)

    // 直接返回原始ID，不进行映射
    // 如果茶地不存在，后续的API调用会返回错误，由错误处理逻辑处理
    return originalId
  },

setDefaultTeaDetail() {
    const defaultData = {
        id: this.validateTeaFieldId(this.data.teaId) || 1,
        name: '西湖龙井茶园',
        description: '位于杭州西湖风景区核心地带的优质龙井茶园，拥有得天独厚的自然环境和悠久的种植历史。茶园采用传统工艺与现代科技相结合的管理方式，确保茶叶品质上乘。',
        category: { name: '绿茶' },
        tea_variety: '龙井茶',
        price: 12000,
        area: 2.5,
        location: '浙江省杭州市西湖区',
        detailed_location: '西湖龙井茶园核心产区梅家坞村',
        latitude: 30.2741,
        longitude: 120.1551,
        altitude: 1700,
        min_investment: 5000,
        max_investment: 50000,
        expected_return: 15.5,
        harvest_season: '春季、秋季',
        planting_date: '2020-03-15',
        maturity_date: '2025-03-15',
        soil_type: '红壤',
        climate: '亚热带季风气候',
        climate_type: '亚热带季风气候',
        water_source: '山泉水灌溉',
        organic_certified: true,
        insurance_covered: true,
        management_team: '专业茶园管理团队',
        view_count: 1250,
        purchase_count: 85,
        rating: 4.8,
        availability_rate: 75,
        images: ['/images/tea-default.jpg'],
        is_favorited: false,
        status: 'available'
      }

    this.setData({
      teaDetail: defaultData,
      mapMarkers: this.createMapMarkers(defaultData)
    // 显示提示信息
    wx.showToast({
      title: '显示示例数据',
      icon: 'none'}),

  // 加载相关推荐
  loadRelatedTeas() {
    api.teaFieldApi.getList({
      page_size: 6,
      exclude: this.data.teaId,
      region: this.data.teaDetail.region})).then(res => {
      if (res.code === 200 && res.data) {
        let results = []
        if (res.data.results && Array.isArray(res.data.results)) {
          results = res.data.results
        } else if (Array.isArray(res.data)) {
          results = res.data
        }

        this.setData({ relatedTeas: results })
      }
    }).catch(error => {}),

  // 加载监测数据
  loadMonitoringData() {
    if (!this.data.teaId) return
    // 暂时使用默认监测数据，因为监测API可能需要认证
    this.setDefaultMonitoringData()

    /*
    // 如果监测API是公开的，可以启用以下代码
    api.monitoringApi.getRealtime(this.data.teaId).then(res => {
      if (res.code === 200 && res.data) {
        const data = res.data
        this.setData({
          monitoringData: {
            updateTime: new Date().toLocaleString(),
            temperature: data.temperature || 0,
            temperature_status: this.getStatusByValue(data.temperature, 15, 30),
            temperature_text: this.getStatusText(data.temperature, 15, 30, '偏低', '适宜', '偏高'),
            humidity: data.humidity || 0,
            humidity_status: this.getStatusByValue(data.humidity, 40, 80),
            humidity_text: this.getStatusText(data.humidity, 40, 80, '偏低', '良好', '偏高'),
            light: data.light_intensity ? `${data.light_intensity} lux` : '无数据',
            light_status: this.getStatusByValue(data.light_intensity, 10000, 40000),
            light_text: this.getStatusText(data.light_intensity, 10000, 40000, '不足', '充足', '过强'),
            ph: data.ph_value || 0,
            ph_status: this.getStatusByValue(data.ph_value, 6.0, 7.5),
            ph_text: this.getStatusText(data.ph_value, 6.0, 7.5, '偏酸', '适中', '偏碱'),
            soilMoisture: data.soil_moisture || 0,
            soil_status: this.getStatusByValue(data.soil_moisture, 30, 70),
            soil_text: this.getStatusText(data.soil_moisture, 30, 70, '干燥', '湿润', '过湿'),
            weather: data.weather || '无数据',
            weather_desc: data.weather_desc || '数据获取中'
          }
      } else {
        // 使用默认监控数据
        this.setDefaultMonitoringData()
      }
    }).catch(error => {
    this.calculateReturn()
  },

  // 增加面积
  increaseArea() {
    const newArea = this.data.calculatorArea + 1
    if (newArea <= this.data.teaDetail.available_area) {
      this.setData({ calculatorArea: newArea })
      this.calculateReturn()
    } else {
      wx.showToast({
        title: '超过可认购面积',
        icon: 'none'
    }}),

  // 减少面积
  decreaseArea() {
    const newArea = this.data.calculatorArea - 1
    if (newArea >= 1) {
      this.setData({ calculatorArea: newArea })
      this.calculateReturn()
    }
  },

  // 预览图片
  previewImage(e) {
    const { url } = e.currentTarget.dataset
    wx.previewImage({
      current: url,
      urls: this.data.teaDetail.images}),

  // 返回
  goBack() {
    wx.navigateBack()
  },

  // 分享详情
  shareDetail() {
    wx.showShareMenu({
      withShareTicket: true}),

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    // 如果切换到监测标签页，刷新监测数据
    if (tab === 'monitoring') {
      this.refreshMonitoringData()
    }
  },

  // 刷新监测数据
  refreshMonitoringData() {
    // 重新加载监测数据
    this.loadMonitoringData()
  },

  // 查看历史数据
  viewHistory() {
    wx.navigateTo({
      url: `/pages/monitoring-history/monitoring-history?teaId=${this.data.teaId}`}),

  // 设置提醒
  setAlert() {
    wx.showModal({
      title: '设置监控提醒',
      content: '是否开启环境异常提醒？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '提醒设置成功',
            icon: 'success'
        }
      }}),

  // 导出报告
  downloadReport() {
    wx.showLoading({
      title: '生成报告中...'
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '报告已生成',
        icon: 'success'}), 2000)
  },

  // 查看地图
  viewMap() {
    const { latitude, longitude, name, location } = this.data.teaDetail
    // Debug log removed

    // 首先检查位置权限
    this.checkLocationPermission(() => {
      this.openLocationMap(latitude, longitude, name, location)
  },

  // 检查位置权限
  checkLocationPermission(callback) {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation'] !== false) {
          // 权限已授权或未询问过
          callback && callback()
        } else {
          // 权限被拒绝，引导用户开启
          wx.showModal({
            title: '位置权限',
            content: '需要获取您的位置权限来显示地图，请在设置中开启位置权限',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting()
              }
            }
        }
      },
      fail: () => {
        // 获取设置失败，直接尝试打开地图
        callback && callback()
      }
  },

  // 打开位置地图
  openLocationMap(latitude, longitude, name, location) {
    // 检查经纬度数据
    if (!latitude || !longitude) {
      } else {
        // 添加收藏
        teaFieldAPI.favoriteTeaField(teaId).then(() => {
          wx.showToast({
            title: '收藏成功',
            icon: 'success',
            duration: 1500})).catch(error => {
      }
      */
}) catch (error) {
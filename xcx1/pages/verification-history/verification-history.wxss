/* pages/verification-history/verification-history.wxss */

.container {
  min-height: 100vh;
  background: #F5F5F5;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border-bottom: 1rpx solid #E0E0E0;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  height: 88rpx;
}

.navbar-left {
  width: 80rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  color: #333;
  padding: 8rpx;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212121;
}

.navbar-right {
  width: 80rpx;
}

/* 筛选条件 */
.filter-section {
  margin-top: calc(88rpx + env(safe-area-inset-top) + 20rpx);
  background: white;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  font-size: 26rpx;
  color: #666;
  width: 80rpx;
  flex-shrink: 0;
}

.filter-options {
  display: flex;
  gap: 16rpx;
  flex: 1;
}

.filter-option {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #F5F5F5;
  border: 1rpx solid transparent;
}

.filter-option.active {
  background: #E8F5E8;
  border-color: #4CAF50;
}

.filter-option text {
  font-size: 24rpx;
  color: #666;
}

.filter-option.active text {
  color: #4CAF50;
  font-weight: 500;
}

/* 滚动容器 */
.scroll-container {
  height: calc(100vh - 88rpx - env(safe-area-inset-top) - 120rpx);
  padding-bottom: 40rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E0E0E0;
  border-top: 4rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
}

/* 认证记录列表 */
.record-list {
  padding: 24rpx 32rpx;
}

.record-item {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.record-type {
  display: flex;
  align-items: center;
}

.type-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.type-text {
  font-size: 28rpx;
  color: #212121;
  font-weight: 500;
}

.record-status text {
  font-size: 24rpx;
  font-weight: 500;
}

.record-content {
  margin-bottom: 16rpx;
}

.content-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.record-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-time {
  font-size: 22rpx;
  color: #999;
}

.record-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
}

.detail-btn {
  background: #F0F0F0;
  color: #666;
}

.resubmit-btn {
  background: #E8F5E8;
  color: #4CAF50;
}

.reject-reason {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #FFF5F5;
  border-radius: 8rpx;
  border-left: 4rpx solid #F44336;
}

.reason-label {
  font-size: 22rpx;
  color: #F44336;
  font-weight: 500;
}

.reason-text {
  font-size: 22rpx;
  color: #666;
  margin-left: 8rpx;
}

/* 加载更多 */
.load-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 32rpx;
}

.load-more-text {
  font-size: 24rpx;
  color: #999;
}

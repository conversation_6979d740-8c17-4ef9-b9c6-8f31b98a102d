<!--pages/verification-history/verification-history.wxml-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="navbar-title">认证记录</view>
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 筛选条件 -->
  <view class="filter-section">
    <view class="filter-row">
      <text class="filter-label">类型：</text>
      <view class="filter-options">
        <view class="filter-option {{filterType === 'all' ? 'active' : ''}}" bindtap="onFilterTypeChange" data-type="all">
          <text>全部</text>
        </view>
        <view class="filter-option {{filterType === 'personal' ? 'active' : ''}}" bindtap="onFilterTypeChange" data-type="personal">
          <text>个人</text>
        </view>
        <view class="filter-option {{filterType === 'enterprise' ? 'active' : ''}}" bindtap="onFilterTypeChange" data-type="enterprise">
          <text>企业</text>
        </view>
        <view class="filter-option {{filterType === 'bankcard' ? 'active' : ''}}" bindtap="onFilterTypeChange" data-type="bankcard">
          <text>银行卡</text>
        </view>
      </view>
    </view>
    
    <view class="filter-row">
      <text class="filter-label">状态：</text>
      <view class="filter-options">
        <view class="filter-option {{filterStatus === 'all' ? 'active' : ''}}" bindtap="onFilterStatusChange" data-status="all">
          <text>全部</text>
        </view>
        <view class="filter-option {{filterStatus === 'pending' ? 'active' : ''}}" bindtap="onFilterStatusChange" data-status="pending">
          <text>待审核</text>
        </view>
        <view class="filter-option {{filterStatus === 'approved' ? 'active' : ''}}" bindtap="onFilterStatusChange" data-status="approved">
          <text>已通过</text>
        </view>
        <view class="filter-option {{filterStatus === 'rejected' ? 'active' : ''}}" bindtap="onFilterStatusChange" data-status="rejected">
          <text>已拒绝</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading && verificationList.length === 0}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view wx:elif="{{!loading && verificationList.length === 0}}" class="empty-container">
    <view class="empty-icon">📋</view>
    <text class="empty-title">暂无认证记录</text>
    <text class="empty-desc">您还没有提交过任何认证申请</text>
  </view>

  <!-- 认证记录列表 -->
  <scroll-view wx:else scroll-y class="scroll-container" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" bindrefresherrefresh="onPullDownRefresh" bindscrolltolower="onReachBottom">
    
    <view class="record-list">
      <view wx:for="{{verificationList}}" wx:key="id" class="record-item">
        <view class="record-header">
          <view class="record-type">
            <text class="type-icon">
              {{item.type === 'personal' ? '👤' : item.type === 'enterprise' ? '🏢' : '💳'}}
            </text>
            <text class="type-text">{{getTypeText(item.type)}}</text>
          </view>
          <view class="record-status" style="color: {{getStatusColor(item.status)}}">
            <text>{{getStatusText(item.status)}}</text>
          </view>
        </view>
        
        <view class="record-content">
          <view wx:if="{{item.type === 'personal'}}" class="content-info">
            <text class="info-item">姓名：{{item.real_name || '未填写'}}</text>
            <text class="info-item">身份证：{{item.id_card || '未填写'}}</text>
          </view>
          
          <view wx:elif="{{item.type === 'enterprise'}}" class="content-info">
            <text class="info-item">企业：{{item.company_name || '未填写'}}</text>
            <text class="info-item">信用代码：{{item.credit_code || '未填写'}}</text>
          </view>
          
          <view wx:elif="{{item.type === 'bankcard'}}" class="content-info">
            <text class="info-item">银行：{{item.bank_name || '未填写'}}</text>
            <text class="info-item">持卡人：{{item.card_holder_name || '未填写'}}</text>
          </view>
        </view>
        
        <view class="record-footer">
          <text class="record-time">{{formatTime(item.created_at)}}</text>
          <view class="record-actions">
            <view class="action-btn detail-btn" bindtap="viewDetail" data-item="{{item}}">
              <text>详情</text>
            </view>
            <view wx:if="{{item.status === 'rejected'}}" class="action-btn resubmit-btn" bindtap="resubmit" data-item="{{item}}">
              <text>重新提交</text>
            </view>
          </view>
        </view>
        
        <view wx:if="{{item.reject_reason}}" class="reject-reason">
          <text class="reason-label">拒绝原因：</text>
          <text class="reason-text">{{item.reject_reason}}</text>
        </view>
      </view>
    </view>

    <!-- 加载更多提示 -->
    <view wx:if="{{loading && verificationList.length > 0}}" class="load-more">
      <view class="loading-spinner small"></view>
      <text class="load-more-text">加载中...</text>
    </view>
    
    <view wx:elif="{{!hasMore && verificationList.length > 0}}" class="load-more">
      <text class="load-more-text">没有更多数据了</text>
    </view>

  </scroll-view>
</view>

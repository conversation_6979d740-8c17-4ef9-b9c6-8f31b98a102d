// pages/verification-history/verification-history.js
const api = require('../../api/index.js')

Page({
  data: {
    // 认证记录列表
    verificationList: [],
    
    // 筛选条件
    filterType: 'all', // all, personal, enterprise, bankcard
    filterStatus: 'all', // all, pending, approved, rejected
    
    // 加载状态
    loading: true,
    refreshing: false,
    
    // 分页
    page: 1,
    hasMore: true
  },

  onLoad(options) {
    this.loadVerificationHistory()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData()
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  // 刷新数据
  refreshData() {
    this.setData({
      page: 1,
      hasMore: true,
      refreshing: true
    })
    this.loadVerificationHistory()
  },

  // 加载更多
  loadMore() {
    this.setData({
      page: this.data.page + 1
    })
    this.loadVerificationHistory(false)
  },

  // 加载认证历史
  async loadVerificationHistory(showLoading = true) {
    try {
      if (showLoading) {
        this.setData({ loading: true })
      }
      
      // 构建查询参数
      const params = {
        page: this.data.page,
        page_size: 10
      }
      
      if (this.data.filterType !== 'all') {
        params.type = this.data.filterType
      }
      
      if (this.data.filterStatus !== 'all') {
        params.status = this.data.filterStatus
      }
      
      // 调用API获取认证历史
      const res = await api.authApi.getHistory(params)
      
      if (res.code === 200 && res.data) {
        const newList = res.data.results || []
        
        let verificationList = []
        if (this.data.page === 1) {
          verificationList = newList
        } else {
          verificationList = [...this.data.verificationList, ...newList]
        }
        
        this.setData({
          verificationList: verificationList,
          hasMore: newList.length >= 10,
          loading: false,
          refreshing: false
        })
      } else {
        throw new Error('获取认证历史失败')
      }
    } catch (error) {
      console.error('加载认证历史失败:', error)
      this.setData({
        loading: false,
        refreshing: false
      })
      
      if (this.data.page === 1) {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  // 筛选类型改变
  onFilterTypeChange(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      filterType: type,
      page: 1,
      hasMore: true
    })
    this.loadVerificationHistory()
  },

  // 筛选状态改变
  onFilterStatusChange(e) {
    const status = e.currentTarget.dataset.status
    this.setData({
      filterStatus: status,
      page: 1,
      hasMore: true
    })
    this.loadVerificationHistory()
  },

  // 查看认证详情
  viewDetail(e) {
    const item = e.currentTarget.dataset.item
    
    let title = ''
    let content = ''
    
    switch (item.type) {
      case 'personal':
        title = '个人认证详情'
        content = `姓名：${item.real_name || '未填写'}\n`
        content += `身份证：${item.id_card || '未填写'}\n`
        content += `手机号：${item.phone || '未填写'}\n`
        break
      case 'enterprise':
        title = '企业认证详情'
        content = `企业名称：${item.company_name || '未填写'}\n`
        content += `信用代码：${item.credit_code || '未填写'}\n`
        content += `法人代表：${item.legal_person || '未填写'}\n`
        content += `联系人：${item.contact_name || '未填写'}\n`
        content += `联系电话：${item.contact_phone || '未填写'}\n`
        break
      case 'bankcard':
        title = '银行卡认证详情'
        content = `银行名称：${item.bank_name || '未填写'}\n`
        content += `持卡人：${item.card_holder_name || '未填写'}\n`
        content += `卡号：${item.card_number ? item.card_number.replace(/(\d{4})\d{8}(\d{4})/, '$1****$2') : '未填写'}\n`
        content += `手机号：${item.phone || '未填写'}\n`
        break
    }
    
    content += `\n提交时间：${this.formatTime(item.created_at)}`
    
    if (item.review_time) {
      content += `\n审核时间：${this.formatTime(item.review_time)}`
    }
    
    if (item.reject_reason) {
      content += `\n拒绝原因：${item.reject_reason}`
    }
    
    wx.showModal({
      title: title,
      content: content,
      showCancel: false,
      confirmText: '确定'
    })
  },

  // 重新提交认证
  resubmit(e) {
    const item = e.currentTarget.dataset.item
    
    let url = ''
    switch (item.type) {
      case 'personal':
        url = '/pages/real-name-auth/real-name-auth'
        break
      case 'enterprise':
        url = '/pages/enterprise-auth/enterprise-auth'
        break
      case 'bankcard':
        url = '/pages/bank-card-auth/bank-card-auth'
        break
    }
    
    if (url) {
      wx.navigateTo({
        url: url
      })
    }
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return '未知时间'
    const date = new Date(timeStr)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  },

  // 获取认证类型文本
  getTypeText(type) {
    const typeMap = {
      'personal': '个人认证',
      'enterprise': '企业认证',
      'bankcard': '银行卡认证'
    }
    return typeMap[type] || '未知类型'
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待审核',
      'approved': '已通过',
      'rejected': '已拒绝'
    }
    return statusMap[status] || '未知状态'
  },

  // 获取状态颜色
  getStatusColor(status) {
    const colorMap = {
      'pending': '#FF9800',
      'approved': '#4CAF50',
      'rejected': '#F44336'
    }
    return colorMap[status] || '#999'
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  }
})

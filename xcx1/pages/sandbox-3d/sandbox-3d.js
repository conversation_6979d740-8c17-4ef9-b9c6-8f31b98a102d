// 3D沙盘展示页面
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    // 3D场景配置
    sceneConfig: {
      backgroundColor: '#87CEEB',
      cameraPosition: { x: 0, y: 50, z: 100 },
      lightIntensity: 1.0,
      showGrid: true,
      showLabels: true
    },
    
    // 茶地数据
    teaFieldsData: [],
    selectedField: null,
    
    // 控制面板
    showControls: false,
    viewMode: 'overview', // overview, detail, monitoring
    
    // 实时数据
    realtimeData: {
      temperature: 0,
      humidity: 0,
      soilMoisture: 0,
      lastUpdate: ''
    },
    
    // 统计信息
    statistics: {
      totalFields: 0,
      availableFields: 0,
      purchasedFields: 0,
      totalRevenue: 0
    },
    
    // 加载状态
    loading: true,
    error: null
  },

  // 页面加载
  onLoad(options) {
    this.initScene()
    this.loadTeaFieldsData()
    this.loadStatistics()
    this.startRealtimeUpdate()
  },

  // 页面显示
  onShow() {
    this.refreshData()
  },

  // 页面隐藏
  onHide() {
    this.stopRealtimeUpdate()
  },

  // 初始化3D场景
  initScene() {
    try {
      // 这里可以集成Three.js或其他3D库
      // 由于小程序限制，使用Canvas 2D模拟3D效果
      this.initCanvas()
    } catch (error) {
      console.error('3D场景初始化失败:', error)
      this.setData({
        error: '3D场景初始化失败',
        loading: false
      })
    }
  },

  // 初始化Canvas
  initCanvas() {
    const query = wx.createSelectorQuery()
    query.select('#sandbox-canvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res[0]) {
          const canvas = res[0].node
          const ctx = canvas.getContext('2d')
          
          // 设置canvas尺寸
          const dpr = wx.getSystemInfoSync().pixelRatio
          canvas.width = res[0].width * dpr
          canvas.height = res[0].height * dpr
          ctx.scale(dpr, dpr)
          
          this.canvas = canvas
          this.ctx = ctx
          this.drawScene()
        }
      })
  },

  // 绘制场景
  drawScene() {
    if (!this.ctx) return
    
    const { ctx } = this
    const { sceneConfig, teaFieldsData } = this.data
    
    // 清空画布
    ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
    
    // 绘制背景
    ctx.fillStyle = sceneConfig.backgroundColor
    ctx.fillRect(0, 0, this.canvas.width, this.canvas.height)
    
    // 绘制网格
    if (sceneConfig.showGrid) {
      this.drawGrid()
    }
    
    // 绘制茶地
    this.drawTeaFields()
    
    // 绘制标签
    if (sceneConfig.showLabels) {
      this.drawLabels()
    }
  },

  // 绘制网格
  drawGrid() {
    const { ctx } = this
    const gridSize = 20
    const canvasWidth = this.canvas.width
    const canvasHeight = this.canvas.height
    
    ctx.strokeStyle = '#ddd'
    ctx.lineWidth = 1
    
    // 绘制垂直线
    for (let x = 0; x <= canvasWidth; x += gridSize) {
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, canvasHeight)
      ctx.stroke()
    }
    
    // 绘制水平线
    for (let y = 0; y <= canvasHeight; y += gridSize) {
      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(canvasWidth, y)
      ctx.stroke()
    }
  },

  // 绘制茶地
  drawTeaFields() {
    const { ctx } = this
    const { teaFieldsData } = this.data
    
    teaFieldsData.forEach((field, index) => {
      const x = 50 + (index % 5) * 60
      const y = 50 + Math.floor(index / 5) * 60
      const width = 50
      const height = 50
      
      // 根据状态设置颜色
      let color = '#4CAF50' // 可认购
      if (field.status === 'purchased') {
        color = '#2196F3' // 已认购
      } else if (field.status === 'reserved') {
        color = '#FF9800' // 已预订
      } else if (field.status === 'maintenance') {
        color = '#9E9E9E' // 维护中
      }
      
      // 绘制茶地方块
      ctx.fillStyle = color
      ctx.fillRect(x, y, width, height)
      
      // 绘制边框
      ctx.strokeStyle = '#333'
      ctx.lineWidth = 2
      ctx.strokeRect(x, y, width, height)
      
      // 存储位置信息用于点击检测
      field._renderInfo = { x, y, width, height }
    })
  },

  // 绘制标签
  drawLabels() {
    const { ctx } = this
    const { teaFieldsData } = this.data
    
    ctx.fillStyle = '#333'
    ctx.font = '12px Arial'
    ctx.textAlign = 'center'
    
    teaFieldsData.forEach((field) => {
      if (field._renderInfo) {
        const { x, y, width, height } = field._renderInfo
        const centerX = x + width / 2
        const centerY = y + height / 2
        
        ctx.fillText(field.code || field.name, centerX, centerY + 4)
      }
    })
  },

  // 加载茶地数据
  async loadTeaFieldsData() {
    try {
      const response = await api.sandboxApi.getTeaData()
      if (response.code === 200) {
        this.setData({
          teaFieldsData: response.data.fields || [],
          loading: false
        })
        this.drawScene()
      } else {
        throw new Error(response.message || '数据加载失败')
      }
    } catch (error) {
      console.error('加载茶地数据失败:', error)
      this.setData({
        error: '数据加载失败',
        loading: false
      })
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    }
  },

  // 加载统计数据
  async loadStatistics() {
    try {
      const response = await api.sandboxApi.getStatistics()
      if (response.code === 200) {
        this.setData({
          statistics: response.data
        })
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  },

  // 开始实时数据更新
  startRealtimeUpdate() {
    this.realtimeTimer = setInterval(() => {
      this.loadRealtimeData()
    }, 30000) // 30秒更新一次
    
    // 立即加载一次
    this.loadRealtimeData()
  },

  // 停止实时数据更新
  stopRealtimeUpdate() {
    if (this.realtimeTimer) {
      clearInterval(this.realtimeTimer)
      this.realtimeTimer = null
    }
  },

  // 加载实时数据
  async loadRealtimeData() {
    try {
      const response = await api.sandboxApi.getRealtimeData()
      if (response.code === 200) {
        this.setData({
          realtimeData: {
            ...response.data,
            lastUpdate: new Date().toLocaleTimeString()
          }
        })
      }
    } catch (error) {
      console.error('加载实时数据失败:', error)
    }
  },

  // 刷新数据
  refreshData() {
    this.setData({ loading: true })
    Promise.all([
      this.loadTeaFieldsData(),
      this.loadStatistics(),
      this.loadRealtimeData()
    ]).finally(() => {
      this.setData({ loading: false })
    })
  },

  // Canvas点击事件
  onCanvasClick(e) {
    const { x, y } = e.detail
    const { teaFieldsData } = this.data
    
    // 检测点击的茶地
    for (let field of teaFieldsData) {
      if (field._renderInfo) {
        const { x: fx, y: fy, width, height } = field._renderInfo
        if (x >= fx && x <= fx + width && y >= fy && y <= fy + height) {
          this.selectField(field)
          break
        }
      }
    }
  },

  // 选择茶地
  selectField(field) {
    this.setData({
      selectedField: field,
      showControls: true
    })
    
    // 重新绘制场景，高亮选中的茶地
    this.drawScene()
    this.highlightSelectedField()
  },

  // 高亮选中的茶地
  highlightSelectedField() {
    const { selectedField } = this.data
    if (!selectedField || !selectedField._renderInfo) return
    
    const { ctx } = this
    const { x, y, width, height } = selectedField._renderInfo
    
    // 绘制高亮边框
    ctx.strokeStyle = '#FF5722'
    ctx.lineWidth = 4
    ctx.strokeRect(x - 2, y - 2, width + 4, height + 4)
  },

  // 切换视图模式
  switchViewMode(e) {
    const { mode } = e.currentTarget.dataset
    this.setData({ viewMode: mode })
    this.drawScene()
  },

  // 切换控制面板
  toggleControls() {
    this.setData({
      showControls: !this.data.showControls
    })
  },

  // 重置视图
  resetView() {
    this.setData({
      selectedField: null,
      viewMode: 'overview',
      sceneConfig: {
        ...this.data.sceneConfig,
        cameraPosition: { x: 0, y: 50, z: 100 }
      }
    })
    this.drawScene()
  },

  // 清除选择
  clearSelection() {
    this.setData({
      selectedField: null,
      showControls: false
    })
    this.drawScene()
  },

  // 网格切换
  onGridToggle(e) {
    this.setData({
      'sceneConfig.showGrid': e.detail.value
    })
    this.drawScene()
  },

  // 标签切换
  onLabelsToggle(e) {
    this.setData({
      'sceneConfig.showLabels': e.detail.value
    })
    this.drawScene()
  },

  // 相机高度调整
  onCameraHeightChange(e) {
    this.setData({
      'sceneConfig.cameraPosition.y': e.detail.value
    })
    this.drawScene()
  },

  // 光照强度调整
  onLightIntensityChange(e) {
    this.setData({
      'sceneConfig.lightIntensity': e.detail.value
    })
    this.drawScene()
  },

  // 跳转到认购页面
  goToPurchase() {
    const { selectedField } = this.data
    if (selectedField) {
      wx.navigateTo({
        url: `/pages/purchase/purchase?fieldId=${selectedField.id}`
      })
    }
  },

  // 查看茶地详情
  viewFieldDetail() {
    const { selectedField } = this.data
    if (selectedField) {
      wx.navigateTo({
        url: `/pages/tea-detail/tea-detail?id=${selectedField.id}`
      })
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '茶园3D沙盘 - 沉浸式茶地认购体验',
      path: '/pages/sandbox-3d/sandbox-3d',
      imageUrl: '/images/sandbox-share.jpg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '茶园3D沙盘 - 沉浸式茶地认购体验',
      imageUrl: '/images/sandbox-share.jpg'
    }
  },

  // 页面卸载
  onUnload() {
    this.stopRealtimeUpdate()
  }
})

// 监控页面
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    // 自定义底部导航
    customTabBarConfig: null,
    currentTabIndex: 2,
    // 实时数据
    realtimeData: {
      temperature: 0,
      humidity: 0,
      light: 0,
      ph: 0,
      soil_moisture: 0,
      wind_speed: 0
    },
    
    // 设备列表
    devices: [],
    
    // 告警信息
    alerts: [],
    
    // 摄像头相关
    cameras: [],
    activeCamera: 'cam1',
    currentCamera: {},

    // 视频控制
    isPlaying: false,
    audioEnabled: true,

    // 图表相关
    timeRange: '24h',
    chartType: 'temperature',
    chartData: [],
    chartLoading: false,
    currentChartValue: 0,
    averageChartValue: 0,
    
    // 环境建议
    suggestions: [],
    
    // 历史摘要
    historySummary: {},
    
    // 页面状态
    lastUpdateTime: '',
    refreshing: false,
    autoRefresh: true,
    refreshTimer: null,

    // 当前时间
    currentTime: ''
  },

  // 页面加载
  onLoad(options) {
    // 获取茶地ID，如果没有则使用默认值
    this.setData({
      fieldId: options.fieldId || '1' // 默认使用第一个茶地
    })

    // 初始化当前摄像头
    this.initCurrentCamera()

    this.initPage()
    this.updateCurrentTime()
    // 每秒更新时间
    this.timeTimer = setInterval(() => {
      this.updateCurrentTime()
    }, 1000)
  
    // 初始化自定义底部导航
    this.initCustomTabBar()
  },

  // 页面显示
  onShow() {
    this.startAutoRefresh()
  },

  // 页面隐藏
  onHide() {
    this.stopAutoRefresh()
  },

  // 页面卸载
  onUnload() {
    this.stopAutoRefresh()
    if (this.timeTimer) {
      clearInterval(this.timeTimer)
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 初始化页面
  async initPage() {
    try {
      await this.loadAllData()
      this.initChart()
    } catch (error) {
      console.error('初始化监控页面失败:', error)
    }
  },

  // 加载所有数据
  async loadAllData() {
    try {
      const fieldId = this.data.fieldId
      const [realtimeRes, devicesRes, alertsRes, camerasRes] = await Promise.all([
        api.monitoringApi.getRealtime(fieldId).catch(() => ({ data: {
    // 自定义底部导航
    customTabBarConfig: null,
    currentTabIndex: 2,} })),
        api.monitoringApi.getDevices(fieldId).catch(() => ({ data: [] })),
        api.monitoringApi.getAlerts(fieldId).catch(() => ({ data: [] })),
        api.monitoringApi.getCameras(fieldId).catch(() => ({ data: [] }))
      ])

      // 更新实时数据
      if (realtimeRes.data && Object.keys(realtimeRes.data).length > 0) {
        this.setData({
          realtimeData: {
            ...realtimeRes.data,
            temperature_status: this.getTemperatureStatus(realtimeRes.data.temperature),
            temperature_text: this.getTemperatureText(realtimeRes.data.temperature),
            humidity_status: this.getHumidityStatus(realtimeRes.data.humidity),
            humidity_text: this.getHumidityText(realtimeRes.data.humidity),
            light_status: this.getLightStatus(realtimeRes.data.light),
            light_text: this.getLightText(realtimeRes.data.light),
            ph_status: this.getPhStatus(realtimeRes.data.ph),
            ph_text: this.getPhText(realtimeRes.data.ph),
            moisture_status: this.getMoistureStatus(realtimeRes.data.soil_moisture),
            moisture_text: this.getMoistureText(realtimeRes.data.soil_moisture),
            wind_status: this.getWindStatus(realtimeRes.data.wind_speed),
            wind_text: this.getWindText(realtimeRes.data.wind_speed)
          },
          lastUpdateTime: this.formatTime(new Date())
        })
      } else {
        // 设置空数据而不是模拟数据
        this.setData({
          realtimeData: {
            temperature: 0,
            humidity: 0,
            light: 0,
            ph: 0,
            soil_moisture: 0,
            wind_speed: 0,
            temperature_status: 'unknown',
            temperature_text: '无数据',
            humidity_status: 'unknown',
            humidity_text: '无数据',
            light_status: 'unknown',
            light_text: '无数据',
            ph_status: 'unknown',
            ph_text: '无数据',
            moisture_status: 'unknown',
            moisture_text: '无数据',
            wind_status: 'unknown',
            wind_text: '无数据'
          },
          lastUpdateTime: this.formatTime(new Date())
        })
      }

      // 更新设备列表
      if (devicesRes.data) {
        // 处理不同的数据格式
        let devicesData = []
        if (Array.isArray(devicesRes.data)) {
          devicesData = devicesRes.data
        } else if (devicesRes.data.devices && Array.isArray(devicesRes.data.devices)) {
          devicesData = devicesRes.data.devices
        } else if (devicesRes.data.list && Array.isArray(devicesRes.data.list)) {
          devicesData = devicesRes.data.list
        }

        this.setData({
          devices: devicesData.map(device => ({
            ...device,
            signal_text: this.getSignalText(device.signal)
          }))
        })
      }

      // 更新告警信息
      if (alertsRes.data) {
        // 处理不同的数据格式
        let alertsData = []
        if (Array.isArray(alertsRes.data)) {
          alertsData = alertsRes.data
        } else if (alertsRes.data.alerts && Array.isArray(alertsRes.data.alerts)) {
          alertsData = alertsRes.data.alerts
        } else if (alertsRes.data.list && Array.isArray(alertsRes.data.list)) {
          alertsData = alertsRes.data.list
        }

        this.setData({
          alerts: alertsData.slice(0, 5) // 只显示前5条
        })
      }

      // 更新摄像头数据
      if (camerasRes.data && camerasRes.data.length > 0) {
        this.setData({
          cameras: camerasRes.data
        })
        // 初始化当前摄像头
        this.initCurrentCamera()
      } else {
        // 设置空摄像头数据而不是硬编码
        this.setData({
          cameras: []
        })
        // 设置默认的当前摄像头状态
        this.setData({
          currentCamera: {
            id: 'none',
            name: '暂无摄像头',
            icon: '📹',
            status: 'offline',
            description: '暂无可用摄像头'
          }
        })
      }

      // 加载图表数据
      this.loadChartData()
      
      // 生成环境建议
      this.generateSuggestions()
      
      // 加载历史摘要
      this.loadHistorySummary()

    } catch (error) {
      console.error('❌ 加载监控数据失败:', error)

      // 显示错误提示
      wx.showToast({
        title: '监控数据加载失败',
        icon: 'none',
        duration: 2000
      })

      // 设置空数据
      this.setData({
        realtimeData: {
          temperature: 0,
          humidity: 0,
          light: 0,
          ph: 0,
          soil_moisture: 0,
          wind_speed: 0
        },
        devices: [],
        alerts: [],
        cameras: []
      })
    }
  },

  // 刷新数据
  async refreshData() {
    this.setData({ refreshing: true })
    
    try {
      await this.loadAllData()
    } catch (error) {
      wx.showToast({
        title: '刷新失败',
        icon: 'none'
      })
    } finally {
      this.setData({ refreshing: false })
    }
  },

  // 开始自动刷新
  startAutoRefresh() {
    if (this.data.autoRefresh && !this.data.refreshTimer) {
      const timer = setInterval(() => {
        this.refreshData()
      }, 30000) // 30秒刷新一次
      
      this.setData({ refreshTimer: timer })
    }
  },

  // 停止自动刷新
  stopAutoRefresh() {
    if (this.data.refreshTimer) {
      clearInterval(this.data.refreshTimer)
      this.setData({ refreshTimer: null })
    }
  },

  // 切换时间范围
  changeTimeRange(e) {
    const { range } = e.currentTarget.dataset
    this.setData({ timeRange: range })
    this.loadChartData()
  },

  // 切换图表类型
  changeChartType(e) {
    const { type } = e.currentTarget.dataset
    this.setData({ chartType: type })
    this.loadChartData()
  },

  // 加载图表数据
  async loadChartData() {
    this.setData({ chartLoading: true })

    try {
      const fieldId = this.data.fieldId
      const params = {
        type: this.data.chartType,
        range: this.data.timeRange
      }

      const res = await api.monitoringApi.getHistory(fieldId, params)

      if (res.code === 200 && res.data) {
        this.setData({
          chartData: res.data.data || [],
          currentChartValue: res.data.current || 0,
          averageChartValue: res.data.average || 0
        })

        this.drawChart()
      } else {
        this.setData({
          chartData: [],
          currentChartValue: 0,
          averageChartValue: 0
        })
      }
    } catch (error) {
      console.error('❌ 图表数据加载失败:', error)
      this.setData({
        chartData: [],
        currentChartValue: 0,
        averageChartValue: 0
      })
    } finally {
      this.setData({ chartLoading: false })
    }
  },

  // 初始化图表
  initChart() {
    // 这里可以初始化图表库，如 ECharts 或自定义绘制
  },

  // 绘制图表
  drawChart() {
    // 这里实现图表绘制逻辑
    // 简单的 Canvas 绘制示例
    const ctx = wx.createCanvasContext('dataChart', this)
    const { chartData } = this.data
    
    if (chartData.length === 0) return
    
    // 清空画布
    ctx.clearRect(0, 0, 300, 200)
    
    // 绘制坐标轴
    ctx.setStrokeStyle('#E0E0E0')
    ctx.setLineWidth(1)
    
    // Y轴
    ctx.beginPath()
    ctx.moveTo(40, 20)
    ctx.lineTo(40, 180)
    ctx.stroke()
    
    // X轴
    ctx.beginPath()
    ctx.moveTo(40, 180)
    ctx.lineTo(280, 180)
    ctx.stroke()
    
    // 绘制数据线
    if (chartData.length > 1) {
      ctx.setStrokeStyle('#2E7D32')
      ctx.setLineWidth(2)
      ctx.beginPath()
      
      const maxValue = Math.max(...chartData.map(item => item.value))
      const minValue = Math.min(...chartData.map(item => item.value))
      const range = maxValue - minValue || 1
      
      chartData.forEach((item, index) => {
        const x = 40 + (index / (chartData.length - 1)) * 240
        const y = 180 - ((item.value - minValue) / range) * 160
        
        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      
      ctx.stroke()
    }
    
    ctx.draw()
  },

  // 图表触摸事件
  chartTouchStart(e) {
  },

  chartTouchMove(e) {
  },

  // 生成环境建议
  generateSuggestions() {
    const suggestions = []
    const { realtimeData } = this.data
    
    // 温度建议
    if (realtimeData.temperature < 15) {
      suggestions.push({
        id: 1,
        type: 'warning',
        icon: '🌡️',
        title: '温度偏低',
        description: '当前温度较低，建议采取保温措施'
      })
    } else if (realtimeData.temperature > 35) {
      suggestions.push({
        id: 2,
        type: 'warning',
        icon: '🌡️',
        title: '温度偏高',
        description: '当前温度较高，建议增加遮阴或通风'
      })
    }
    
    // 湿度建议
    if (realtimeData.humidity < 40) {
      suggestions.push({
        id: 3,
        type: 'info',
        icon: '💧',
        title: '湿度偏低',
        description: '建议增加灌溉或喷雾保湿'
      })
    } else if (realtimeData.humidity > 80) {
      suggestions.push({
        id: 4,
        type: 'warning',
        icon: '💧',
        title: '湿度偏高',
        description: '湿度过高可能导致病害，建议通风'
      })
    }
    
    this.setData({ suggestions })
  },

  // 加载历史摘要
  async loadHistorySummary() {
    try {
      const fieldId = this.data.fieldId
      const res = await api.monitoringApi.getHistory(fieldId, { type: 'summary' })

      if (res.code === 200 && res.data) {
        this.setData({
          historySummary: {
            max_temperature: res.data.max_temperature || 0,
            min_temperature: res.data.min_temperature || 0,
            avg_humidity: res.data.avg_humidity || 0,
            light_hours: res.data.light_hours || 0
          }
        })
      } else {
        this.setData({
          historySummary: {
            max_temperature: 0,
            min_temperature: 0,
            avg_humidity: 0,
            light_hours: 0
          }
        })
      }
    } catch (error) {
      console.error('❌ 加载历史摘要失败:', error)
      this.setData({
        historySummary: {
          max_temperature: 0,
          min_temperature: 0,
          avg_humidity: 0,
          light_hours: 0
        }
      })
    }
  },

  // 获取状态文本和样式
  getTemperatureStatus(temp) {
    if (temp < 15) return 'low'
    if (temp > 35) return 'high'
    return 'normal'
  },

  getHumidityStatus(humidity) {
    if (humidity < 40) return 'low'
    if (humidity > 80) return 'high'
    return 'normal'
  },

  getLightStatus(light) {
    if (light < 10000) return 'low'
    if (light > 50000) return 'high'
    return 'normal'
  },

  getPhStatus(ph) {
    if (ph < 6.0) return 'low'
    if (ph > 7.5) return 'high'
    return 'normal'
  },

  getMoistureStatus(moisture) {
    if (moisture < 30) return 'low'
    if (moisture > 70) return 'high'
    return 'normal'
  },

  getWindStatus(wind) {
    if (wind > 10) return 'high'
    return 'normal'
  },

  getSignalText(signal) {
    if (signal >= 90) return '信号优秀'
    if (signal >= 70) return '信号良好'
    if (signal >= 50) return '信号一般'
    return '信号较差'
  },

  // 获取状态文本
  getTemperatureText(temp) {
    if (temp < 15) return '偏低'
    if (temp > 35) return '偏高'
    return '适宜'
  },

  getHumidityText(humidity) {
    if (humidity < 40) return '偏低'
    if (humidity > 80) return '偏高'
    return '适宜'
  },

  getLightText(light) {
    if (light < 500) return '偏低'
    if (light > 1200) return '偏高'
    return '适宜'
  },

  getPhText(ph) {
    if (ph < 6.0) return '偏酸'
    if (ph > 7.5) return '偏碱'
    return '适宜'
  },

  getMoistureText(moisture) {
    if (moisture < 30) return '偏低'
    if (moisture > 70) return '偏高'
    return '适宜'
  },

  getWindText(wind) {
    if (wind > 10) return '偏强'
    return '适宜'
  },

  // 格式化时间
  formatTime(date) {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    
    return `${year}-${month}-${day} ${hour}:${minute}`
  },

  // 查看设备详情
  viewDeviceDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.showToast({
      title: '设备详情开发中',
      icon: 'none'
    })
  },

  // 处理告警
  handleAlert(e) {
    const { id } = e.currentTarget.dataset
    wx.showModal({
      title: '处理告警',
      content: '确认要标记此告警为已处理吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里调用处理告警的API
          wx.showToast({
            title: '处理成功',
            icon: 'success'
          })
          
          // 从列表中移除
          const alerts = this.data.alerts.filter(alert => alert.id !== id)
          this.setData({ alerts })
        }
      }
    })
  },

  // 查看所有告警
  viewAllAlerts() {
    wx.showToast({
      title: '告警列表开发中',
      icon: 'none'
    })
  },

  // 查看历史详情
  viewHistoryDetail() {
    wx.showToast({
      title: '历史详情开发中',
      icon: 'none'
    })
  },

  // 更新当前时间
  updateCurrentTime() {
    const now = new Date()
    const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
    this.setData({
      currentTime: timeStr
    })
  },

  // 初始化当前摄像头
  initCurrentCamera() {
    if (this.data.cameras && this.data.cameras.length > 0) {
      const currentCamera = this.data.cameras.find(cam => cam.id === this.data.activeCamera)
      this.setData({
        currentCamera: currentCamera || this.data.cameras[0]
      })
    } else {
      // 如果没有摄像头数据，设置空状态
      this.setData({
        currentCamera: {
          id: 'none',
          name: '暂无摄像头',
          icon: '📹',
          status: 'offline',
          description: '暂无可用摄像头'
        }
      })
    }
  },

  // 切换摄像头
  switchCamera(e) {
    const { id } = e.currentTarget.dataset
    const camera = this.data.cameras.find(cam => cam.id === id)

    if (!camera) return

    if (camera.status === 'offline') {
      wx.showToast({
        title: '摄像头离线',
        icon: 'none'
      })
      return
    }

    this.setData({
      activeCamera: id,
      currentCamera: camera,
      isPlaying: false // 切换摄像头时停止播放
    })

    wx.showToast({
      title: `已切换到${camera.name}`,
      icon: 'success'
    })
  },

  // 播放/暂停视频
  playVideo() {
    const { isPlaying } = this.data
    this.setData({
      isPlaying: !isPlaying
    })

    wx.showToast({
      title: isPlaying ? '视频已暂停' : '视频播放中',
      icon: 'success'
    })
  },

  // 全屏切换
  toggleFullscreen() {
    wx.showToast({
      title: '全屏功能开发中',
      icon: 'none'
    })
  },

  // 截图
  takeScreenshot() {
    wx.showToast({
      title: '截图已保存',
      icon: 'success'
    })
  },

  // 音频切换
  toggleAudio() {
    const { audioEnabled } = this.data
    this.setData({
      audioEnabled: !audioEnabled
    })

    wx.showToast({
      title: audioEnabled ? '音频已关闭' : '音频已开启',
      icon: 'success'
    })
  },

  // ==================== 自定义底部导航相关方法 ====================

  // 初始化自定义底部导航
  async initCustomTabBar() {
    try {
      const app = getApp()
      let config = app.globalData.customTabBarConfig
      if (!config) {
        await app.reloadCustomTabBar()
        config = app.globalData.customTabBarConfig
      }
      this.setData({ customTabBarConfig: config })
      this.updateCustomTabBarIndex()
    } catch (error) {
      console.error('❌ 实时监控初始化自定义底部导航失败:', error)
    }
  },

  // 更新自定义导航选中状态
  updateCustomTabBarIndex() {
    const app = getApp()
    const currentIndex = app.getCurrentTabIndex()
    this.setData({ currentTabIndex: currentIndex })
    const customTabBar = this.selectComponent('#custom-tabbar')
    if (customTabBar) {
      customTabBar.setData({ current: currentIndex })
    }
  },

  // 自定义导航切换事件
  onCustomTabChange(e) {
    const { index, item } = e.detail
    const app = getApp()
    app.navigateToPage(item.pagePath)
  }
})
// 邀请好友页面
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    inviteCode: '',
    inviteUrl: '',
    inviteStats: {
      total_invites: 0,
      successful_invites: 0,
      total_rewards: 0,
      pending_rewards: 0
    },
    inviteList: [],
    showQrCode: false
  },

  // 页面加载
  onLoad(options) {
    this.loadInviteData()
  },

  // 页面显示
  onShow() {
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadInviteData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '两山·茶管家 - 专业的茶园认购投资平台',
      path: `/pages/index/index?invite_code=${this.data.inviteCode}`,
      imageUrl: '/images/share-invite.jpg'
    }
  },

  // 加载邀请数据
  async loadInviteData() {
    try {
      // 调用邀请数据API
      const res = await api.inviteApi.getInviteData()

      if (res.code === 200) {
        this.setData({
          inviteCode: res.data.invite_code,
          inviteUrl: res.data.invite_url,
          inviteStats: res.data.stats,
          inviteList: res.data.invite_list || []
        })
      } else {
        // 使用默认数据
        this.setData({
          inviteCode: 'TC' + Date.now().toString().slice(-6),
          inviteUrl: 'https://teabuy.yizhangkj.com/invite/default',
          inviteStats: {
            total_invites: 0,
            successful_invites: 0,
            total_rewards: 0,
            pending_rewards: 0
          },
          inviteList: []
        })
      }
    } catch (error) {
      console.error('加载邀请数据失败:', error)
    }
  },

  // 复制邀请码
  copyInviteCode() {
    wx.setClipboardData({
      data: this.data.inviteCode,
      success: () => {
        wx.showToast({
          title: '邀请码已复制',
          icon: 'success'
        })
      }
    })
  },

  // 复制邀请链接
  copyInviteUrl() {
    wx.setClipboardData({
      data: this.data.inviteUrl,
      success: () => {
        wx.showToast({
          title: '邀请链接已复制',
          icon: 'success'
        })
      }
    })
  },

  // 显示二维码
  showQrCode() {
    this.setData({ showQrCode: true })
    this.generateQrCode()
  },

  // 隐藏二维码
  hideQrCode() {
    this.setData({ showQrCode: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击二维码内容时关闭弹窗
  },

  // 生成二维码
  generateQrCode() {
    const ctx = wx.createCanvasContext('inviteQrCode', this)
    
    // 绘制白色背景
    ctx.setFillStyle('#FFFFFF')
    ctx.fillRect(0, 0, 200, 200)
    
    // 绘制黑色边框
    ctx.setStrokeStyle('#000000')
    ctx.setLineWidth(2)
    ctx.strokeRect(10, 10, 180, 180)
    
    // 绘制一些示例方块
    ctx.setFillStyle('#000000')
    for (let i = 0; i < 10; i++) {
      for (let j = 0; j < 10; j++) {
        if (Math.random() > 0.5) {
          ctx.fillRect(20 + i * 16, 20 + j * 16, 14, 14)
        }
      }
    }
    
    ctx.draw()
  },

  // 保存二维码
  saveQrCode() {
    wx.canvasToTempFilePath({
      canvasId: 'inviteQrCode',
      success: (res) => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            wx.showToast({
              title: '保存成功',
              icon: 'success'
            })
          },
          fail: () => {
            wx.showToast({
              title: '保存失败',
              icon: 'none'
            })
          }
        })
      }
    }, this)
  },

  // 查看邀请详情
  viewInviteDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.showToast({
      title: '邀请详情功能开发中',
      icon: 'none'
    })
  },

  // 查看奖励规则
  viewRewardRules() {
    wx.showModal({
      title: '奖励规则',
      content: '邀请好友注册可获得300元奖励，好友首次认购可额外获得500元奖励。',
      showCancel: false
    })
  }
})

// 自定义底部导航演示页面
Page({
  data: {
    // 自定义导航配置
    customTabBarConfig: null,
    currentTabIndex: 0,
    
    // 演示数据
    demoItems: [
      { title: '完全自定义', desc: '不受小程序原生限制' },
      { title: '动态增减', desc: '可以任意添加或删除导航项' },
      { title: '灵活样式', desc: '支持任意颜色、大小、动画' },
      { title: '丰富交互', desc: '徽章、提示、动画效果' },
      { title: '实时同步', desc: '后台配置实时生效' }
    ]
  },

  onLoad() {
    this.initCustomTabBar()
  },

  onShow() {
    // 更新当前tab索引
    this.updateCurrentTabIndex()
  },

  // 初始化自定义底部导航
  async initCustomTabBar() {
    try {
      const app = getApp()
      
      // 获取全局配置
      let config = app.globalData.customTabBarConfig
      
      // 如果没有配置，重新加载
      if (!config) {
        await app.reloadCustomTabBar()
        config = app.globalData.customTabBarConfig
      }
      
      this.setData({ 
        customTabBarConfig: config 
      })
      
      this.updateCurrentTabIndex()
      
    } catch (error) {
      console.error('❌ 初始化自定义底部导航失败:', error)
    }
  },

  // 更新当前tab索引
  updateCurrentTabIndex() {
    const app = getApp()
    const currentIndex = app.getCurrentTabIndex()
    
    this.setData({ 
      currentTabIndex: currentIndex 
    })
    
    // 更新自定义导航组件的选中状态
    const customTabBar = this.selectComponent('#custom-tabbar')
    if (customTabBar) {
      customTabBar.setData({ 
        current: currentIndex 
      })
    }
  },

  // 自定义导航切换事件
  onCustomTabChange(e) {
    const { index, item, pagePath } = e.detail
    // 使用页面路由管理器导航
    const app = getApp()
    app.navigateToPage(pagePath)
  },

  // 重新加载配置
  async reloadConfig() {
    wx.showLoading({ title: '重新加载...' })
    
    try {
      const app = getApp()
      const success = await app.reloadCustomTabBar()
      
      if (success) {
        const config = app.globalData.customTabBarConfig
        this.setData({ 
          customTabBarConfig: config 
        })
        
        wx.showToast({
          title: '配置已更新',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: '重新加载失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('❌ 重新加载配置失败:', error)
      wx.showToast({
        title: '重新加载失败',
        icon: 'error'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 测试徽章功能
  testBadge() {
    const customTabBar = this.selectComponent('#custom-tabbar')
    if (customTabBar) {
      // 为第二个导航项添加徽章
      customTabBar.setBadge(1, '99+')
      
      wx.showToast({
        title: '徽章已添加',
        icon: 'success'
      })
      
      // 3秒后移除徽章
      setTimeout(() => {
        customTabBar.removeBadge(1)
        wx.showToast({
          title: '徽章已移除',
          icon: 'success'
        })
      }, 3000)
    }
  },

  // 测试隐藏/显示导航
  testToggleTabBar() {
    const customTabBar = this.selectComponent('#custom-tabbar')
    if (customTabBar) {
      const isVisible = customTabBar.data.isVisible
      
      if (isVisible) {
        customTabBar.hide()
        wx.showToast({
          title: '导航已隐藏',
          icon: 'success'
        })
      } else {
        customTabBar.show()
        wx.showToast({
          title: '导航已显示',
          icon: 'success'
        })
      }
    }
  },

  // 查看配置详情
  showConfigDetail() {
    const config = this.data.customTabBarConfig
    if (!config) {
      wx.showToast({
        title: '没有配置数据',
        icon: 'none'
      })
      return
    }

    const detail = `
配置详情:
• 背景色: ${config.backgroundColor}
• 默认文字色: ${config.color}
• 选中文字色: ${config.selectedColor}
• 导航项数量: ${config.list.length}

导航项列表:
${config.list.map((item, index) => `${index + 1}. ${item.text} - ${item.pagePath}`).join('\n')}
    `.trim()

    wx.showModal({
      title: '自定义导航配置',
      content: detail,
      showCancel: false,
      confirmText: '确定'
    })
  }
})

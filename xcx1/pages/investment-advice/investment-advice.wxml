<!--pages/investment-advice/investment-advice.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="header-content">
      <text class="header-title">🎯 AI投资建议</text>
      <text class="header-subtitle">基于大数据分析的个性化投资建议</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">AI正在分析中...</text>
  </view>

  <!-- 投资建议内容 -->
  <view wx:else class="content">
    <!-- 建议摘要 -->
    <view wx:if="{{adviceData}}" class="advice-summary">
      <view class="summary-card">
        <view class="summary-header">
          <text class="summary-title">{{adviceData.title}}</text>
          <view class="confidence-badge">
            <text class="confidence-text">置信度 {{confidenceScore}}%</text>
          </view>
        </view>
        <text class="summary-desc">{{adviceData.summary}}</text>
      </view>
    </view>

    <!-- 推荐茶地列表 -->
    <view wx:if="{{adviceData.recommendations}}" class="recommendations">
      <view class="section-header">
        <text class="section-title">📊 推荐茶地</text>
        <text class="section-subtitle">基于您的投资偏好智能推荐</text>
      </view>
      
      <view class="recommendation-list">
        <view class="recommendation-item" wx:for="{{adviceData.recommendations}}" wx:key="index" bindtap="viewTeaField" data-id="{{item.tea_field_id}}">
          <view class="rec-header">
            <view class="rec-icon">{{item.icon}}</view>
            <view class="rec-info">
              <text class="rec-title">{{item.title}}</text>
              <text class="rec-desc">{{item.description}}</text>
            </view>
            <view class="rec-score" style="color: {{item.scoreColor}}">
              <text class="score-value">{{item.score}}</text>
              <text class="score-label">评分</text>
            </view>
          </view>
          <view class="rec-actions">
            <button class="btn-detail" bindtap="viewTeaField" data-id="{{item.tea_field_id}}">查看详情</button>
            <button class="btn-invest" bindtap="investNow" data-id="{{item.tea_field_id}}">立即投资</button>
          </view>
        </view>
      </view>
    </view>

    <!-- AI聊天入口 -->
    <view class="ai-chat-section">
      <view class="chat-card">
        <view class="chat-header">
          <text class="chat-title">💬 AI投资顾问</text>
          <text class="chat-subtitle">有投资疑问？随时咨询AI助手</text>
        </view>
        <view class="chat-actions">
          <button class="btn-chat" bindtap="openAIChat">开始咨询</button>
          <button class="btn-refresh" bindtap="refreshAdvice">刷新建议</button>
        </view>
      </view>
    </view>

    <!-- 市场分析 -->
    <view class="market-analysis">
      <view class="section-header">
        <text class="section-title">📈 市场分析</text>
        <text class="section-subtitle">当前茶叶投资市场概况</text>
      </view>
      
      <view class="analysis-cards">
        <view class="analysis-card">
          <view class="card-icon">📊</view>
          <view class="card-content">
            <text class="card-title">市场趋势</text>
            <text class="card-value">稳步上升</text>
            <text class="card-desc">茶叶投资市场持续向好</text>
          </view>
        </view>
        
        <view class="analysis-card">
          <view class="card-icon">💰</view>
          <view class="card-content">
            <text class="card-title">平均收益</text>
            <text class="card-value">12.5%</text>
            <text class="card-desc">年化收益率</text>
          </view>
        </view>
        
        <view class="analysis-card">
          <view class="card-icon">⚖️</view>
          <view class="card-content">
            <text class="card-title">风险等级</text>
            <text class="card-value">中低</text>
            <text class="card-desc">相对稳健的投资选择</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 投资指南 -->
    <view class="investment-guide">
      <view class="section-header">
        <text class="section-title">📚 投资指南</text>
        <text class="section-subtitle">新手投资必读</text>
      </view>
      
      <view class="guide-list">
        <view class="guide-item" wx:for="{{guideItems}}" wx:key="index" bindtap="viewGuide" data-id="{{item.id}}">
          <view class="guide-icon">{{item.icon}}</view>
          <view class="guide-content">
            <text class="guide-title">{{item.title}}</text>
            <text class="guide-desc">{{item.description}}</text>
          </view>
          <view class="guide-arrow">></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{!loading && !adviceData}}" class="empty-state">
    <text class="empty-icon">🤖</text>
    <text class="empty-title">暂无投资建议</text>
    <text class="empty-desc">点击下方按钮生成AI投资建议</text>
    <button class="btn-generate" bindtap="generateAdvice">生成AI建议</button>
  </view>
</view>

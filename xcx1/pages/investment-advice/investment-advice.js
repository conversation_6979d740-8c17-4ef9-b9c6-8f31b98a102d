// pages/investment-advice/investment-advice.js
const api = require('../../api/index')

Page({
  data: {
    loading: true,
    adviceData: null,
    confidenceScore: 85,
    guideItems: [
      {
        id: 1,
        icon: '📖',
        title: '茶叶投资基础知识',
        description: '了解茶叶投资的基本概念和术语'
      },
      {
        id: 2,
        icon: '🎯',
        title: '如何选择优质茶园',
        description: '学会评估茶园的投资价值和潜力'
      },
      {
        id: 3,
        icon: '⚖️',
        title: '风险控制与管理',
        description: '掌握投资风险的识别和控制方法'
      },
      {
        id: 4,
        icon: '💰',
        title: '收益计算与预期',
        description: '了解茶叶投资的收益模式和计算方法'
      }
    ]
  },

  onLoad(options) {
    this.loadInvestmentAdvice()
  },

  onShow() {
    // 页面显示时刷新数据
    if (!this.data.loading && !this.data.adviceData) {
      this.loadInvestmentAdvice()
    }
  },

  onPullDownRefresh() {
    this.loadInvestmentAdvice().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载投资建议
  loadInvestmentAdvice() {
    return new Promise((resolve, reject) => {
      this.setData({
        loading: true
      })

      // 调用AI投资建议API
      api.aiApi.getInvestmentAdvice().then(res => {
        if (res.code === 200 && res.data) {
          this.setData({
            adviceData: res.data,
            confidenceScore: Math.floor(Math.random() * 15) + 85, // 85-99的随机置信度
            loading: false
          })
        } else {
          this.setData({
            adviceData: this.getMockAdviceData(),
            loading: false
          })
        }
        resolve()
      }).catch(error => {
        console.error('❌ AI投资建议API调用失败:', error)
        this.setData({
          adviceData: this.getMockAdviceData(),
          loading: false
        })
        wx.showToast({
          title: '加载失败，显示模拟数据',
          icon: 'none',
          duration: 2000
        })
        resolve()
      })
    })
  },

  // 获取模拟建议数据
  getMockAdviceData() {
    return {
      title: '基于您的投资偏好，AI为您推荐',
      summary: '根据当前市场行情和您的风险偏好，以下茶地具有较好的投资潜力和稳定收益',
      recommendations: [
        {
          icon: '🌟',
          title: '凤凰茶园 - 凤凰单枞精品区',
          description: '地理位置优越，凤凰山独特气候，品种稀有，市场认可度高，预期年收益15%',
          score: '9.2',
          scoreColor: '#10b981',
          tea_field_id: 39
        },
        {
          icon: '💎',
          title: '南万茶园 - 南万单枞(白叶)',
          description: '高端品种，收藏价值高，性价比优良，适合稳健投资，预期年收益12%',
          score: '8.8',
          scoreColor: '#3b82f6',
          tea_field_id: 35
        },
        {
          icon: '🚀',
          title: '南万茶园 - 南万红茶(金萱)',
          description: '价格优势明显，适合入门投资，未来增值潜力大，预期年收益10%',
          score: '8.5',
          scoreColor: '#f59e0b',
          tea_field_id: 31
        }
      ]
    }
  },

  // 生成AI建议
  generateAdvice() {
    this.loadInvestmentAdvice()
  },

  // 刷新建议
  refreshAdvice() {
    this.loadInvestmentAdvice()
  },

  // 查看茶地详情
  viewTeaField(e) {
    const teaFieldId = e.currentTarget.dataset.id
    if (teaFieldId) {
      wx.navigateTo({
        url: `/pages/tea-detail/tea-detail?id=${teaFieldId}`
      })
    } else {
      wx.showToast({
        title: '茶地信息不完整',
        icon: 'none'
      })
    }
  },

  // 立即投资
  investNow(e) {
    const teaFieldId = e.currentTarget.dataset.id
    if (teaFieldId) {
      wx.navigateTo({
        url: `/pages/tea-detail/tea-detail?id=${teaFieldId}&action=invest`
      })
    } else {
      wx.showToast({
        title: '茶地信息不完整',
        icon: 'none'
      })
    }
  },

  // 打开AI聊天
  openAIChat() {
    wx.navigateTo({
      url: '/pages/ai-chat/ai-chat'
    })
  },

  // 查看投资指南
  viewGuide(e) {
    const guideId = e.currentTarget.dataset.id
    wx.showModal({
      title: '投资指南',
      content: '投资指南功能正在开发中，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'AI投资建议 - 智能茶园投资助手',
      path: '/pages/investment-advice/investment-advice',
      imageUrl: '/images/share-investment.png'
    }
  },

  onShareTimeline() {
    return {
      title: 'AI投资建议 - 智能茶园投资助手',
      query: '',
      imageUrl: '/images/share-investment.png'
    }
  }
})

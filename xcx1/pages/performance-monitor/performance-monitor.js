/**
 * 性能监控页面
 */
const { globalPerformanceManager, PERFORMANCE_METRICS } = require('../../utils/performance.js')

Page({
  data: {
    // 主题状态
    themeClass: '',
    
    // 性能概览
    performanceScore: 85,
    performanceTrend: 'up',
    avgPageLoad: 1200,
    pageLoadStatus: 'good',
    avgApiResponse: 800,
    apiResponseStatus: 'good',
    memoryUsage: 45,
    memoryStatus: 'good',
    
    // 监控状态
    isMonitoring: true,
    
    // 图表数据
    chartData: [],
    
    // 当前标签
    currentTab: 'page',
    
    // 详细指标
    pageMetrics: [],
    apiMetrics: [],
    
    // 资源使用
    storageUsage: 0,
    storageLimit: 10240,
    storageUsagePercent: 0,
    networkType: 'wifi',
    networkStatus: 'good',
    deviceInfo: '',
    
    // 性能建议
    recommendations: [],
    
    // 模态框
    showReportModal: false,
    reportContent: ''
  },

  onLoad(options) {
    this.initPerformanceMonitor()
  },

  onShow() {
    this.refreshData()
  },

  onUnload() {
    // 移除性能监控观察者
    globalPerformanceManager.removeObserver(this.onPerformanceUpdate.bind(this))
  },

  // 初始化性能监控
  initPerformanceMonitor() {
    try {
      // 添加性能监控观察者
      globalPerformanceManager.addObserver(this.onPerformanceUpdate.bind(this))
      
      // 初始化图表数据
      this.initChartData()
      
      // 获取设备信息
      this.getDeviceInfo()
      
      // 刷新数据
      this.refreshData()
      
      // 启动实时更新
      this.startRealTimeUpdate()
      
    } catch (error) {
      console.error('❌ 初始化性能监控失败:', error)
    }
  },

  // 性能数据更新回调
  onPerformanceUpdate(event, data) {
    switch (event) {
      case 'performance_report':
        this.updatePerformanceReport(data)
        break
      case 'performance_warning':
        this.handlePerformanceWarning(data)
        break
    }
  },

  // 更新性能报告
  updatePerformanceReport(report) {
    // 更新概览数据
    this.updateOverviewMetrics(report.metrics)
    
    // 更新详细指标
    this.updateDetailedMetrics()
    
    // 更新建议
    this.setData({
      recommendations: report.recommendations || []
    })
  },

  // 更新概览指标
  updateOverviewMetrics(metrics) {
    let performanceScore = 100
    let avgPageLoad = 0
    let avgApiResponse = 0
    
    // 计算页面加载平均时间
    if (metrics[PERFORMANCE_METRICS.PAGE_LOAD]) {
      avgPageLoad = Math.round(metrics[PERFORMANCE_METRICS.PAGE_LOAD].avgDuration || 0)
      if (avgPageLoad > 2000) performanceScore -= 20
      else if (avgPageLoad > 1000) performanceScore -= 10
    }
    
    // 计算API响应平均时间
    if (metrics[PERFORMANCE_METRICS.API_REQUEST]) {
      avgApiResponse = Math.round(metrics[PERFORMANCE_METRICS.API_REQUEST].avgDuration || 0)
      if (avgApiResponse > 3000) performanceScore -= 20
      else if (avgApiResponse > 1500) performanceScore -= 10
    }
    
    this.setData({
      performanceScore: Math.max(0, performanceScore),
      avgPageLoad,
      avgApiResponse,
      pageLoadStatus: avgPageLoad > 2000 ? 'error' : avgPageLoad > 1000 ? 'warning' : 'good',
      apiResponseStatus: avgApiResponse > 3000 ? 'error' : avgApiResponse > 1500 ? 'warning' : 'good'
    })
  },

  // 更新详细指标
  updateDetailedMetrics() {
    // 获取页面性能指标
    const pageMetrics = globalPerformanceManager.getMetrics(PERFORMANCE_METRICS.PAGE_LOAD)
      .slice(-10) // 最近10条
      .map(metric => ({
        name: metric.data.page || metric.data.type || '未知页面',
        duration: metric.data.duration,
        status: metric.data.duration > 2000 ? 'error' : metric.data.duration > 1000 ? 'warning' : 'good',
        statusText: metric.data.duration > 2000 ? '慢' : metric.data.duration > 1000 ? '一般' : '快'
      }))
    
    // 获取API性能指标
    const apiMetrics = globalPerformanceManager.getMetrics(PERFORMANCE_METRICS.API_REQUEST)
      .slice(-10) // 最近10条
      .map(metric => ({
        url: this.formatUrl(metric.data.url),
        duration: metric.data.duration,
        status: metric.data.duration > 3000 ? 'error' : metric.data.duration > 1500 ? 'warning' : 'good',
        statusText: metric.data.status === 'error' ? '失败' : 
                   metric.data.duration > 3000 ? '慢' : 
                   metric.data.duration > 1500 ? '一般' : '快'
      }))
    
    this.setData({
      pageMetrics,
      apiMetrics
    })
  },

  // 格式化URL显示
  formatUrl(url) {
    if (!url) return '未知接口'
    
    // 提取路径部分
    try {
      const urlObj = new URL(url)
      return urlObj.pathname.split('/').pop() || urlObj.pathname
    } catch {
      return url.split('/').pop() || url
    }
  },

  // 处理性能警告
  handlePerformanceWarning(warning) {
    // 添加到建议列表
    const newRecommendation = {
      type: warning.type,
      level: 'warning',
      title: '性能警告',
      message: warning.message
    }
    
    const recommendations = [...this.data.recommendations, newRecommendation]
    this.setData({ recommendations })
  },

  // 初始化图表数据
  initChartData() {
    const chartData = []
    for (let i = 0; i < 20; i++) {
      chartData.push({
        height: Math.random() * 80 + 20,
        x: (i / 19) * 100
      })
    }
    this.setData({ chartData })
  },

  // 获取设备信息
  getDeviceInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.setData({
          deviceInfo: `${res.brand} ${res.model} (${res.system})`
        })
      }
    })
    
    // 获取网络状态
    wx.getNetworkType({
      success: (res) => {
        this.setData({
          networkType: res.networkType,
          networkStatus: res.networkType === 'none' ? 'error' : 
                        res.networkType === '2g' ? 'warning' : 'good'
        })
      }
    })
    
    // 获取存储信息
    wx.getStorageInfo({
      success: (res) => {
        const usagePercent = (res.currentSize / res.limitSize) * 100
        this.setData({
          storageUsage: res.currentSize,
          storageLimit: res.limitSize,
          storageUsagePercent: Math.min(100, usagePercent)
        })
      }
    })
  },

  // 启动实时更新
  startRealTimeUpdate() {
    this.updateTimer = setInterval(() => {
      if (this.data.isMonitoring) {
        this.updateChartData()
        this.getDeviceInfo()
      }
    }, 5000) // 每5秒更新一次
  },

  // 更新图表数据
  updateChartData() {
    const chartData = [...this.data.chartData]
    
    // 移除第一个数据点
    chartData.shift()
    
    // 添加新的数据点
    chartData.push({
      height: Math.random() * 80 + 20,
      x: 95
    })
    
    // 重新计算x坐标
    chartData.forEach((item, index) => {
      item.x = (index / (chartData.length - 1)) * 100
    })
    
    this.setData({ chartData })
  },

  // 刷新数据
  refreshData() {
    this.updateDetailedMetrics()
    this.getDeviceInfo()
  },

  // 监控开关切换
  onMonitorToggle(e) {
    const { value } = e.detail
    this.setData({ isMonitoring: value })
    
    if (value) {
      globalPerformanceManager.startMonitoring()
      wx.showToast({
        title: '性能监控已开启',
        icon: 'success'
      })
    } else {
      globalPerformanceManager.stopMonitoring()
      wx.showToast({
        title: '性能监控已关闭',
        icon: 'none'
      })
    }
  },

  // 切换标签
  switchTab(e) {
    const { tab } = e.currentTarget.dataset
    this.setData({ currentTab: tab })
  },

  // 处理建议
  handleRecommendation(e) {
    const { item } = e.currentTarget.dataset
    
    wx.showModal({
      title: item.title,
      content: item.message,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 生成报告
  generateReport() {
    const report = globalPerformanceManager.generatePerformanceReport()
    
    const reportContent = `
性能报告 (${new Date().toLocaleString()})

概览:
- 性能评分: ${this.data.performanceScore}/100
- 平均页面加载: ${this.data.avgPageLoad}ms
- 平均API响应: ${this.data.avgApiResponse}ms
- 内存使用: ${this.data.memoryUsage}MB

详细指标:
- 页面性能记录: ${this.data.pageMetrics.length}条
- API性能记录: ${this.data.apiMetrics.length}条

建议:
${this.data.recommendations.map(r => `- ${r.message}`).join('\n')}
    `.trim()
    
    this.setData({
      showReportModal: true,
      reportContent
    })
  },

  // 清除数据
  clearData() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有性能监控数据吗？',
      success: (res) => {
        if (res.confirm) {
          globalPerformanceManager.clearMetrics()
          this.setData({
            pageMetrics: [],
            apiMetrics: [],
            recommendations: []
          })
          
          wx.showToast({
            title: '数据已清除',
            icon: 'success'
          })
        }
      }
    })
  },

  // 显示优化建议
  showOptimizationTips() {
    wx.navigateTo({
      url: '/pages/optimization-tips/optimization-tips'
    })
  },

  // 隐藏报告模态框
  hideReportModal() {
    this.setData({ showReportModal: false })
  },

  // 阻止模态框关闭
  preventClose() {
    // 阻止事件冒泡
  },

  // 导出报告
  exportReport() {
    wx.setClipboardData({
      data: this.data.reportContent,
      success: () => {
        wx.showToast({
          title: '报告已复制到剪贴板',
          icon: 'success'
        })
        this.hideReportModal()
      }
    })
  }
})

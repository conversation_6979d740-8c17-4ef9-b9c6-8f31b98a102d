/**
 * 主题设置页面
 */
const { globalThemeManager, THEME_TYPES, COLOR_SCHEMES } = require('../../utils/theme.js')

Page({
  data: {
    // 主题状态
    themeClass: '',
    currentTheme: 'auto',
    currentColorScheme: 'green',
    isDarkMode: false,
    systemTheme: '浅色',
    
    // 个性化设置
    animationEnabled: true,
    hapticEnabled: true,
    soundEnabled: false,
    fontSize: 28,
    
    // 主题信息
    themeInfo: {
      typeLabel: '跟随系统',
      schemeLabel: '茶绿色'
    },
    
    // 预设信息
    themePresets: {}
  },

  onLoad(options) {
    this.initThemeSettings()
  },

  onShow() {
    // 更新主题状态
    this.updateThemeState()
  },

  // 初始化主题设置
  initThemeSettings() {
    try {
      // 获取主题预设
      const presets = globalThemeManager.getThemePresets()
      
      // 获取当前主题
      const currentTheme = globalThemeManager.getCurrentTheme()
      
      // 获取系统主题
      const systemDark = globalThemeManager.getSystemDarkMode()
      
      // 加载个性化设置
      this.loadPersonalSettings()
      
      this.setData({
        themePresets: presets,
        currentTheme: currentTheme.type,
        currentColorScheme: currentTheme.colorScheme,
        isDarkMode: currentTheme.isDark,
        systemTheme: systemDark ? '暗色' : '浅色',
        themeInfo: {
          typeLabel: presets.typeLabels[currentTheme.type],
          schemeLabel: presets.schemeLabels[currentTheme.colorScheme]
        }
      })
      
      // 应用主题
      this.updateThemeState()
      
      // 添加主题变化监听
      globalThemeManager.addListener(this.onThemeChange.bind(this))
      
    } catch (error) {
      console.error('❌ 初始化主题设置失败:', error)
    }
  },

  // 加载个性化设置
  loadPersonalSettings() {
    try {
      const animationEnabled = wx.getStorageSync('animation_enabled') !== false
      const hapticEnabled = wx.getStorageSync('haptic_enabled') !== false
      const soundEnabled = wx.getStorageSync('sound_enabled') === true
      const fontSize = wx.getStorageSync('font_size') || 28
      
      this.setData({
        animationEnabled,
        hapticEnabled,
        soundEnabled,
        fontSize
      })
    } catch (error) {
      console.error('❌ 加载个性化设置失败:', error)
    }
  },

  // 保存个性化设置
  savePersonalSettings() {
    try {
      wx.setStorageSync('animation_enabled', this.data.animationEnabled)
      wx.setStorageSync('haptic_enabled', this.data.hapticEnabled)
      wx.setStorageSync('sound_enabled', this.data.soundEnabled)
      wx.setStorageSync('font_size', this.data.fontSize)
    } catch (error) {
      console.error('❌ 保存个性化设置失败:', error)
    }
  },

  // 更新主题状态
  updateThemeState() {
    const currentTheme = globalThemeManager.getCurrentTheme()
    const themeClass = currentTheme.isDark ? 'theme-dark' : 'theme-light'
    
    this.setData({
      themeClass,
      isDarkMode: currentTheme.isDark
    })
  },

  // 主题变化监听
  onThemeChange(event, data) {
    if (event === 'themeTypeChanged' || event === 'colorSchemeChanged') {
      this.updateThemeState()
      
      // 更新显示信息
      const currentTheme = globalThemeManager.getCurrentTheme()
      const presets = this.data.themePresets
      
      this.setData({
        currentTheme: currentTheme.type,
        currentColorScheme: currentTheme.colorScheme,
        themeInfo: {
          typeLabel: presets.typeLabels[currentTheme.type],
          schemeLabel: presets.schemeLabels[currentTheme.colorScheme]
        }
      })
    }
  },

  // 选择主题类型
  selectThemeType(e) {
    const { type } = e.currentTarget.dataset
    
    if (type === this.data.currentTheme) {
      return
    }
    
    // 触觉反馈
    if (this.data.hapticEnabled) {
      wx.vibrateShort()
    }
    
    globalThemeManager.setThemeType(type)
    
    wx.showToast({
      title: '主题已切换',
      icon: 'success',
      duration: 1500
    })
  },

  // 选择色彩方案
  selectColorScheme(e) {
    const { scheme } = e.currentTarget.dataset
    
    if (scheme === this.data.currentColorScheme) {
      return
    }
    
    // 触觉反馈
    if (this.data.hapticEnabled) {
      wx.vibrateShort()
    }
    
    globalThemeManager.setColorScheme(scheme)
    
    wx.showToast({
      title: '色彩方案已切换',
      icon: 'success',
      duration: 1500
    })
  },

  // 动画效果开关
  onAnimationChange(e) {
    const { value } = e.detail
    this.setData({ animationEnabled: value })
    this.savePersonalSettings()
    
    wx.showToast({
      title: value ? '动画效果已开启' : '动画效果已关闭',
      icon: 'none',
      duration: 1500
    })
  },

  // 震动反馈开关
  onHapticChange(e) {
    const { value } = e.detail
    this.setData({ hapticEnabled: value })
    this.savePersonalSettings()
    
    if (value) {
      wx.vibrateShort()
    }
    
    wx.showToast({
      title: value ? '震动反馈已开启' : '震动反馈已关闭',
      icon: 'none',
      duration: 1500
    })
  },

  // 声音效果开关
  onSoundChange(e) {
    const { value } = e.detail
    this.setData({ soundEnabled: value })
    this.savePersonalSettings()
    
    wx.showToast({
      title: value ? '声音效果已开启' : '声音效果已关闭',
      icon: 'none',
      duration: 1500
    })
  },

  // 字体大小变化
  onFontSizeChange(e) {
    const { value } = e.detail
    this.setData({ fontSize: value })
    this.savePersonalSettings()
  },

  // 保存设置
  saveSettings() {
    try {
      this.savePersonalSettings()
      
      wx.showToast({
        title: '设置已保存',
        icon: 'success',
        duration: 2000
      })
      
      // 触觉反馈
      if (this.data.hapticEnabled) {
        wx.vibrateShort()
      }
      
    } catch (error) {
      console.error('❌ 保存设置失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  // 恢复默认设置
  resetSettings() {
    wx.showModal({
      title: '确认重置',
      content: '确定要恢复所有默认设置吗？此操作不可撤销。',
      confirmText: '确定重置',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.performReset()
        }
      }
    })
  },

  // 执行重置
  performReset() {
    try {
      // 重置主题
      globalThemeManager.resetTheme()
      
      // 重置个性化设置
      this.setData({
        animationEnabled: true,
        hapticEnabled: true,
        soundEnabled: false,
        fontSize: 28
      })
      
      this.savePersonalSettings()
      
      // 更新显示
      this.initThemeSettings()
      
      wx.showToast({
        title: '已恢复默认设置',
        icon: 'success',
        duration: 2000
      })
      
      // 触觉反馈
      wx.vibrateShort()
      
    } catch (error) {
      console.error('❌ 重置设置失败:', error)
      wx.showToast({
        title: '重置失败',
        icon: 'none'
      })
    }
  },

  // 导出设置
  exportSettings() {
    try {
      const themeConfig = globalThemeManager.exportThemeConfig()
      const personalConfig = {
        animationEnabled: this.data.animationEnabled,
        hapticEnabled: this.data.hapticEnabled,
        soundEnabled: this.data.soundEnabled,
        fontSize: this.data.fontSize
      }
      
      const fullConfig = {
        theme: themeConfig,
        personal: personalConfig,
        exportTime: Date.now(),
        version: '1.0.0'
      }
      
      // 复制到剪贴板
      wx.setClipboardData({
        data: JSON.stringify(fullConfig, null, 2),
        success: () => {
          wx.showToast({
            title: '配置已复制到剪贴板',
            icon: 'success',
            duration: 2000
          })
        },
        fail: () => {
          wx.showModal({
            title: '导出配置',
            content: JSON.stringify(fullConfig, null, 2),
            showCancel: false,
            confirmText: '知道了'
          })
        }
      })
      
    } catch (error) {
      console.error('❌ 导出设置失败:', error)
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      })
    }
  },

  // 页面卸载
  onUnload() {
    // 移除主题监听器
    globalThemeManager.removeListener(this.onThemeChange.bind(this))
  }
})

// pages/enterprise-auth/enterprise-auth.js
const app = getApp()

Page({
  data: {
    formData: {
      companyName: '',
      creditCode: '',
      legalPerson: '',
      address: '',
      licenseImage: '',
      idFrontImage: '',
      idBackImage: ''
    },
    submitting: false,
    canSubmit: false
  },

  onLoad(options) {
    this.checkFormValid()
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: value
    }, () => {
      this.checkFormValid()
    })
  },

  // 选择图片
  chooseImage(e) {
    const { type } = e.currentTarget.dataset
    
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        
        // 这里应该上传到服务器，现在先用本地路径
        this.setData({
          [`formData.${type}Image`]: tempFilePath
        }, () => {
          this.checkFormValid()
        })
        
        // TODO: 上传到服务器
        this.uploadImage(tempFilePath, type)
      },
      fail: (error) => {
        console.error('选择图片失败:', error)
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        })
      }
    })
  },

  // 上传图片到服务器
  uploadImage(filePath, type) {
    wx.showLoading({
      title: '上传中...'
    })

    // TODO: 实际的上传逻辑
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '上传成功',
        icon: 'success'
      })
    }, 1000)
  },

  // 检查表单是否有效
  checkFormValid() {
    const { formData } = this.data
    const canSubmit = formData.companyName && 
                     formData.creditCode && 
                     formData.legalPerson && 
                     formData.address &&
                     formData.licenseImage &&
                     formData.idFrontImage &&
                     formData.idBackImage

    this.setData({ canSubmit })
  },

  // 提交认证
  submitAuth() {
    if (!this.data.canSubmit || this.data.submitting) {
      return
    }

    // 验证统一社会信用代码格式
    if (!/^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/.test(this.data.formData.creditCode)) {
      wx.showToast({
        title: '请输入正确的统一社会信用代码',
        icon: 'none'
      })
      return
    }

    this.setData({ submitting: true })

    // 调用后端API提交企业认证
    const submitData = {
      company_name: this.data.formData.companyName,
      credit_code: this.data.formData.creditCode,
      legal_person: this.data.formData.legalPerson,
      contact_name: this.data.formData.contactName,
      contact_phone: this.data.formData.contactPhone,
      business_license: this.data.formData.businessLicense
    }

    api.authApi.submitEnterprise(submitData).then(res => {
      if (res.code === 200) {
        wx.showModal({
          title: '提交成功',
          content: '企业认证申请已提交，我们将在1-3个工作日内完成审核，请耐心等待。',
          showCancel: false,
          confirmText: '确定',
          success: () => {
            wx.navigateBack()
          }
        })
      } else {
        throw new Error(res.message || '提交失败')
      }
    }).catch(error => {
      console.error('提交企业认证失败:', error)
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      })
    }).finally(() => {
      this.setData({ submitting: false })
    })
  }
})

// 实名认证页面
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    // 认证状态
    authStatus: 'pending', // pending, reviewing, approved, rejected
    statusTitle: '',
    statusDesc: '',
    authTime: '',
    authInfo: {},
    
    // 表单显示
    showAuthForm: false,
    
    // 表单数据
    formData: {
      realName: '',
      idCard: '',
      idCardFront: '',
      idCardBack: ''
    },
    
    // 人脸识别
    faceStatus: 'pending', // pending, success, failed
    faceStatusText: '未开始',
    
    // 协议同意
    agreedToAuth: false,
    showAgreementModal: false,
    agreementTitle: '',
    agreementContent: '',
    
    // 认证记录
    authHistory: [],
    
    // 页面状态
    canSubmit: false,
    submitting: false
  },

  // 页面加载
  onLoad(options) {
    this.loadAuthStatus()
    this.loadAuthHistory()
  },

  // 页面显示
  onShow() {
  },

  // 加载认证状态
  async loadAuthStatus() {
    try {
      // 调用认证状态API
      const res = await api.authApi.getStatus()

      let authStatus = 'pending'
      if (res.code === 200 && res.data) {
        // 使用新的认证状态汇总API
        const authData = res.data
        if (authData.personal_verified) {
          authStatus = 'approved'
        } else if (authData.personal_info) {
          authStatus = authData.personal_info.status || 'pending'
        }
      } else {
        // 从用户信息获取状态作为后备
        const userInfo = app.getUserInfo()
        authStatus = userInfo?.auth_status || 'pending'
      }
      
      let statusTitle = ''
      let statusDesc = ''
      let authTime = ''
      let authInfo = {}
      
      switch (authStatus) {
        case 'pending':
          statusTitle = '未认证'
          statusDesc = '请完成实名认证以享受更多服务'
          // 如果是pending状态，显示认证表单
          this.setData({ showAuthForm: true })
          break
        case 'reviewing':
          statusTitle = '审核中'
          statusDesc = '您的认证信息正在审核中，请耐心等待'
          authTime = '提交时间：2024-06-15 14:30'
          break
        case 'approved':
          statusTitle = '认证成功'
          statusDesc = '您已完成实名认证，可以享受全部服务'
          authTime = '认证时间：2024-06-16 10:20'
          authInfo = {
            real_name: '张三',
            id_card: '110101199001011234',
            auth_time: '2024-06-16 10:20'
          }
          break
        case 'rejected':
          statusTitle = '认证失败'
          statusDesc = '认证信息审核未通过，请重新提交'
          authTime = '审核时间：2024-06-16 16:45'
          break
      }
      
      this.setData({
        authStatus,
        statusTitle,
        statusDesc,
        authTime,
        authInfo
      })
    } catch (error) {
      console.error('加载认证状态失败:', error)
    }
  },

  // 加载认证记录
  async loadAuthHistory() {
    try {
      // 调用认证记录API
      const res = await api.authApi.getHistory()

      if (res.code === 200) {
        this.setData({
          authHistory: res.data.results || []
        })
      } else {
        this.setData({
          authHistory: []
        })
      }
    } catch (error) {
      console.error('加载认证记录失败:', error)
      this.setData({
        authHistory: []
      })
    }
  },

  // 开始认证
  startAuth() {
    this.setData({ showAuthForm: true })
  },

  // 真实姓名输入
  onRealNameInput(e) {
    this.setData({
      'formData.realName': e.detail.value
    })
    this.checkCanSubmit()
  },

  // 身份证号输入
  onIdCardInput(e) {
    this.setData({
      'formData.idCard': e.detail.value
    })
    this.checkCanSubmit()
  },

  // 上传身份证正面
  uploadIdCardFront() {
    this.chooseIdCardImage('front')
  },

  // 上传身份证反面
  uploadIdCardBack() {
    this.chooseIdCardImage('back')
  },

  // 选择身份证图片
  chooseIdCardImage(type) {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.uploadIdCardImage(res.tempFiles[0].tempFilePath, type)
      }
    })
  },

  // 上传身份证图片
  async uploadIdCardImage(filePath, type) {
    try {
      wx.showLoading({
        title: '上传中...'
      })
      
      // 调用图片上传API
      const res = await api.uploadApi.uploadFile(filePath)

      wx.hideLoading()

      if (res.code === 200) {
        const field = type === 'front' ? 'idCardFront' : 'idCardBack'
        this.setData({
          [`formData.${field}`]: res.data.url
        })

        wx.showToast({
          title: '上传成功',
          icon: 'success'
        })
      } else {
        throw new Error(res.message || '上传失败')
      }
      
      this.checkCanSubmit()
    } catch (error) {
      console.error('上传身份证图片失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '上传失败',
        icon: 'none'
      })
    }
  },

  // 开始人脸识别
  async startFaceRecognition() {
    if (this.data.faceStatus === 'success') return
    
    try {
      this.setData({
        faceStatus: 'processing',
        faceStatusText: '识别中...'
      })
      
      // 调用人脸识别API
      const res = await api.authApi.faceRecognition({
        real_name: this.data.formData.realName,
        id_number: this.data.formData.idNumber
      })

      if (res.code === 200) {
        this.setData({
          faceStatus: 'success',
          faceStatusText: '识别成功'
        })

        wx.showToast({
          title: '人脸识别成功',
          icon: 'success'
        })

        this.checkCanSubmit()
      } else {
        throw new Error(res.message || '识别失败')
      }
    } catch (error) {
      console.error('人脸识别失败:', error)
      this.setData({
        faceStatus: 'failed',
        faceStatusText: '识别失败，请重试'
      })
      
      wx.showToast({
        title: '识别失败，请重试',
        icon: 'none'
      })
    }
  },

  // 切换协议同意状态
  toggleAgreement() {
    this.setData({
      agreedToAuth: !this.data.agreedToAuth
    })
    this.checkCanSubmit()
  },

  // 查看认证协议
  viewAuthAgreement() {
    this.setData({
      showAgreementModal: true,
      agreementTitle: '实名认证协议',
      agreementContent: this.getAuthAgreementContent()
    })
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    this.setData({
      showAgreementModal: true,
      agreementTitle: '隐私政策',
      agreementContent: this.getPrivacyPolicyContent()
    })
  },

  // 隐藏协议弹窗
  hideAgreementModal() {
    this.setData({ showAgreementModal: false })
  },

  // 确认协议
  confirmAgreement() {
    this.setData({
      showAgreementModal: false,
      agreedToAuth: true
    })
    this.checkCanSubmit()
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { formData, faceStatus, agreedToAuth } = this.data
    
    const canSubmit = formData.realName.trim() &&
                     this.validateIdCard(formData.idCard) &&
                     formData.idCardFront &&
                     formData.idCardBack &&
                     faceStatus === 'success' &&
                     agreedToAuth
    
    this.setData({ canSubmit })
  },

  // 验证身份证号
  validateIdCard(idCard) {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return idCardRegex.test(idCard)
  },

  // 提交认证
  async submitAuth() {
    if (!this.data.canSubmit || this.data.submitting) return
    
    this.setData({ submitting: true })
    
    try {
      wx.showLoading({
        title: '提交中...'
      })
      
      const authData = {
        real_name: this.data.formData.realName.trim(),
        id_card: this.data.formData.idCard,
        id_card_front: this.data.formData.idCardFront,
        id_card_back: this.data.formData.idCardBack,
        face_recognition: this.data.faceStatus === 'success'
      }
      
      // 调用新的个人认证API
      const res = await api.authApi.submitPersonal(authData)

      wx.hideLoading()

      if (res.code === 200) {
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        })

        // 更新状态
        this.setData({
          authStatus: 'reviewing',
          statusTitle: '审核中',
          statusDesc: '您的认证信息正在审核中，请耐心等待',
          authTime: '提交时间：' + new Date().toLocaleString(),
          showAuthForm: false
        })

        // 刷新认证记录
        this.loadAuthHistory()
      } else {
        throw new Error(res.message || '提交失败')
      }
    } catch (error) {
      console.error('提交认证失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ submitting: false })
    }
  },

  // 格式化身份证号
  formatIdCard(idCard) {
    if (!idCard) return ''
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
  },

  // 获取认证协议内容
  getAuthAgreementContent() {
    return `实名认证协议

为了保障您的账户安全和合法权益，根据相关法律法规要求，您需要完成实名认证。

一、认证目的
1. 验证用户身份的真实性
2. 保障账户和资金安全
3. 符合监管要求

二、认证信息
1. 真实姓名
2. 身份证号码
3. 身份证照片
4. 人脸识别验证

三、信息保护
1. 您的个人信息将被严格保密
2. 仅用于身份验证和法律要求
3. 采用加密技术保护数据安全

四、认证流程
1. 填写真实信息
2. 上传身份证照片
3. 完成人脸识别
4. 等待审核结果

五、注意事项
1. 请确保信息真实有效
2. 虚假信息将无法通过审核
3. 认证通过后不可随意更改

如有疑问，请联系客服：400-123-4567`
  },

  // 获取隐私政策内容
  getPrivacyPolicyContent() {
    return `隐私政策

我们非常重视您的隐私保护，特别是在实名认证过程中。

一、信息收集
在实名认证过程中，我们会收集：
1. 姓名和身份证号码
2. 身份证照片
3. 人脸识别数据

二、信息使用
收集的信息仅用于：
1. 身份验证
2. 账户安全保护
3. 法律法规要求

三、信息保护
1. 采用银行级加密技术
2. 严格的访问控制
3. 定期安全审计

四、信息共享
除法律要求外，我们不会与第三方分享您的认证信息。

五、您的权利
1. 查看认证信息
2. 更正错误信息
3. 注销账户和数据

如有隐私相关问题，请联系：
邮箱：<EMAIL>
电话：400-123-4567`
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击协议弹窗内容时关闭弹窗
  }
})

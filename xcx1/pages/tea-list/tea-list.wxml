<!-- 茶地列表页 -->
<page-transition id="page-transition" animation-type="slide-left">
  <view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">{{pageTitle}}</text>
      <text class="header-subtitle">{{pageSubtitle}}</text>
    </view>
  </view>

  <!-- 智能搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <smart-search
        id="smartSearch"
        placeholder="搜索茶地名称、产地、茶叶类型..."
        show-categories="{{true}}"
        default-category="tea_name"
        auto-focus="{{false}}"
        bindsearch="onSmartSearch"
        bindinput="onSearchInput"
        bindcategorychange="onCategoryChange"
        class-name="tea-search">
      </smart-search>

      <view class="filter-btn" bindtap="showHierarchySelector">
        <text class="filter-icon">🌳</text>
        <text>位置</text>
        <view wx:if="{{filters.garden}}" class="filter-badge">✓</view>
      </view>

      <view class="filter-btn" bindtap="showFilter">
        <text class="filter-icon">⚙️</text>
        <text>筛选</text>
        <view wx:if="{{filterCount > 0}}" class="filter-badge">{{filterCount}}</view>
      </view>
    </view>
    
    <!-- 已选筛选标签 -->
    <scroll-view class="filter-tags" scroll-x="{{true}}" show-scrollbar="{{false}}" wx:if="{{filterTags.length > 0}}">
      <view class="tag-list">
        <view class="filter-tag" wx:for="{{filterTags}}" wx:key="key" bindtap="removeFilter" data-key="{{item.key}}">
          <text>{{item.label}}</text>
          <text class="tag-close">×</text>
        </view>
        <view class="clear-all" bindtap="clearAllFilters">清空</view>
      </view>
    </scroll-view>
  </view>

  <!-- 排序栏 -->
  <view class="sort-section">
    <view class="sort-item {{sortBy === 'default' ? 'active' : ''}}" bindtap="onSort" data-type="default">
      <text>默认排序</text>
    </view>
    <view class="sort-item {{sortBy === 'price' ? 'active' : ''}}" bindtap="onSort" data-type="price">
      <text>价格</text>
      <text class="sort-arrow {{sortBy === 'price' ? (sortOrder === 'asc' ? 'up' : 'down') : ''}}">↕️</text>
    </view>
    <view class="sort-item {{sortBy === 'return' ? 'active' : ''}}" bindtap="onSort" data-type="return">
      <text>收益率</text>
      <text class="sort-arrow {{sortBy === 'return' ? (sortOrder === 'asc' ? 'up' : 'down') : ''}}">↕️</text>
    </view>
    <view class="sort-item {{sortBy === 'area' ? 'active' : ''}}" bindtap="onSort" data-type="area">
      <text>面积</text>
      <text class="sort-arrow {{sortBy === 'area' ? (sortOrder === 'asc' ? 'up' : 'down') : ''}}">↕️</text>
    </view>
  </view>

  <!-- 茶地列表 -->
  <view class="tea-list-section">
    <view class="list-header">
      <text class="total-count">共找到 {{totalCount}} 个茶地</text>
      <view class="view-mode">
        <view class="mode-btn {{viewMode === 'list' ? 'active' : ''}}" bindtap="changeViewMode" data-mode="list">
          <text>📋</text>
        </view>
        <view class="mode-btn {{viewMode === 'grid' ? 'active' : ''}}" bindtap="changeViewMode" data-mode="grid">
          <text>⊞</text>
        </view>
        <view class="mode-btn {{viewMode === 'virtual' ? 'active' : ''}}" bindtap="changeViewMode" data-mode="virtual">
          <text>⚡</text>
        </view>
      </view>
    </view>

    <!-- 骨架屏加载效果 -->
    <loading-skeleton
      loading="{{loading && teaList.length === 0}}"
      type="card"
      count="{{6}}"
      class-name="tea-list-skeleton">
    </loading-skeleton>

    <!-- 错误状态 -->
    <view class="error-container" wx:if="{{showError}}">
      <text class="error-icon">😞</text>
      <text class="error-title">加载失败</text>
      <text class="error-desc">{{errorMessage}}</text>
      <button class="retry-btn" bindtap="retryLoad">重新加载</button>
    </view>

    <!-- 列表视图 -->
    <view class="tea-list" wx:if="{{viewMode === 'list' && !loading && !showError}}">
      <view class="tea-item-list" wx:for="{{teaList}}" wx:key="id" bindtap="viewDetail" data-id="{{item.id}}">
        <view class="tea-image-container">
          <image wx:if="{{item.main_image}}" src="{{item.main_image}}" class="tea-image" mode="aspectFill" binderror="onImageError" data-index="{{index}}"></image>
          <view wx:else class="tea-image-placeholder">
            <text class="placeholder-icon">🌱</text>
          </view>
          <view class="live-badge" wx:if="{{item.has_monitoring}}">
            <text class="live-text">LIVE</text>
            <view class="live-dot"></view>
          </view>
        </view>
        <view class="tea-content">
          <view class="tea-header">
            <text class="tea-name">{{item.name}}</text>
            <view class="tea-actions">
              <button class="btn-detail" bindtap="viewDetail" data-id="{{item.id}}">详情</button>
              <button class="btn-purchase {{(item.status || 'available') !== 'available' ? 'disabled' : ''}}"
                      bindtap="startPurchase"
                      data-id="{{item.id}}"
                      disabled="{{(item.status || 'available') !== 'available'}}">
                {{(item.status || 'available') === 'available' ? '认购' : '售完'}}
              </button>
            </view>
          </view>
          <text class="tea-location">📍 {{item.location}}</text>
          <text class="tea-description">{{item.description}}</text>
          <view class="tea-details">
            <view class="detail-item">
              <text class="label">面积:</text>
              <text class="value">{{item.area}}亩</text>
            </view>
            <view class="detail-item">
              <text class="label">价格:</text>
              <text class="value price">¥{{item.price}}/亩</text>
            </view>
          </view>
          <view class="tea-details">
            <view class="detail-item">
              <text class="label">预期收益:</text>
              <text class="value return">{{item.expected_return}}%</text>
            </view>
            <view class="detail-item">
              <text class="label">状态:</text>
              <text class="value status-text {{(item.status || 'available') === 'available' ? 'available' : 'sold_out'}}">
                {{(item.status || 'available') === 'available' ? '可认购' : '已售完'}}
              </text>
            </view>
          </view>
          <view class="tea-tags">
            <text class="tag" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 网格视图 -->
    <view class="tea-grid" wx:if="{{viewMode === 'grid' && !loading && !showError}}">
      <view class="tea-item-grid" wx:for="{{teaList}}" wx:key="id" bindtap="viewDetail" data-id="{{item.id}}">
        <view class="tea-image-container">
          <image wx:if="{{item.main_image}}" src="{{item.main_image}}" class="tea-image" mode="aspectFill" binderror="onImageError" data-index="{{index}}"></image>
          <view wx:else class="tea-image-placeholder">
            <text class="placeholder-icon">🌱</text>
          </view>
          <view class="live-badge" wx:if="{{item.has_monitoring}}">
            <text class="live-text">LIVE</text>
            <view class="live-dot"></view>
          </view>
        </view>
        <view class="tea-overlay">
          <view class="tea-status {{item.status}}">{{item.status_display}}</view>
        </view>
        <view class="tea-info">
          <text class="tea-name">{{item.name}}</text>
          <text class="tea-location">📍 {{item.location}}</text>
          <view class="tea-price-info">
            <text class="price">¥{{item.price}}/亩</text>
            <text class="return">{{item.expected_return}}%</text>
          </view>
          <view class="tea-area">{{item.area}}亩</view>
        </view>
      </view>
    </view>

    <!-- 虚拟滚动视图 -->
    <virtual-list
      wx:if="{{viewMode === 'virtual' && !loading && !showError}}"
      id="virtualList"
      items="{{teaList}}"
      item-height="{{120}}"
      height="calc(100vh - 400rpx)"
      buffer-size="{{5}}"
      unique-key="id"
      display-field="name"
      has-more="{{hasMore}}"
      loading-more="{{loading}}"
      show-load-more="{{true}}"
      refresher-enabled="{{true}}"
      enable-back-to-top="{{true}}"
      show-scroll-indicator="{{true}}"
      show-performance-monitor="{{false}}"
      binditemtap="onVirtualItemTap"
      bindloadmore="loadMore"
      bindrefresh="onPullDownRefresh"
      class-name="tea-virtual-list">

      <!-- 自定义茶地项目模板 -->
      <template slot="item">
        <view class="virtual-tea-item" data-id="{{item.id}}">
          <view class="virtual-tea-image">
            <image wx:if="{{item.main_image}}" src="{{item.main_image}}"
                   class="tea-thumb"
                   mode="aspectFill"
                   lazy-load="{{true}}"></image>
            <view wx:else class="tea-thumb-placeholder">
              <text class="placeholder-icon">🌱</text>
            </view>
            <view class="live-badge" wx:if="{{item.has_monitoring}}">
              <text class="live-text">LIVE</text>
            </view>
          </view>

          <view class="virtual-tea-content">
            <view class="tea-title">{{item.name}}</view>
            <view class="tea-location">📍 {{item.location}}</view>
            <view class="tea-info">
              <text class="tea-price">¥{{item.price}}/亩</text>
              <text class="tea-area">{{item.area}}亩</text>
            </view>
          </view>

          <view class="virtual-tea-actions">
            <button class="btn-mini" bindtap="viewDetail" data-id="{{item.id}}">详情</button>
            <button class="btn-mini primary" bindtap="startPurchase" data-id="{{item.id}}">认购</button>
          </view>
        </view>
      </template>
    </virtual-list>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && viewMode !== 'virtual'}}">
      <view class="loading" wx:if="{{loading}}">
        <text class="loading-text">加载中...</text>
      </view>
      <button class="load-more-btn" wx:else bindtap="loadMore">加载更多</button>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{teaList.length === 0 && !loading}}">
      <view class="empty-icon">🍃</view>
      <text class="empty-text">暂无茶地数据</text>
      <button class="refresh-btn" bindtap="refreshData">刷新</button>
    </view>
  </view>
</view>

<!-- 筛选弹窗 -->
<view class="filter-modal" wx:if="{{showFilterModal}}" bindtap="hideFilter">
  <view class="filter-content" catchtap="stopPropagation">
    <view class="filter-header">
      <text class="filter-title">筛选条件</text>
      <view class="filter-close" bindtap="hideFilter">×</view>
    </view>
    
    <scroll-view class="filter-body" scroll-y="{{true}}">
      <!-- 地区筛选 -->
      <view class="filter-group">
        <text class="group-title">地区</text>
        <view class="option-list">
          <view class="option-item {{filters.region === item.value ? 'selected' : ''}}" 
                wx:for="{{regionOptions}}" wx:key="value"
                bindtap="selectFilter" data-type="region" data-value="{{item.value}}">
            <text>{{item.label}}</text>
          </view>
        </view>
      </view>

      <!-- 价格筛选 -->
      <view class="filter-group">
        <text class="group-title">价格区间</text>
        <view class="option-list">
          <view class="option-item {{filters.priceRange === item.value ? 'selected' : ''}}" 
                wx:for="{{priceRangeOptions}}" wx:key="value"
                bindtap="selectFilter" data-type="priceRange" data-value="{{item.value}}">
            <text>{{item.label}}</text>
          </view>
        </view>
      </view>

      <!-- 收益率筛选 -->
      <view class="filter-group">
        <text class="group-title">预期收益率</text>
        <view class="option-list">
          <view class="option-item {{filters.returnRange === item.value ? 'selected' : ''}}" 
                wx:for="{{returnRangeOptions}}" wx:key="value"
                bindtap="selectFilter" data-type="returnRange" data-value="{{item.value}}">
            <text>{{item.label}}</text>
          </view>
        </view>
      </view>

      <!-- 面积筛选 -->
      <view class="filter-group">
        <text class="group-title">茶地面积</text>
        <view class="option-list">
          <view class="option-item {{filters.areaRange === item.value ? 'selected' : ''}}" 
                wx:for="{{areaRangeOptions}}" wx:key="value"
                bindtap="selectFilter" data-type="areaRange" data-value="{{item.value}}">
            <text>{{item.label}}</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <view class="filter-footer">
      <button class="reset-btn" bindtap="resetFilters">重置</button>
      <button class="confirm-btn" bindtap="applyFilters">确定</button>
    </view>
  </view>
  </view>

  <!-- 三级联动选择器 -->
  <hierarchy-selector
    show="{{hierarchySelector.show}}"
    default-value="{{hierarchySelector.defaultValue}}"
    bindconfirm="onHierarchyConfirm"
    bindcancel="onHierarchyCancel">
  </hierarchy-selector>
</page-transition>

<!-- 自定义底部导航 -->
<custom-tabbar
  id="custom-tabbar"
  config="{{customTabBarConfig}}"
  current="{{currentTabIndex}}"
  bind:tabchange="onCustomTabChange"
/>

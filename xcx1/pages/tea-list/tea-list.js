// 茶地列表页
const app = getApp()
const api = require('../../api/index.js')
const { teaFieldAPI } = require('../../utils/apis.js')
const { formatTeaImageUrl } = require('../../utils/util')
const { LoadingManager, PageLoadingMixin } = require('../../utils/loading.js')
const { Navigation } = require('../../utils/navigation.js')
const { globalScrollOptimizer } = require('../../utils/scroll-optimizer.js')
const { globalSearchManager } = require('../../utils/search.js')

Page(Object.assign({}, PageLoadingMixin, {
  data: {
    // 自定义底部导航
    customTabBarConfig: null,
    currentTabIndex: 1,
    // 页面标题
    pageTitle: '茶地列表',
    pageSubtitle: '发现优质茶园投资机会',

    // 搜索和筛选
    searchKeyword: '',
    showFilterModal: false,
    presetFilter: 'all', // 预设筛选
    filters: {
      region: '',
      priceRange: '',
      returnRange: '',
      areaRange: '',
      // 三级联动筛选
      garden: null,
      gardenRegion: null,
      plot: null
    },
    filterTags: [],

    // 三级联动选择器
    hierarchySelector: {
      show: false,
      defaultValue: {}
    },

    // 排序
    sortBy: 'default',
    sortOrder: 'desc',

    // 视图模式
    viewMode: 'list', // list | grid | virtual

    // 茶地数据
    teaList: [],
    totalCount: 0,
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,

    // 错误状态
    showError: false,
    errorMessage: '',

    // 筛选选项
    regionOptions: [],
    priceRangeOptions: [],
    returnRangeOptions: [],
    areaRangeOptions: [],
    categoryOptions: [],

    // 搜索防抖定时器
    searchTimer: null,
    searchLoading: false,

    // 筛选条件计数
    filterCount: 0
  },

  // 页面加载
  onLoad(options) {
    // 处理页面参数和动态标题
    this.updatePageTitle(options)

    try {
      // 加载筛选选项
      this.loadFilterOptions()

      // 加载茶地数据
      this.loadTeaList()
    } catch (error) {
      console.error('❌ 页面初始化失败:', error)
      this.setData({ loading: false })
    }
  
    // 初始化自定义底部导航
    this.initCustomTabBar()
  },

  // 更新页面标题
  updatePageTitle(options = {}) {
    let title = '茶地列表'
    let subtitle = '发现优质茶园投资机会'

    // 根据来源参数调整标题
    if (options.action === 'purchase') {
      title = '选择茶地认购'
      subtitle = '选择您心仪的茶地进行认购'
      wx.setNavigationBarTitle({ title })
    } else if (options.garden) {
      title = `${options.garden} - 茶地列表`
      subtitle = '该茶园的优质茶地'
    } else if (options.region) {
      title = `${options.region} - 茶地列表`
      subtitle = '该区域的优质茶地'
    } else if (options.variety) {
      title = `${options.variety} - 茶地列表`
      subtitle = '精选优质茶地'
    }

    this.setData({
      pageTitle: title,
      pageSubtitle: subtitle
    })
  },

  // 页面显示
  onShow() {
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData()
  },

  // 上拉加载
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  // 刷新数据
  refreshData() {
    this.setData({
      currentPage: 1,
      teaList: [],
      hasMore: true
    })

    // 重新加载数据
    this.loadTeaList()

    // 延迟停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 加载筛选选项
  loadFilterOptions() {
    // 调用筛选选项API获取真实数据
    api.teaFieldApi.getFilterOptions().then(res => {
      if (res.code === 200 && res.data) {
        // 使用后台返回的真实筛选选项
        this.setData({
          regionOptions: res.data.regions || [],
          priceRangeOptions: res.data.price_ranges || [],
          returnRangeOptions: res.data.return_ranges || [],
          areaRangeOptions: res.data.area_ranges || [],
          categoryOptions: res.data.categories || []
        })
      } else {
        this.setDefaultFilterOptions()
      }
    }).catch(error => {
      console.error('❌ 加载筛选选项失败:', error)
      // 使用默认筛选选项
      this.setDefaultFilterOptions()
    })
  },

  // 设置默认筛选选项
  setDefaultFilterOptions() {
    this.setData({
      regionOptions: this.getDefaultRegionOptions(),
      priceRangeOptions: this.getDefaultPriceRangeOptions(),
      returnRangeOptions: this.getDefaultReturnRangeOptions(),
      areaRangeOptions: this.getDefaultAreaRangeOptions()
    })
  },

  // 获取默认地区选项
  getDefaultRegionOptions() {
    return [
      { label: '全部地区', value: '' },
      { label: '浙江', value: 'zhejiang' },
      { label: '福建', value: 'fujian' },
      { label: '安徽', value: 'anhui' },
      { label: '江苏', value: 'jiangsu' },
      { label: '云南', value: 'yunnan' },
      { label: '四川', value: 'sichuan' }
    ]
  },

  // 获取默认价格范围选项
  getDefaultPriceRangeOptions() {
    return [
      { label: '全部价格', value: '' },
      { label: '1000以下', value: '0-1000' },
      { label: '1000-3000', value: '1000-3000' },
      { label: '3000-5000', value: '3000-5000' },
      { label: '5000-8000', value: '5000-8000' },
      { label: '8000以上', value: '8000-' }
    ]
  },

  // 获取默认收益范围选项
  getDefaultReturnRangeOptions() {
    return [
      { label: '全部收益', value: '' },
      { label: '5%以下', value: '0-5' },
      { label: '5%-10%', value: '5-10' },
      { label: '10%-15%', value: '10-15' },
      { label: '15%-20%', value: '15-20' },
      { label: '20%以上', value: '20-' }
    ]
  },

  // 获取默认面积范围选项
  getDefaultAreaRangeOptions() {
    return [
      { label: '全部面积', value: '' },
      { label: '1亩以下', value: '0-1' },
      { label: '1-5亩', value: '1-5' },
      { label: '5-10亩', value: '5-10' },
      { label: '10-20亩', value: '10-20' },
      { label: '20亩以上', value: '20-' }
    ]
  },

  // 加载茶地列表
  loadTeaList() {
    if (this.data.loading) return
    // 使用统一的加载状态管理
    this.setData({ loading: true })
    this.showLocalLoading('teaList', {
      skeleton: this.data.teaList.length === 0,
      skeletonType: 'card'
    })

    const params = {
      page: this.data.currentPage,
      page_size: this.data.pageSize,
      search: this.data.searchKeyword,
      ...this.buildFilterParams(),
      ...this.buildSortParams()
    }
    // 调用公开的茶地列表API
    api.teaFieldApi.getList(params).then(res => {
      // 处理API返回的数据格式
      let results = []
      let count = 0
      let hasNext = false

      if (res && res.code === 200 && res.data) {
        // 处理不同的数据格式
        if (res.data.results && Array.isArray(res.data.results)) {
          // 分页格式
          results = res.data.results
          count = res.data.count || 0
          hasNext = res.data.next !== null
        } else if (Array.isArray(res.data)) {
          // 简单数组格式
          results = res.data
          count = results.length
          hasNext = false
        }
      }

      if (results && results.length >= 0) {
        // 处理图片URL和监控状态
        const processedResults = results.map(tea => {
          // 处理图片URL - 优先使用cover_image
          let imageUrl = null
          if (tea.cover_image) {
            if (tea.cover_image.image_url) {
              imageUrl = tea.cover_image.image_url
            } else if (tea.cover_image.image) {
              imageUrl = tea.cover_image.image.startsWith('http') ?
                tea.cover_image.image :
                `https://teabuy.yizhangkj.com${tea.cover_image.image}`
            }
          }

          return {
            ...tea,
            // 映射新的字段名到前端期望的字段名
            name: tea.plot_name || tea.name || '未知茶地',
            location: tea.region?.name || tea.region?.garden?.location || tea.location || '未知位置',
            description: this.generateTeaDescription(tea),
            main_image: imageUrl,
            has_monitoring: tea.has_monitoring || false,
            status: tea.status || 'available',
            expected_return: tea.expected_return || '8-12',
            // 添加一些标签用于显示
            tags: tea.tags || [
              tea.region?.tea_variety || '绿茶',
              tea.status === 'available' ? '可认购' : '已售完',
              `${tea.expected_return || 15}%收益`
            ]
          }
        })

        const newList = this.data.currentPage === 1 ? processedResults : [...this.data.teaList, ...processedResults]

        this.setData({
          teaList: newList,
          totalCount: count,
          hasMore: hasNext,
          currentPage: this.data.currentPage + 1
        })
      } else {
        if (this.data.currentPage === 1) {
          this.setData({ teaList: [] })
        }
      }
    }).catch(error => {
      console.error('❌ 加载茶地列表失败:', error)

      // 显示更友好的错误提示
      this.showErrorMessage(error)

      // 如果是首次加载失败，使用模拟数据进行测试
      if (this.data.currentPage === 1) {
        this.setMockData()
      }
    }).finally(() => {
      this.setData({ loading: false })
      this.hideLocalLoading('teaList')
    })
  },

  // 设置模拟数据
  setMockData() {
    const mockData = [
      {
        id: 1,
        name: '云南西双版纳勐海县',
        location: '云南省西双版纳州勐海县',
        description: '云南西双版纳核心产区，面积价格预期收益',
        area: 10,
        price: 7500,
        expected_return: 25,
        status: 'available',
        status_display: '可认购',
        main_image: null, // 不使用默认图片，从后台获取
        has_monitoring: true,
        tags: ['普洱茶', '可认购', '25%收益']
      },
      {
        id: 8,
        name: '福建泉州市安溪县',
        location: '福建省泉州市安溪县',
        description: '安溪县核心产区，面积价格预期收益',
        area: 8,
        price: 6000,
        expected_return: 12,
        status: 'available',
        status_display: '可认购',
        main_image: null, // 不使用默认图片，从后台获取
        has_monitoring: false,
        tags: ['铁观音', '可认购', '12%收益']
      }
    ]

    this.setData({
      teaList: mockData,
      totalCount: mockData.length,
      hasMore: false,
      loading: false,
      showError: false
    })
  },

  // 构建筛选参数
  buildFilterParams() {
    const params = {}
    const { filters, presetFilter } = this.data

    // 预设筛选
    if (presetFilter && presetFilter !== 'all') {
      if (presetFilter === 'available') {
        params.status = 'available'
      } else {
        // 茶叶品种筛选
        const teaTypeMap = {
          'longjing': '龙井茶',
          'biluochun': '碧螺春',
          'tieguanyin': '铁观音',
          'wulong': '乌龙茶',
          'maofeng': '毛峰茶'
        }
        if (teaTypeMap[presetFilter]) {
          params.tea_type = teaTypeMap[presetFilter]
        }
      }
    }

    if (filters.region) {
      params.region = filters.region
    }

    if (filters.priceRange) {
      const [min, max] = filters.priceRange.split('-')
      if (min) params.price_min = min
      if (max) params.price_max = max
    }

    if (filters.returnRange) {
      const [min, max] = filters.returnRange.split('-')
      if (min) params.return_min = min
      if (max) params.return_max = max
    }

    if (filters.areaRange) {
      const [min, max] = filters.areaRange.split('-')
      if (min) params.area_min = min
      if (max) params.area_max = max
    }

    return params
  },

  // 构建排序参数
  buildSortParams() {
    const params = {}
    
    if (this.data.sortBy !== 'default') {
      const orderPrefix = this.data.sortOrder === 'desc' ? '-' : ''
      params.ordering = orderPrefix + this.data.sortBy
    }
    
    return params
  },

  // 预设筛选
  onPresetFilter(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      presetFilter: type,
      currentPage: 1
    })
    this.loadTeaList()
  },

  // 智能搜索事件
  onSmartSearch(e) {
    const { keyword, category } = e.detail
    // 更新搜索状态
    this.setData({
      searchKeyword: keyword,
      searchCategory: category || 'tea_name',
      currentPage: 1
    })

    // 执行搜索
    this.performSearch(keyword, category)
  },

  // 搜索类别变化
  onCategoryChange(e) {
    const { category } = e.detail
    this.setData({ searchCategory: category })

    // 如果有搜索关键词，重新搜索
    if (this.data.searchKeyword) {
      this.performSearch(this.data.searchKeyword, category)
    }
  },

  // 搜索输入 - 带防抖处理
  onSearchInput(e) {
    const keyword = e.detail.value.trim()
    this.setData({ searchKeyword: keyword })

    // 清除之前的定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer)
    }

    // 如果搜索关键词为空，立即搜索
    if (!keyword) {
      this.performSearch(keyword)
      return
    }

    // 设置搜索加载状态
    this.setData({ searchLoading: true })

    // 设置新的定时器，500ms后执行搜索
    const timer = setTimeout(() => {
      this.performSearch(keyword)
    }, 500)

    this.setData({ searchTimer: timer })
  },

  // 执行搜索 - 点击搜索按钮或确认搜索
  onSearch() {
    // 清除防抖定时器，立即执行搜索
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer)
      this.setData({ searchTimer: null })
    }

    this.performSearch(this.data.searchKeyword)
  },

  // 实际执行搜索的方法
  performSearch(keyword, category = 'tea_name') {
    try {
      this.setData({
        searchLoading: true,
        currentPage: 1,
        hasMore: true,
        showError: false
      })

      // 构建搜索参数
      const searchParams = {
        keyword: keyword || '',
        category: category || 'tea_name',
        page: 1,
        page_size: this.data.pageSize,
        ...this.buildFilterParams()
      }

      // 调用搜索API - 使用茶地列表API进行搜索
      this.setData({ searchKeyword: keyword })
      this.loadTeaList()

      // 搜索完成后的处理在loadTeaList中完成
      /*
      if (result && result.results) {
        const processedList = result.results.map(item => ({
          ...item,
          main_image: formatTeaImageUrl(item.main_image)
        }))

        this.setData({
          teaList: processedList,
          totalCount: result.total || 0,
          hasMore: result.results.length >= this.data.pageSize,
          currentPage: 1
        })

        }
      */

    } catch (error) {
      console.error('搜索失败:', error)
      this.setData({
        showError: true,
        errorMessage: error.message || '搜索失败，请稍后重试'
      })

      wx.showToast({
        title: '搜索失败',
        icon: 'none',
        duration: 2000
      })
    } finally {
      this.setData({
        searchLoading: false,
        searchTimer: null
      })
    }
  },

  // 构建筛选参数
  buildFilterParams() {
    const params = {}

    // 预设筛选
    if (this.data.presetFilter !== 'all') {
      params.tea_type = this.data.presetFilter
    }

    // 自定义筛选
    const { filters } = this.data
    if (filters.region) params.region = filters.region
    if (filters.priceRange) params.price_range = filters.priceRange
    if (filters.returnRange) params.return_range = filters.returnRange
    if (filters.areaRange) params.area_range = filters.areaRange

    // 三级联动筛选
    if (filters.garden) {
      params.garden_id = filters.garden.id
    }
    if (filters.gardenRegion) {
      params.region_id = filters.gardenRegion.id
    }
    if (filters.plot) {
      params.plot_id = filters.plot.id
    }

    // 排序
    if (this.data.sortBy !== 'default') {
      params.sort_by = this.data.sortBy
      params.sort_order = this.data.sortOrder
    }

    return params
  },

  // 清空搜索
  clearSearch() {
    // 清除定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer)
    }

    this.setData({
      searchKeyword: '',
      searchTimer: null,
      searchLoading: false
    })

    // 重新加载数据
    this.refreshData()
  },

  // 排序
  onSort(e) {
    const { type } = e.currentTarget.dataset
    let { sortBy, sortOrder } = this.data

    if (sortBy === type) {
      // 切换排序方向
      sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'
    } else {
      // 切换排序字段
      sortBy = type
      sortOrder = 'desc'
    }

    this.setData({ sortBy, sortOrder })
    this.refreshData()
  },

  // 切换视图模式
  changeViewMode(e) {
    const { mode } = e.currentTarget.dataset
    this.setData({ viewMode: mode })

    // 如果切换到虚拟滚动模式，优化滚动性能
    if (mode === 'virtual') {
      this.optimizeVirtualScroll()
    }
  },

  // 优化虚拟滚动
  optimizeVirtualScroll() {
    // 使用滚动优化器
    const optimizedConfig = globalScrollOptimizer.optimizeList({
      selector: '#virtualList',
      itemSelector: '.virtual-tea-item',
      onScroll: (e) => {
        // 处理滚动事件
      },
      onLoadMore: () => {
        this.loadMore()
      }
    })
  },

  // 虚拟列表项目点击
  onVirtualItemTap(e) {
    const { item } = e.detail
    this.viewDetail({ currentTarget: { dataset: { id: item.id } } })
  },

  // 显示筛选弹窗
  showFilter() {
    // 加载筛选选项（如果还没有加载）
    if (this.data.regionOptions.length === 0) {
      this.loadFilterOptions()
    }
    this.setData({ showFilterModal: true })
  },

  // 隐藏筛选弹窗
  hideFilter() {
    this.setData({ showFilterModal: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击筛选内容时关闭弹窗
  },

  // 选择筛选条件
  selectFilter(e) {
    const { type, value } = e.currentTarget.dataset
    this.setData({
      [`filters.${type}`]: value
    })
  },

  // 重置筛选
  resetFilters() {
    this.setData({
      filters: {
        region: '',
        priceRange: '',
        returnRange: '',
        areaRange: ''
      }
    })
  },

  // 应用筛选
  applyFilters() {
    this.updateFilterTags()
    this.updateFilterCount()
    this.hideFilter()
    this.refreshData()
  },

  // 更新筛选条件计数
  updateFilterCount() {
    const { filters, presetFilter } = this.data
    let count = 0

    // 计算预设筛选
    if (presetFilter !== 'all') {
      count++
    }

    // 计算自定义筛选
    Object.values(filters).forEach(value => {
      if (value && value !== '') {
        count++
      }
    })

    this.setData({ filterCount: count })
  },

  // 更新筛选标签
  updateFilterTags() {
    const tags = []
    const { filters } = this.data

    // 三级联动位置标签
    if (filters.garden || filters.gardenRegion || filters.plot) {
      let locationLabel = '位置：'

      if (filters.garden) {
        locationLabel += filters.garden.garden_name
      }

      if (filters.gardenRegion) {
        locationLabel += ' → ' + filters.gardenRegion.name
      }

      if (filters.plot) {
        locationLabel += ' → ' + (filters.plot.plot_name || '地块' + filters.plot.plot_number)
      }

      tags.push({
        key: 'hierarchy',
        label: locationLabel,
        type: 'hierarchy' // 特殊类型，用于特殊处理
      })
    }

    // 地区标签
    if (filters.region) {
      const option = this.data.regionOptions.find(item => item.value === filters.region)
      if (option) {
        tags.push({ key: 'region', label: option.label })
      }
    }

    // 价格标签
    if (filters.priceRange) {
      const option = this.data.priceRangeOptions.find(item => item.value === filters.priceRange)
      if (option) {
        tags.push({ key: 'priceRange', label: option.label })
      }
    }

    // 收益标签
    if (filters.returnRange) {
      const option = this.data.returnRangeOptions.find(item => item.value === filters.returnRange)
      if (option) {
        tags.push({ key: 'returnRange', label: option.label })
      }
    }

    // 面积标签
    if (filters.areaRange) {
      const option = this.data.areaRangeOptions.find(item => item.value === filters.areaRange)
      if (option) {
        tags.push({ key: 'areaRange', label: option.label })
      }
    }

    this.setData({ filterTags: tags })
  },

  // 移除筛选标签
  removeFilter(e) {
    const { key } = e.currentTarget.dataset

    // 特殊处理三级联动标签
    if (key === 'hierarchy') {
      this.clearHierarchySelection()
      return
    }

    this.setData({
      [`filters.${key}`]: ''
    })
    this.updateFilterTags()
    this.refreshData()
  },

  // 清空所有筛选
  clearAllFilters() {
    this.resetFilters()
    this.setData({ filterTags: [] })
    this.refreshData()
  },

  // 加载更多
  loadMore() {
    this.loadTeaList()
  },

  // 图片加载错误处理
  onImageError(e) {
    const { index } = e.currentTarget.dataset
    const teaList = this.data.teaList

    if (teaList[index]) {
      // 图片加载失败时设置为null，让模板显示占位符
      teaList[index].main_image = null
      this.setData({
        [`teaList[${index}].main_image`]: null
      })
    }
  },

  // 查看详情
  viewDetail(e) {
    const { id } = e.currentTarget.dataset
    // Debug log removed

    if (!id || id === 'undefined') {
      console.error('❌ 茶地ID无效，无法跳转')
      wx.showToast({
        title: '茶地信息错误',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/tea-detail/tea-detail?id=${id}`
    })
  },

  // 开始认购
  startPurchase(e) {
    e.stopPropagation() // 阻止事件冒泡
    const { id } = e.currentTarget.dataset
    if (!id || id === 'undefined') {
      console.error('❌ 茶地ID无效，无法认购')
      wx.showToast({
        title: '茶地信息错误',
        icon: 'none'
      })
      return
    }

    // 检查登录状态
    if (!app.checkLogin()) {
      return
    }

    wx.navigateTo({
      url: `/pages/tea-detail/tea-detail?id=${id}&action=purchase`
    })
  },

  // 图片加载错误处理
  onImageError(e) {
    const index = e.currentTarget.dataset.index
    if (index !== undefined) {
      const teaList = this.data.teaList
      // 图片加载失败时设置为null，让模板显示占位符
      teaList[index].main_image = null
      this.setData({ teaList })
    }
  },

  // 显示错误信息
  showErrorMessage(error) {
    let message = '加载失败，请重试'

    if (error.code) {
      switch (error.code) {
        case 'NETWORK_ERROR':
          message = '网络连接异常，请检查网络设置'
          break
        case 'TIMEOUT':
          message = '请求超时，请稍后重试'
          break
        case 'SERVER_ERROR':
          message = '服务器异常，请稍后重试'
          break
        default:
          message = error.message || '未知错误'
      }
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
  },

  // 重试加载
  retryLoad() {
    this.setData({
      showError: false,
      currentPage: 1,
      teaList: []
    })
    this.loadTeaList()
  },

  // 重置并加载茶地列表
  resetAndLoadTeaList() {
    this.setData({
      currentPage: 1,
      teaList: [],
      hasMore: true
    })
    this.loadTeaList()
  },

  // ===== 三级联动选择器相关方法 =====

  // 显示三级联动选择器
  showHierarchySelector() {
    // 设置默认值（如果已有选择）
    const defaultValue = {}
    if (this.data.filters.garden) {
      defaultValue.garden = this.data.filters.garden
    }
    if (this.data.filters.gardenRegion) {
      defaultValue.region = this.data.filters.gardenRegion
    }
    if (this.data.filters.plot) {
      defaultValue.plot = this.data.filters.plot
    }

    this.setData({
      'hierarchySelector.show': true,
      'hierarchySelector.defaultValue': defaultValue
    })
  },

  // 三级联动选择确认
  onHierarchyConfirm(e) {
    const selection = e.detail
    // 更新筛选条件
    const newFilters = {
      ...this.data.filters,
      garden: selection.garden,
      gardenRegion: selection.region,
      plot: selection.plot
    }

    this.setData({
      filters: newFilters,
      'hierarchySelector.show': false
    })

    // 更新筛选标签
    this.updateFilterTags()

    // 重新加载茶地列表
    this.resetAndLoadTeaList()

    // 显示选择结果
    this.showHierarchySelectionResult(selection)
  },

  // 三级联动选择取消
  onHierarchyCancel() {
    this.setData({
      'hierarchySelector.show': false
    })
  },

  // 显示选择结果
  showHierarchySelectionResult(selection) {
    let message = '已选择：'

    if (selection.garden) {
      message += selection.garden.garden_name
    }

    if (selection.region) {
      message += ' → ' + selection.region.name
    }

    if (selection.plot) {
      message += ' → ' + (selection.plot.plot_name || '地块' + selection.plot.plot_number)
    }

    if (!selection.garden) {
      message = '已清空位置选择'
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  },

  // 清除三级联动选择
  clearHierarchySelection() {
    this.setData({
      'filters.garden': null,
      'filters.gardenRegion': null,
      'filters.plot': null
    })

    // 更新筛选标签
    this.updateFilterTags()

    // 重新加载茶地列表
    this.resetAndLoadTeaList()

    wx.showToast({
      title: '已清空位置选择',
      icon: 'none'
    })
  },

  // 生成茶地简介描述
  generateTeaDescription(tea) {
    // 如果有现成的详细描述，优先使用
    if (tea.description && tea.description.trim() && tea.description.length > 20) {
      return tea.description
    }

    // 生成详细描述
    const plotName = tea.plot_name || '茶地'
    const gardenName = tea.region?.garden?.garden_name || '茶园'
    const location = tea.region?.garden?.location || '优质产区'
    const regionName = tea.region?.name || '茶区'
    const teaVariety = tea.region?.tea_variety || '绿茶'
    const area = tea.area || 1
    const price = tea.price || 0
    const fullCode = tea.full_code || ''

    // 构建更详细的描述文本
    let description = ''

    // 开头：位置和基本信息
    description += `${plotName}位于${location}，`

    // 茶园和区域信息
    if (gardenName && regionName && regionName !== gardenName) {
      description += `隶属于${gardenName}的${regionName}，`
    } else if (gardenName) {
      description += `隶属于${gardenName}，`
    }

    // 茶叶品种和特色
    if (teaVariety && teaVariety !== '绿茶') {
      description += `专门种植${teaVariety}品种，`
    } else {
      description += `种植优质茶叶，`
    }

    // 环境和品质描述
    const environmentDesc = this.getEnvironmentDescription(location, teaVariety)
    description += environmentDesc

    // 面积和价格信息
    description += `该地块占地面积${area}亩，`

    if (price > 0) {
      description += `认购价格为¥${price}/亩，`
    }

    // 投资价值描述
    const investmentDesc = this.getInvestmentDescription(teaVariety, price, area)
    description += investmentDesc

    // 编码信息
    if (fullCode) {
      description += `地块编号：${fullCode}。`
    }

    return description
  },

  // 获取环境描述
  getEnvironmentDescription(location, teaVariety) {
    const locationDescMap = {
      '广东省潮州市凤凰镇': '得益于凤凰山独特的地理环境和气候条件，',
      '广东省潮州市凤凰镇南万村': '南万村拥有悠久的茶叶种植历史，山清水秀，',
      '汕尾市陆河县': '陆河县山区气候宜人，土壤肥沃，'
    }

    const varietyDescMap = {
      '单枞': '单枞茶以其独特的香韵和口感著称，',
      '鸭屎香': '鸭屎香是凤凰单枞中的珍品，香气浓郁，',
      '白叶': '白叶单枞茶质优良，叶色独特，'
    }

    let envDesc = ''

    // 根据位置添加环境描述
    for (const [loc, desc] of Object.entries(locationDescMap)) {
      if (location && location.includes(loc.split('省')[1]?.split('市')[0])) {
        envDesc += desc
        break
      }
    }

    if (!envDesc) {
      envDesc += '当地自然环境优越，气候适宜，'
    }

    // 根据茶叶品种添加特色描述
    for (const [variety, desc] of Object.entries(varietyDescMap)) {
      if (teaVariety && teaVariety.includes(variety)) {
        envDesc += desc
        break
      }
    }

    if (!envDesc.includes('茶')) {
      envDesc += '茶叶品质上乘，'
    }

    return envDesc
  },

  // 获取投资价值描述
  getInvestmentDescription(teaVariety, price, area) {
    let investDesc = ''

    if (price > 20000) {
      investDesc += '属于高端茶园地块，投资价值较高，'
    } else if (price > 15000) {
      investDesc += '性价比优良，适合中长期投资，'
    } else {
      investDesc += '价格亲民，是茶叶投资的理想选择，'
    }

    if (area >= 2) {
      investDesc += '面积适中，便于管理和收益，'
    } else {
      investDesc += '小而精致，易于精细化管理，'
    }

    investDesc += '预期收益稳定，适合茶叶爱好者和投资者认购。'

    return investDesc
  }
,

  // ==================== 自定义底部导航相关方法 ====================

  // 初始化自定义底部导航
  async initCustomTabBar() {
    try {
      const app = getApp()
      let config = app.globalData.customTabBarConfig
      if (!config) {
        await app.reloadCustomTabBar()
        config = app.globalData.customTabBarConfig
      }
      this.setData({ customTabBarConfig: config })
      this.updateCustomTabBarIndex()
    } catch (error) {
      console.error('❌ 茶地列表初始化自定义底部导航失败:', error)
    }
  },

  // 更新自定义导航选中状态
  updateCustomTabBarIndex() {
    const app = getApp()
    const currentIndex = app.getCurrentTabIndex()
    this.setData({ currentTabIndex: currentIndex })
    const customTabBar = this.selectComponent('#custom-tabbar')
    if (customTabBar) {
      customTabBar.setData({ current: currentIndex })
    }
  },

  // 自定义导航切换事件
  onCustomTabChange(e) {
    const { index, item } = e.detail
    const app = getApp()
    app.navigateToPage(item.pagePath)
  }
}))

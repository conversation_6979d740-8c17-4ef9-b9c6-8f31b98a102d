// 认购成功页面
const app = getApp()
const api = require('../../api/index.js')

Page({
  data: {
    // 订单信息
    orderNumber: '',
    paymentTime: '',
    paymentMethodText: '',
    totalAmount: '',
    
    // 茶园信息
    teaField: {},
    purchaseArea: 0,
    
    // 收益预测
    earningsProjection: [],
    totalProjectedEarnings: 0,
    totalReturnRate: 0,
    
    // 分享相关
    showShareModal: false
  },

  // 页面加载
  onLoad(options) {
    if (options.orderNumber) {
      this.setData({ orderNumber: options.orderNumber })
      this.loadOrderInfo()
    }
    
    this.setPaymentTime()
  },

  // 页面显示
  onShow() {
  },

  // 分享
  onShareAppMessage() {
    const teaFieldId = this.data.teaField?.id
    if (!teaFieldId || teaFieldId === 'undefined') {
      console.error('❌ 分享时茶地ID无效')
      return {
        title: '两山·茶管家 - 优质茶地认购平台',
        path: '/pages/index/index'
      }
    }

    return {
      title: `我刚刚认购了${this.data.teaField.name}，预期年收益${this.data.teaField.expected_return}%！`,
      path: `/pages/tea-detail/tea-detail?id=${teaFieldId}&from=share`,
      imageUrl: this.data.teaField.main_image
    }
  },

  // 设置支付时间
  setPaymentTime() {
    const now = new Date()
    const paymentTime = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    
    this.setData({
      paymentTime,
      paymentMethodText: '微信支付'
    })
  },

  // 加载订单信息
  async loadOrderInfo() {
    try {
      // 调用订单详情API
      const res = await api.orderApi.getDetail(this.data.orderNumber)

      if (res.code === 200) {
        const orderInfo = res.data

        this.setData({
          teaField: orderInfo.tea_field,
          purchaseArea: orderInfo.area,
          totalAmount: orderInfo.total_amount
        })

        this.calculateEarningsProjection()
      } else {
        throw new Error(res.message || '加载订单信息失败')
      }
    } catch (error) {
      console.error('加载订单信息失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 计算收益预测
  calculateEarningsProjection() {
    const { teaField, purchaseArea, totalAmount } = this.data
    const investmentAmount = parseFloat(totalAmount)
    const yearlyReturnRate = teaField.expected_return / 100
    const contractPeriod = teaField.contract_period
    
    const projection = []
    let totalEarnings = 0
    
    for (let year = 1; year <= contractPeriod; year++) {
      const yearlyEarnings = investmentAmount * yearlyReturnRate
      totalEarnings += yearlyEarnings
      
      projection.push({
        year,
        amount: yearlyEarnings.toFixed(0),
        percentage: (year / contractPeriod) * 100
      })
    }
    
    const totalReturnRate = ((totalEarnings / investmentAmount) * 100).toFixed(1)
    
    this.setData({
      earningsProjection: projection,
      totalProjectedEarnings: totalEarnings.toFixed(0),
      totalReturnRate
    })
  },

  // 复制订单号
  copyOrderNumber() {
    wx.setClipboardData({
      data: this.data.orderNumber,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        })
      }
    })
  },

  // 查看我的茶地
  viewMyFields() {
    wx.switchTab({
      url: '/pages/my-fields/my-fields'
    })
  },

  // 查看监控
  viewMonitoring() {
    wx.navigateTo({
      url: `/pages/monitoring/monitoring?teaId=${this.data.teaField.id}`
    })
  },

  // 查看合同
  viewContract() {
    wx.showToast({
      title: '合同生成中，请稍后查看',
      icon: 'none'
    })
  },

  // 邀请好友
  inviteFriends() {
    this.setData({ showShareModal: true })
  },

  // 隐藏分享弹窗
  hideShareModal() {
    this.setData({ showShareModal: false })
  },

  // 分享到朋友圈
  shareToMoments() {
    wx.showToast({
      title: '请使用右上角分享功能',
      icon: 'none'
    })
  },

  // 复制分享链接
  copyShareLink() {
    const teaFieldId = this.data.teaField?.id
    if (!teaFieldId || teaFieldId === 'undefined') {
      console.error('❌ 复制链接时茶地ID无效')
      wx.showToast({
        title: '茶地信息错误',
        icon: 'none'
      })
      return
    }

    const shareLink = `https://teabuy.com/tea-detail/${teaFieldId}?from=share`

    wx.setClipboardData({
      data: shareLink,
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success'
        })
        this.hideShareModal()
      }
    })
  },

  // 返回首页
  backToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击分享弹窗内容时关闭弹窗
  }
})

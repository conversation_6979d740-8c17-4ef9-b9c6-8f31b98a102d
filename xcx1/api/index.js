/**
 * API接口定义
 */

const { get, post, put, del, upload } = require('./request.js')

// 用户相关API
const userApi = {
  // 微信登录
  wechatLogin(data) {
    return post('/users/login/', data)
  },
  
  // 获取用户信息
  getProfile() {
    return get('/users/profile/')
  },
  
  // 更新用户信息
  updateProfile(data) {
    return put('/users/profile/update/', data)
  },
  
  // 手机号登录
  phoneLogin(data) {
    return post('/users/phone-login/', data)
  },
  
  // 发送短信验证码
  sendSms(data) {
    return post('/users/send-sms/', data)
  },

  // 获取用户统计
  getStats() {
    return get('/analytics/user-stats/')
  },

  // 获取安全信息 (使用用户资料API作为替代)
  getSecurityInfo() {
    return get('/users/profile/')
  },

  // 获取登录日志
  getLoginLogs() {
    return get('/users/login-logs/')
  },

  // 获取操作日志
  getOperationLogs() {
    return get('/users/operation-logs/')
  },

  // 注销账户 (使用用户资料API作为替代)
  deleteAccount() {
    return post('/users/profile/update/', { is_active: false })
  },

  // 更新通知设置
  updateNotificationSettings(data) {
    return put('/users/notification-settings/update/', data)
  },

  // 更新隐私设置 (使用用户资料更新API作为替代)
  updatePrivacySettings(data) {
    return put('/users/profile/update/', data)
  },

  // 头像上传 (文件上传)
  uploadAvatar(filePath) {
    return upload('/users/avatar-upload/', filePath, 'avatar')
  },

  // 头像上传 (Base64)
  uploadAvatarBase64(avatarBase64) {
    return post('/users/avatar-upload-base64/', { avatar_base64: avatarBase64 })
  },

  // 绑定手机号
  bindPhone(data) {
    return post('/users/bind-phone/', data)
  },

  // 解绑手机号
  unbindPhone(data) {
    return post('/users/unbind-phone/', data)
  },

  // 修改密码
  changePassword(data) {
    return post('/users/change-password/', data)
  }
}

// 茶地相关API
const teaFieldApi = {
  // 获取茶地分类
  getCategories() {
    return get('/tea-fields/categories/')
  },
  
  // 获取茶地列表
  getList(params = {}) {
    return get('/tea-fields/', params)
  },
  
  // 获取茶地详情
  getDetail(id) {
    return get(`/tea-fields/${id}/`)
  },
  
  // 获取茶地区域
  getRegions() {
    return get('/tea-field-regions/')
  },

  // 茶地对比
  compare(data) {
    return post('/tea-fields/compare/', data)
  },

  // 三级联动相关API
  // 获取茶园列表
  getGardens() {
    return get('/tea-fields/gardens/')
  },

  // 获取茶区域列表（根据茶园筛选）
  getRegionsByGarden(gardenId = null) {
    const params = gardenId ? { garden: gardenId } : {}
    return get('/tea-field-regions/', params)
  },

  // 获取茶地块列表（根据茶区域筛选）
  getPlotsByRegion(regionId = null) {
    const params = regionId ? { region: regionId } : {}
    return get('/tea-field-plots/', params)
  },

  // 根据三级选择获取茶地列表
  getFieldsByHierarchy(params = {}) {
    return get('/tea-fields/', params)
  },

  // 获取筛选选项
  getFilterOptions() {
    return get('/tea-fields/filter-options/')
  }
}

// 订单相关API
const orderApi = {
  // 获取订单列表
  getList(params = {}) {
    return get('/orders/', params)
  },

  // 创建订单
  create(data) {
    return post('/orders/create/', data)
  },

  // 获取订单详情
  getDetail(orderId) {
    return get(`/orders/${orderId}/`)
  },

  // 支付订单
  pay(orderId, data) {
    return post(`/orders/${orderId}/pay/`, data)
  },

  // 取消订单
  cancel(orderId) {
    return post(`/orders/${orderId}/cancel/`)
  },

  // 删除订单
  delete(orderId) {
    return del(`/orders/${orderId}/`)
  }
}

// 监控相关API
const monitoringApi = {
  // 获取实时监控数据
  getRealtime(fieldId) {
    return get('/monitoring/realtime/', { field_id: fieldId })
  },

  // 获取设备列表
  getDevices(fieldId) {
    return get('/monitoring/devices/', { field_id: fieldId })
  },

  // 获取告警历史
  getAlerts(fieldId) {
    return get('/monitoring/alerts/', { field_id: fieldId })
  },

  // 获取历史数据
  getHistory(fieldId, params = {}) {
    return get('/monitoring/history/', { field_id: fieldId, ...params })
  },

  // 获取摄像头列表 (通过设备API获取)
  async getCameras(fieldId) {
    try {
      const devicesRes = await get('/monitoring/devices/', { field_id: fieldId })

      if (devicesRes.code === 200 && devicesRes.data) {
        // 从设备列表中筛选摄像头设备
        const cameras = devicesRes.data.filter(device =>
          device.device_type === 'camera' || device.name.includes('摄像头') || device.name.includes('camera')
        ).map((device, index) => ({
          id: device.id || `cam${index + 1}`,
          name: device.name || `${index + 1}号摄像头`,
          icon: '📹',
          status: device.status || 'online',
          description: device.description || `茶园监控视角${index + 1}`
        }))

        return {
          code: 200,
          data: cameras.length > 0 ? cameras : [
            { id: 'cam1', name: '1号摄像头', icon: '📹', status: 'online', description: '茶园全景视角' },
            { id: 'cam2', name: '2号摄像头', icon: '🎥', status: 'online', description: '茶树特写视角' },
            { id: 'cam3', name: '3号摄像头', icon: '📷', status: 'online', description: '加工车间监控' },
            { id: 'cam4', name: '4号摄像头', icon: '🎬', status: 'offline', description: '仓储区域监控' }
          ]
        }
      } else {
        throw new Error('获取设备列表失败')
      }
    } catch (error) {
      // 返回默认摄像头数据
      return {
        code: 200,
        data: [
          { id: 'cam1', name: '1号摄像头', icon: '📹', status: 'online', description: '茶园全景视角' },
          { id: 'cam2', name: '2号摄像头', icon: '🎥', status: 'online', description: '茶树特写视角' },
          { id: 'cam3', name: '3号摄像头', icon: '📷', status: 'online', description: '加工车间监控' },
          { id: 'cam4', name: '4号摄像头', icon: '🎬', status: 'offline', description: '仓储区域监控' }
        ]
      }
    }
  }
}

// 分析相关API
const analyticsApi = {
  // 获取收益分析
  getEarnings() {
    return get('/analytics/earnings/')
  },

  // 获取数据概览
  getOverview(params = {}) {
    return get('/analytics/overview/', params)
  },

  // 获取市场数据 (使用用户统计API作为替代)
  getMarketData() {
    return get('/analytics/user-stats/')
  },

  // 获取收益图表
  getRevenueChart(params = {}) {
    return get('/analytics/earnings-chart/', params)
  },

  // 获取环境数据
  getEnvironment(params = {}) {
    return get('/analytics/environment/', params)
  },

  // 获取产量数据
  getProduction(params = {}) {
    return get('/analytics/production/', params)
  },

  // 获取用户统计
  getUserStats(params = {}) {
    return get('/analytics/user-stats/', params)
  }
}

// 收益相关API
const earningsApi = {
  // 获取收益列表
  getList(params = {}) {
    return get('/analytics/earnings/', params)
  },

  // 获取收益详情
  getDetail(id) {
    return get(`/analytics/earnings/${id}/`)
  },

  // 申请提现
  withdraw(data) {
    return post('/payments/', data)
  },

  // 获取收益概览
  getOverview() {
    return get('/analytics/earnings/')
  },

  // 获取收益图表
  getChart(params = {}) {
    return get('/analytics/earnings-chart/', params)
  }
}

// 通用API
const commonApi = {
  // 获取轮播图
  getBanners() {
    return get('/banners/')
  },
  
  // 获取系统配置
  getSystemConfig() {
    return get('/system/config/')
  },
  
  // 获取协议内容
  getAgreement(type) {
    return get(`/agreements/${type}/`)
  },
  
  // 获取公司信息
  getCompanyInfo() {
    return get('/common/company-info/')
  },
  
  // 文件上传
  uploadFile(filePath) {
    return upload('/upload/file/', filePath)
  },

  // 获取数据概览
  getOverview() {
    return get('/overview/')
  },

  // 获取首页统计数据
  getHomeStats() {
    return get('/dashboard/overview/')
  }
}

// 反馈相关API
const feedbackApi = {
  // 获取反馈类型 (使用系统配置作为替代)
  getTypes() {
    return get('/system/config/')
  },

  // 获取历史反馈 (使用通知API作为替代)
  getHistory() {
    return get('/notifications/')
  },

  // 提交反馈 (使用文件上传API作为替代)
  submit(data) {
    return post('/upload/file/', data)
  }
}

// 帮助相关API
const helpApi = {
  // 获取热门问题 (使用茶叶资讯作为替代)
  getHotQuestions() {
    return get('/common/tea-news/')
  },

  // 获取帮助分类 (使用茶叶资讯作为替代)
  getCategories() {
    return get('/common/tea-news/')
  },

  // 获取所有问题 (使用茶叶资讯作为替代)
  getAllQuestions() {
    return get('/common/tea-news/')
  }
}

// 收获相关API
const harvestApi = {
  // 获取收获提醒 (使用通知API作为替代)
  getReminder() {
    return get('/notifications/')
  },

  // 获取收获通知 (使用通知API作为替代)
  getNotifications() {
    return get('/notifications/')
  }
}

// 合同相关API
const contractApi = {
  // 获取合同列表
  getList(params = {}) {
    return get('/enterprise/contracts/', params)
  },

  // 下载合同
  download(id) {
    return get(`/enterprise/contracts/${id}/download/`)
  }
}

// 收藏相关API
const favoriteApi = {
  // 切换收藏状态 (使用收藏检查API)
  toggle(teaId, action) {
    return get(`/favorites/check/${teaId}/`)
  }
}

// 我的茶地相关API
const myFieldsApi = {
  // 获取我的茶地统计数据
  getStats() {
    return get('/analytics/my-fields-stats/')
  },

  // 获取我的茶地列表 (使用订单API，因为用户的茶地就是已购买的订单)
  getList(params = {}) {
    return get('/orders/', params)
  },

  // 获取茶地详情
  getFieldDetail(id) {
    return get(`/tea-fields/${id}/`)
  }
}

// 发票相关API
const invoiceApi = {
  // 获取发票列表
  getList(params = {}) {
    return get('/enterprise/invoices/', params)
  }
}

// 邀请相关API
const inviteApi = {
  // 获取邀请数据 (使用分析用户统计API作为替代)
  getInviteData() {
    return get('/analytics/user-stats/')
  }
}

// 消息相关API
const messageApi = {
  // 获取消息列表
  getList(params = {}) {
    return get('/notifications/', params)
  },

  // 获取未读消息数
  getUnreadCount() {
    return get('/notifications/unread-count/')
  },

  // 标记所有为已读
  markAllAsRead() {
    return post('/notifications/mark-all-read/')
  },

  // 删除消息
  delete(id) {
    return post(`/notifications/${id}/read/`)
  }
}

// 认证相关API
const authApi = {
  // 获取认证状态汇总
  getStatus() {
    return get('/verification/status/')
  },

  // 获取认证信息（兼容旧接口）
  getInfo() {
    return get('/verification/info/')
  },

  // 获取认证历史
  getHistory(params = {}) {
    return get('/verification/history/', params)
  },

  // 提交个人认证
  submitPersonal(data) {
    return post('/verification/personal/submit/', data)
  },

  // 提交企业认证
  submitEnterprise(data) {
    return post('/verification/enterprise/submit/', data)
  },

  // 提交银行卡认证
  submitBankCard(data) {
    return post('/verification/bank-card/submit/', data)
  },

  // 提交认证（通用接口，兼容旧版本）
  submit(data) {
    return post('/verification/submit/', data)
  },

  // 人脸识别
  faceRecognition(data) {
    return post('/verification/submit/', data)
  }
}

// 支付相关API
const paymentApi = {
  // 创建支付（统一接口，支持微信支付等）
  create(data) {
    return post('/payments/', data)
  },

  // 微信支付（保持兼容性）
  wechatPay(data, queryParams = {}) {
    // 构建查询字符串
    const queryString = Object.keys(queryParams).length > 0
      ? '?' + Object.entries(queryParams).map(([key, value]) => `${key}=${encodeURIComponent(value)}`).join('&')
      : ''

    return post(`/payments/${queryString}`, { ...data, payment_method: 'wechat' })
  },

  // 其他支付
  otherPay(data) {
    return post('/payments/', { ...data, payment_method: 'other' })
  },

  // 查询支付状态
  getStatus(paymentId) {
    return get(`/payments/${paymentId}/status/`)
  },

  // 测试微信支付配置
  testConfig() {
    return get('/payments/wechat/test-config/')
  }
}

// 上传相关API
const uploadApi = {
  // 上传文件
  uploadFile(filePath) {
    return upload('/upload/file/', filePath)
  },

  // 上传头像
  uploadAvatar(data) {
    return post('/upload/avatar-base64/', data)
  }
}

// 新闻相关API
const newsApi = {
  // 获取新闻列表
  getList(params = {}) {
    return get('/common/tea-news/', params)
  },

  // 获取新闻详情
  getDetail(newsId) {
    return get(`/common/tea-news/${newsId}/`)
  }
}

// 3D沙盘相关API
const sandboxApi = {
  // 获取茶地数据
  getTeaData(params = {}) {
    return get('/sandbox/tea-data/', params)
  },

  // 获取实时数据
  getRealtimeData(params = {}) {
    return get('/sandbox/real-time/', params)
  },

  // 获取配置信息
  getConfig() {
    return get('/sandbox/config/')
  },

  // 获取统计数据
  getStatistics() {
    return get('/sandbox/statistics/')
  },

  // 更新配置
  updateConfig(data) {
    return post('/sandbox/config/', data)
  }
}

// AI相关API
const aiApi = {
  // 获取投资建议
  getInvestmentAdvice(params = {}) {
    return get('/ai/investment-advice/', params)
  },

  // AI聊天对话
  chat(data) {
    return post('/ai/chat/', data)
  },

  // 获取用户画像分析
  getUserProfile(params = {}) {
    return get('/ai/user-profile/', params)
  },

  // 茶地风险评估
  assessRisk(data) {
    return post('/ai/risk-assessment/', data)
  },

  // 收益预测
  predictReturn(data) {
    return post('/ai/return-prediction/', data)
  }
}

// 视频号相关API
const videoApi = {
  // 获取视频号列表
  getChannelVideos(params = {}) {
    return get('/video/channel-videos/', params)
  },

  // 获取视频详情
  getVideoDetail(id) {
    return get(`/video/videos/${id}/`)
  },

  // 视频播放统计
  recordPlay(data) {
    return post('/video/play-record/', data)
  },

  // 视频点赞
  likeVideo(data) {
    return post('/video/like/', data)
  },

  // 获取视频统计数据
  getVideoStats(params = {}) {
    return get('/video/stats/', params)
  }
}

module.exports = {
  userApi,
  teaFieldApi,
  orderApi,
  monitoringApi,
  analyticsApi,
  earningsApi,
  commonApi,
  feedbackApi,
  helpApi,
  harvestApi,
  contractApi,
  favoriteApi,
  myFieldsApi,
  invoiceApi,
  inviteApi,
  messageApi,
  authApi,
  paymentApi,
  uploadApi,
  newsApi,
  sandboxApi,
  aiApi,
  videoApi
}

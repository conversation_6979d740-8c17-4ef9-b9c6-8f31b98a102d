/**
 * 网络请求封装 (增强版)
 */

const BASE_URL = 'https://teabuy.yizhangkj.com/api/v1'
const { cacheManager, performanceMonitor } = require('../utils/cache.js')

// 错误处理工具
const ErrorHandler = {
  // 错误码映射
  errorCodes: {
    400: '请求参数错误',
    401: '登录已过期',
    403: '权限不足',
    404: '请求的资源不存在',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务暂不可用',
    504: '网关超时'
  },

  // 处理HTTP错误
  handleHttpError(statusCode, data) {
    const message = this.errorCodes[statusCode] || '网络错误'

    if (statusCode === 401) {
      // token过期处理
      this.handleTokenExpired()
    } else {
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      })
    }

    return { code: statusCode, message, data }
  },

  // 处理业务错误
  handleBusinessError(error) {
    const message = error.message || '操作失败'

    // 特殊错误码处理
    if (error.code === 40001) {
      this.handleTokenExpired()
      return
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  },

  // 处理Token过期
  handleTokenExpired() {
    wx.removeStorageSync('token')
    wx.removeStorageSync('userInfo')

    // 清除全局状态
    const app = getApp()
    if (app) {
      app.globalData.isLogin = false
      app.globalData.userInfo = null
      app.globalData.token = ''
    }

    wx.showModal({
      title: '提示',
      content: '登录已过期，请重新登录',
      showCancel: false,
      success: () => {
        wx.reLaunch({
          url: '/pages/login/login'
        })
      }
    })
  },

  // 处理网络错误
  handleNetworkError(error) {
    console.error('网络错误:', error)

    let message = '网络连接失败'
    if (error.errMsg) {
      if (error.errMsg.includes('timeout')) {
        message = '请求超时，请检查网络'
      } else if (error.errMsg.includes('fail')) {
        message = '网络连接失败，请检查网络设置'
      }
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }
}

// 请求拦截器
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 获取token
    const token = wx.getStorageSync('token')
    
    // 设置请求头
    const header = {
      'Content-Type': 'application/json',
      ...options.header
    }
    
    if (token) {
      header['Authorization'] = `Token ${token}`
    }
    
    // 发起请求
    wx.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header,
      success: (res) => {
        // 处理响应
        if (res.statusCode === 200 || res.statusCode === 201) {
          if (res.data.code === 200) {
            resolve(res.data)
          } else {
            // 业务错误
            reject(res.data)
          }
        } else if (res.statusCode === 401) {
          // token过期，清除登录状态
          // 清除本地存储
          wx.removeStorageSync('token')
          wx.removeStorageSync('userInfo')

          // 清除全局状态
          const app = getApp()
          if (app) {
            app.globalData.isLogin = false
            app.globalData.userInfo = null
            app.globalData.token = ''
          }

          // 静默处理401错误，不显示提示
          reject({
            code: 401,
            message: '未授权',
            silent: true
          })
        } else {
          // HTTP错误
          console.error('HTTP错误:', res.statusCode, res.data)
          reject({
            code: res.statusCode,
            message: '网络请求失败',
            data: res.data
          })
        }
      },
      fail: (err) => {
        console.error('API请求失败:', options.url, err)
        wx.showToast({
          title: '网络连接失败',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

// GET请求
const get = (url, data = {}) => {
  return request({
    url,
    method: 'GET',
    data
  })
}

// POST请求
const post = (url, data = {}) => {
  return request({
    url,
    method: 'POST',
    data
  })
}

// PUT请求
const put = (url, data = {}) => {
  return request({
    url,
    method: 'PUT',
    data
  })
}

// DELETE请求
const del = (url, data = {}) => {
  return request({
    url,
    method: 'DELETE',
    data
  })
}

// 文件上传 (优化版本)
const upload = (url, filePath, name = 'file', formData = {}) => {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token')
    const header = {}

    if (token) {
      header['Authorization'] = `Token ${token}`
    }

    // 显示上传进度
    wx.showLoading({
      title: '上传中...',
      mask: true
    })

    const uploadTask = wx.uploadFile({
      url: BASE_URL + url,
      filePath,
      name,
      formData,
      header,
      success: (res) => {
        wx.hideLoading()
        try {
          const data = JSON.parse(res.data)
          if (data.code === 200) {
            wx.showToast({
              title: '上传成功',
              icon: 'success'
            })
            resolve(data)
          } else {
            wx.showToast({
              title: data.message || '上传失败',
              icon: 'none'
            })
            reject(data)
          }
        } catch (e) {
          console.error('数据解析失败:', e)
          wx.showToast({
            title: '数据解析失败',
            icon: 'none'
          })
          reject({ code: 500, message: '数据解析失败' })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        console.error('文件上传失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        reject(err)
      }
    })

    // 监听上传进度
    uploadTask.onProgressUpdate((res) => {
    })
  })
}

module.exports = {
  request,
  get,
  post,
  put,
  del,
  upload
}

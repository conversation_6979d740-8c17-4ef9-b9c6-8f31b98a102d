/* 三级联动选择器组件样式 */
.hierarchy-selector {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

/* 遮罩层 */
.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

/* 选择器内容 */
.selector-content {
  position: relative;
  width: 100%;
  max-height: 85vh;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 头部 */
.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 32rpx;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #f8fffe 0%, #f0f9ff 100%);
  border-radius: 24rpx 24rpx 0 0;
}

.header-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  letter-spacing: 1rpx;
}

.header-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  font-size: 28rpx;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}

.reset-btn {
  color: #22c55e;
  background-color: rgba(34, 197, 94, 0.1);
}

.close-btn {
  color: #999;
  font-size: 32rpx;
  font-weight: bold;
}

/* 错误提示 */
.error-tip {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: #fef2f2;
  border-left: 4rpx solid #ef4444;
  margin: 0 32rpx 24rpx;
  border-radius: 8rpx;
}

.error-text {
  font-size: 28rpx;
  color: #dc2626;
}

.retry-btn {
  font-size: 28rpx;
  color: #22c55e;
  padding: 8rpx 16rpx;
  background-color: rgba(34, 197, 94, 0.1);
  border-radius: 8rpx;
}

/* 选择器主体 */
.selector-body {
  flex: 1;
  overflow: hidden;
  padding: 24rpx 40rpx;
  background-color: #fafbfc;
}

/* 级别区域 */
.level-section {
  margin-bottom: 40rpx;
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f1f5f9;
}

.level-title {
  font-size: 34rpx;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.level-title::before {
  content: '';
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-radius: 4rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

/* 级别列表 */
.level-list {
  max-height: 280rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  background-color: #ffffff;
  overflow: hidden;
}

.level-item {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 1px solid #f1f5f9;
  background-color: white;
  transition: all 0.3s ease;
  gap: 24rpx;
  position: relative;
}

.level-item:last-child {
  border-bottom: none;
}

.level-item.selected {
  background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
  border-left: 6rpx solid #22c55e;
  transform: translateX(8rpx);
}

.level-item:active {
  background-color: #f8fafc;
  transform: scale(0.98);
}

.level-item:hover {
  background-color: #f8fafc;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 图片样式 */
.item-image {
  width: 96rpx;
  height: 96rpx;
  border-radius: 16rpx;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  font-size: 32rpx;
  color: #9ca3af;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-left: 8rpx;
}

.item-code {
  font-size: 26rpx;
  color: #22c55e;
  font-weight: 600;
  background: rgba(34, 197, 94, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  align-self: flex-start;
}

.item-name {
  font-size: 32rpx;
  color: #1e293b;
  font-weight: 600;
  line-height: 1.4;
}

.item-location {
  font-size: 26rpx;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.item-location::before {
  content: '📍';
  font-size: 24rpx;
}

.item-area {
  font-size: 24rpx;
  color: #666;
}

.item-price {
  font-size: 24rpx;
  color: #22c55e;
  font-weight: 500;
}

.item-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
  margin-right: 16rpx;
}

.item-area {
  font-size: 24rpx;
  color: #666;
}

.item-status {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  color: white;
}

.status-available {
  background-color: #22c55e;
}

.status-sold {
  background-color: #ef4444;
}

.status-reserved {
  background-color: #f59e0b;
}

.selected-icon {
  font-size: 32rpx;
  color: #22c55e;
  font-weight: bold;
}

.empty-tip {
  padding: 48rpx 24rpx;
  text-align: center;
  font-size: 28rpx;
  color: #999;
}

/* 底部 */
.selector-footer {
  padding: 24rpx 32rpx 32rpx;
  border-top: 1px solid #f0f0f0;
  background-color: white;
}

.current-selection {
  margin-bottom: 24rpx;
  padding: 16rpx;
  background-color: #f9fafb;
  border-radius: 8rpx;
}

.selection-label {
  font-size: 24rpx;
  color: #666;
}

.selection-path {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.footer-actions {
  display: flex;
  gap: 24rpx;
}

.action-button {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}

.cancel-button {
  background-color: #f3f4f6;
  color: #374151;
}

.confirm-button.enabled {
  background-color: #22c55e;
  color: white;
}

.confirm-button.disabled {
  background-color: #e5e7eb;
  color: #9ca3af;
}

// 三级联动选择器组件
const api = require('../../api/index.js')

Component({
  properties: {
    // 是否显示选择器
    show: {
      type: Boolean,
      value: false
    },
    // 初始选择值
    defaultValue: {
      type: Object,
      value: {}
    }
  },

  data: {
    // 数据源
    gardens: [],      // 茶园列表
    regions: [],      // 茶区域列表
    plots: [],        // 茶地块列表
    
    // 当前选择
    selectedGarden: null,
    selectedRegion: null,
    selectedPlot: null,
    
    // 加载状态
    loading: {
      gardens: false,
      regions: false,
      plots: false
    },
    
    // 错误状态
    error: null
  },

  lifetimes: {
    attached() {
      this.loadGardens()
    }
  },

  observers: {
    'show': function(show) {
      if (show) {
        this.resetSelection()
        this.loadGardens()
      }
    },
    
    'defaultValue': function(defaultValue) {
      if (defaultValue && Object.keys(defaultValue).length > 0) {
        this.setDefaultSelection(defaultValue)
      }
    }
  },

  methods: {
    // 加载茶园列表
    loadGardens() {
      this.setData({
        'loading.gardens': true,
        error: null
      })

      api.teaFieldApi.getGardens().then(res => {
        if (res.code === 200 && res.data) {
          this.setData({
            gardens: res.data,
            'loading.gardens': false
          })
        } else {
          this.setData({
            error: '加载茶园列表失败',
            'loading.gardens': false
          })
        }
      }).catch(error => {
        console.error('❌ 茶园列表加载失败:', error)
        this.setData({
          error: '网络错误，请重试',
          'loading.gardens': false
        })
      })
    },

    // 加载茶区域列表
    loadRegions(gardenId) {
      this.setData({
        'loading.regions': true,
        regions: [],
        selectedRegion: null,
        plots: [],
        selectedPlot: null
      })

      api.teaFieldApi.getRegionsByGarden(gardenId).then(res => {
        if (res.code === 200 && res.data) {
          this.setData({
            regions: res.data,
            'loading.regions': false
          })
        } else {
          this.setData({
            error: '加载茶区域列表失败',
            'loading.regions': false
          })
        }
      }).catch(error => {
        console.error('❌ 茶区域列表加载失败:', error)
        this.setData({
          error: '网络错误，请重试',
          'loading.regions': false
        })
      })
    },

    // 加载茶地块列表
    loadPlots(regionId) {
      this.setData({
        'loading.plots': true,
        plots: [],
        selectedPlot: null
      })

      api.teaFieldApi.getPlotsByRegion(regionId).then(res => {
        if (res.code === 200 && res.data) {
          this.setData({
            plots: res.data,
            'loading.plots': false
          })
        } else {
          this.setData({
            error: '加载茶地块列表失败',
            'loading.plots': false
          })
        }
      }).catch(error => {
        console.error('❌ 茶地块列表加载失败:', error)
        this.setData({
          error: '网络错误，请重试',
          'loading.plots': false
        })
      })
    },

    // 选择茶园
    onGardenSelect(e) {
      const gardenId = parseInt(e.currentTarget.dataset.id)
      const garden = this.data.gardens.find(item => item.id === gardenId)
      this.setData({
        selectedGarden: garden,
        selectedRegion: null,
        selectedPlot: null,
        regions: [],
        plots: []
      })
      
      if (gardenId) {
        this.loadRegions(gardenId)
      }
    },

    // 选择茶区域
    onRegionSelect(e) {
      const regionId = parseInt(e.currentTarget.dataset.id)
      const region = this.data.regions.find(item => item.id === regionId)
      this.setData({
        selectedRegion: region,
        selectedPlot: null,
        plots: []
      })

      if (regionId) {
        this.loadPlots(regionId)
      }
    },

    // 选择茶地块
    onPlotSelect(e) {
      const plotId = parseInt(e.currentTarget.dataset.id)
      const plot = this.data.plots.find(item => item.id === plotId)
      this.setData({
        selectedPlot: plot
      })
    },

    // 重置选择
    resetSelection() {
      this.setData({
        selectedGarden: null,
        selectedRegion: null,
        selectedPlot: null,
        regions: [],
        plots: []
      })
    },

    // 设置默认选择
    setDefaultSelection(defaultValue) {
      if (defaultValue.garden) {
        this.setData({ selectedGarden: defaultValue.garden })
        this.loadRegions(defaultValue.garden.id)
      }
      
      if (defaultValue.region) {
        this.setData({ selectedRegion: defaultValue.region })
        this.loadPlots(defaultValue.region.id)
      }
      
      if (defaultValue.plot) {
        this.setData({ selectedPlot: defaultValue.plot })
      }
    },

    // 确认选择
    onConfirm() {
      const selection = {
        garden: this.data.selectedGarden,
        region: this.data.selectedRegion,
        plot: this.data.selectedPlot
      }
      this.triggerEvent('confirm', selection)
    },

    // 取消选择
    onCancel() {
      this.triggerEvent('cancel')
    },

    // 关闭选择器
    onClose() {
      this.onCancel()
    }
  }
})

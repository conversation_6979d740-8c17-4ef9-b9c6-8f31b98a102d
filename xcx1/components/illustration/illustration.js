/**
 * 插图组件
 */
Component({
  properties: {
    // 插图名称
    name: {
      type: String,
      value: 'default'
    },
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 描述
    description: {
      type: String,
      value: ''
    },
    // 是否显示操作按钮
    showAction: {
      type: Boolean,
      value: false
    },
    // 操作按钮文字
    actionText: {
      type: String,
      value: '重试'
    },
    // 自定义图片源（当name为custom时使用）
    customSrc: {
      type: String,
      value: ''
    },
    // 尺寸
    size: {
      type: String,
      value: 'normal' // small, normal, large
    },
    // 主题
    theme: {
      type: String,
      value: 'primary' // primary, secondary, success, warning, error, info
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 自定义类名
    className: {
      type: String,
      value: ''
    }
  },

  data: {
    // 预定义插图配置
    illustrationConfigs: {
      'empty-data': {
        title: '暂无数据',
        description: '当前没有可显示的内容',
        actionText: '刷新'
      },
      'network-error': {
        title: '网络连接失败',
        description: '请检查网络连接后重试',
        actionText: '重试'
      },
      'loading': {
        title: '加载中',
        description: '正在为您获取数据...',
        actionText: ''
      },
      'success': {
        title: '操作成功',
        description: '您的操作已成功完成',
        actionText: '确定'
      },
      'error': {
        title: '操作失败',
        description: '操作过程中发生错误，请重试',
        actionText: '重试'
      },
      'no-search-result': {
        title: '未找到相关内容',
        description: '试试其他关键词或筛选条件',
        actionText: '重新搜索'
      },
      'maintenance': {
        title: '系统维护中',
        description: '系统正在升级维护，请稍后再试',
        actionText: '知道了'
      },
      'no-permission': {
        title: '权限不足',
        description: '您没有访问此内容的权限',
        actionText: '联系管理员'
      }
    }
  },

  lifetimes: {
    attached() {
      this.initIllustration()
    }
  },

  observers: {
    'name': function(newName) {
      this.updateIllustrationConfig(newName)
    }
  },

  methods: {
    // 初始化插图
    initIllustration() {
      this.updateIllustrationConfig(this.data.name)
      this.updateClassName()
    },

    // 更新插图配置
    updateIllustrationConfig(name) {
      const config = this.data.illustrationConfigs[name]
      if (config) {
        // 如果没有自定义标题和描述，使用预定义的
        const title = this.data.title || config.title
        const description = this.data.description || config.description
        const actionText = this.data.actionText === '重试' ? config.actionText : this.data.actionText
        
        this.setData({
          title,
          description,
          actionText: actionText || this.data.actionText
        })
      }
    },

    // 更新类名
    updateClassName() {
      const classes = [this.data.className]
      
      // 添加尺寸类
      if (this.data.size && this.data.size !== 'normal') {
        classes.push(this.data.size)
      }
      
      // 添加主题类
      if (this.data.theme) {
        classes.push(`theme-${this.data.theme}`)
      }
      
      this.setData({
        className: classes.filter(Boolean).join(' ')
      })
    },

    // 操作按钮点击事件
    onActionTap() {
      // 触发自定义事件
      this.triggerEvent('action', {
        name: this.data.name,
        actionText: this.data.actionText
      })

      // 根据插图类型执行默认操作
      this.handleDefaultAction()
    },

    // 处理默认操作
    handleDefaultAction() {
      switch (this.data.name) {
        case 'network-error':
          this.handleNetworkRetry()
          break
        case 'empty-data':
          this.handleDataRefresh()
          break
        case 'error':
          this.handleErrorRetry()
          break
        case 'no-search-result':
          this.handleSearchReset()
          break
        case 'maintenance':
          this.handleMaintenanceAck()
          break
        case 'no-permission':
          this.handlePermissionContact()
          break
        default:
      }
    },

    // 处理网络重试
    handleNetworkRetry() {
      wx.showLoading({ title: '重试中...' })
      
      // 检查网络状态
      wx.getNetworkType({
        success: (res) => {
          wx.hideLoading()
          if (res.networkType === 'none') {
            wx.showToast({
              title: '请检查网络连接',
              icon: 'none'
            })
          } else {
            wx.showToast({
              title: '网络已连接',
              icon: 'success'
            })
          }
        },
        fail: () => {
          wx.hideLoading()
          wx.showToast({
            title: '网络检查失败',
            icon: 'none'
          })
        }
      })
    },

    // 处理数据刷新
    handleDataRefresh() {
      wx.showToast({
        title: '正在刷新...',
        icon: 'loading'
      })
      
      setTimeout(() => {
        wx.hideToast()
      }, 1500)
    },

    // 处理错误重试
    handleErrorRetry() {
      wx.showToast({
        title: '正在重试...',
        icon: 'loading'
      })
      
      setTimeout(() => {
        wx.hideToast()
      }, 1500)
    },

    // 处理搜索重置
    handleSearchReset() {
      wx.showToast({
        title: '重置搜索条件',
        icon: 'none'
      })
    },

    // 处理维护确认
    handleMaintenanceAck() {
      wx.showToast({
        title: '感谢您的理解',
        icon: 'none'
      })
    },

    // 处理权限联系
    handlePermissionContact() {
      wx.showModal({
        title: '联系管理员',
        content: '如需获取访问权限，请联系系统管理员',
        showCancel: false,
        confirmText: '知道了'
      })
    },

    // 设置插图名称
    setName(name) {
      this.setData({ name })
      this.updateIllustrationConfig(name)
    },

    // 设置标题
    setTitle(title) {
      this.setData({ title })
    },

    // 设置描述
    setDescription(description) {
      this.setData({ description })
    },

    // 设置操作按钮文字
    setActionText(actionText) {
      this.setData({ actionText })
    },

    // 显示操作按钮
    showActionButton(show = true) {
      this.setData({ showAction: show })
    },

    // 隐藏操作按钮
    hideActionButton() {
      this.setData({ showAction: false })
    },

    // 获取插图信息
    getIllustrationInfo() {
      return {
        name: this.data.name,
        title: this.data.title,
        description: this.data.description,
        showAction: this.data.showAction,
        actionText: this.data.actionText,
        size: this.data.size,
        theme: this.data.theme
      }
    }
  }
})

/* 装修板块组件样式 */

.decoration-block {
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

/* 布局样式 */
.layout-grid-2 .tea-grid,
.layout-grid-2 .nav-grid {
  display: flex;
  flex-wrap: wrap;
}

.layout-grid-2 .tea-item,
.layout-grid-2 .nav-item {
  width: 50%;
}

.layout-grid-3 .tea-grid,
.layout-grid-3 .nav-grid {
  display: flex;
  flex-wrap: wrap;
}

.layout-grid-3 .tea-item,
.layout-grid-3 .nav-item {
  width: 33.333%;
}

.layout-grid-4 .nav-grid {
  display: flex;
  flex-wrap: wrap;
}

.layout-grid-4 .nav-item {
  width: 25%;
}

.layout-list .tea-grid,
.layout-list .news-list {
  display: flex;
  flex-direction: column;
}

/* 轮播图板块 */
.block-banner {
  width: 100%;
  position: relative;
}

.banner-swiper {
  width: 100%;
  height: 400rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.6));
  color: white;
  padding: 20px 15px 15px;
}

.banner-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.banner-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

/* 轮播图覆盖层 */
.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(transparent 40%, rgba(0,0,0,0.7));
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 40rpx;
}

.banner-overlay .banner-content {
  position: static;
  background: none;
  padding: 0;
}

.banner-overlay .banner-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
}

.banner-overlay .banner-subtitle {
  font-size: 28rpx;
  color: white;
  opacity: 0.9;
  margin-bottom: 24rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
}

.banner-actions {
  display: flex;
  gap: 20rpx;
}

.btn-guide, .btn-browse {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 50rpx;
  font-size: 26rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.3);
  transition: all 0.3s ease;
}

.btn-guide {
  background: rgba(255,255,255,0.2);
  color: white;
  border: 2rpx solid rgba(255,255,255,0.3);
}

.btn-browse {
  background: #52c41a;
  color: white;
}

.btn-guide .icon, .btn-browse .icon {
  font-size: 24rpx;
}

/* 快捷导航板块 */
.block-quick-nav {
  width: 100%;
}

.nav-grid {
  display: flex;
  flex-wrap: wrap;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 5px;
  box-sizing: border-box;
}

.nav-icon {
  margin-bottom: 8px;
}

.icon-image {
  display: block;
}

.icon-image.size-small {
  width: 30px;
  height: 30px;
}

.icon-image.size-medium {
  width: 40px;
  height: 40px;
}

.icon-image.size-large {
  width: 50px;
  height: 50px;
}

.nav-title {
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
}

/* 茶园展示板块 */
.block-tea-list {
  width: 100%;
}

.tea-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tea-grid.layout-grid .tea-item {
  flex: 1;
  min-width: calc(50% - 5px);
  max-width: calc(50% - 5px);
}

.tea-grid.layout-list .tea-item {
  width: 100%;
  display: flex;
  margin-bottom: 10px;
}

.tea-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.tea-image-wrapper {
  position: relative;
  width: 100%;
  height: 120px;
}

.tea-grid.layout-list .tea-image-wrapper {
  width: 100px;
  height: 80px;
  flex-shrink: 0;
}

.tea-image {
  width: 100%;
  height: 100%;
}

.tea-info {
  padding: 10px;
}

.tea-grid.layout-list .tea-info {
  flex: 1;
  padding: 10px;
}

.tea-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tea-subtitle {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.tea-location {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
}

.tea-price {
  font-size: 16px;
  color: #ff4757;
  font-weight: bold;
}

.tea-sales {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 视频展示板块 */
.block-video {
  width: 100%;
}

.video-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.video-item {
  flex: 1;
  min-width: calc(33.333% - 7px);
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 100px;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  background: rgba(0,0,0,0.6);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.video-info {
  padding: 8px;
}

.video-title {
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-duration {
  font-size: 10px;
  color: #999;
  margin-top: 3px;
}

/* 公告通知板块 */
.block-notice {
  width: 100%;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
}

.notice-content {
  display: flex;
  align-items: center;
  padding: 8px 12px;
}

.notice-icon {
  margin-right: 8px;
  font-size: 14px;
}

.notice-swiper {
  flex: 1;
  height: 20px;
}

.notice-text {
  font-size: 12px;
  color: #d48806;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 资讯列表板块 */
.block-news {
  width: 100%;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.news-item {
  display: flex;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  padding: 10px;
}

.news-image {
  width: 80px;
  height: 60px;
  margin-right: 10px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.news-image .news-img {
  width: 100%;
  height: 100%;
}

.news-content {
  flex: 1;
}

.news-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.news-summary {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-time {
  font-size: 11px;
  color: #999;
}

/* 间距和分割线 */
.block-spacer {
  width: 100%;
}

.block-divider {
  width: 100%;
  padding: 10px 0;
}

.divider-line {
  width: 100%;
  height: 0;
  border-top: 1px solid #e0e0e0;
}

/* 自定义HTML */
.block-custom {
  width: 100%;
}

/* 未知板块 */
.block-unknown {
  width: 100%;
  padding: 20px;
  text-align: center;
}

.unknown-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.unknown-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.unknown-text {
  font-size: 12px;
}

/* 编辑模式 */
.edit-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(24, 144, 255, 0.1);
  border: 2px dashed #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.edit-info {
  background: rgba(24, 144, 255, 0.9);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  text-align: center;
}

.edit-type {
  font-size: 12px;
  font-weight: bold;
}

.edit-name {
  font-size: 10px;
  opacity: 0.8;
}

/* 数据概览板块 */
.block-stats-overview {
  padding: 30rpx;
  background: #fff;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stats-item {
  text-align: center;
  flex: 1;
}

.stats-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 10rpx;
}

.stats-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.stats-subtitle {
  font-size: 24rpx;
  color: #999;
}

.trend-prefix, .trend-suffix {
  color: #52c41a;
}

/* 新手引导板块 */
.block-guide-banner {
  margin: 20rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
  border-radius: 16rpx;
  border: 2rpx solid #ffd591;
}

.guide-content {
  display: flex;
  align-items: center;
}

.guide-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.guide-text {
  flex: 1;
}

.guide-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #d48806;
  margin-bottom: 8rpx;
}

.guide-subtitle {
  font-size: 26rpx;
  color: #ad6800;
  line-height: 1.4;
}

.guide-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.btn-start {
  background: #fa8c16;
  color: white;
  border: none;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.btn-later {
  background: transparent;
  color: #d48806;
  border: 2rpx solid #d48806;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
}

/* 快速入口板块 */
.block-features-grid {
  padding: 30rpx;
  background: #fff;
}

.features-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.features-grid.columns-4 .feature-item {
  width: calc(25% - 15rpx);
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border-radius: 16rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.feature-item.bg-green { background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%); }
.feature-item.bg-blue { background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%); }
.feature-item.bg-orange { background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%); }
.feature-item.bg-purple { background: linear-gradient(135deg, #f9f0ff 0%, #d3adf7 100%); }
.feature-item.bg-teal { background: linear-gradient(135deg, #e6fffb 0%, #87e8de 100%); }
.feature-item.bg-indigo { background: linear-gradient(135deg, #f0f5ff 0%, #adc6ff 100%); }
.feature-item.bg-pink { background: linear-gradient(135deg, #fff0f6 0%, #ffadd2 100%); }
.feature-item.bg-yellow { background: linear-gradient(135deg, #feffe6 0%, #eaff8f 100%); }

.feature-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.feature-title {
  font-size: 26rpx;
  color: #333;
  text-align: center;
}

/* 推荐茶地板块 */
.block-recommended-tea {
  padding: 30rpx;
  background: #fff;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #1890ff;
}

.tea-scroll {
  width: 100%;
}

.tea-list {
  display: flex;
  gap: 20rpx;
}

.tea-card {
  width: 280rpx;
  flex-shrink: 0;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.tea-image {
  width: 100%;
  height: 200rpx;
}

.tea-info {
  padding: 20rpx;
}

.tea-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.tea-location {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
  display: block;
}

.tea-stats {
  display: flex;
  justify-content: space-between;
}

.tea-price {
  font-size: 26rpx;
  color: #f5222d;
  font-weight: bold;
}

.tea-return {
  font-size: 26rpx;
  color: #52c41a;
  font-weight: bold;
}

/* AI投资建议板块 */
.block-ai-investment {
  padding: 30rpx;
  background: #fff;
}

.ai-card {
  background: linear-gradient(135deg, #f0f5ff 0%, #e6f7ff 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  border: 2rpx solid #d6e4ff;
}

.ai-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.ai-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.ai-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1890ff;
  flex: 1;
}

.btn-refresh {
  background: transparent;
  border: none;
  font-size: 24rpx;
  color: #1890ff;
  padding: 8rpx;
}

.ai-content {
  margin-bottom: 20rpx;
}

.advice-item {
  background: #fff;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  border-left: 4rpx solid #1890ff;
}

.advice-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  display: block;
  margin-bottom: 8rpx;
}

.advice-confidence {
  font-size: 24rpx;
  color: #52c41a;
}

.ai-loading, .ai-empty {
  text-align: center;
  padding: 40rpx 20rpx;
}

.loading-icon, .empty-icon {
  font-size: 48rpx;
  display: block;
  margin-bottom: 16rpx;
}

.loading-text, .empty-text {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 20rpx;
}

.btn-generate {
  background: #1890ff;
  color: white;
  border: none;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.ai-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.btn-chat, .btn-more {
  background: transparent;
  color: #1890ff;
  border: 2rpx solid #1890ff;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
}

/* 视频频道板块 */
.block-video-channel {
  padding: 30rpx;
  background: #fff;
}

.video-scroll {
  width: 100%;
}

.video-list {
  display: flex;
  gap: 20rpx;
}

.video-card {
  width: 280rpx;
  flex-shrink: 0;
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.video-image {
  width: 100%;
  height: 100%;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.play-icon {
  font-size: 48rpx;
  color: white;
  margin-bottom: 8rpx;
}

.video-duration {
  font-size: 24rpx;
  color: white;
  background: rgba(0,0,0,0.6);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.video-info {
  padding: 16rpx 0;
}

.video-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-stats {
  display: flex;
  gap: 20rpx;
}

.view-count, .like-count {
  font-size: 24rpx;
  color: #999;
}

/* 新闻列表板块 */
.block-news-list {
  padding: 30rpx;
  background: #fff;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.news-item {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.news-image {
  width: 200rpx;
  height: 150rpx;
  flex-shrink: 0;
}

.news-content {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.news-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.news-summary {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  display: block;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.news-badge {
  background: #f5222d;
  color: white;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.news-date {
  font-size: 24rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 64rpx;
  display: block;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  display: block;
  margin-bottom: 8rpx;
}

.empty-desc {
  font-size: 24rpx;
  display: block;
  opacity: 0.7;
}

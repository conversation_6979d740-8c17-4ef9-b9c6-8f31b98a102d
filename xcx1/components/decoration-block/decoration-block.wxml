<!-- 装修板块组件模板 -->
<view class="decoration-block {{getLayoutClass(processedConfig.layout_type)}}" 
      style="{{blockStyle}}" 
      wx:if="{{isVisible}}"
      bindtap="onBlockTap">

  <!-- 轮播图板块 -->
  <view wx:if="{{currentBlockType === 'banner'}}" class="block-banner">
    <swiper
      class="banner-swiper"
      autoplay="{{processedConfig.autoplay}}"
      interval="{{processedConfig.interval}}"
      indicator-dots="{{processedConfig.indicatorDots}}"
      circular="{{processedConfig.circular}}">
      <swiper-item
        wx:for="{{processedConfig.items}}"
        wx:key="id"
        data-item="{{item}}"
        bindtap="onItemTap">
        <image
          class="banner-image"
          src="{{item.image_url}}"
          mode="aspectFill"
          lazy-load="{{true}}" />
        <view class="banner-overlay" wx:if="{{processedConfig.show_overlay}}">
          <view class="banner-content">
            <text class="banner-title">{{item.title}}</text>
            <text class="banner-subtitle" wx:if="{{item.subtitle}}">{{item.subtitle}}</text>
            <view class="banner-actions" wx:if="{{processedConfig.show_actions && item.extra_config}}">
              <button class="btn-guide" wx:if="{{item.extra_config.show_guide_button}}"
                      bindtap="onActionTap" data-action="guide" data-item="{{item}}">
                <text class="icon">📖</text>
                <text>{{item.extra_config.guide_text || '新手指南'}}</text>
              </button>
              <button class="btn-browse" wx:if="{{item.extra_config.show_browse_button}}"
                      bindtap="onActionTap" data-action="browse" data-item="{{item}}">
                <text class="icon">🍃</text>
                <text>{{item.extra_config.browse_text || '立即浏览'}}</text>
              </button>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 数据概览板块 -->
  <view wx:elif="{{currentBlockType === 'stats_overview'}}" class="block-stats-overview">
    <view class="stats-grid">
      <view
        class="stats-item"
        wx:for="{{processedConfig.items}}"
        wx:key="id"
        data-item="{{item}}"
        bindtap="onItemTap">
        <view class="stats-value">{{item.value || '--'}}</view>
        <view class="stats-title">{{item.title}}</view>
        <view class="stats-subtitle" wx:if="{{item.subtitle}}">
          <text class="trend-prefix" wx:if="{{item.extra_config.trend_prefix}}">{{item.extra_config.trend_prefix}}</text>
          <text class="trend-value">{{item.trend || '--'}}</text>
          <text class="trend-suffix" wx:if="{{item.extra_config.trend_suffix}}">{{item.extra_config.trend_suffix}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 新手引导板块 -->
  <view wx:elif="{{currentBlockType === 'guide_banner'}}" class="block-guide-banner">
    <view
      class="guide-content"
      wx:for="{{processedConfig.items}}"
      wx:key="id"
      data-item="{{item}}">
      <view class="guide-icon">{{item.extra_config.icon || '💡'}}</view>
      <view class="guide-text">
        <view class="guide-title">{{item.title}}</view>
        <view class="guide-subtitle">{{item.subtitle}}</view>
      </view>
      <view class="guide-actions" wx:if="{{processedConfig.show_actions}}">
        <button class="btn-start" bindtap="onActionTap" data-action="start" data-item="{{item}}">
          {{item.extra_config.start_text || '开始指南'}}
        </button>
        <button class="btn-later" bindtap="onActionTap" data-action="later" data-item="{{item}}">
          {{item.extra_config.later_text || '稍后再说'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 快速入口板块 -->
  <view wx:elif="{{currentBlockType === 'features_grid'}}" class="block-features-grid">
    <view class="features-grid columns-{{processedConfig.columns || 4}}">
      <view
        class="feature-item {{item.extra_config.bg_color}}"
        wx:for="{{processedConfig.items}}"
        wx:key="id"
        data-item="{{item}}"
        bindtap="onItemTap">
        <view class="feature-icon">{{item.image_url}}</view>
        <view class="feature-title" wx:if="{{processedConfig.show_labels}}">{{item.title}}</view>
      </view>
    </view>
  </view>

  <!-- 快捷导航板块 (保持兼容) -->
  <view wx:elif="{{currentBlockType === 'quick_nav'}}" class="block-quick-nav">
    <view class="nav-grid columns-{{processedConfig.columns}}">
      <view
        class="nav-item"
        wx:for="{{processedConfig.items}}"
        wx:key="id"
        data-item="{{item}}"
        bindtap="onItemTap">
        <view class="nav-icon">
          <image
            class="icon-image size-{{processedConfig.iconSize}}"
            src="{{item.image_url}}"
            mode="aspectFit" />
        </view>
        <view class="nav-title" wx:if="{{processedConfig.showTitle}}">{{item.title}}</view>
      </view>
    </view>
  </view>

  <!-- 茶园展示板块 (精选/热门/新品) -->
  <view wx:elif="{{currentBlockType === 'featured_tea' || currentBlockType === 'hot_tea' || currentBlockType === 'new_tea'}}" 
        class="block-tea-list">
    <view class="tea-grid layout-{{processedConfig.layout}}">
      <view 
        class="tea-item" 
        wx:for="{{processedConfig.items}}" 
        wx:key="id"
        data-item="{{item}}"
        bindtap="onItemTap">
        <view class="tea-image-wrapper">
          <image 
            class="tea-image" 
            src="{{item.image_url}}" 
            mode="aspectFill" />
        </view>
        <view class="tea-info">
          <view class="tea-title">{{item.title}}</view>
          <view class="tea-subtitle" wx:if="{{item.subtitle}}">{{item.subtitle}}</view>
          <view class="tea-location" wx:if="{{processedConfig.showLocation && item.extra_config.location}}">
            📍 {{item.extra_config.location}}
          </view>
          <view class="tea-price" wx:if="{{processedConfig.showPrice && item.extra_config.price}}">
            ¥{{item.extra_config.price}}
          </view>
          <view class="tea-sales" wx:if="{{processedConfig.showSales && item.extra_config.sales}}">
            已售 {{item.extra_config.sales}}
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 推荐茶地板块 -->
  <view wx:elif="{{currentBlockType === 'recommended_tea'}}" class="block-recommended-tea">
    <view class="section-header">
      <text class="section-title">🍃 推荐茶地</text>
      <text class="section-more" wx:if="{{processedConfig.show_more_link}}" bindtap="onMoreTap">查看更多 ></text>
    </view>
    <scroll-view class="tea-scroll" scroll-x="{{processedConfig.scroll_horizontal}}" enable-flex="true">
      <view class="tea-list">
        <view
          class="tea-card"
          wx:for="{{teaData}}"
          wx:key="id"
          data-item="{{item}}"
          bindtap="onTeaItemTap">
          <image class="tea-image" src="{{item.image_url}}" mode="aspectFill" />
          <view class="tea-info">
            <text class="tea-name">{{item.name}}</text>
            <text class="tea-location">📍 {{item.location}}</text>
            <view class="tea-stats">
              <text class="tea-price" wx:if="{{processedConfig.show_price}}">¥{{item.price}}/亩</text>
              <text class="tea-return" wx:if="{{processedConfig.show_return}}">{{item.return_rate}}%</text>
            </view>
          </view>
        </view>
        <view class="empty-state" wx:if="{{!teaData || teaData.length === 0}}">
          <text class="empty-icon">🍃</text>
          <text class="empty-text">暂无推荐茶地</text>
          <text class="empty-desc">管理员还未设置推荐茶地</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- AI投资建议板块 -->
  <view wx:elif="{{currentBlockType === 'ai_investment'}}" class="block-ai-investment">
    <view class="ai-card">
      <view class="ai-header">
        <text class="ai-icon">🎯</text>
        <text class="ai-title">AI投资建议</text>
        <button class="btn-refresh" wx:if="{{processedConfig.show_refresh_button}}"
                bindtap="onRefreshAI">🔄</button>
      </view>
      <view class="ai-content" wx:if="{{aiAdvice && aiAdvice.length > 0}}">
        <view class="advice-item" wx:for="{{aiAdvice}}" wx:key="id">
          <text class="advice-text">{{item.content}}</text>
          <text class="advice-confidence">置信度: {{item.confidence}}%</text>
        </view>
      </view>
      <view class="ai-loading" wx:elif="{{aiLoading}}">
        <text class="loading-icon">🤖</text>
        <text class="loading-text">AI正在分析中...</text>
      </view>
      <view class="ai-empty" wx:else>
        <text class="empty-icon">🎯</text>
        <text class="empty-text">暂无投资建议</text>
        <button class="btn-generate" bindtap="onGenerateAI">生成AI建议</button>
      </view>
      <view class="ai-actions">
        <button class="btn-chat" wx:if="{{processedConfig.show_chat_button}}"
                bindtap="onChatAI">💬 AI咨询</button>
        <button class="btn-more" bindtap="onMoreAI">查看更多</button>
      </view>
    </view>
  </view>

  <!-- 茶园视频号板块 -->
  <view wx:elif="{{currentBlockType === 'video_channel'}}" class="block-video-channel">
    <view class="section-header">
      <text class="section-title">📹 茶园视频号</text>
      <text class="section-more" wx:if="{{processedConfig.show_more_link}}" bindtap="onMoreTap">查看更多 ></text>
    </view>
    <scroll-view class="video-scroll" scroll-x="true" enable-flex="true">
      <view class="video-list">
        <view
          class="video-card"
          wx:for="{{videoData}}"
          wx:key="id"
          data-item="{{item}}"
          bindtap="onVideoItemTap">
          <view class="video-thumbnail">
            <image class="video-image" src="{{item.thumbnail_url}}" mode="aspectFill" />
            <view class="play-overlay">
              <text class="play-icon">▶</text>
              <text class="video-duration" wx:if="{{processedConfig.show_duration}}">{{item.duration}}</text>
            </view>
          </view>
          <view class="video-info">
            <text class="video-title">{{item.title}}</text>
            <view class="video-stats" wx:if="{{processedConfig.show_stats}}">
              <text class="view-count">👁 {{item.view_count}}</text>
              <text class="like-count">❤ {{item.like_count}}</text>
            </view>
          </view>
        </view>
        <view class="empty-state" wx:if="{{!videoData || videoData.length === 0}}">
          <text class="empty-icon">📹</text>
          <text class="empty-text">暂无视频内容</text>
          <text class="empty-desc">管理员还未发布任何视频</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 最新动态板块 -->
  <view wx:elif="{{currentBlockType === 'news_list'}}" class="block-news-list">
    <view class="section-header">
      <text class="section-title">📰 最新动态</text>
      <text class="section-more" wx:if="{{processedConfig.show_more_link}}" bindtap="onMoreTap">查看更多 ></text>
    </view>
    <view class="news-list">
      <view
        class="news-item"
        wx:for="{{newsData}}"
        wx:key="id"
        data-item="{{item}}"
        bindtap="onNewsItemTap">
        <view class="news-image" wx:if="{{processedConfig.show_images && item.image_url}}">
          <image class="news-img" src="{{item.image_url}}" mode="aspectFill" />
        </view>
        <view class="news-content">
          <text class="news-title">{{item.title}}</text>
          <text class="news-summary">{{item.summary}}</text>
          <view class="news-meta" wx:if="{{processedConfig.show_meta}}">
            <text class="news-badge" wx:if="{{processedConfig.show_badges && item.badge}}">{{item.badge}}</text>
            <text class="news-date">{{item.publish_date}}</text>
          </view>
        </view>
      </view>
      <view class="empty-state" wx:if="{{!newsData || newsData.length === 0}}">
        <text class="empty-icon">📰</text>
        <text class="empty-text">暂无最新动态</text>
        <text class="empty-desc">管理员还未发布任何动态信息</text>
      </view>
    </view>
  </view>

  <!-- 视频展示板块 (保持兼容) -->
  <view wx:elif="{{currentBlockType === 'video_showcase'}}" class="block-video">
    <view class="video-list">
      <view
        class="video-item"
        wx:for="{{processedConfig.items}}"
        wx:key="id"
        data-item="{{item}}"
        bindtap="onItemTap">
        <view class="video-thumbnail">
          <image
            class="thumbnail-image"
            src="{{item.image_url}}"
            mode="aspectFill" />
          <view class="play-icon">▶</view>
        </view>
        <view class="video-info" wx:if="{{processedConfig.showTitle}}">
          <view class="video-title">{{item.title}}</view>
          <view class="video-duration" wx:if="{{item.extra_config.duration}}">{{item.extra_config.duration}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 公告通知板块 -->
  <view wx:elif="{{currentBlockType === 'notice'}}" class="block-notice">
    <view class="notice-content">
      <view class="notice-icon">📢</view>
      <swiper class="notice-swiper" vertical="{{true}}" autoplay="{{true}}" interval="3000">
        <swiper-item 
          wx:for="{{processedConfig.items}}" 
          wx:key="id"
          data-item="{{item}}"
          bindtap="onItemTap">
          <view class="notice-text">{{item.title}}</view>
        </swiper-item>
      </swiper>
    </view>
  </view>

  <!-- 资讯列表板块 -->
  <view wx:elif="{{currentBlockType === 'news_list'}}" class="block-news">
    <view class="news-list">
      <view 
        class="news-item" 
        wx:for="{{processedConfig.items}}" 
        wx:key="id"
        data-item="{{item}}"
        bindtap="onItemTap">
        <view class="news-image" wx:if="{{item.image_url}}">
          <image class="news-img" src="{{item.image_url}}" mode="aspectFill" />
        </view>
        <view class="news-content">
          <view class="news-title">{{item.title}}</view>
          <view class="news-summary" wx:if="{{item.description}}">{{item.description}}</view>
          <view class="news-time" wx:if="{{item.extra_config.publish_time}}">{{item.extra_config.publish_time}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 间距板块 -->
  <view wx:elif="{{currentBlockType === 'spacer'}}" class="block-spacer">
    <view style="height: {{processedConfig.height || 20}}px;"></view>
  </view>

  <!-- 分割线板块 -->
  <view wx:elif="{{currentBlockType === 'divider'}}" class="block-divider">
    <view class="divider-line" style="border-color: {{processedConfig.color || '#e0e0e0'}};"></view>
  </view>

  <!-- 自定义HTML板块 -->
  <view wx:elif="{{currentBlockType === 'custom_html'}}" class="block-custom">
    <rich-text nodes="{{processedConfig.html_content}}"></rich-text>
  </view>

  <!-- 未知板块类型 -->
  <view wx:else class="block-unknown">
    <view class="unknown-content">
      <view class="unknown-icon">❓</view>
      <view class="unknown-text">未知板块类型: {{currentBlockType}}</view>
    </view>
  </view>

  <!-- 编辑模式遮罩 -->
  <view wx:if="{{editMode}}" class="edit-mask">
    <view class="edit-info">
      <view class="edit-type">{{currentBlockType}}</view>
      <view class="edit-name">{{blockConfig.name}}</view>
    </view>
  </view>

</view>

/**
 * 装修板块组件
 * 根据配置动态渲染不同类型的板块
 */

Component({
  properties: {
    // 板块配置
    blockConfig: {
      type: Object,
      value: {},
      observer: 'onBlockConfigChange'
    },
    // 全局配置
    globalConfig: {
      type: Object,
      value: {}
    },
    // 是否显示编辑模式
    editMode: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // 当前板块类型
    currentBlockType: '',
    // 处理后的样式
    blockStyle: '',
    // 处理后的内容配置
    processedConfig: {},
    // 是否显示板块
    isVisible: true,
    // 动态数据
    teaData: [],
    videoData: [],
    newsData: [],
    aiAdvice: [],
    aiLoading: false
  },

  lifetimes: {
    attached() {
      this.processBlockConfig()

      // 延迟加载数据，确保父页面数据已加载
      setTimeout(() => {
        this.refreshDynamicData()
      }, 1000)
    },

    ready() {
      // 组件准备就绪后再次刷新数据
      setTimeout(() => {
        this.refreshDynamicData()
      }, 2000)
    }
  },

  methods: {
    /**
     * 板块配置变化处理
     */
    onBlockConfigChange() {
      this.processBlockConfig()
    },

    /**
     * 处理板块配置
     */
    processBlockConfig() {
      const { blockConfig, globalConfig } = this.data
      
      if (!blockConfig || !blockConfig.block_type) {
        this.setData({
          isVisible: false
        })
        return
      }

      // 检查是否启用
      if (!blockConfig.is_enabled) {
        this.setData({
          isVisible: false
        })
        return
      }

      // 处理样式
      const blockStyle = this.generateBlockStyle(blockConfig, globalConfig)
      
      // 处理内容配置
      const processedConfig = this.processContentConfig(blockConfig)

      this.setData({
        currentBlockType: blockConfig.block_type,
        blockStyle,
        processedConfig,
        isVisible: true
      })

      // 加载动态数据
      this.loadDynamicData(blockConfig.block_type)
    },

    /**
     * 生成板块样式
     */
    generateBlockStyle(blockConfig, globalConfig) {
      const style = blockConfig.style || {}
      const global = globalConfig?.style || {}

      const styleProps = []

      // 背景色
      if (style.background_color) {
        styleProps.push(`background-color: ${style.background_color}`)
      }

      // 文字颜色
      if (style.text_color) {
        styleProps.push(`color: ${style.text_color}`)
      }

      // 边距
      if (style.margin_top) {
        styleProps.push(`margin-top: ${style.margin_top}px`)
      }
      if (style.margin_bottom) {
        styleProps.push(`margin-bottom: ${style.margin_bottom}px`)
      }

      // 内边距
      if (style.padding) {
        styleProps.push(`padding: ${style.padding}px`)
      }

      // 其他自定义样式
      if (style.border_radius) {
        styleProps.push(`border-radius: ${style.border_radius}px`)
      }
      if (style.box_shadow) {
        styleProps.push(`box-shadow: ${style.box_shadow}`)
      }

      return styleProps.join('; ')
    },

    /**
     * 处理内容配置
     */
    processContentConfig(blockConfig) {
      const config = {
        ...blockConfig.content_config,
        items: blockConfig.items || [],
        layout_type: blockConfig.layout_type || 'custom'
      }

      // 根据板块类型处理特定配置
      switch (blockConfig.block_type) {
        case 'banner':
          return this.processBannerConfig(config)
        case 'quick_nav':
          return this.processQuickNavConfig(config)
        case 'features_grid':
          return this.processFeaturesGridConfig(config)
        case 'featured_tea':
        case 'hot_tea':
        case 'new_tea':
          return this.processTeaListConfig(config)
        case 'video_showcase':
          return this.processVideoConfig(config)
        default:
          return config
      }
    },

    /**
     * 处理轮播图配置
     */
    processBannerConfig(config) {
      return {
        ...config,
        autoplay: config.auto_play !== false,
        interval: config.interval || 3000,
        indicatorDots: config.indicator_dots !== false,
        circular: config.circular !== false,
        items: (config.items || []).filter(item => item.is_enabled !== false)
      }
    },

    /**
     * 处理快捷导航配置
     */
    processQuickNavConfig(config) {
      return {
        ...config,
        columns: config.columns || 4,
        showTitle: config.show_title !== false,
        iconSize: config.icon_size || 'medium',
        items: (config.items || []).filter(item => item.is_enabled !== false)
      }
    },

    /**
     * 处理快速入口配置
     */
    processFeaturesGridConfig(config) {
      // 过滤启用的项目
      const enabledItems = (config.items || []).filter(item => item.is_enabled !== false)
      // Debug log removed

      return {
        ...config,
        columns: config.columns || 4,
        show_labels: config.show_labels !== false,
        card_style: config.card_style !== false,
        items: enabledItems
      }
    },

    /**
     * 处理茶园列表配置
     */
    processTeaListConfig(config) {
      return {
        ...config,
        limit: config.limit || 4,
        showPrice: config.show_price !== false,
        showLocation: config.show_location !== false,
        showSales: config.show_sales || false,
        layout: config.layout || 'grid',
        items: (config.items || []).filter(item => item.is_enabled !== false)
      }
    },

    /**
     * 处理视频展示配置
     */
    processVideoConfig(config) {
      return {
        ...config,
        limit: config.limit || 3,
        autoPlay: config.auto_play || false,
        showTitle: config.show_title !== false,
        items: (config.items || []).filter(item => item.is_enabled !== false)
      }
    },

    /**
     * 板块点击事件
     */
    onBlockTap(e) {
      const { blockConfig } = this.data
      
      this.triggerEvent('blockTap', {
        blockType: blockConfig.block_type,
        blockConfig: blockConfig,
        detail: e.detail
      })
    },

    /**
     * 项目点击事件
     */
    onItemTap(e) {
      const { item } = e.currentTarget.dataset
      
      if (!item || !item.link_url) {
        return
      }

      // 处理不同类型的链接
      switch (item.link_type) {
        case 'page':
          wx.navigateTo({
            url: item.link_url
          })
          break
        case 'external':
          // 外部链接处理
          wx.showModal({
            title: '提示',
            content: '即将跳转到外部链接',
            success: (res) => {
              if (res.confirm) {
                // 这里可以处理外部链接跳转
              }
            }
          })
          break
        case 'mini_program':
          // 小程序页面跳转
          wx.navigateTo({
            url: item.link_url
          })
          break
        default:
      }

      this.triggerEvent('itemTap', {
        item: item,
        blockType: this.data.currentBlockType
      })
    },

    /**
     * 获取布局类名
     */
    getLayoutClass(layoutType) {
      const layoutClasses = {
        'grid_2': 'layout-grid-2',
        'grid_3': 'layout-grid-3',
        'grid_4': 'layout-grid-4',
        'list': 'layout-list',
        'carousel': 'layout-carousel',
        'banner': 'layout-banner',
        'scroll_horizontal': 'layout-scroll-horizontal',
        'card': 'layout-card',
        'custom': 'layout-custom'
      }

      return layoutClasses[layoutType] || 'layout-custom'
    },

    /**
     * 加载动态数据
     */
    loadDynamicData(blockType) {
      switch (blockType) {
        case 'stats_overview':
          this.loadStatsData()
          break
        case 'recommended_tea':
          this.loadTeaData()
          break
        case 'ai_investment':
          this.loadAIAdvice()
          break
        case 'video_channel':
          this.loadVideoData()
          break
        case 'news_list':
          this.loadNewsData()
          break
      }
    },

    /**
     * 加载统计数据
     */
    loadStatsData() {
      // 从父页面获取真实的统计数据
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]

      if (currentPage && currentPage.data && currentPage.data.stats) {
        const stats = currentPage.data.stats

        // 更新统计数据到配置项
        const processedConfig = { ...this.data.processedConfig }
        if (processedConfig.items) {
          processedConfig.items.forEach(item => {
            if (item.extra_config && item.extra_config.data_key) {
              switch (item.extra_config.data_key) {
                case 'totalAcres':
                  item.value = stats.totalAcres?.toFixed(1) || '--'
                  break
                case 'availableAcres':
                  item.value = stats.availableAcres?.toFixed(1) || '--'
                  break
                case 'soldAcres':
                  item.value = stats.soldAcres?.toFixed(1) || '--'
                  break
                default:
                  item.value = '--'
              }
            }
            if (item.extra_config && item.extra_config.trend_key) {
              switch (item.extra_config.trend_key) {
                case 'monthlyIncrease':
                  item.trend = stats.monthlyIncrease?.toFixed(1) || '--'
                  break
                case 'soldRate':
                  item.trend = stats.soldRate?.toFixed(1) || '--'
                  break
                default:
                  item.trend = '--'
              }
            }
          })
        }

        this.setData({ processedConfig })
      } else {
      }
    },

    /**
     * 加载茶地数据
     */
    loadTeaData() {
      // 从父页面获取真实的茶地数据
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]

      if (currentPage && currentPage.data && currentPage.data.recommendedTeas) {
        const recommendedTeas = currentPage.data.recommendedTeas

        // 转换数据格式以适配装修组件
        const teaData = recommendedTeas.map(tea => ({
          id: tea.id,
          name: tea.name,
          location: tea.location,
          price: tea.price,
          return_rate: parseFloat(tea.expected_return?.split('-')[1] || '12'),
          image_url: tea.image || '/images/default-tea.jpg'
        }))

        this.setData({ teaData })
      } else {
        // 如果没有数据，设置空数组
        this.setData({ teaData: [] })
      }
    },

    /**
     * 加载AI建议
     */
    loadAIAdvice() {
      // 从父页面获取真实的AI建议数据
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]

      if (currentPage && currentPage.data && currentPage.data.investmentAdvice) {
        const investmentAdvice = currentPage.data.investmentAdvice

        if (investmentAdvice.loading) {
          this.setData({ aiLoading: true })
        } else if (investmentAdvice.data && investmentAdvice.data.recommendations) {
          // 转换数据格式以适配装修组件
          const aiAdvice = investmentAdvice.data.recommendations.map((item, index) => ({
            id: index + 1,
            content: item.description || item.title,
            confidence: parseInt(item.score?.replace('.', '') || '80')
          }))

          this.setData({
            aiAdvice,
            aiLoading: false
          })
        } else {
          this.setData({
            aiAdvice: [],
            aiLoading: false
          })
        }
      } else {
        // 如果没有数据，设置空数组
        this.setData({
          aiAdvice: [],
          aiLoading: false
        })
      }
    },

    /**
     * 加载视频数据
     */
    loadVideoData() {
      // 从父页面获取真实的视频数据
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]

      if (currentPage && currentPage.data && currentPage.data.videoChannel) {
        const videoChannel = currentPage.data.videoChannel

        // 转换数据格式以适配装修组件
        const videoData = videoChannel.map(video => ({
          id: video.id,
          title: video.title,
          thumbnail_url: video.thumbnail || video.cover_image || '/images/default-video.jpg',
          duration: video.duration || '00:00',
          view_count: video.view_count || 0,
          like_count: video.like_count || 0
        }))

        this.setData({ videoData })
      } else {
        // 如果没有数据，设置空数组
        this.setData({ videoData: [] })
      }
    },

    /**
     * 加载新闻数据
     */
    loadNewsData() {
      // 从父页面获取真实的新闻数据
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]

      if (currentPage && currentPage.data && currentPage.data.news) {
        const news = currentPage.data.news

        // 转换数据格式以适配装修组件
        const newsData = news.map(item => ({
          id: item.id,
          title: item.title,
          summary: item.summary || item.content?.substring(0, 100) + '...' || '',
          image_url: item.image || item.cover_image || '/images/default-news.jpg',
          badge: item.is_hot ? '热门' : (item.is_recommended ? '推荐' : ''),
          publish_date: item.publish_date || item.created_at?.split('T')[0] || ''
        }))

        this.setData({ newsData })
      } else {
        // 如果没有数据，设置空数组
        this.setData({ newsData: [] })
      }
    },

    // 新增事件处理方法
    onActionTap(e) {
      const { action, item } = e.currentTarget.dataset
      switch (action) {
        case 'guide':
          wx.navigateTo({ url: '/pages/guide/guide' })
          break
        case 'browse':
          wx.navigateTo({ url: '/pages/tea-list/tea-list' })
          break
        case 'start':
          wx.navigateTo({ url: '/pages/guide/guide' })
          break
        case 'later':
          // 隐藏引导
          break
      }
    },

    onMoreTap() {
      const blockType = this.data.currentBlockType
      let url = ''

      switch (blockType) {
        case 'recommended_tea':
          url = '/pages/tea-list/tea-list'
          break
        case 'video_channel':
          url = '/pages/video-channel/video-channel'
          break
        case 'news_list':
          url = '/pages/news/news'
          break
      }

      if (url) {
        wx.navigateTo({ url })
      }
    },

    onTeaItemTap(e) {
      const item = e.currentTarget.dataset.item
      wx.navigateTo({
        url: `/pages/tea-detail/tea-detail?id=${item.id}`
      })
    },

    onVideoItemTap(e) {
      const item = e.currentTarget.dataset.item
      wx.navigateTo({
        url: `/pages/video-detail/video-detail?id=${item.id}`
      })
    },

    onNewsItemTap(e) {
      const item = e.currentTarget.dataset.item
      wx.navigateTo({
        url: `/pages/news-detail/news-detail?id=${item.id}`
      })
    },

    onRefreshAI() {
      this.loadAIAdvice()
    },

    onGenerateAI() {
      this.loadAIAdvice()
    },

    onChatAI() {
      wx.navigateTo({
        url: '/pages/ai-chat/ai-chat'
      })
    },

    onMoreAI() {
      wx.navigateTo({
        url: '/pages/investment-advice/investment-advice'
      })
    },

    /**
     * 刷新动态数据
     */
    refreshDynamicData() {
      const blockType = this.data.currentBlockType
      if (blockType) {
        this.loadDynamicData(blockType)
      }
    }
  }
})

/**
 * 自定义底部导航组件
 */

Component({
  properties: {
    // 导航配置
    config: {
      type: Object,
      value: {},
      observer: 'onConfigChange'
    },
    // 当前选中的索引
    current: {
      type: Number,
      value: 0,
      observer: 'onCurrentChange'
    },
    // 是否显示
    show: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 内部状态
    items: [],
    currentIndex: 0,
    isVisible: true,
    // 动画状态
    animating: false
  },

  lifetimes: {
    attached() {
      this.initTabBar()
    }
  },

  methods: {
    // 初始化底部导航
    async initTabBar() {
      try {
        // 如果没有传入配置，从API获取
        if (!this.data.config || !this.data.config.list) {
          await this.loadConfig()
        } else {
          this.updateItems()
        }
        
      } catch (error) {
        console.error('❌ 初始化自定义底部导航失败:', error)
        this.loadDefaultConfig()
      }
    },

    // 从API加载配置
    async loadConfig() {
      return new Promise((resolve, reject) => {
        wx.request({
          url: 'https://teabuy.yizhangkj.com/api/v1/page-decoration/tabbar/config/',
          method: 'GET',
          header: {
            'Cache-Control': 'no-cache'
          },
          success: (res) => {
            if (res.data && res.data.code === 200) {
              const config = res.data.data.config
              // Debug log removed
              
              this.setData({ 
                config: config 
              })
              this.updateItems()
              resolve(config)
            } else {
              this.loadDefaultConfig()
              resolve()
            }
          },
          fail: (error) => {
            console.error('❌ 加载配置失败，使用默认配置:', error)
            this.loadDefaultConfig()
            reject(error)
          }
        })
      })
    },

    // 加载默认配置
    loadDefaultConfig() {
      const defaultConfig = {
        color: '#999999',
        selectedColor: '#2E7D32',
        backgroundColor: '#FFFFFF',
        borderStyle: 'black',
        list: [
          {
            pagePath: 'pages/index/index',
            text: '首页',
            iconPath: 'images/tabbar/home.png',
            selectedIconPath: 'images/tabbar/home-active.png'
          },
          {
            pagePath: 'pages/tea-list/tea-list',
            text: '茶地',
            iconPath: 'images/tabbar/tea.png',
            selectedIconPath: 'images/tabbar/tea-active.png'
          },
          {
            pagePath: 'pages/analytics/analytics',
            text: '分析',
            iconPath: 'images/tabbar/chart.png',
            selectedIconPath: 'images/tabbar/chart-active.png'
          },
          {
            pagePath: 'pages/profile/profile',
            text: '我的',
            iconPath: 'images/tabbar/profile.png',
            selectedIconPath: 'images/tabbar/profile-active.png'
          }
        ]
      }
      
      this.setData({ config: defaultConfig })
      this.updateItems()
    },

    // 配置变化处理
    onConfigChange(newConfig) {
      if (newConfig && newConfig.list) {
        this.updateItems()
      }
    },

    // 当前索引变化处理
    onCurrentChange(newCurrent) {
      this.setData({ currentIndex: newCurrent })
    },

    // 更新导航项
    updateItems() {
      const config = this.data.config
      if (!config || !config.list) return

      const items = config.list.map((item, index) => ({
        ...item,
        index: index,
        isActive: index === this.data.currentIndex
      }))

      this.setData({ items })
      // 调试：打印图标路径
      items.forEach((item, index) => {
        // Debug log removed
      })
    },

    // 导航项点击事件
    onTabClick(e) {
      const { index, item } = e.currentTarget.dataset
      
      if (this.data.animating) return // 防止快速点击
      // 设置动画状态
      this.setData({ animating: true })
      
      // 更新选中状态
      this.setData({ currentIndex: index })
      this.updateItems()
      
      // 触发页面跳转事件
      this.triggerEvent('tabchange', {
        index: index,
        item: item,
        pagePath: item.pagePath
      })
      
      // 重置动画状态
      setTimeout(() => {
        this.setData({ animating: false })
      }, 300)
    },

    // 重新加载配置
    async reloadConfig() {
      try {
        await this.loadConfig()
        return true
      } catch (error) {
        console.error('❌ 重新加载配置失败:', error)
        return false
      }
    },

    // 显示/隐藏导航栏
    show() {
      this.setData({ isVisible: true })
    },

    hide() {
      this.setData({ isVisible: false })
    },

    // 设置徽章
    setBadge(index, text) {
      const items = this.data.items
      if (items[index]) {
        items[index].badge = text
        this.setData({ items })
      }
    },

    // 移除徽章
    removeBadge(index) {
      const items = this.data.items
      if (items[index]) {
        delete items[index].badge
        this.setData({ items })
      }
    },

    // 图标加载成功
    onImageLoad(e) {
      const item = e.currentTarget.dataset.item
    },

    // 图标加载失败
    onImageError(e) {
      const item = e.currentTarget.dataset.item
      console.error(`❌ 图标加载失败: ${item.text} - ${e.currentTarget.src}`)

      // 标记图标加载失败，显示占位符
      const items = this.data.items
      const index = items.findIndex(i => i.text === item.text)
      if (index !== -1) {
        items[index].imageError = true
        this.setData({ items })
      }
    }
  }
})

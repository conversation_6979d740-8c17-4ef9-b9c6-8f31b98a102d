/* 装修页面组件样式 */

.decoration-page {
  width: 100%;
  min-height: 100vh;
  position: relative;
}

/* 原版首页样式 - 完全复制 */
.original-homepage {
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
  min-height: 100vh;
}

/* 轮播图区域 */
.banner-section {
  position: relative;
  height: 400rpx;
  margin-bottom: 32rpx;
  border-radius: 0 0 32rpx 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.banner-swiper {
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.8), rgba(76, 175, 80, 0.6));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.banner-content {
  text-align: center;
  color: white;
  padding: 0 40rpx;
}

.banner-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.banner-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 40rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.banner-actions {
  display: flex;
  gap: 24rpx;
  justify-content: center;
}

.btn-guide, .btn-browse {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 32rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.btn-guide {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.btn-browse {
  background: #4CAF50;
  color: white;
}

.btn-guide .icon, .btn-browse .icon {
  font-size: 32rpx;
}

/* 数据概览 */
.stats-section {
  margin: 32rpx 24rpx;
}

.stats-grid {
  display: flex;
  background: white;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  gap: 24rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -12rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 2rpx;
  height: 60rpx;
  background: #f0f0f0;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2E7D32;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.stat-trend {
  display: block;
  font-size: 20rpx;
  color: #4CAF50;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.error-text {
  font-size: 14px;
  color: #ff4757;
  margin-bottom: 20px;
  line-height: 1.5;
}

.retry-button {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 20px;
  font-size: 14px;
}

.retry-button::after {
  border: none;
}

/* 页面内容 */
.page-content {
  width: 100%;
  min-height: 100vh;
}

/* 页面标题 (编辑模式) */
.page-header {
  background: #1890ff;
  color: white;
  padding: 10px 15px;
  text-align: center;
}

.page-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 3px;
}

.page-type {
  font-size: 12px;
  opacity: 0.8;
}

/* 板块容器 */
.blocks-container {
  width: 100%;
}

/* 空状态 */
.empty-blocks,
.empty-template {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 10px;
}

.empty-hint {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

/* 编辑模式工具栏 */
.edit-toolbar {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 8px;
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.tool-button {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  padding: 5px 8px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 50px;
}

.tool-button::after {
  border: none;
}

.tool-icon {
  font-size: 16px;
  margin-bottom: 2px;
}

.tool-text {
  font-size: 10px;
}

.tool-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.info-item {
  font-size: 10px;
  opacity: 0.8;
  white-space: nowrap;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .page-header {
    padding: 8px 12px;
  }
  
  .page-title {
    font-size: 14px;
  }
  
  .empty-blocks,
  .empty-template {
    padding: 60px 15px;
  }
  
  .empty-icon {
    font-size: 48px;
  }
  
  .empty-text {
    font-size: 14px;
  }
  
  .edit-toolbar {
    bottom: 15px;
    right: 15px;
    padding: 8px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .loading-text,
  .empty-text {
    color: #ccc;
  }
  
  .empty-hint {
    color: #999;
  }
  
  .loading-spinner {
    border-color: #444;
    border-top-color: #1890ff;
  }
}

/* 新手引导提示 */
.guide-banner {
  margin: 32rpx 24rpx;
  background: linear-gradient(135deg, #FFF3E0, #FFE0B2);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(255, 152, 0, 0.1);
  border: 2rpx solid #FFB74D;
}

.guide-content {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.guide-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.guide-text {
  flex: 1;
}

.guide-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #E65100;
  margin-bottom: 8rpx;
}

.guide-desc {
  display: block;
  font-size: 26rpx;
  color: #BF360C;
  line-height: 1.4;
}

.guide-close {
  font-size: 32rpx;
  color: #FF9800;
  padding: 8rpx;
  margin-left: 16rpx;
}

.guide-actions {
  display: flex;
  gap: 24rpx;
}

.btn-start, .btn-later {
  flex: 1;
  padding: 20rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
}

.btn-start {
  background: #FF9800;
  color: white;
}

.btn-later {
  background: rgba(255, 152, 0, 0.1);
  color: #FF9800;
  border: 2rpx solid #FFB74D;
}

/* 功能入口 */
.features-section {
  margin: 32rpx 24rpx;
}

.card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.card-more {
  font-size: 26rpx;
  color: #4CAF50;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.feature-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.bg-green { background: linear-gradient(135deg, #4CAF50, #66BB6A); }
.bg-blue { background: linear-gradient(135deg, #2196F3, #42A5F5); }
.bg-orange { background: linear-gradient(135deg, #FF9800, #FFB74D); }
.bg-purple { background: linear-gradient(135deg, #9C27B0, #BA68C8); }
.bg-teal { background: linear-gradient(135deg, #009688, #4DB6AC); }
.bg-indigo { background: linear-gradient(135deg, #3F51B5, #7986CB); }
.bg-pink { background: linear-gradient(135deg, #E91E63, #F06292); }
.bg-yellow { background: linear-gradient(135deg, #FFC107, #FFD54F); }

.feature-label {
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
}

/* 推荐茶地 */
.recommended-section {
  margin: 32rpx 24rpx;
}

.tea-scroll {
  margin-top: 24rpx;
}

.tea-list {
  display: flex;
  gap: 24rpx;
  padding: 0 8rpx;
}

.tea-item {
  width: 280rpx;
  flex-shrink: 0;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.tea-image {
  width: 100%;
  height: 160rpx;
  object-fit: cover;
}

.tea-image-placeholder {
  width: 100%;
  height: 160rpx;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  font-size: 48rpx;
  color: #ccc;
}

.tea-info {
  padding: 24rpx;
}

.tea-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tea-location {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.tea-price, .tea-return {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.price-label, .return-label {
  font-size: 22rpx;
  color: #666;
}

.price-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #E65100;
}

.return-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #4CAF50;
}

/* AI投资建议 */
.investment-advice-section {
  margin: 32rpx 24rpx;
}

.advice-content {
  margin-top: 24rpx;
}

.advice-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
  text-align: center;
}

.advice-loading .loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #666;
}

.advice-result {
  background: linear-gradient(135deg, #E3F2FD, #BBDEFB);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 2rpx solid #2196F3;
}

.advice-summary {
  margin-bottom: 24rpx;
}

.advice-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #1565C0;
  margin-bottom: 12rpx;
}

.advice-desc {
  display: block;
  font-size: 26rpx;
  color: #1976D2;
  line-height: 1.4;
}

.advice-recommendations {
  margin-bottom: 24rpx;
}

.recommendation-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
}

.rec-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.rec-content {
  flex: 1;
}

.rec-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.rec-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  line-height: 1.3;
}

.rec-score {
  font-size: 24rpx;
  font-weight: bold;
  margin-left: 16rpx;
}

.advice-actions {
  display: flex;
  gap: 16rpx;
}

.btn-ai-chat, .btn-refresh-advice, .btn-generate-advice {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  text-align: center;
  border: none;
}

.btn-ai-chat {
  background: #2196F3;
  color: white;
}

.btn-refresh-advice {
  background: rgba(33, 150, 243, 0.1);
  color: #2196F3;
  border: 2rpx solid #2196F3;
}

.advice-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
  text-align: center;
}

.empty-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 24rpx;
}

.btn-generate-advice {
  background: #4CAF50;
  color: white;
  padding: 24rpx 48rpx;
  border-radius: 50rpx;
}

/* 视频号展示 */
.video-channel-section {
  margin: 32rpx 24rpx;
}

.video-content {
  margin-top: 24rpx;
}

.video-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
  text-align: center;
}

.video-loading .loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #666;
}

.video-scroll {
  margin-top: 24rpx;
}

.video-list {
  display: flex;
  gap: 24rpx;
  padding: 0 8rpx;
}

.video-item {
  width: 320rpx;
  flex-shrink: 0;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 180rpx;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-button {
  font-size: 48rpx;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.video-duration {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.video-info {
  padding: 20rpx;
}

.video-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-stats {
  display: flex;
  gap: 16rpx;
  margin-bottom: 8rpx;
}

.stat-item {
  font-size: 20rpx;
  color: #999;
}

.video-time {
  font-size: 20rpx;
  color: #ccc;
}

.video-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
  text-align: center;
}

.empty-desc {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 最新动态 */
.news-section {
  margin: 32rpx 24rpx;
}

.news-list {
  margin-top: 24rpx;
}

.news-item {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  gap: 20rpx;
}

.news-content {
  flex: 1;
}

.news-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.news-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
  flex: 1;
  margin-right: 16rpx;
}

.news-badges {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  flex-shrink: 0;
}

.badge {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  text-align: center;
  min-width: 60rpx;
}

.top-badge {
  background: #FF5722;
  color: white;
}

.featured-badge {
  background: #4CAF50;
  color: white;
}

.news-type {
  font-size: 18rpx;
  font-weight: bold;
}

.news-summary {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.news-time {
  font-size: 20rpx;
  color: #999;
}

.news-views {
  font-size: 20rpx;
  color: #999;
}

.news-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 12rpx;
  object-fit: cover;
  flex-shrink: 0;
}

.news-image-placeholder {
  width: 160rpx;
  height: 120rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 20rpx;
  text-align: center;
}

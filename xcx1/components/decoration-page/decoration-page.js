/**
 * 装修页面组件
 * 根据页面类型加载和渲染装修配置
 */

const decorationManager = require('../../utils/decoration-manager.js')

Component({
  properties: {
    // 页面类型
    pageType: {
      type: String,
      value: 'home',
      observer: 'onPageTypeChange'
    },
    // 是否显示编辑模式
    editMode: {
      type: Boolean,
      value: false
    },
    // 额外的数据源
    dataSource: {
      type: Object,
      value: {},
      observer: 'onDataSourceChange'
    },
    // 调试模式
    debugMode: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // 页面配置
    pageConfig: null,
    // 全局配置
    globalConfig: null,
    // 页面样式
    pageStyle: '',
    // 加载状态
    loading: true,
    // 错误信息
    error: null
  },

  lifetimes: {
    attached() {
      this.loadPageConfig()
    },

    ready() {
      // Debug log removed
      // Debug log removed

      // 如果数据源已经存在，立即设置
      if (this.properties.dataSource && Object.keys(this.properties.dataSource).length > 0) {
        this.setData({
          componentDataSource: this.properties.dataSource
        })
      } else {
      }
    }
  },

  methods: {
    /**
     * 页面类型变化处理
     */
    onPageTypeChange() {
      this.loadPageConfig()
    },

    /**
     * 数据源变化处理
     */
    onDataSourceChange(newVal, oldVal) {
      // Debug log removed

      // 数据源更新时，触发页面重新渲染
      if (newVal && typeof newVal === 'object') {
        // 更新组件数据源
        this.setData({
          componentDataSource: newVal || {}
        })
      } else {
        this.setData({
          componentDataSource: {}
        })
      }
    },

    /**
     * 加载页面配置
     */
    async loadPageConfig() {
      const { pageType } = this.data
      
      this.setData({
        loading: true,
        error: null
      })

      try {
        // 并行加载页面配置和全局配置
        const [pageConfig, globalConfig] = await Promise.all([
          decorationManager.getPageConfig(pageType),
          decorationManager.getGlobalConfig()
        ])

        // 处理页面样式
        const pageStyle = this.generatePageStyle(globalConfig)

        // 合并数据源到板块配置
        const processedPageConfig = this.processPageConfig(pageConfig)

        this.setData({
          pageConfig: processedPageConfig,
          globalConfig,
          pageStyle,
          loading: false
        })

        // 触发配置加载完成事件
        this.triggerEvent('configLoaded', {
          pageType,
          pageConfig: processedPageConfig,
          globalConfig
        })

      } catch (error) {
        console.error(`❌ 加载页面配置失败: ${pageType}`, error)
        
        this.setData({
          loading: false,
          error: error.message || '加载配置失败'
        })

        // 触发错误事件
        this.triggerEvent('configError', {
          pageType,
          error: error.message || '加载配置失败'
        })
      }
    },

    /**
     * 生成页面样式
     */
    generatePageStyle(globalConfig) {
      if (!globalConfig || !globalConfig.style) {
        return ''
      }

      const style = globalConfig.style
      const styleProps = []

      // 背景色
      if (style.background_color) {
        styleProps.push(`background-color: ${style.background_color}`)
      }

      // 文字颜色
      if (style.text_color) {
        styleProps.push(`color: ${style.text_color}`)
      }

      return styleProps.join('; ')
    },

    /**
     * 处理页面配置
     */
    processPageConfig(pageConfig) {
      if (!pageConfig || !pageConfig.template) {
        return pageConfig
      }

      const { dataSource } = this.data
      const template = pageConfig.template
      
      // 处理板块配置
      if (template.blocks) {
        template.blocks = template.blocks.map(block => {
          return this.processBlockConfig(block, dataSource)
        })
      }

      return pageConfig
    },

    /**
     * 处理板块配置
     */
    processBlockConfig(block, dataSource) {
      // 根据板块类型处理数据源
      switch (block.block_type) {
        case 'featured_tea':
          return this.processFeaturedTeaBlock(block, dataSource)
        case 'hot_tea':
          return this.processHotTeaBlock(block, dataSource)
        case 'new_tea':
          return this.processNewTeaBlock(block, dataSource)
        case 'video_showcase':
          return this.processVideoBlock(block, dataSource)
        case 'recommended_tea':
          return this.processRecommendedTeaBlock(block, dataSource)
        case 'video_channel':
          return this.processVideoChannelBlock(block, dataSource)
        case 'news_list':
          return this.processNewsListBlock(block, dataSource)
        case 'ai_investment':
          return this.processAIInvestmentBlock(block, dataSource)
        case 'stats_overview':
          return this.processStatsOverviewBlock(block, dataSource)
        default:
          return block
      }
    },

    /**
     * 处理精选茶园板块
     */
    processFeaturedTeaBlock(block, dataSource) {
      if (dataSource.featuredTeas && dataSource.featuredTeas.length > 0) {
        const limit = block.content_config?.limit || 4
        const items = dataSource.featuredTeas.slice(0, limit).map((tea, index) => ({
          id: `featured_${tea.id || index}`,
          title: tea.name || tea.title,
          subtitle: tea.variety || tea.subtitle,
          description: tea.description,
          image_url: tea.image || tea.image_url,
          link_url: `/pages/tea-detail/tea-detail?id=${tea.id}`,
          link_type: 'page',
          is_enabled: true,
          sort_order: index,
          extra_config: {
            price: tea.price,
            location: tea.location,
            sales: tea.sales_count
          }
        }))
        
        return {
          ...block,
          items
        }
      }
      
      return block
    },

    /**
     * 处理热门茶园板块
     */
    processHotTeaBlock(block, dataSource) {
      if (dataSource.hotTeas && dataSource.hotTeas.length > 0) {
        const limit = block.content_config?.limit || 6
        const items = dataSource.hotTeas.slice(0, limit).map((tea, index) => ({
          id: `hot_${tea.id || index}`,
          title: tea.name || tea.title,
          subtitle: tea.variety || tea.subtitle,
          description: tea.description,
          image_url: tea.image || tea.image_url,
          link_url: `/pages/tea-detail/tea-detail?id=${tea.id}`,
          link_type: 'page',
          is_enabled: true,
          sort_order: index,
          extra_config: {
            price: tea.price,
            location: tea.location,
            sales: tea.sales_count
          }
        }))
        
        return {
          ...block,
          items
        }
      }
      
      return block
    },

    /**
     * 处理新品茶园板块
     */
    processNewTeaBlock(block, dataSource) {
      if (dataSource.newTeas && dataSource.newTeas.length > 0) {
        const limit = block.content_config?.limit || 4
        const items = dataSource.newTeas.slice(0, limit).map((tea, index) => ({
          id: `new_${tea.id || index}`,
          title: tea.name || tea.title,
          subtitle: tea.variety || tea.subtitle,
          description: tea.description,
          image_url: tea.image || tea.image_url,
          link_url: `/pages/tea-detail/tea-detail?id=${tea.id}`,
          link_type: 'page',
          is_enabled: true,
          sort_order: index,
          extra_config: {
            price: tea.price,
            location: tea.location,
            sales: tea.sales_count
          }
        }))
        
        return {
          ...block,
          items
        }
      }
      
      return block
    },

    /**
     * 处理视频展示板块
     */
    processVideoBlock(block, dataSource) {
      if (dataSource.videos && dataSource.videos.length > 0) {
        const limit = block.content_config?.limit || 3
        const items = dataSource.videos.slice(0, limit).map((video, index) => ({
          id: `video_${video.id || index}`,
          title: video.title,
          subtitle: video.description,
          description: video.description,
          image_url: video.thumbnail || video.image_url,
          link_url: `/pages/video-detail/video-detail?id=${video.id}`,
          link_type: 'page',
          is_enabled: true,
          sort_order: index,
          extra_config: {
            duration: video.duration,
            view_count: video.view_count
          }
        }))
        
        return {
          ...block,
          items
        }
      }
      
      return block
    },

    /**
     * 刷新页面配置
     */
    async refreshConfig() {
      // 清除缓存
      decorationManager.clearCache(this.data.pageType)
      
      // 重新加载配置
      await this.loadPageConfig()
    },

    /**
     * 板块点击事件
     */
    onBlockTap(e) {
      const { blockType, blockConfig, detail } = e.detail
      this.triggerEvent('blockTap', {
        blockType,
        blockConfig,
        detail
      })
    },

    /**
     * 项目点击事件
     */
    onItemTap(e) {
      const { item, blockType } = e.detail
      this.triggerEvent('itemTap', {
        item,
        blockType
      })
    },

    /**
     * 处理推荐茶地板块
     */
    processRecommendedTeaBlock(block, dataSource) {
      // 推荐茶地板块不需要静态配置，由组件动态加载
      return block
    },

    /**
     * 处理视频频道板块
     */
    processVideoChannelBlock(block, dataSource) {
      // 视频频道板块不需要静态配置，由组件动态加载
      return block
    },

    /**
     * 处理新闻列表板块
     */
    processNewsListBlock(block, dataSource) {
      // 新闻列表板块不需要静态配置，由组件动态加载
      return block
    },

    /**
     * 处理AI投资建议板块
     */
    processAIInvestmentBlock(block, dataSource) {
      // AI投资建议板块不需要静态配置，由组件动态加载
      return block
    },

    /**
     * 处理统计概览板块
     */
    processStatsOverviewBlock(block, dataSource) {
      // 统计概览板块不需要静态配置，由组件动态加载
      return block
    },

    /**
     * 重试加载
     */
    onRetryLoad() {
      this.loadPageConfig()
    },

    /**
     * 轮播图点击事件
     */
    onBannerTap(e) {
      const url = e.currentTarget.dataset.url
      if (url) {
        wx.navigateTo({ url })
      }
    },

    /**
     * 开始新手指南
     */
    startGuide() {
      wx.navigateTo({ url: '/pages/guide/guide' })
    },

    /**
     * 浏览茶地
     */
    browseFields() {
      wx.navigateTo({ url: '/pages/tea-list/tea-list' })
    },

    /**
     * 关闭引导
     */
    closeGuide() {
      this.setData({ showGuide: false })
    },

    /**
     * 开始引导步骤
     */
    startGuideStep() {
      wx.navigateTo({ url: '/pages/guide/guide' })
    },

    /**
     * 页面导航
     */
    navigateTo(e) {
      const url = e.currentTarget.dataset.url
      if (url) {
        wx.navigateTo({ url })
      }
    },

    /**
     * 查看茶地详情
     */
    viewTeaDetail(e) {
      const id = e.currentTarget.dataset.id
      wx.navigateTo({ url: `/pages/tea-detail/tea-detail?id=${id}` })
    },

    /**
     * 打开AI聊天
     */
    openAIChat() {
      wx.navigateTo({ url: '/pages/ai-chat/ai-chat' })
    },

    /**
     * 刷新AI建议
     */
    refreshAdvice() {
      this.triggerEvent('refreshAdvice')
    },

    /**
     * 生成AI建议
     */
    generateAdvice() {
      this.triggerEvent('generateAdvice')
    },

    /**
     * 播放视频
     */
    playVideo(e) {
      const video = e.currentTarget.dataset.video
      wx.navigateTo({
        url: `/pages/video-detail/video-detail?id=${video.id}`
      })
    },

    /**
     * 查看新闻详情
     */
    viewNewsDetail(e) {
      const id = e.currentTarget.dataset.id
      wx.navigateTo({ url: `/pages/news-detail/news-detail?id=${id}` })
    },

    /**
     * 显示即将推出
     */
    showComingSoon() {
      wx.showToast({
        title: '功能即将推出',
        icon: 'none'
      })
    }
  }
})

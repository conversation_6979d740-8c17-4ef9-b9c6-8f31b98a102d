/**
 * 图标组件
 */
Component({
  properties: {
    // 图标名称
    name: {
      type: String,
      value: ''
    },
    // 图标类型
    type: {
      type: String,
      value: 'svg' // svg, emoji, text, image
    },
    // 图标大小
    size: {
      type: String,
      value: 'md' // xs, sm, md, lg, xl, xxl
    },
    // 图标颜色
    color: {
      type: String,
      value: 'primary' // primary, secondary, success, warning, error, info, white, black, gray
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      value: false
    },
    // 动画效果
    animation: {
      type: String,
      value: '' // spin, pulse, bounce, shake
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 自定义类名
    className: {
      type: String,
      value: ''
    }
  },

  data: {
    // 内部状态
    isPressed: false
  },

  lifetimes: {
    attached() {
      this.initIcon()
    }
  },

  observers: {
    'animation': function(newAnimation) {
      this.updateAnimation(newAnimation)
    }
  },

  methods: {
    // 初始化图标
    initIcon() {
      // 验证图标名称
      if (!this.data.name) {
        return
      }

      // 验证图标类型
      const validTypes = ['svg', 'emoji', 'text', 'image']
      if (!validTypes.includes(this.data.type)) {
        return
      }

      // 验证图标大小
      const validSizes = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl']
      if (!validSizes.includes(this.data.size)) {
        return
      }

      // 应用动画
      this.updateAnimation(this.data.animation)
    },

    // 更新动画
    updateAnimation(animation) {
      if (animation) {
        const validAnimations = ['spin', 'pulse', 'bounce', 'shake']
        if (validAnimations.includes(animation)) {
          this.setData({
            className: `${this.data.className} ${animation}`.trim()
          })
        }
      }
    },

    // 图标点击事件
    onIconTap(e) {
      if (!this.data.clickable) {
        return
      }

      // 设置按下状态
      this.setData({ isPressed: true })
      
      // 触发点击事件
      this.triggerEvent('tap', {
        name: this.data.name,
        type: this.data.type,
        size: this.data.size,
        color: this.data.color
      })

      // 重置按下状态
      setTimeout(() => {
        this.setData({ isPressed: false })
      }, 150)
    },

    // 设置图标名称
    setName(name) {
      this.setData({ name })
    },

    // 设置图标类型
    setType(type) {
      this.setData({ type })
    },

    // 设置图标大小
    setSize(size) {
      this.setData({ size })
    },

    // 设置图标颜色
    setColor(color) {
      this.setData({ color })
    },

    // 播放动画
    playAnimation(animation, duration = 1000) {
      if (!animation) return

      const validAnimations = ['spin', 'pulse', 'bounce', 'shake']
      if (!validAnimations.includes(animation)) {
        return
      }

      // 添加动画类
      const currentClass = this.data.className
      const newClass = `${currentClass} ${animation}`.trim()
      
      this.setData({ className: newClass })

      // 动画结束后移除类
      if (duration > 0) {
        setTimeout(() => {
          this.setData({ 
            className: currentClass 
          })
        }, duration)
      }
    },

    // 停止动画
    stopAnimation() {
      const className = this.data.className
        .replace(/\b(spin|pulse|bounce|shake)\b/g, '')
        .trim()
      
      this.setData({ className })
    },

    // 获取图标信息
    getIconInfo() {
      return {
        name: this.data.name,
        type: this.data.type,
        size: this.data.size,
        color: this.data.color,
        clickable: this.data.clickable,
        animation: this.data.animation
      }
    }
  }
})

/**
 * 离线状态指示器组件
 */
const { OfflineManager } = require('../../utils/offline.js')

Component({
  properties: {
    // 是否显示指示器
    showIndicator: {
      type: Boolean,
      value: true
    },
    // 是否显示状态栏
    showBar: {
      type: Boolean,
      value: true
    },
    // 自定义类名
    className: {
      type: String,
      value: ''
    },
    // 自动显示模态框
    autoShowModal: {
      type: Boolean,
      value: false
    }
  },

  data: {
    isOffline: false,
    statusText: '网络正常',
    pendingSyncs: 0,
    syncInProgress: false,
    showModal: false,
    showCacheStatus: false,
    offlineTimeText: '',
    cacheHitRate: '',
    cacheSize: '',
    cacheItems: 0,
    updateTimer: null
  },

  lifetimes: {
    attached() {
      this.initComponent()
    },

    detached() {
      this.cleanup()
    }
  },

  methods: {
    // 初始化组件
    initComponent() {
      try {
        // 初始化离线管理器
        OfflineManager.init()

        // 监听网络状态变化
        this.initNetworkListener()

        // 延迟更新状态，确保初始化完成
        setTimeout(() => {
          this.updateStatus()
        }, 100)

        // 启动定时更新
        this.startUpdateTimer()
      } catch (error) {
        console.error('❌ 离线指示器初始化失败:', error)
        // 设置默认状态
        this.setData({
          isOffline: false,
          cacheStats: {
            hitRate: 0,
            sizeFormatted: '0KB',
            items: 0
          }
        })
      }
    },

    // 清理资源
    cleanup() {
      if (this.data.updateTimer) {
        clearInterval(this.data.updateTimer)
      }
    },

    // 初始化网络监听
    initNetworkListener() {
      wx.onNetworkStatusChange((res) => {
        this.setData({
          isOffline: !res.isConnected,
          statusText: res.isConnected ? '网络已恢复' : '网络连接异常'
        })

        // 触发外部事件
        this.triggerEvent('networkchange', {
          isOffline: !res.isConnected,
          networkType: res.networkType
        })

        // 自动显示模态框
        if (!res.isConnected && this.data.autoShowModal) {
          this.showModal()
        }

        // 更新状态
        this.updateStatus()
      })
    },

    // 更新状态
    updateStatus() {
      try {
        // 获取离线状态
        const offlineState = OfflineManager.getOfflineState()

        // 获取缓存统计
        const rawStats = OfflineManager.cache ? OfflineManager.cache.getStats() : {
          memorySize: 0,
          persistentSize: 0,
          maxItems: 0,
          defaultTTL: 0
        }

        // 转换为组件需要的格式
        const cacheStats = {
          hitRate: 0, // 暂时设为0，后续可以添加命中率统计
          sizeFormatted: `${(rawStats.memorySize + rawStats.persistentSize)}项`,
          items: rawStats.memorySize + rawStats.persistentSize
        }

        this.setData({
          isOffline: offlineState.isOffline,
          pendingSyncs: offlineState.pendingSyncs.length,
          syncInProgress: offlineState.syncInProgress,
          offlineTimeText: this.formatOfflineTime(offlineState.offlineTime),
          cacheHitRate: cacheStats.hitRate || 0,
          cacheSize: cacheStats.sizeFormatted || '0项',
          cacheItems: cacheStats.items || 0
        })

        // 更新状态文本
        if (offlineState.isOffline) {
          this.setData({
            statusText: `离线 ${this.formatOfflineTime(offlineState.offlineTime)}`
          })
        } else if (offlineState.pendingSyncs.length > 0) {
          this.setData({
            statusText: `${offlineState.pendingSyncs.length}项待同步`
          })
        } else {
          this.setData({
            statusText: '网络正常'
          })
        }
      } catch (error) {
        console.error('❌ 更新离线状态失败:', error)
      }
    },

    // 启动定时更新
    startUpdateTimer() {
      const timer = setInterval(() => {
        this.updateStatus()
      }, 5000) // 每5秒更新一次

      this.setData({ updateTimer: timer })
    },

    // 格式化离线时间
    formatOfflineTime(milliseconds) {
      if (milliseconds < 60000) {
        return '刚刚'
      } else if (milliseconds < 3600000) {
        return `${Math.floor(milliseconds / 60000)}分钟`
      } else if (milliseconds < 86400000) {
        return `${Math.floor(milliseconds / 3600000)}小时`
      } else {
        return `${Math.floor(milliseconds / 86400000)}天`
      }
    },

    // 同步点击
    onSyncTap() {
      if (this.data.syncInProgress) {
        return
      }

      this.triggerEvent('sync')
      
      // 开始同步
      OfflineManager.syncOfflineData().then(() => {
        wx.showToast({
          title: '同步完成',
          icon: 'success'
        })
        this.updateStatus()
      }).catch((error) => {
        wx.showToast({
          title: '同步失败',
          icon: 'none'
        })
        console.error('❌ 手动同步失败:', error)
      })
    },

    // 显示模态框
    showModal() {
      this.setData({ showModal: true })
      this.triggerEvent('modalshow')
    },

    // 隐藏模态框
    hideModal() {
      this.setData({ showModal: false })
      this.triggerEvent('modalhide')
    },

    // 重试连接
    retryConnection() {
      this.hideModal()
      
      // 检查网络状态
      wx.getNetworkType({
        success: (res) => {
          if (res.networkType !== 'none') {
            wx.showToast({
              title: '网络已恢复',
              icon: 'success'
            })
            this.updateStatus()
          } else {
            wx.showToast({
              title: '网络仍不可用',
              icon: 'none'
            })
          }
        }
      })

      this.triggerEvent('retry')
    },

    // 显示缓存状态
    showCacheStatus() {
      this.setData({ showCacheStatus: true })
      this.updateStatus()
    },

    // 隐藏缓存状态
    hideCacheStatus() {
      this.setData({ showCacheStatus: false })
    },

    // 清理缓存
    clearCache() {
      wx.showModal({
        title: '确认清理',
        content: '确定要清理所有离线缓存吗？这将删除所有本地数据。',
        success: (res) => {
          if (res.confirm) {
            OfflineManager.clearOfflineData()
            
            wx.showToast({
              title: '缓存已清理',
              icon: 'success'
            })
            
            this.updateStatus()
            this.triggerEvent('cachecleared')
          }
        }
      })
    },

    // 立即同步
    syncNow() {
      this.hideCacheStatus()
      this.onSyncTap()
    },

    // 获取离线状态
    getOfflineState() {
      return OfflineManager.getOfflineState()
    },

    // 获取缓存统计
    getCacheStats() {
      return OfflineManager.cache.getStats()
    },

    // 手动更新状态
    refresh() {
      this.updateStatus()
    }
  }
})

/**
 * 消息徽章组件
 */
const { NotificationManager } = require('../../utils/notification.js')

Component({
  properties: {
    // 最大显示消息数量
    maxCount: {
      type: Number,
      value: 5
    },
    // 是否自动刷新
    autoRefresh: {
      type: Boolean,
      value: true
    },
    // 刷新间隔（毫秒）
    refreshInterval: {
      type: Number,
      value: 30000
    }
  },

  data: {
    unreadCount: 0,
    messages: [],
    showPopup: false,
    refreshTimer: null
  },

  lifetimes: {
    attached() {
      this.initComponent()
    },

    detached() {
      this.cleanup()
    }
  },

  methods: {
    // 初始化组件
    initComponent() {
      // 加载消息数据
      this.loadMessages()
      
      // 监听消息变化
      NotificationManager.addListener(this.onMessageChange.bind(this))
      
      // 设置自动刷新
      if (this.data.autoRefresh) {
        this.startAutoRefresh()
      }
    },

    // 清理资源
    cleanup() {
      // 移除消息监听器
      NotificationManager.removeListener(this.onMessageChange.bind(this))
      
      // 清除定时器
      if (this.data.refreshTimer) {
        clearInterval(this.data.refreshTimer)
      }
    },

    // 加载消息数据
    loadMessages() {
      const result = NotificationManager.getMessages({
        limit: this.data.maxCount
      })

      const messages = result.messages.map(msg => ({
        ...msg,
        timeText: this.formatTime(msg.timestamp)
      }))

      this.setData({
        messages,
        unreadCount: result.unreadCount
      })

      // 触发外部事件
      this.triggerEvent('messageload', {
        unreadCount: result.unreadCount,
        totalCount: result.total
      })
    },

    // 消息变化监听
    onMessageChange(event, data) {
      switch (event) {
        case 'new_message':
          this.loadMessages()
          // 触发新消息事件
          this.triggerEvent('newmessage', data)
          break
        case 'message_read':
        case 'all_read':
        case 'message_deleted':
        case 'all_cleared':
          this.loadMessages()
          break
      }
    },

    // 点击消息图标
    onTap() {
      this.setData({ showPopup: true })
      this.triggerEvent('badgetap')
    },

    // 关闭弹窗
    closePopup() {
      this.setData({ showPopup: false })
    },

    // 点击消息项
    onMessageTap(e) {
      const { message } = e.currentTarget.dataset
      
      // 标记为已读
      if (!message.read) {
        NotificationManager.markAsRead(message.id)
      }

      // 关闭弹窗
      this.closePopup()

      // 触发消息点击事件
      this.triggerEvent('messagetap', message)

      // 如果有跳转数据，执行跳转
      if (message.data && message.data.url) {
        wx.navigateTo({
          url: message.data.url,
          fail: () => {
            // 如果是tabBar页面，使用switchTab
            wx.switchTab({
              url: message.data.url
            })
          }
        })
      }
    },

    // 删除消息
    deleteMessage(e) {
      const { id } = e.currentTarget.dataset
      
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这条消息吗？',
        success: (res) => {
          if (res.confirm) {
            NotificationManager.deleteMessage(id)
            this.triggerEvent('messagedelete', { id })
          }
        }
      })
    },

    // 标记全部已读
    markAllRead() {
      NotificationManager.markAllAsRead()
      this.triggerEvent('allread')
    },

    // 查看全部消息
    viewAllMessages() {
      this.closePopup()
      wx.navigateTo({
        url: '/pages/notifications/notifications'
      })
      this.triggerEvent('viewall')
    },

    // 开始自动刷新
    startAutoRefresh() {
      const timer = setInterval(() => {
        this.loadMessages()
      }, this.data.refreshInterval)

      this.setData({ refreshTimer: timer })
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.data.refreshTimer) {
        clearInterval(this.data.refreshTimer)
        this.setData({ refreshTimer: null })
      }
    },

    // 手动刷新
    refresh() {
      this.loadMessages()
    },

    // 格式化时间
    formatTime(timestamp) {
      const now = Date.now()
      const diff = now - timestamp
      
      if (diff < 60000) {
        return '刚刚'
      } else if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) {
        return `${Math.floor(diff / 3600000)}小时前`
      } else if (diff < 604800000) {
        return `${Math.floor(diff / 86400000)}天前`
      } else {
        const date = new Date(timestamp)
        return `${date.getMonth() + 1}/${date.getDate()}`
      }
    },

    // 获取未读数量
    getUnreadCount() {
      return this.data.unreadCount
    },

    // 显示弹窗
    showPopup() {
      this.setData({ showPopup: true })
    },

    // 隐藏弹窗
    hidePopup() {
      this.setData({ showPopup: false })
    }
  }
})

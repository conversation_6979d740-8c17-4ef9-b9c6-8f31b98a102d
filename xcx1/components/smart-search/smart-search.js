/**
 * 智能搜索组件
 */
const { globalSearchManager, SEARCH_CATEGORIES } = require('../../utils/search.js')

Component({
  properties: {
    // 占位符文本
    placeholder: {
      type: String,
      value: '搜索茶地、产地、茶叶类型...'
    },
    // 最大输入长度
    maxLength: {
      type: Number,
      value: 50
    },
    // 是否显示搜索类别
    showCategories: {
      type: Boolean,
      value: true
    },
    // 默认搜索类别
    defaultCategory: {
      type: String,
      value: 'tea_name'
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 自定义类名
    className: {
      type: String,
      value: ''
    },
    // 是否自动获取焦点
    autoFocus: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // 搜索值
    searchValue: '',
    // 是否显示建议
    showSuggestions: false,
    // 输入框焦点
    inputFocus: false,
    // 当前搜索类别
    currentCategory: 'tea_name',
    // 搜索建议
    suggestions: [],
    // 搜索历史
    searchHistory: [],
    // 热门关键词
    hotKeywords: [],
    // 搜索类别列表
    categories: [
      { key: 'tea_name', name: '茶叶名称', icon: '🍃' },
      { key: 'location', name: '产地', icon: '📍' },
      { key: 'price', name: '价格', icon: '💰' },
      { key: 'area', name: '面积', icon: '📏' },
      { key: 'type', name: '茶叶类型', icon: '🌿' }
    ],
    // 加载状态
    loading: false
  },

  lifetimes: {
    attached() {
      this.initComponent()
    }
  },

  methods: {
    // 初始化组件
    initComponent() {
      // 设置默认类别
      this.setData({
        currentCategory: this.data.defaultCategory
      })
      
      // 加载搜索数据
      this.loadSearchData()
      
      // 设置自动焦点
      if (this.data.autoFocus) {
        setTimeout(() => {
          this.setData({ inputFocus: true })
        }, 100)
      }
    },

    // 加载搜索数据
    loadSearchData() {
      try {
        // 加载搜索历史
        const searchHistory = globalSearchManager.getSearchHistory(10)
        
        // 加载热门关键词
        const hotKeywords = globalSearchManager.getHotKeywords(8)
        
        this.setData({
          searchHistory,
          hotKeywords
        })
      } catch (error) {
        console.error('❌ 加载搜索数据失败:', error)
      }
    },

    // 输入事件
    onInput(e) {
      const value = e.detail.value
      this.setData({ searchValue: value })
      
      // 防抖获取搜索建议
      globalSearchManager.debounceSearch(value, (query) => {
        this.getSuggestions(query)
      })
      
      // 触发外部输入事件
      this.triggerEvent('input', { value })
    },

    // 获取搜索建议
    async getSuggestions(query) {
      if (!query || query.trim().length === 0) {
        this.setData({ suggestions: [] })
        return
      }

      try {
        this.setData({ loading: true })
        
        const suggestions = await globalSearchManager.getSearchSuggestions(
          query, 
          this.data.currentCategory
        )
        
        this.setData({ 
          suggestions,
          loading: false
        })
      } catch (error) {
        console.error('❌ 获取搜索建议失败:', error)
        this.setData({ 
          suggestions: [],
          loading: false
        })
      }
    },

    // 输入框获得焦点
    onFocus() {
      this.setData({ 
        inputFocus: true,
        showSuggestions: true
      })
      
      // 如果没有搜索值，重新加载搜索数据
      if (!this.data.searchValue) {
        this.loadSearchData()
      }
      
      this.triggerEvent('focus')
    },

    // 输入框失去焦点
    onBlur() {
      // 延迟隐藏建议，以便点击建议项
      setTimeout(() => {
        this.setData({ 
          inputFocus: false,
          showSuggestions: false
        })
      }, 200)
      
      this.triggerEvent('blur')
    },

    // 确认搜索
    onConfirm(e) {
      const value = e.detail.value || this.data.searchValue
      this.performSearch(value)
    },

    // 点击搜索按钮
    onSearch() {
      this.performSearch(this.data.searchValue)
    },

    // 执行搜索
    performSearch(keyword) {
      if (!keyword || keyword.trim().length === 0) {
        wx.showToast({
          title: '请输入搜索关键词',
          icon: 'none'
        })
        return
      }

      const trimmedKeyword = keyword.trim()
      
      // 添加到搜索历史
      globalSearchManager.addSearchHistory(trimmedKeyword, this.data.currentCategory)
      
      // 隐藏建议
      this.setData({ showSuggestions: false })
      
      // 触发搜索事件
      this.triggerEvent('search', {
        keyword: trimmedKeyword,
        category: this.data.currentCategory
      })
    },

    // 选择搜索建议
    selectSuggestion(e) {
      const suggestion = e.currentTarget.dataset.suggestion
      this.setData({ searchValue: suggestion.keyword })
      this.performSearch(suggestion.keyword)
    },

    // 插入搜索建议到输入框
    insertSuggestion(e) {
      const keyword = e.currentTarget.dataset.keyword
      this.setData({ 
        searchValue: keyword,
        inputFocus: true
      })
      
      // 重新获取建议
      this.getSuggestions(keyword)
    },

    // 选择搜索历史
    selectHistory(e) {
      const item = e.currentTarget.dataset.item
      this.setData({ 
        searchValue: item.keyword,
        currentCategory: item.category
      })
      this.performSearch(item.keyword)
    },

    // 删除搜索历史
    deleteHistory(e) {
      const item = e.currentTarget.dataset.item
      
      wx.showModal({
        title: '确认删除',
        content: `确定要删除搜索历史"${item.keyword}"吗？`,
        success: (res) => {
          if (res.confirm) {
            globalSearchManager.removeSearchHistory(item.keyword, item.category)
            this.loadSearchData()
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
          }
        }
      })
    },

    // 清空搜索历史
    clearHistory() {
      wx.showModal({
        title: '确认清空',
        content: '确定要清空所有搜索历史吗？',
        success: (res) => {
          if (res.confirm) {
            globalSearchManager.clearSearchHistory()
            this.loadSearchData()
            
            wx.showToast({
              title: '清空成功',
              icon: 'success'
            })
          }
        }
      })
    },

    // 选择热门关键词
    selectHotKeyword(e) {
      const keyword = e.currentTarget.dataset.keyword
      this.setData({ searchValue: keyword })
      this.performSearch(keyword)
    },

    // 选择搜索类别
    selectCategory(e) {
      const category = e.currentTarget.dataset.category
      this.setData({ currentCategory: category })
      
      // 如果有搜索值，重新获取建议
      if (this.data.searchValue) {
        this.getSuggestions(this.data.searchValue)
      }
      
      this.triggerEvent('categorychange', { category })
    },

    // 清空输入
    clearInput() {
      this.setData({ 
        searchValue: '',
        suggestions: []
      })
      
      // 重新加载搜索数据
      this.loadSearchData()
      
      this.triggerEvent('clear')
    },

    // 隐藏建议
    hideSuggestions() {
      this.setData({ showSuggestions: false })
    },

    // 获取建议图标
    getSuggestionIcon(type) {
      const icons = {
        'history': '🕐',
        'hot': '🔥',
        'smart': '💡'
      }
      return icons[type] || '🔍'
    },

    // 设置搜索值
    setSearchValue(value) {
      this.setData({ searchValue: value })
      if (value) {
        this.getSuggestions(value)
      }
    },

    // 获取搜索值
    getSearchValue() {
      return this.data.searchValue
    },

    // 获取当前类别
    getCurrentCategory() {
      return this.data.currentCategory
    },

    // 显示建议面板
    showSuggestionsPanel() {
      this.setData({ showSuggestions: true })
      this.loadSearchData()
    },

    // 刷新搜索数据
    refreshSearchData() {
      this.loadSearchData()
    }
  }
})

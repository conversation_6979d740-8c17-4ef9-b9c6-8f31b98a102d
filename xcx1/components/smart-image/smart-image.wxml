<!-- 智能图片组件 -->
<view class="smart-image-container {{customClass}}">
  <!-- 加载状态 -->
  <view wx:if="{{loading && showLoading}}" class="image-loading">
    <view class="loading-spinner"></view>
  </view>
  
  <!-- 图片内容 -->
  <image 
    wx:if="{{currentSrc && !loadError}}"
    src="{{currentSrc}}"
    mode="{{mode}}"
    lazy-load="{{lazyLoad}}"
    class="smart-image"
    bindload="onImageLoad"
    binderror="onImageError"
    bindtap="onImageTap"
  />
  
  <!-- 占位符 -->
  <view wx:elif="{{loadError || !currentSrc}}" class="image-placeholder" bindtap="onImageTap">
    <view class="placeholder-content">
      <text wx:if="{{placeholder}}" class="placeholder-text">{{placeholder}}</text>
      <text wx:else class="placeholder-icon">
        {{type === 'avatar' ? '👤' : type === 'banner' ? '🖼️' : '🌱'}}
      </text>
    </view>
  </view>
</view>

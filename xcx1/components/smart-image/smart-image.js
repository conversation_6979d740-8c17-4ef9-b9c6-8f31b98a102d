/**
 * 智能图片组件
 * 支持懒加载、错误处理、占位符等功能
 */
Component({
  properties: {
    // 图片地址
    src: {
      type: String,
      value: ''
    },
    // 图片类型（用于选择默认图片）
    type: {
      type: String,
      value: 'tea' // tea, avatar, banner
    },
    // 显示模式
    mode: {
      type: String,
      value: 'aspectFill'
    },
    // 是否启用懒加载
    lazyLoad: {
      type: Boolean,
      value: true
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    // 占位符文字
    placeholder: {
      type: String,
      value: ''
    },
    // 是否显示加载状态
    showLoading: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 当前显示的图片地址
    currentSrc: '',
    // 是否加载失败
    loadError: false,
    // 是否正在加载
    loading: true,
    // 默认图片映射
    defaultImages: {
      'tea': '/images/tea-default.jpg',
      'avatar': '/images/avatar-default.png',
      'banner': '/images/logo.png'
    }
  },

  lifetimes: {
    attached() {
      this.initImage()
    }
  },

  observers: {
    'src': function(newSrc) {
      this.initImage()
    }
  },

  methods: {
    // 初始化图片
    initImage() {
      const { src, type } = this.properties
      
      if (src && src.trim() !== '') {
        this.setData({
          currentSrc: src,
          loadError: false,
          loading: true
        })
      } else {
        // 没有图片地址，直接显示占位符
        this.setData({
          currentSrc: '',
          loadError: true,
          loading: false
        })
      }
    },

    // 图片加载成功
    onImageLoad(e) {
      this.setData({
        loading: false,
        loadError: false
      })
      
      // 触发加载成功事件
      this.triggerEvent('load', {
        src: this.properties.src,
        detail: e.detail
      })
    },

    // 图片加载失败
    onImageError(e) {
      const { type } = this.properties
      const defaultSrc = this.data.defaultImages[type] || this.data.defaultImages.tea
      
      this.setData({
        currentSrc: defaultSrc,
        loadError: true,
        loading: false
      })
      
      // 触发加载失败事件
      this.triggerEvent('error', {
        src: this.properties.src,
        defaultSrc: defaultSrc,
        detail: e.detail
      })
    },

    // 图片点击事件
    onImageTap(e) {
      this.triggerEvent('tap', {
        src: this.data.currentSrc,
        originalSrc: this.properties.src
      })
    },

    // 重新加载图片
    reload() {
      this.initImage()
    }
  }
})

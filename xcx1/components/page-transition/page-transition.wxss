/* 页面切换动画样式 */

.page-transition {
  width: 100%;
  min-height: 100vh;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center center;
}

/* 淡入淡出动画 */
.fade-enter {
  opacity: 0;
  transform: translateY(20rpx);
}

.fade-enter-active {
  opacity: 1;
  transform: translateY(0);
}

.fade-exit {
  opacity: 1;
  transform: translateY(0);
}

.fade-exit-active {
  opacity: 0;
  transform: translateY(-20rpx);
}

/* 滑动动画 */
.slide-left-enter {
  opacity: 0;
  transform: translateX(100%);
}

.slide-left-enter-active {
  opacity: 1;
  transform: translateX(0);
}

.slide-left-exit {
  opacity: 1;
  transform: translateX(0);
}

.slide-left-exit-active {
  opacity: 0;
  transform: translateX(-100%);
}

.slide-right-enter {
  opacity: 0;
  transform: translateX(-100%);
}

.slide-right-enter-active {
  opacity: 1;
  transform: translateX(0);
}

.slide-right-exit {
  opacity: 1;
  transform: translateX(0);
}

.slide-right-exit-active {
  opacity: 0;
  transform: translateX(100%);
}

/* 缩放动画 */
.scale-enter {
  opacity: 0;
  transform: scale(0.9);
}

.scale-enter-active {
  opacity: 1;
  transform: scale(1);
}

.scale-exit {
  opacity: 1;
  transform: scale(1);
}

.scale-exit-active {
  opacity: 0;
  transform: scale(1.1);
}

/* 弹性动画 */
.bounce-enter {
  opacity: 0;
  transform: scale(0.3);
  animation: bounceIn 0.5s ease-out forwards;
}

.bounce-enter-active {
  opacity: 1;
  transform: scale(1);
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 旋转动画 */
.rotate-enter {
  opacity: 0;
  transform: rotate(-180deg) scale(0.5);
}

.rotate-enter-active {
  opacity: 1;
  transform: rotate(0deg) scale(1);
}

/* 翻转动画 */
.flip-enter {
  opacity: 0;
  transform: perspective(400px) rotateY(90deg);
}

.flip-enter-active {
  opacity: 1;
  transform: perspective(400px) rotateY(0deg);
}

/* 页面加载骨架屏动画 */
.skeleton-loading {
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
}

/* 响应式适配 */
@media (prefers-reduced-motion: reduce) {
  .page-transition {
    transition: none;
    animation: none;
  }

  .page-transition view,
  .page-transition text,
  .page-transition image,
  .page-transition button {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

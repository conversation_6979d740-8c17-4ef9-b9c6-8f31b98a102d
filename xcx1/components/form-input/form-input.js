/**
 * 表单输入组件
 */
const { FormValidator, VALIDATION_TYPES } = require('../../utils/validator.js')

Component({
  properties: {
    // 输入值
    value: {
      type: String,
      value: ''
    },
    // 输入类型
    type: {
      type: String,
      value: 'text' // text, number, password, textarea, email, phone
    },
    // 标签
    label: {
      type: String,
      value: ''
    },
    // 占位符
    placeholder: {
      type: String,
      value: ''
    },
    // 是否必填
    required: {
      type: Boolean,
      value: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 最大长度
    maxLength: {
      type: Number,
      value: -1
    },
    // 是否自动获取焦点
    focus: {
      type: Boolean,
      value: false
    },
    // 前缀图标
    prefixIcon: {
      type: String,
      value: ''
    },
    // 后缀图标
    suffixIcon: {
      type: String,
      value: ''
    },
    // 是否显示清空按钮
    showClear: {
      type: Boolean,
      value: true
    },
    // 是否显示密码切换（仅password类型）
    showPassword: {
      type: Boolean,
      value: true
    },
    // 是否显示字符计数
    showCharCount: {
      type: Boolean,
      value: false
    },
    // 是否自动高度（仅textarea）
    autoHeight: {
      type: Boolean,
      value: true
    },
    // 确认按钮类型
    confirmType: {
      type: String,
      value: 'done'
    },
    // 帮助文本
    helpText: {
      type: String,
      value: ''
    },
    // 验证规则
    rules: {
      type: Array,
      value: []
    },
    // 是否实时验证
    realTimeValidation: {
      type: Boolean,
      value: true
    },
    // 是否显示验证状态
    showValidation: {
      type: Boolean,
      value: true
    },
    // 自定义类名
    className: {
      type: String,
      value: ''
    }
  },

  data: {
    // 当前长度
    currentLength: 0,
    // 是否有错误
    error: false,
    // 错误信息
    errorMessage: '',
    // 验证状态
    validationStatus: '', // success, error, loading
    // 密码是否可见
    passwordVisible: false,
    // 输入框类型
    inputType: 'text',
    // 是否已触摸
    touched: false,
    // 验证器
    validator: null
  },

  lifetimes: {
    attached() {
      this.initComponent()
    }
  },

  observers: {
    'value': function(newValue) {
      this.updateCurrentLength(newValue)
      
      // 实时验证
      if (this.data.realTimeValidation && this.data.touched) {
        this.validateInput(newValue)
      }
    },
    'type': function(newType) {
      this.updateInputType(newType)
    },
    'rules': function(newRules) {
      this.initValidator(newRules)
    }
  },

  methods: {
    // 初始化组件
    initComponent() {
      // 初始化验证器
      this.initValidator(this.data.rules)
      
      // 更新输入类型
      this.updateInputType(this.data.type)
      
      // 更新当前长度
      this.updateCurrentLength(this.data.value)
    },

    // 初始化验证器
    initValidator(rules) {
      if (rules && rules.length > 0) {
        this.setData({
          validator: new FormValidator({
            input: rules
          })
        })
      }
    },

    // 更新输入类型
    updateInputType(type) {
      let inputType = 'text'
      
      switch (type) {
        case 'number':
          inputType = 'number'
          break
        case 'email':
          inputType = 'text'
          break
        case 'phone':
          inputType = 'number'
          break
        case 'password':
          inputType = this.data.passwordVisible ? 'text' : 'password'
          break
        default:
          inputType = 'text'
      }
      
      this.setData({ inputType })
    },

    // 更新当前长度
    updateCurrentLength(value) {
      const length = value ? String(value).length : 0
      this.setData({ currentLength: length })
    },

    // 输入事件
    onInput(e) {
      const value = e.detail.value
      
      // 更新值
      this.setData({ value })
      
      // 触发外部输入事件
      this.triggerEvent('input', { value })
      
      // 实时验证
      if (this.data.realTimeValidation && this.data.touched) {
        this.validateInput(value)
      }
    },

    // 获得焦点
    onFocus(e) {
      this.setData({ touched: true })
      this.triggerEvent('focus', e.detail)
    },

    // 失去焦点
    onBlur(e) {
      const value = e.detail.value
      
      // 失去焦点时验证
      this.validateInput(value)
      
      this.triggerEvent('blur', { value })
    },

    // 确认输入
    onConfirm(e) {
      const value = e.detail.value
      this.triggerEvent('confirm', { value })
    },

    // 验证输入
    async validateInput(value) {
      if (!this.data.validator) {
        return { valid: true }
      }

      try {
        // 显示验证中状态
        this.setData({ validationStatus: 'loading' })
        
        // 执行验证
        const result = this.data.validator.validateField('input', value)
        
        // 更新验证状态
        this.setData({
          error: !result.valid,
          errorMessage: result.errors.length > 0 ? result.errors[0] : '',
          validationStatus: result.valid ? 'success' : 'error'
        })
        
        // 触发验证事件
        this.triggerEvent('validate', {
          valid: result.valid,
          errors: result.errors,
          value
        })
        
        return result
      } catch (error) {
        console.error('❌ 输入验证失败:', error)
        
        this.setData({
          error: true,
          errorMessage: '验证过程中发生错误',
          validationStatus: 'error'
        })
        
        return { valid: false, errors: ['验证失败'] }
      }
    },

    // 清空输入
    clearInput() {
      this.setData({ 
        value: '',
        error: false,
        errorMessage: '',
        validationStatus: ''
      })
      
      this.triggerEvent('input', { value: '' })
      this.triggerEvent('clear')
    },

    // 切换密码显示
    togglePassword() {
      const passwordVisible = !this.data.passwordVisible
      this.setData({ passwordVisible })
      this.updateInputType(this.data.type)
      
      this.triggerEvent('passwordtoggle', { visible: passwordVisible })
    },

    // 手动验证
    validate() {
      return this.validateInput(this.data.value)
    },

    // 重置验证状态
    resetValidation() {
      this.setData({
        error: false,
        errorMessage: '',
        validationStatus: '',
        touched: false
      })
    },

    // 设置错误
    setError(message) {
      this.setData({
        error: true,
        errorMessage: message,
        validationStatus: 'error'
      })
    },

    // 清除错误
    clearError() {
      this.setData({
        error: false,
        errorMessage: '',
        validationStatus: this.data.value ? 'success' : ''
      })
    },

    // 获取值
    getValue() {
      return this.data.value
    },

    // 设置值
    setValue(value) {
      this.setData({ value })
      
      // 如果已经触摸过，进行验证
      if (this.data.touched) {
        this.validateInput(value)
      }
    },

    // 获取验证状态
    getValidationState() {
      return {
        valid: !this.data.error,
        error: this.data.error,
        errorMessage: this.data.errorMessage,
        touched: this.data.touched,
        value: this.data.value
      }
    },

    // 设置焦点
    setFocus() {
      this.setData({ focus: true })
    },

    // 失去焦点
    blur() {
      this.setData({ focus: false })
    }
  }
})

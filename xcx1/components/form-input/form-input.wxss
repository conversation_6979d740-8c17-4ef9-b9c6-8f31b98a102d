/* 表单输入组件样式 */

.form-input {
  width: 100%;
  margin-bottom: 32rpx;
}

/* 标签 */
.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.required-mark {
  color: #ff4444;
  margin-left: 8rpx;
  font-size: 28rpx;
}

/* 输入框容器 */
.input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  min-height: 88rpx;
}

.input-container:focus-within {
  border-color: #2E7D32;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(46, 125, 50, 0.1);
}

.form-input.error .input-container {
  border-color: #ff4444;
  background: #fff5f5;
}

.form-input.error .input-container:focus-within {
  border-color: #ff4444;
  box-shadow: 0 0 0 6rpx rgba(255, 68, 68, 0.1);
}

.form-input.disabled .input-container {
  background: #f5f5f5;
  border-color: #ddd;
  opacity: 0.6;
}

/* 前缀图标 */
.input-prefix {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  margin-left: 16rpx;
}

.prefix-icon {
  font-size: 28rpx;
  color: #666;
}

/* 输入框 */
.form-input-field,
.form-textarea-field {
  flex: 1;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  line-height: 1.4;
}

.form-textarea-field {
  min-height: 120rpx;
  resize: none;
}

.input-placeholder {
  color: #999;
}

/* 后缀内容 */
.input-suffix {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding-right: 16rpx;
}

.clear-btn,
.password-toggle,
.suffix-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.clear-btn {
  opacity: 0;
  visibility: hidden;
  transform: scale(0.8);
}

.clear-btn.show {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

.clear-btn:active,
.password-toggle:active,
.suffix-icon:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
}

.clear-icon,
.password-icon,
.icon {
  font-size: 24rpx;
  color: #666;
}

/* 验证状态指示 */
.validation-indicator {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.validation-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 20rpx;
  font-weight: bold;
}

.validation-icon.success {
  background: #e8f5e8;
  color: #2E7D32;
}

.validation-icon.error {
  background: #ffebee;
  color: #ff4444;
}

.validation-icon.loading {
  background: #e3f2fd;
  color: #2196f3;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 错误信息 */
.error-message {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-top: 12rpx;
  padding: 12rpx 16rpx;
  background: #ffebee;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff4444;
}

.error-icon {
  font-size: 24rpx;
  color: #ff4444;
}

.error-text {
  font-size: 24rpx;
  color: #ff4444;
  line-height: 1.4;
}

/* 帮助文本 */
.help-text {
  margin-top: 12rpx;
  padding: 0 16rpx;
}

.help-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 字符计数 */
.char-count {
  display: flex;
  justify-content: flex-end;
  margin-top: 8rpx;
  padding: 0 16rpx;
}

.count-text {
  font-size: 22rpx;
  color: #999;
}

.form-input.error .count-text {
  color: #ff4444;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .form-input {
    margin-bottom: 24rpx;
  }
  
  .input-container {
    min-height: 80rpx;
  }
  
  .form-input-field,
  .form-textarea-field {
    padding: 16rpx 20rpx;
    font-size: 26rpx;
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .label-text {
    color: #fff;
  }
  
  .input-container {
    background: #2a2a2a;
    border-color: #444;
  }
  
  .input-container:focus-within {
    background: #333;
  }
  
  .form-input-field,
  .form-textarea-field {
    color: #fff;
  }
  
  .input-placeholder {
    color: #666;
  }
  
  .help-content {
    color: #ccc;
  }
  
  .count-text {
    color: #666;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .input-container,
  .clear-btn,
  .password-toggle,
  .suffix-icon {
    transition: none;
  }
  
  .validation-icon.loading {
    animation: none;
  }
}

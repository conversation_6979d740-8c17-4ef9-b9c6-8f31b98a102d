/**
 * 小程序优化配置
 * 包含性能优化、用户体验优化等配置
 */

// 性能优化配置
const PERFORMANCE_CONFIG = {
  // 页面预加载
  preloadPages: [
    '/pages/tea-list/tea-list',
    '/pages/tea-detail/tea-detail',
    '/pages/profile/profile'
  ],
  
  // 图片优化
  imageOptimization: {
    enableLazyLoad: true,
    enableWebP: true,
    compressionQuality: 80,
    maxWidth: 750,
    maxHeight: 750
  },
  
  // 缓存配置
  cache: {
    maxSize: 50 * 1024 * 1024, // 50MB
    defaultTTL: 24 * 60 * 60 * 1000, // 24小时
    enableOfflineCache: true,
    enableMemoryCache: true
  },
  
  // 网络优化
  network: {
    timeout: 10000,
    retryTimes: 3,
    retryDelay: 1000,
    enableRequestMerge: true,
    enableResponseCache: true
  },
  
  // 渲染优化
  rendering: {
    enableVirtualList: true,
    listItemHeight: 120,
    bufferSize: 10,
    enableSkeleton: true
  }
}

// 用户体验配置
const UX_CONFIG = {
  // 动画配置
  animation: {
    enablePageTransition: true,
    enableMicroInteraction: true,
    defaultDuration: 300,
    defaultEasing: 'ease-out'
  },
  
  // 反馈配置
  feedback: {
    enableHaptic: true,
    enableSound: false,
    enableVisual: true,
    hapticIntensity: 'light'
  },
  
  // 主题配置
  theme: {
    defaultTheme: 'auto',
    defaultColorScheme: 'green',
    enableDarkMode: true,
    enableCustomColors: true
  },
  
  // 无障碍配置
  accessibility: {
    enableScreenReader: true,
    enableHighContrast: true,
    enableLargeText: true,
    enableReducedMotion: true
  }
}

// 开发配置
const DEVELOPMENT_CONFIG = {
  // 调试配置
  debug: {
    enableConsoleLog: true,
    enablePerformanceMonitor: true,
    enableErrorReporting: true,
    logLevel: 'info'
  },
  
  // 测试配置
  testing: {
    enableAutoTest: false,
    testTimeout: 5000,
    enableMockData: false,
    enableTestReporting: true
  },
  
  // 代码质量
  codeQuality: {
    enableLinting: true,
    enableTypeChecking: false,
    enableCodeAnalysis: true,
    maxComplexity: 10
  }
}

// 生产配置
const PRODUCTION_CONFIG = {
  // 性能监控
  monitoring: {
    enablePerformanceTracking: true,
    enableErrorTracking: true,
    enableUserTracking: false,
    reportingUrl: null
  },
  
  // 安全配置
  security: {
    enableDataEncryption: true,
    enableRequestSigning: false,
    enableCSRFProtection: true,
    maxRequestSize: 10 * 1024 * 1024 // 10MB
  },
  
  // 优化配置
  optimization: {
    enableCodeSplitting: true,
    enableTreeShaking: true,
    enableMinification: true,
    enableGzip: true
  }
}

// 功能开关配置
const FEATURE_FLAGS = {
  // 新功能
  newFeatures: {
    enableAdvancedSearch: true,
    enableSocialSharing: true,
    enableOfflineMode: true,
    enablePushNotifications: true
  },
  
  // 实验性功能
  experimental: {
    enableAIRecommendation: false,
    enableVoiceSearch: false,
    enableARView: false,
    enableBlockchain: false
  },
  
  // 第三方集成
  integrations: {
    enableWeChatPay: true,
    enableAlipay: true,
    enableMap: true,
    enableAnalytics: true
  }
}

// 环境配置
const ENVIRONMENT_CONFIG = {
  development: {
    ...DEVELOPMENT_CONFIG,
    apiBaseUrl: 'https://dev-api.example.com',
    enableMockData: true,
    logLevel: 'debug'
  },
  
  staging: {
    ...DEVELOPMENT_CONFIG,
    ...PRODUCTION_CONFIG,
    apiBaseUrl: 'https://staging-api.example.com',
    enableMockData: false,
    logLevel: 'info'
  },
  
  production: {
    ...PRODUCTION_CONFIG,
    apiBaseUrl: 'https://api.example.com',
    enableMockData: false,
    logLevel: 'error'
  }
}

/**
 * 配置管理器
 */
class ConfigManager {
  constructor() {
    this.environment = this.detectEnvironment()
    this.config = this.loadConfig()
  }

  /**
   * 检测当前环境
   */
  detectEnvironment() {
    // 根据小程序版本或其他标识判断环境
    try {
      const accountInfo = wx.getAccountInfoSync()
      const envVersion = accountInfo.miniProgram.envVersion
      
      switch (envVersion) {
        case 'develop':
          return 'development'
        case 'trial':
          return 'staging'
        case 'release':
          return 'production'
        default:
          return 'development'
      }
    } catch (error) {
      console.warn('❌ 检测环境失败，使用默认环境:', error)
      return 'development'
    }
  }

  /**
   * 加载配置
   */
  loadConfig() {
    const envConfig = ENVIRONMENT_CONFIG[this.environment] || ENVIRONMENT_CONFIG.development
    
    return {
      environment: this.environment,
      performance: PERFORMANCE_CONFIG,
      ux: UX_CONFIG,
      features: FEATURE_FLAGS,
      ...envConfig
    }
  }

  /**
   * 获取配置
   * @param {string} key 配置键
   * @returns {any} 配置值
   */
  get(key) {
    const keys = key.split('.')
    let value = this.config
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        return undefined
      }
    }
    
    return value
  }

  /**
   * 设置配置
   * @param {string} key 配置键
   * @param {any} value 配置值
   */
  set(key, value) {
    const keys = key.split('.')
    let target = this.config
    
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i]
      if (!target[k] || typeof target[k] !== 'object') {
        target[k] = {}
      }
      target = target[k]
    }
    
    target[keys[keys.length - 1]] = value
  }

  /**
   * 检查功能是否启用
   * @param {string} feature 功能名称
   * @returns {boolean} 是否启用
   */
  isFeatureEnabled(feature) {
    return this.get(`features.newFeatures.${feature}`) || 
           this.get(`features.experimental.${feature}`) || 
           this.get(`features.integrations.${feature}`) || 
           false
  }

  /**
   * 获取API基础URL
   * @returns {string} API基础URL
   */
  getApiBaseUrl() {
    return this.get('apiBaseUrl') || 'https://api.example.com'
  }

  /**
   * 获取性能配置
   * @returns {Object} 性能配置
   */
  getPerformanceConfig() {
    return this.get('performance') || PERFORMANCE_CONFIG
  }

  /**
   * 获取用户体验配置
   * @returns {Object} 用户体验配置
   */
  getUXConfig() {
    return this.get('ux') || UX_CONFIG
  }

  /**
   * 是否为开发环境
   * @returns {boolean} 是否为开发环境
   */
  isDevelopment() {
    return this.environment === 'development'
  }

  /**
   * 是否为生产环境
   * @returns {boolean} 是否为生产环境
   */
  isProduction() {
    return this.environment === 'production'
  }

  /**
   * 获取日志级别
   * @returns {string} 日志级别
   */
  getLogLevel() {
    return this.get('logLevel') || 'info'
  }

  /**
   * 导出配置
   * @returns {Object} 完整配置
   */
  exportConfig() {
    return JSON.parse(JSON.stringify(this.config))
  }

  /**
   * 重新加载配置
   */
  reload() {
    this.environment = this.detectEnvironment()
    this.config = this.loadConfig()
  }
}

// 创建全局配置管理器实例
const globalConfigManager = new ConfigManager()

// 初始化日志
// Debug log removed
  apiBaseUrl: globalConfigManager.getApiBaseUrl(),
  logLevel: globalConfigManager.getLogLevel(),
  isDevelopment: globalConfigManager.isDevelopment(),
  isProduction: globalConfigManager.isProduction()
})

module.exports = {
  ConfigManager,
  globalConfigManager,
  PERFORMANCE_CONFIG,
  UX_CONFIG,
  DEVELOPMENT_CONFIG,
  PRODUCTION_CONFIG,
  FEATURE_FLAGS,
  ENVIRONMENT_CONFIG
}

// 两山·茶管家 - 应用入口文件
const api = require('./api/index.js')

// 内联的页面路由管理器
const pageRouter = {
  currentPage: '',
  tabPages: new Set(),
  pageStack: [],

  // 初始化路由管理器
  init(tabPages = []) {
    // 设置tab页面列表
    this.tabPages = new Set(tabPages.map(page => this.normalizePath(page)))

    // 获取当前页面
    this.updateCurrentPage()
    // Debug log removed
  },

  // 标准化页面路径
  normalizePath(path) {
    // 移除开头的斜杠，确保路径格式一致
    return path.replace(/^\//, '')
  },

  // 更新当前页面信息
  updateCurrentPage() {
    const pages = getCurrentPages()
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      this.currentPage = currentPage.route
      this.pageStack = pages.map(page => page.route)
    }
  },

  // 判断是否为tab页面
  isTabPage(path) {
    const normalizedPath = this.normalizePath(path)
    return this.tabPages.has(normalizedPath)
  },

  // 获取当前tab页面的索引
  getCurrentTabIndex(tabList) {
    this.updateCurrentPage()

    for (let i = 0; i < tabList.length; i++) {
      const tabPath = this.normalizePath(tabList[i].pagePath)
      if (tabPath === this.currentPage) {
        return i
      }
    }

    return 0 // 默认返回第一个
  },

  // 导航到指定页面
  navigateTo(path, params = {}) {
    const normalizedPath = this.normalizePath(path)
    // 对于自定义底部导航，所有页面都使用navigateTo
    // 不使用switchTab，因为没有原生tabBar配置
    if (this.isTabPage(normalizedPath)) {
      // 自定义Tab页面：检查是否已在页面栈中
      this.navigateToTabPage(normalizedPath, params)
    } else {
      // 非Tab页面使用navigateTo
      this.navigateToPage(normalizedPath, params)
    }
  },

  // 导航到自定义Tab页面
  navigateToTabPage(path, params = {}) {
    const normalizedPath = this.normalizePath(path)

    // 检查当前页面栈
    const pages = getCurrentPages()
    const currentPage = pages.length > 0 ? pages[pages.length - 1].route : ''

    // 如果已经在目标页面，不需要导航
    if (currentPage === normalizedPath) {
      return
    }

    // 检查页面栈中是否已有目标页面
    const targetPageIndex = pages.findIndex(page => page.route === normalizedPath)

    if (targetPageIndex !== -1) {
      // 目标页面已在栈中，返回到该页面
      const delta = pages.length - 1 - targetPageIndex
      wx.navigateBack({
        delta: delta,
        success: () => {
          this.updateCurrentPage()
        },
        fail: (error) => {
          console.error(`❌ 返回页面失败: ${normalizedPath}`, error)
          // 降级处理：使用reLaunch
          this.reLaunchPage(normalizedPath, params)
        }
      })
    } else {
      // 目标页面不在栈中，使用navigateTo
      this.navigateToPage(normalizedPath, params)
    }
  },

  // 切换到Tab页面
  switchTab(path, params = {}) {
    const normalizedPath = this.normalizePath(path)

    // 构建URL
    let url = `/${normalizedPath}`

    // 添加参数（注意：switchTab不支持参数，这里只是为了兼容）
    if (Object.keys(params).length > 0) {
    }

    wx.switchTab({
      url: url,
      success: () => {
        this.updateCurrentPage()
      },
      fail: (error) => {
        console.error(`❌ 切换Tab页面失败: ${normalizedPath}`, error)
        // 降级处理：使用reLaunch
        this.reLaunchPage(normalizedPath, params)
      }
    })
  },

  // 导航到普通页面
  navigateToPage(path, params = {}) {
    const normalizedPath = this.normalizePath(path)

    // 构建URL
    let url = `/${normalizedPath}`

    // 添加参数
    if (Object.keys(params).length > 0) {
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&')
      url += `?${queryString}`
    }

    wx.navigateTo({
      url: url,
      success: () => {
        this.updateCurrentPage()
      },
      fail: (error) => {
        console.error(`❌ 导航到页面失败: ${normalizedPath}`, error)
      }
    })
  },

  // 重新启动到指定页面
  reLaunchPage(path, params = {}) {
    const normalizedPath = this.normalizePath(path)

    // 构建URL
    let url = `/${normalizedPath}`

    // 添加参数
    if (Object.keys(params).length > 0) {
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&')
      url += `?${queryString}`
    }

    wx.reLaunch({
      url: url,
      success: () => {
        this.updateCurrentPage()
      },
      fail: (error) => {
        console.error(`❌ 重启到页面失败: ${normalizedPath}`, error)
      }
    })
  }
}

// 内联的简单底部导航管理器
const simpleTabBarManager = {
  // 获取底部导航配置
  async getTabBarConfig() {
    try {
      return new Promise((resolve, reject) => {
        wx.request({
          url: 'https://teabuy.yizhangkj.com/api/v1/page-decoration/tabbar/config/',
          method: 'GET',
          header: {
            'Cache-Control': 'no-cache'
          },
          success: (res) => {
            if (res.data && res.data.code === 200) {
              resolve(res.data.data.config)
            } else {
              resolve(this.getDefaultConfig())
            }
          },
          fail: (error) => {
            console.error('❌ 请求失败，使用默认配置:', error)
            resolve(this.getDefaultConfig())
          }
        })
      })
    } catch (error) {
      console.error('❌ 获取配置异常，使用默认配置:', error)
      return this.getDefaultConfig()
    }
  },

  // 默认配置
  getDefaultConfig() {
    return {
      color: '#999999',
      selectedColor: '#2E7D32',
      backgroundColor: '#FFFFFF',
      borderStyle: 'black',
      position: 'bottom',
      list: [
        {
          pagePath: 'pages/index/index',
          text: '首页',
          iconPath: 'images/tabbar/home.png',
          selectedIconPath: 'images/tabbar/home-active.png'
        },
        {
          pagePath: 'pages/tea-list/tea-list',
          text: '茶地',
          iconPath: 'images/tabbar/tea.png',
          selectedIconPath: 'images/tabbar/tea-active.png'
        },
        {
          pagePath: 'pages/monitoring/monitoring',
          text: '监控',
          iconPath: 'images/tabbar/monitor.png',
          selectedIconPath: 'images/tabbar/monitor-active.png'
        },
        {
          pagePath: 'pages/analytics/analytics',
          text: '分析',
          iconPath: 'images/tabbar/chart.png',
          selectedIconPath: 'images/tabbar/chart-active.png'
        },
        {
          pagePath: 'pages/profile/profile',
          text: '我的',
          iconPath: 'images/tabbar/profile.png',
          selectedIconPath: 'images/tabbar/profile-active.png'
        }
      ]
    }
  },

  // 应用配置
  async applyTabBarConfig(config) {
    try {
      return new Promise((resolve, reject) => {
        wx.setTabBarStyle({
          color: config.color,
          selectedColor: config.selectedColor,
          backgroundColor: config.backgroundColor,
          borderStyle: config.borderStyle,
          success: () => {
            resolve(true)
          },
          fail: (error) => {
            console.error('❌ 底部导航样式应用失败:', error)
            resolve(false)
          }
        })
      })
    } catch (error) {
      console.error('❌ 应用配置异常:', error)
      return false
    }
  }
}

App({
  // 全局数据
  globalData: {
    userInfo: null,
    token: '',
    apiBaseUrl: 'https://teabuy.yizhangkj.com/api/v1',
    systemInfo: null,
    statusBarHeight: 0,
    navBarHeight: 0,
    isLogin: false
  },

  // 应用启动
  onLaunch(options) {
    // 初始化应用
    this.initApp()

    // 检查更新
    this.checkUpdate()

    // 获取系统信息
    this.getSystemInfo()

    // 检查登录状态
    this.checkLoginStatus()

    // 初始化自定义底部导航
    this.initCustomTabBar()
  },

  // 应用显示
  onShow(options) {
  },

  // 应用隐藏
  onHide() {
  },

  // 应用错误
  onError(error) {
    console.error('应用错误:', error)
  },

  // 页面不存在
  onPageNotFound(res) {
    wx.redirectTo({
      url: '/pages/index/index'
    })
  },

  // 初始化应用
  initApp() {
    // 获取系统信息
    this.getSystemInfo()

    // 检查登录状态
    this.checkLoginStatus()
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      // 使用新的 API 获取系统信息
      const windowInfo = wx.getWindowInfo()
      const deviceInfo = wx.getDeviceInfo()
      const appBaseInfo = wx.getAppBaseInfo()

      // 合并系统信息
      const systemInfo = {
        ...windowInfo,
        ...deviceInfo,
        ...appBaseInfo
      }

      this.globalData.systemInfo = systemInfo

      // 计算状态栏和导航栏高度
      this.globalData.statusBarHeight = windowInfo.statusBarHeight
      this.globalData.navBarHeight = windowInfo.statusBarHeight + 44
    } catch (error) {
      console.error('获取系统信息失败:', error)
    }
  },

  // 检查更新
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()

      updateManager.onCheckForUpdate((res) => {
      })

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })

      updateManager.onUpdateFailed(() => {
      })
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')

    if (token && userInfo) {
      this.globalData.token = token
      this.globalData.userInfo = userInfo
      this.globalData.isLogin = true

      // 验证token有效性
      this.validateToken()
    } else {
      this.globalData.isLogin = false
    }
  },

  // 验证token有效性
  validateToken() {
    // 如果没有token，直接跳过验证
    if (!this.globalData.token) {
      return
    }

    api.userApi.getProfile().then(res => {
      if (res.code === 200) {
        this.globalData.userInfo = res.data
        wx.setStorageSync('userInfo', res.data)
      } else {
      }
    }).catch(error => {
      console.error('❌ token验证失败:', error)

      // 检查是否是401错误（token真正失效）
      if (error && (error.code === 401 || error.message.includes('401'))) {
        this.logout()
      } else {
      }
    })
  },

  // 登录
  login(userInfo, token) {
    // 登录成功，保存用户信息
    this.globalData.userInfo = userInfo
    this.globalData.token = token
    this.globalData.isLogin = true

    // 存储到本地
    wx.setStorageSync('userInfo', userInfo)
    wx.setStorageSync('token', token)

    // 更新全局状态
    this.updateGlobalState()
  },

  // 退出登录
  logout() {
    this.globalData.userInfo = null
    this.globalData.token = ''
    this.globalData.isLogin = false

    // 清除本地存储
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('token')

    // 不自动跳转到登录页，让用户继续使用基本功能
  },

  // 获取用户信息
  getUserInfo() {
    return this.globalData.userInfo
  },

  // 设置用户信息
  setUserInfo(userInfo) {
    this.globalData.userInfo = userInfo
    wx.setStorageSync('userInfo', userInfo)
  },

  // 更新全局状态
  updateGlobalState() {
    // 更新全局状态信息
    const globalState = {
      isLogin: this.globalData.isLogin,
      hasUserInfo: !!this.globalData.userInfo,
      hasToken: !!this.globalData.token
    }

    // 可以在这里添加状态变化的处理逻辑
    return globalState
  },

  // 检查是否登录（仅用于需要登录的功能）
  checkLogin() {
    if (!this.globalData.isLogin) {
      wx.showModal({
        title: '提示',
        content: '此功能需要登录后使用，是否前往登录？',
        confirmText: '去登录',
        cancelText: '暂不登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
      return false
    }
    return true
  },

  // 检查是否需要登录（温和提示）
  checkLoginOptional() {
    return this.globalData.isLogin
  },

  // 初始化自定义底部导航
  async initCustomTabBar() {
    try {
      // 获取底部导航配置
      const config = await simpleTabBarManager.getTabBarConfig()

      // 初始化页面路由管理器
      if (config && config.list) {
        const tabPages = config.list.map(item => item.pagePath)
        pageRouter.init(tabPages)
      }

      // 存储配置到全局数据
      this.globalData.customTabBarConfig = config
    } catch (error) {
      console.error('❌ 自定义底部导航初始化失败:', error)
    }
  },

  // 重新加载自定义底部导航配置
  async reloadCustomTabBar() {
    try {
      const config = await simpleTabBarManager.getTabBarConfig()

      // 更新全局配置
      this.globalData.customTabBarConfig = config

      // 更新页面路由管理器
      if (config && config.list) {
        const tabPages = config.list.map(item => item.pagePath)
        pageRouter.init(tabPages)
      }

      // 通知所有页面更新自定义导航
      this.updateAllCustomTabBars(config)
      return true
    } catch (error) {
      console.error('❌ 重新加载自定义底部导航配置失败:', error)
      return false
    }
  },

  // 更新所有页面的自定义导航
  updateAllCustomTabBars(config) {
    try {
      // 获取当前页面栈
      const pages = getCurrentPages()

      pages.forEach(page => {
        // 检查页面是否有自定义导航组件
        if (page.selectComponent && typeof page.selectComponent === 'function') {
          const customTabBar = page.selectComponent('#custom-tabbar')
          if (customTabBar && customTabBar.reloadConfig) {
            customTabBar.setData({ config: config })
          }
        }
      })
    } catch (error) {
      console.error('❌ 更新自定义导航失败:', error)
    }
  },

  // 导航到指定页面
  navigateToPage(pagePath, params = {}) {
    pageRouter.navigateTo(pagePath, params)
  },

  // 获取当前tab索引
  getCurrentTabIndex() {
    const config = this.globalData.customTabBarConfig
    if (config && config.list) {
      return pageRouter.getCurrentTabIndex(config.list)
    }
    return 0
  },

  // 应用导航项启用/禁用状态
  async applyTabBarItemStates(config) {
    if (!config || !config.list) {
      return
    }

    try {
      // app.json中的原始导航项配置
      const originalItems = [
        { text: '首页', pagePath: 'pages/index/index', iconPath: 'images/tabbar/home.png', selectedIconPath: 'images/tabbar/home-active.png' },
        { text: '茶地', pagePath: 'pages/tea-list/tea-list', iconPath: 'images/tabbar/tea.png', selectedIconPath: 'images/tabbar/tea-active.png' },
        { text: '监控', pagePath: 'pages/monitoring/monitoring', iconPath: 'images/tabbar/monitor.png', selectedIconPath: 'images/tabbar/monitor-active.png' },
        { text: '分析', pagePath: 'pages/analytics/analytics', iconPath: 'images/tabbar/chart.png', selectedIconPath: 'images/tabbar/chart-active.png' },
        { text: '我的', pagePath: 'pages/profile/profile', iconPath: 'images/tabbar/profile.png', selectedIconPath: 'images/tabbar/profile-active.png' }
      ]

      // 创建启用状态映射
      const enabledMap = {}
      config.list.forEach(item => {
        enabledMap[item.pagePath] = true
      })
      // Debug log removed

      // 应用每个导航项的状态
      for (let i = 0; i < originalItems.length; i++) {
        const originalItem = originalItems[i]
        const isEnabled = enabledMap[originalItem.pagePath] || false

        await this.setTabBarItemState(i, originalItem, isEnabled)
      }
    } catch (error) {
      console.error('❌ 应用导航项状态失败:', error)
    }
  },

  // 设置单个导航项状态
  async setTabBarItemState(index, item, isEnabled) {
    try {
      if (isEnabled) {
        // 启用状态：正常显示
        await new Promise((resolve, reject) => {
          wx.setTabBarItem({
            index: index,
            text: item.text,
            iconPath: item.iconPath,
            selectedIconPath: item.selectedIconPath,
            success: resolve,
            fail: reject
          })
        })
      } else {
        // 禁用状态：显示为灰色并添加"禁用"标识
        await new Promise((resolve, reject) => {
          wx.setTabBarItem({
            index: index,
            text: `${item.text}(禁用)`,
            iconPath: item.iconPath, // 可以后续添加灰色图标
            selectedIconPath: item.iconPath, // 禁用时选中也是灰色
            success: resolve,
            fail: reject
          })
        })
      }

    } catch (error) {
      console.error(`❌ 设置导航项状态失败 ${item.text}:`, error)
    }
  }
})

{"pages": ["pages/index/index", "pages/tea-list/tea-list", "pages/tea-detail/tea-detail", "pages/monitoring/monitoring", "pages/analytics/analytics", "pages/profile/profile", "pages/verification-center/verification-center", "pages/verification-history/verification-history", "pages/login/login", "pages/my-fields/my-fields", "pages/earnings/earnings", "pages/orders/orders", "pages/order-detail/order-detail", "pages/settings/settings", "pages/purchase/purchase", "pages/purchase-success/purchase-success", "pages/payment-test/payment-test", "pages/help/help", "pages/feedback/feedback", "pages/real-name-auth/real-name-auth", "pages/enterprise-auth/enterprise-auth", "pages/bank-card-auth/bank-card-auth", "pages/contracts/contracts", "pages/logs/logs", "pages/invoices/invoices", "pages/invite/invite", "pages/notifications/notifications", "pages/security/security", "pages/edit-profile/edit-profile", "pages/enterprise-info/enterprise-info", "pages/bind-phone/bind-phone", "pages/set-payment-password/set-payment-password", "pages/change-payment-password/change-payment-password", "pages/theme-settings/theme-settings", "pages/performance-monitor/performance-monitor", "pages/test-runner/test-runner", "pages/debug-test/debug-test", "pages/news-detail/news-detail", "pages/investment-advice/investment-advice", "pages/video-channel/video-channel", "pages/ai-chat/ai-chat", "pages/tabbar-test/tabbar-test", "pages/simple-tabbar-test/simple-tabbar-test", "pages/custom-tabbar-demo/custom-tabbar-demo", "pages/simple-custom-tabbar-test/simple-custom-tabbar-test", "pages/icon-debug/icon-debug", "pages/api-test/api-test"], "subPackages": [], "window": {"navigationBarTextStyle": "white", "navigationBarTitleText": "两山·茶管家", "navigationBarBackgroundColor": "#2E7D32", "backgroundColor": "#F5F5F5", "enablePullDownRefresh": true, "onReachBottomDistance": 50, "pageOrientation": "portrait", "restartStrategy": "homePage"}, "networkTimeout": {"request": 15000, "downloadFile": 30000, "uploadFile": 30000}, "requiredPrivateInfos": ["getLocation", "chooseLocation"], "requiredBackgroundModes": ["location"], "style": "v2", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents"}
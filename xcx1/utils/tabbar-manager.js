/**
 * 底部导航管理器 - 简化版
 */

// 简单的底部导航管理器
const tabBarManager = {
  config: null,
  isLoaded: false,

  // 获取底部导航配置
  async getTabBarConfig() {
    try {
      const response = await this.makeRequest('/page-decoration/tabbar/config/')

      if (response && response.code === 200) {
        this.config = response.data.config
        this.isLoaded = true
        // Debug log removed
        // Debug log removed

        return this.config
      } else {
        console.error('❌ 获取底部导航配置失败:', response?.message || '未知错误')
        return this.getDefaultConfig()
      }
    } catch (error) {
      console.error('❌ 底部导航配置请求异常:', error)
      return this.getDefaultConfig()
    }
  },

  // 简单的请求方法
  makeRequest(url) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `https://teabuy.yizhangkj.com/api/v1${url}`,
        method: 'GET',
        header: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        },
        success: (res) => {
          resolve(res.data)
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  },

  // 获取默认配置
  getDefaultConfig() {
    const defaultConfig = {
      color: '#999999',
      selectedColor: '#2E7D32',
      backgroundColor: '#FFFFFF',
      borderStyle: 'black',
      position: 'bottom',
      list: [
        {
          pagePath: 'pages/index/index',
          text: '首页',
          iconPath: 'images/tabbar/home.png',
          selectedIconPath: 'images/tabbar/home-active.png'
        },
        {
          pagePath: 'pages/tea-list/tea-list',
          text: '茶地',
          iconPath: 'images/tabbar/tea.png',
          selectedIconPath: 'images/tabbar/tea-active.png'
        },
        {
          pagePath: 'pages/analytics/analytics',
          text: '分析',
          iconPath: 'images/tabbar/chart.png',
          selectedIconPath: 'images/tabbar/chart-active.png'
        },
        {
          pagePath: 'pages/profile/profile',
          text: '我的',
          iconPath: 'images/tabbar/profile.png',
          selectedIconPath: 'images/tabbar/profile-active.png'
        }
      ]
    }

    this.config = defaultConfig
    this.isLoaded = true

    return defaultConfig
  },

  // 应用底部导航配置
  async applyTabBarConfig() {
    if (!this.isLoaded) {
      await this.getTabBarConfig()
    }

    if (!this.config) {
      console.error('❌ 没有可用的底部导航配置')
      return false
    }

    try {
      await this.setTabBarStyle()
      return true
    } catch (error) {
      console.error('❌ 应用底部导航配置失败:', error)
      return false
    }
  },

  // 设置tabBar样式
  async setTabBarStyle() {
    return new Promise((resolve, reject) => {
      wx.setTabBarStyle({
        color: this.config.color,
        selectedColor: this.config.selectedColor,
        backgroundColor: this.config.backgroundColor,
        borderStyle: this.config.borderStyle,
        success: () => {
          resolve()
        },
        fail: (error) => {
          console.error('❌ tabBar样式设置失败:', error)
          reject(error)
        }
      })
    })
  },

  // 获取当前配置
  getCurrentConfig() {
    return this.config
  },

  // 检查是否已加载
  isConfigLoaded() {
    return this.isLoaded
  },

  // 重新加载配置
  async reloadConfig() {
    this.isLoaded = false
    this.config = null
    return await this.getTabBarConfig()
  }
}

// 导出
module.exports = {
  tabBarManager
}

/**
 * 导航动画管理器
 * 提供统一的页面跳转动画效果
 */

// 页面层级定义，用于确定动画方向
const PAGE_LEVELS = {
  '/pages/index/index': 1,
  '/pages/tea-list/tea-list': 2,
  '/pages/tea-detail/tea-detail': 3,
  '/pages/purchase/purchase': 4,
  '/pages/my-fields/my-fields': 2,
  '/pages/earnings/earnings': 2,
  '/pages/profile/profile': 2,
  '/pages/monitoring/monitoring': 3,
  '/pages/purchase-success/purchase-success': 5
}

// 当前页面栈
let pageStack = []

/**
 * 获取页面层级
 */
function getPageLevel(url) {
  const path = url.split('?')[0]
  return PAGE_LEVELS[path] || 0
}

/**
 * 确定动画类型
 */
function getAnimationType(fromLevel, toLevel) {
  if (toLevel > fromLevel) {
    return 'slide-left' // 向前导航
  } else if (toLevel < fromLevel) {
    return 'slide-right' // 向后导航
  } else {
    return 'fade' // 同级页面
  }
}

/**
 * 增强的导航方法
 */
const Navigation = {
  // 跳转到新页面
  navigateTo(options) {
    const { url, animationType, ...otherOptions } = options
    
    // 记录页面栈
    const currentPages = getCurrentPages()
    const currentPage = currentPages[currentPages.length - 1]
    const currentLevel = getPageLevel(currentPage.route)
    const targetLevel = getPageLevel(url)
    
    // 确定动画类型
    const animation = animationType || getAnimationType(currentLevel, targetLevel)
    
    // 设置页面动画数据
    if (currentPage.setData) {
      currentPage.setData({
        pageAnimation: {
          type: animation,
          direction: 'exit'
        }
      })
    }
    
    // 执行跳转
    return wx.navigateTo({
      url,
      ...otherOptions,
      success: (res) => {
        if (options.success) options.success(res)
      },
      fail: (err) => {
        console.error('❌ 页面跳转失败:', err)
        if (options.fail) options.fail(err)
      }
    })
  },

  // 返回上一页
  navigateBack(options = {}) {
    const { delta = 1, animationType, ...otherOptions } = options
    
    const currentPages = getCurrentPages()
    if (currentPages.length <= delta) {
      // 如果返回层级超过页面栈，则跳转到首页
      return this.reLaunch({ url: '/pages/index/index' })
    }
    
    const currentPage = currentPages[currentPages.length - 1]
    const targetPage = currentPages[currentPages.length - 1 - delta]
    
    const currentLevel = getPageLevel(currentPage.route)
    const targetLevel = getPageLevel(targetPage.route)
    
    // 确定动画类型
    const animation = animationType || getAnimationType(currentLevel, targetLevel)
    
    // 设置页面动画数据
    if (currentPage.setData) {
      currentPage.setData({
        pageAnimation: {
          type: animation,
          direction: 'exit'
        }
      })
    }
    
    return wx.navigateBack({
      delta,
      ...otherOptions,
      success: (res) => {
        if (options.success) options.success(res)
      }
    })
  },

  // 重定向到新页面
  redirectTo(options) {
    const { url, animationType = 'fade', ...otherOptions } = options
    
    const currentPages = getCurrentPages()
    const currentPage = currentPages[currentPages.length - 1]
    
    // 设置页面动画数据
    if (currentPage.setData) {
      currentPage.setData({
        pageAnimation: {
          type: animationType,
          direction: 'exit'
        }
      })
    }
    
    return wx.redirectTo({
      url,
      ...otherOptions,
      success: (res) => {
        if (options.success) options.success(res)
      }
    })
  },

  // 重新启动到新页面
  reLaunch(options) {
    const { url, animationType = 'scale', ...otherOptions } = options
    
    return wx.reLaunch({
      url,
      ...otherOptions,
      success: (res) => {
        if (options.success) options.success(res)
      }
    })
  },

  // 切换到 tabBar 页面
  switchTab(options) {
    const { url, animationType = 'fade', ...otherOptions } = options
    
    const currentPages = getCurrentPages()
    const currentPage = currentPages[currentPages.length - 1]
    
    // 设置页面动画数据
    if (currentPage.setData) {
      currentPage.setData({
        pageAnimation: {
          type: animationType,
          direction: 'exit'
        }
      })
    }
    
    return wx.switchTab({
      url,
      ...otherOptions,
      success: (res) => {
        if (options.success) options.success(res)
      }
    })
  },

  // 预加载页面
  preloadPage(url) {
    return wx.preloadPage({
      url,
      success: () => {
      },
      fail: (err) => {
        console.error(`❌ 页面预加载失败: ${url}`, err)
      }
    })
  }
}

// 页面生命周期增强
const PageLifecycle = {
  // 页面显示时的动画处理
  onPageShow(page) {
    const animation = page.data.pageAnimation
    if (animation && animation.direction === 'enter') {
      // 触发进入动画
      if (page.selectComponent && page.selectComponent('#page-transition')) {
        page.selectComponent('#page-transition').playEnterAnimation()
      }
    }
  },

  // 页面隐藏时的动画处理
  onPageHide(page) {
    const animation = page.data.pageAnimation
    if (animation && animation.direction === 'exit') {
      // 触发退出动画
      if (page.selectComponent && page.selectComponent('#page-transition')) {
        page.selectComponent('#page-transition').playExitAnimation()
      }
    }
  }
}

module.exports = {
  Navigation,
  PageLifecycle,
  getPageLevel,
  getAnimationType
}

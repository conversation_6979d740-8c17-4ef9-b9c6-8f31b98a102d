// 图片工具类
class ImageUtils {
  // 默认图片路径 - 只保留必要的本地图片
  static DEFAULT_IMAGES = {
    tea: '/images/tea-default.jpg',
    avatar: '/images/avatar-default.png',
    banner: '', // 不使用默认图片，必须从后台获取
    news: '' // 不使用默认图片，必须从后台获取
  }

  // 获取默认图片
  static getDefaultImage(type = 'tea') {
    return this.DEFAULT_IMAGES[type] || this.DEFAULT_IMAGES.tea
  }

  // 处理图片URL，如果为空则返回默认图片
  static processImageUrl(url, type = 'tea') {
    if (!url || url.trim() === '') {
      return this.getDefaultImage(type)
    }
    return url
  }

  // 图片加载错误处理
  static handleImageError(e, fallbackType = 'tea') {
    // 获取当前图片元素
    const target = e.currentTarget || e.target
    if (target) {
      // 设置默认图片
      const defaultImage = this.getDefaultImage(fallbackType)
      
      // 如果有dataset中的索引信息，可以用于更新数据
      const index = target.dataset?.index
      const field = target.dataset?.field
      
      return {
        defaultImage,
        index,
        field,
        target
      }
    }
    
    return null
  }

  // 预处理图片列表，确保所有图片都有有效的URL
  static processImageList(imageList, type = 'tea') {
    if (!Array.isArray(imageList)) {
      return []
    }
    
    return imageList.map(item => {
      if (typeof item === 'string') {
        return this.processImageUrl(item, type)
      } else if (typeof item === 'object' && item.url) {
        return {
          ...item,
          url: this.processImageUrl(item.url, type)
        }
      } else if (typeof item === 'object' && item.image) {
        return {
          ...item,
          image: this.processImageUrl(item.image, type)
        }
      } else if (typeof item === 'object' && item.main_image) {
        return {
          ...item,
          main_image: this.processImageUrl(item.main_image, type)
        }
      }
      return item
    })
  }

  // 检查图片URL是否有效
  static isValidImageUrl(url) {
    if (!url || typeof url !== 'string') {
      return false
    }
    
    // 检查是否是有效的URL格式
    const urlPattern = /^(https?:\/\/|\/)/
    return urlPattern.test(url.trim())
  }

  // 获取图片显示尺寸
  static getImageDisplaySize(originalWidth, originalHeight, maxWidth, maxHeight) {
    if (!originalWidth || !originalHeight) {
      return { width: maxWidth, height: maxHeight }
    }
    
    const aspectRatio = originalWidth / originalHeight
    
    let displayWidth = maxWidth
    let displayHeight = maxHeight
    
    if (aspectRatio > 1) {
      // 宽图
      displayHeight = maxWidth / aspectRatio
      if (displayHeight > maxHeight) {
        displayHeight = maxHeight
        displayWidth = maxHeight * aspectRatio
      }
    } else {
      // 高图
      displayWidth = maxHeight * aspectRatio
      if (displayWidth > maxWidth) {
        displayWidth = maxWidth
        displayHeight = maxWidth / aspectRatio
      }
    }
    
    return {
      width: Math.round(displayWidth),
      height: Math.round(displayHeight)
    }
  }
}

module.exports = ImageUtils

/**
 * 智能搜索管理器
 * 提供搜索建议、历史记录、热门搜索等功能
 */

// 搜索配置
const SEARCH_CONFIG = {
  // 最大历史记录数量
  MAX_HISTORY: 20,
  // 最大搜索建议数量
  MAX_SUGGESTIONS: 10,
  // 搜索防抖延迟
  DEBOUNCE_DELAY: 300,
  // 最小搜索长度
  MIN_SEARCH_LENGTH: 1,
  // 缓存过期时间
  CACHE_EXPIRY: 30 * 60 * 1000, // 30分钟
  // 存储键名
  STORAGE_KEYS: {
    HISTORY: 'search_history',
    HOT_KEYWORDS: 'hot_keywords',
    SUGGESTIONS_CACHE: 'suggestions_cache'
  }
}

// 预定义的热门搜索关键词
const DEFAULT_HOT_KEYWORDS = [
  '龙井茶',
  '碧螺春',
  '铁观音',
  '普洱茶',
  '大红袍',
  '毛尖',
  '白茶',
  '乌龙茶',
  '绿茶',
  '红茶'
]

// 搜索类别映射
const SEARCH_CATEGORIES = {
  'tea_name': '茶叶名称',
  'location': '产地',
  'price': '价格',
  'area': '面积',
  'type': '茶叶类型'
}

/**
 * 智能搜索管理器
 */
class SearchManager {
  constructor() {
    this.searchHistory = []
    this.hotKeywords = []
    this.suggestionsCache = new Map()
    this.debounceTimer = null
    
    this.init()
  }

  /**
   * 初始化搜索管理器
   */
  init() {
    try {
      // 加载搜索历史
      this.loadSearchHistory()
      
      // 加载热门关键词
      this.loadHotKeywords()
      
      // 加载建议缓存
      this.loadSuggestionsCache()
    } catch (error) {
      console.error('❌ 搜索管理器初始化失败:', error)
    }
  }

  /**
   * 加载搜索历史
   */
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync(SEARCH_CONFIG.STORAGE_KEYS.HISTORY)
      if (history && Array.isArray(history)) {
        this.searchHistory = history
      }
    } catch (error) {
      console.error('❌ 加载搜索历史失败:', error)
    }
  }

  /**
   * 保存搜索历史
   */
  saveSearchHistory() {
    try {
      wx.setStorageSync(SEARCH_CONFIG.STORAGE_KEYS.HISTORY, this.searchHistory)
    } catch (error) {
      console.error('❌ 保存搜索历史失败:', error)
    }
  }

  /**
   * 加载热门关键词
   */
  loadHotKeywords() {
    try {
      const hotKeywords = wx.getStorageSync(SEARCH_CONFIG.STORAGE_KEYS.HOT_KEYWORDS)
      if (hotKeywords && Array.isArray(hotKeywords)) {
        this.hotKeywords = hotKeywords
      } else {
        this.hotKeywords = [...DEFAULT_HOT_KEYWORDS]
        this.saveHotKeywords()
      }
    } catch (error) {
      console.error('❌ 加载热门关键词失败:', error)
      this.hotKeywords = [...DEFAULT_HOT_KEYWORDS]
    }
  }

  /**
   * 保存热门关键词
   */
  saveHotKeywords() {
    try {
      wx.setStorageSync(SEARCH_CONFIG.STORAGE_KEYS.HOT_KEYWORDS, this.hotKeywords)
    } catch (error) {
      console.error('❌ 保存热门关键词失败:', error)
    }
  }

  /**
   * 加载建议缓存
   */
  loadSuggestionsCache() {
    try {
      const cache = wx.getStorageSync(SEARCH_CONFIG.STORAGE_KEYS.SUGGESTIONS_CACHE)
      if (cache) {
        const cacheData = JSON.parse(cache)
        // 检查缓存是否过期
        Object.keys(cacheData).forEach(key => {
          const item = cacheData[key]
          if (Date.now() - item.timestamp < SEARCH_CONFIG.CACHE_EXPIRY) {
            this.suggestionsCache.set(key, item.data)
          }
        })
      }
    } catch (error) {
      console.error('❌ 加载建议缓存失败:', error)
    }
  }

  /**
   * 保存建议缓存
   */
  saveSuggestionsCache() {
    try {
      const cacheData = {}
      this.suggestionsCache.forEach((value, key) => {
        cacheData[key] = {
          data: value,
          timestamp: Date.now()
        }
      })
      wx.setStorageSync(SEARCH_CONFIG.STORAGE_KEYS.SUGGESTIONS_CACHE, JSON.stringify(cacheData))
    } catch (error) {
      console.error('❌ 保存建议缓存失败:', error)
    }
  }

  /**
   * 添加搜索历史
   * @param {string} keyword 搜索关键词
   * @param {string} category 搜索类别
   */
  addSearchHistory(keyword, category = 'tea_name') {
    if (!keyword || keyword.trim().length < SEARCH_CONFIG.MIN_SEARCH_LENGTH) {
      return
    }

    const trimmedKeyword = keyword.trim()
    
    // 创建历史记录项
    const historyItem = {
      keyword: trimmedKeyword,
      category,
      timestamp: Date.now(),
      count: 1
    }

    // 检查是否已存在
    const existingIndex = this.searchHistory.findIndex(
      item => item.keyword === trimmedKeyword && item.category === category
    )

    if (existingIndex !== -1) {
      // 更新现有记录
      this.searchHistory[existingIndex].timestamp = Date.now()
      this.searchHistory[existingIndex].count++
      
      // 移到最前面
      const item = this.searchHistory.splice(existingIndex, 1)[0]
      this.searchHistory.unshift(item)
    } else {
      // 添加新记录
      this.searchHistory.unshift(historyItem)
    }

    // 限制历史记录数量
    if (this.searchHistory.length > SEARCH_CONFIG.MAX_HISTORY) {
      this.searchHistory = this.searchHistory.slice(0, SEARCH_CONFIG.MAX_HISTORY)
    }

    // 保存到本地存储
    this.saveSearchHistory()
  }

  /**
   * 获取搜索历史
   * @param {number} limit 限制数量
   * @returns {Array} 搜索历史列表
   */
  getSearchHistory(limit = SEARCH_CONFIG.MAX_HISTORY) {
    return this.searchHistory.slice(0, limit).map(item => ({
      ...item,
      categoryText: SEARCH_CATEGORIES[item.category] || item.category,
      timeText: this.formatTime(item.timestamp)
    }))
  }

  /**
   * 清空搜索历史
   */
  clearSearchHistory() {
    this.searchHistory = []
    this.saveSearchHistory()
  }

  /**
   * 删除单个搜索历史
   * @param {string} keyword 关键词
   * @param {string} category 类别
   */
  removeSearchHistory(keyword, category) {
    const index = this.searchHistory.findIndex(
      item => item.keyword === keyword && item.category === category
    )
    
    if (index !== -1) {
      this.searchHistory.splice(index, 1)
      this.saveSearchHistory()
    }
  }

  /**
   * 获取搜索建议
   * @param {string} query 查询关键词
   * @param {string} category 搜索类别
   * @returns {Promise<Array>} 搜索建议列表
   */
  async getSearchSuggestions(query, category = 'tea_name') {
    if (!query || query.trim().length < SEARCH_CONFIG.MIN_SEARCH_LENGTH) {
      return []
    }

    const trimmedQuery = query.trim().toLowerCase()
    const cacheKey = `${category}_${trimmedQuery}`

    // 检查缓存
    if (this.suggestionsCache.has(cacheKey)) {
      return this.suggestionsCache.get(cacheKey)
    }

    try {
      // 生成建议
      const suggestions = await this.generateSuggestions(trimmedQuery, category)
      
      // 缓存结果
      this.suggestionsCache.set(cacheKey, suggestions)
      this.saveSuggestionsCache()
      
      return suggestions
    } catch (error) {
      console.error('❌ 获取搜索建议失败:', error)
      return []
    }
  }

  /**
   * 生成搜索建议
   * @param {string} query 查询关键词
   * @param {string} category 搜索类别
   * @returns {Promise<Array>} 建议列表
   */
  async generateSuggestions(query, category) {
    const suggestions = []

    // 1. 从搜索历史中匹配
    const historyMatches = this.searchHistory
      .filter(item => 
        item.category === category && 
        item.keyword.toLowerCase().includes(query)
      )
      .slice(0, 5)
      .map(item => ({
        keyword: item.keyword,
        type: 'history',
        category: item.category,
        count: item.count,
        source: '历史搜索'
      }))

    suggestions.push(...historyMatches)

    // 2. 从热门关键词中匹配
    const hotMatches = this.hotKeywords
      .filter(keyword => keyword.toLowerCase().includes(query))
      .slice(0, 5)
      .map(keyword => ({
        keyword,
        type: 'hot',
        category,
        source: '热门搜索'
      }))

    suggestions.push(...hotMatches)

    // 3. 智能补全建议
    const smartSuggestions = this.generateSmartSuggestions(query, category)
    suggestions.push(...smartSuggestions)

    // 去重并限制数量
    const uniqueSuggestions = this.removeDuplicateSuggestions(suggestions)
    return uniqueSuggestions.slice(0, SEARCH_CONFIG.MAX_SUGGESTIONS)
  }

  /**
   * 生成智能补全建议
   * @param {string} query 查询关键词
   * @param {string} category 类别
   * @returns {Array} 智能建议列表
   */
  generateSmartSuggestions(query, category) {
    const suggestions = []

    // 根据类别生成不同的建议
    switch (category) {
      case 'tea_name':
        // 茶叶名称建议
        const teaTypes = ['龙井', '碧螺春', '铁观音', '普洱', '大红袍', '毛尖']
        teaTypes.forEach(type => {
          if (type.includes(query) || query.includes(type)) {
            suggestions.push({
              keyword: type + '茶',
              type: 'smart',
              category,
              source: '智能推荐'
            })
          }
        })
        break

      case 'location':
        // 产地建议
        const locations = ['西湖', '洞庭山', '安溪', '云南', '武夷山', '信阳']
        locations.forEach(location => {
          if (location.includes(query) || query.includes(location)) {
            suggestions.push({
              keyword: location,
              type: 'smart',
              category,
              source: '产地推荐'
            })
          }
        })
        break

      case 'price':
        // 价格建议
        if (/\d/.test(query)) {
          const priceRanges = ['100-500元', '500-1000元', '1000-2000元', '2000元以上']
          suggestions.push(...priceRanges.map(range => ({
            keyword: range,
            type: 'smart',
            category,
            source: '价格区间'
          })))
        }
        break
    }

    return suggestions
  }

  /**
   * 去除重复建议
   * @param {Array} suggestions 建议列表
   * @returns {Array} 去重后的建议列表
   */
  removeDuplicateSuggestions(suggestions) {
    const seen = new Set()
    return suggestions.filter(item => {
      const key = `${item.keyword}_${item.category}`
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }

  /**
   * 获取热门搜索关键词
   * @param {number} limit 限制数量
   * @returns {Array} 热门关键词列表
   */
  getHotKeywords(limit = 10) {
    return this.hotKeywords.slice(0, limit).map(keyword => ({
      keyword,
      type: 'hot',
      category: 'tea_name'
    }))
  }

  /**
   * 更新热门关键词
   * @param {Array} keywords 新的热门关键词列表
   */
  updateHotKeywords(keywords) {
    if (Array.isArray(keywords)) {
      this.hotKeywords = keywords
      this.saveHotKeywords()
    }
  }

  /**
   * 防抖搜索
   * @param {string} query 查询关键词
   * @param {Function} callback 回调函数
   * @param {number} delay 延迟时间
   */
  debounceSearch(query, callback, delay = SEARCH_CONFIG.DEBOUNCE_DELAY) {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
    }

    this.debounceTimer = setTimeout(() => {
      callback(query)
    }, delay)
  }

  /**
   * 格式化时间
   * @param {number} timestamp 时间戳
   * @returns {string} 格式化后的时间
   */
  formatTime(timestamp) {
    const now = Date.now()
    const diff = now - timestamp

    if (diff < 60000) {
      return '刚刚'
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`
    } else if (diff < 604800000) {
      return `${Math.floor(diff / 86400000)}天前`
    } else {
      const date = new Date(timestamp)
      return `${date.getMonth() + 1}/${date.getDate()}`
    }
  }

  /**
   * 获取搜索统计
   * @returns {Object} 搜索统计信息
   */
  getSearchStats() {
    const totalSearches = this.searchHistory.reduce((sum, item) => sum + item.count, 0)
    const uniqueKeywords = this.searchHistory.length
    const categories = [...new Set(this.searchHistory.map(item => item.category))]

    return {
      totalSearches,
      uniqueKeywords,
      categories: categories.length,
      cacheSize: this.suggestionsCache.size,
      lastSearch: this.searchHistory.length > 0 ? this.searchHistory[0].timestamp : null
    }
  }
}

// 创建全局搜索管理器实例
const globalSearchManager = new SearchManager()

module.exports = {
  SearchManager,
  globalSearchManager,
  SEARCH_CONFIG,
  SEARCH_CATEGORIES
}

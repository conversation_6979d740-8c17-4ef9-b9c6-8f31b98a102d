/**
 * 全局加载状态管理器
 * 提供统一的加载状态控制和骨架屏管理
 */

// 加载状态存储
const loadingStates = new Map()

// 加载计数器，用于处理并发请求
const loadingCounters = new Map()

/**
 * 加载管理器
 */
const LoadingManager = {
  // 显示加载状态
  show(key = 'global', options = {}) {
    const {
      title = '加载中...',
      mask = true,
      skeleton = false,
      skeletonType = 'card',
      duration = 0
    } = options

    // 增加计数器
    const currentCount = loadingCounters.get(key) || 0
    loadingCounters.set(key, currentCount + 1)

    // 如果是第一次显示，才真正显示加载
    if (currentCount === 0) {
      if (skeleton) {
        // 使用骨架屏
        loadingStates.set(key, {
          type: 'skeleton',
          skeletonType,
          visible: true
        })
      } else {
        // 使用系统加载提示
        wx.showLoading({
          title,
          mask
        })
        
        loadingStates.set(key, {
          type: 'system',
          visible: true
        })
      }

      // 自动隐藏
      if (duration > 0) {
        setTimeout(() => {
          this.hide(key)
        }, duration)
      }
    }
  },

  // 隐藏加载状态
  hide(key = 'global') {
    const currentCount = loadingCounters.get(key) || 0
    
    if (currentCount <= 0) {
      return
    }

    // 减少计数器
    const newCount = currentCount - 1
    loadingCounters.set(key, newCount)

    // 只有当计数器为0时才真正隐藏
    if (newCount === 0) {
      const loadingState = loadingStates.get(key)
      
      if (loadingState) {
        if (loadingState.type === 'system') {
          wx.hideLoading()
        }
        
        loadingStates.set(key, {
          ...loadingState,
          visible: false
        })
      }
    }
  },

  // 强制隐藏加载状态
  forceHide(key = 'global') {
    loadingCounters.set(key, 0)
    
    const loadingState = loadingStates.get(key)
    if (loadingState && loadingState.type === 'system') {
      wx.hideLoading()
    }
    
    loadingStates.set(key, {
      ...loadingState,
      visible: false
    })
  },

  // 获取加载状态
  getState(key = 'global') {
    return loadingStates.get(key) || { visible: false }
  },

  // 检查是否正在加载
  isLoading(key = 'global') {
    const state = this.getState(key)
    return state.visible === true
  },

  // 清除所有加载状态
  clearAll() {
    loadingCounters.clear()
    
    // 隐藏系统加载提示
    wx.hideLoading()
    
    // 清除所有状态
    for (const [key, state] of loadingStates) {
      loadingStates.set(key, {
        ...state,
        visible: false
      })
    }
  },

  // 批量操作
  batch(operations) {
    operations.forEach(op => {
      if (op.action === 'show') {
        this.show(op.key, op.options)
      } else if (op.action === 'hide') {
        this.hide(op.key)
      }
    })
  }
}

/**
 * 页面加载状态混入
 * 为页面提供统一的加载状态管理方法
 */
const PageLoadingMixin = {
  data: {
    // 页面加载状态
    pageLoading: false,
    // 骨架屏状态
    skeletonLoading: false,
    // 局部加载状态
    localLoading: {}
  },

  // 显示页面加载
  showPageLoading(options = {}) {
    const pageKey = this.route || 'unknown'
    LoadingManager.show(pageKey, {
      skeleton: true,
      skeletonType: 'card',
      ...options
    })
    
    this.setData({ 
      pageLoading: true,
      skeletonLoading: options.skeleton !== false
    })
  },

  // 隐藏页面加载
  hidePageLoading() {
    const pageKey = this.route || 'unknown'
    LoadingManager.hide(pageKey)
    
    this.setData({ 
      pageLoading: false,
      skeletonLoading: false
    })
  },

  // 显示局部加载
  showLocalLoading(key, options = {}) {
    LoadingManager.show(`${this.route}-${key}`, options)
    
    this.setData({
      [`localLoading.${key}`]: true
    })
  },

  // 隐藏局部加载
  hideLocalLoading(key) {
    LoadingManager.hide(`${this.route}-${key}`)
    
    this.setData({
      [`localLoading.${key}`]: false
    })
  },

  // 页面卸载时清理
  onUnload() {
    const pageKey = this.route || 'unknown'
    LoadingManager.forceHide(pageKey)
    
    // 清理局部加载状态
    const localKeys = Object.keys(this.data.localLoading || {})
    localKeys.forEach(key => {
      LoadingManager.forceHide(`${this.route}-${key}`)
    })
  }
}

/**
 * 请求加载装饰器
 * 自动管理API请求的加载状态
 */
function withLoading(apiFunction, options = {}) {
  return async function(...args) {
    const {
      key = 'api',
      showLoading = true,
      loadingOptions = {}
    } = options

    try {
      if (showLoading) {
        LoadingManager.show(key, loadingOptions)
      }

      const result = await apiFunction.apply(this, args)
      return result

    } catch (error) {
      throw error
    } finally {
      if (showLoading) {
        LoadingManager.hide(key)
      }
    }
  }
}

module.exports = {
  LoadingManager,
  PageLoadingMixin,
  withLoading
}

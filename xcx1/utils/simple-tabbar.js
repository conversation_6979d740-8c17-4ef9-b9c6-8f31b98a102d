/**
 * 简单的底部导航管理器
 */

const simpleTabBarManager = {
  // 获取底部导航配置
  async getTabBarConfig() {
    try {
      return new Promise((resolve, reject) => {
        wx.request({
          url: 'https://teabuy.yizhangkj.com/api/v1/page-decoration/tabbar/config/',
          method: 'GET',
          header: {
            'Cache-Control': 'no-cache'
          },
          success: (res) => {
            if (res.data && res.data.code === 200) {
              resolve(res.data.data.config)
            } else {
              resolve(this.getDefaultConfig())
            }
          },
          fail: (error) => {
            console.error('❌ 请求失败，使用默认配置:', error)
            resolve(this.getDefaultConfig())
          }
        })
      })
    } catch (error) {
      console.error('❌ 获取配置异常，使用默认配置:', error)
      return this.getDefaultConfig()
    }
  },

  // 默认配置
  getDefaultConfig() {
    return {
      color: '#999999',
      selectedColor: '#2E7D32',
      backgroundColor: '#FFFFFF',
      borderStyle: 'black',
      position: 'bottom',
      list: [
        {
          pagePath: 'pages/index/index',
          text: '首页',
          iconPath: 'images/tabbar/home.png',
          selectedIconPath: 'images/tabbar/home-active.png'
        },
        {
          pagePath: 'pages/tea-list/tea-list',
          text: '茶地',
          iconPath: 'images/tabbar/tea.png',
          selectedIconPath: 'images/tabbar/tea-active.png'
        },
        {
          pagePath: 'pages/monitoring/monitoring',
          text: '监控',
          iconPath: 'images/tabbar/monitor.png',
          selectedIconPath: 'images/tabbar/monitor-active.png'
        },
        {
          pagePath: 'pages/analytics/analytics',
          text: '分析',
          iconPath: 'images/tabbar/chart.png',
          selectedIconPath: 'images/tabbar/chart-active.png'
        },
        {
          pagePath: 'pages/profile/profile',
          text: '我的',
          iconPath: 'images/tabbar/profile.png',
          selectedIconPath: 'images/tabbar/profile-active.png'
        }
      ]
    }
  },

  // 应用配置
  async applyTabBarConfig(config) {
    try {
      return new Promise((resolve, reject) => {
        wx.setTabBarStyle({
          color: config.color,
          selectedColor: config.selectedColor,
          backgroundColor: config.backgroundColor,
          borderStyle: config.borderStyle,
          success: () => {
            resolve(true)
          },
          fail: (error) => {
            console.error('❌ 底部导航样式应用失败:', error)
            resolve(false)
          }
        })
      })
    } catch (error) {
      console.error('❌ 应用配置异常:', error)
      return false
    }
  }
}

module.exports = {
  simpleTabBarManager
}

/**
 * 统一API调用封装
 * 提供统一的请求处理、错误处理、加载状态管理
 */

const config = {
  baseURL: 'https://teabuy.yizhangkj.com', // 替换为实际API地址
  timeout: 15000,
  retryTimes: 3
};

/**
 * 获取用户token
 */
function getToken() {
  return wx.getStorageSync('token') || '';
}

/**
 * 设置用户token
 */
function setToken(token) {
  wx.setStorageSync('token', token);
}

/**
 * 清除用户token
 */
function clearToken() {
  wx.removeStorageSync('token');
}

/**
 * 显示加载提示
 */
function showLoading(title = '加载中...') {
  wx.showLoading({
    title,
    mask: true
  });
}

/**
 * 隐藏加载提示
 */
function hideLoading() {
  wx.hideLoading();
}

/**
 * 显示错误提示
 */
function showError(message, duration = 2000) {
  wx.showToast({
    title: message,
    icon: 'none',
    duration
  });
}

/**
 * 显示成功提示
 */
function showSuccess(message, duration = 2000) {
  wx.showToast({
    title: message,
    icon: 'success',
    duration
  });
}

/**
 * 处理HTTP错误状态码
 */
function handleHttpError(statusCode, data) {
  const errorMessages = {
    400: '请求参数错误',
    401: '未授权，请重新登录',
    403: '拒绝访问',
    404: '请求的资源不存在',
    405: '请求方法不允许',
    408: '请求超时',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时'
  };

  const message = data?.message || errorMessages[statusCode] || `请求失败 (${statusCode})`;
  
  // 401错误特殊处理 - 清除token和全局状态
  if (statusCode === 401) {
    // 清除本地存储
    clearToken();
    wx.removeStorageSync('userInfo');

    // 清除全局状态
    const app = getApp();
    if (app) {
      app.globalData.isLogin = false;
      app.globalData.userInfo = null;
      app.globalData.token = '';
    }
  }

  throw new Error(message);
}

/**
 * 处理网络错误
 */
function handleNetworkError(error) {
  console.error('网络请求失败:', error);
  
  const errorMessages = {
    'timeout': '请求超时，请检查网络连接',
    'fail': '网络连接失败，请检查网络设置'
  };

  const message = errorMessages[error.errMsg] || '网络异常，请稍后重试';
  throw new Error(message);
}

/**
 * 核心请求方法
 */
async function request(url, options = {}) {
  const {
    method = 'GET',
    data = {},
    header = {},
    showLoading: shouldShowLoading = false,
    showError: shouldShowError = true,
    retryTimes = config.retryTimes,
    ...otherOptions
  } = options;

  // 显示加载提示
  if (shouldShowLoading) {
    showLoading(options.loadingText);
  }

  const requestOptions = {
    url: url.startsWith('http') ? url : `${config.baseURL}${url}`,
    method: method.toUpperCase(),
    data,
    header: {
      'Content-Type': 'application/json',
      'Authorization': `Token ${getToken()}`,
      ...header
    },
    timeout: config.timeout,
    ...otherOptions
  };

  let lastError;
  
  // 重试机制
  for (let i = 0; i <= retryTimes; i++) {
    try {
      const response = await new Promise((resolve, reject) => {
        wx.request({
          ...requestOptions,
          success: resolve,
          fail: reject
        });
      });

      // 隐藏加载提示
      if (shouldShowLoading) {
        hideLoading();
      }

      // 检查HTTP状态码
      if (response.statusCode >= 200 && response.statusCode < 300) {
        const { data: responseData } = response;
        
        // 检查业务状态码
        if (responseData.code !== undefined && responseData.code !== 0 && responseData.code !== 200) {
          const error = new Error(responseData.message || '请求失败');
          error.code = responseData.code;
          error.data = responseData;
          throw error;
        }

        return responseData.data || responseData;
      } else {
        handleHttpError(response.statusCode, response.data);
      }

    } catch (error) {
      lastError = error;
      
      // 如果是最后一次重试，抛出错误
      if (i === retryTimes) {
        break;
      }
      
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }

  // 隐藏加载提示
  if (shouldShowLoading) {
    hideLoading();
  }

  // 处理最终错误
  if (lastError.errMsg) {
    handleNetworkError(lastError);
  } else {
    if (shouldShowError) {
      showError(lastError.message);
    }
    throw lastError;
  }
}

/**
 * GET请求
 */
function get(url, params = {}, options = {}) {
  const queryString = Object.keys(params)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
  
  const fullUrl = queryString ? `${url}?${queryString}` : url;
  
  return request(fullUrl, {
    method: 'GET',
    ...options
  });
}

/**
 * POST请求
 */
function post(url, data = {}, options = {}) {
  return request(url, {
    method: 'POST',
    data,
    ...options
  });
}

/**
 * PUT请求
 */
function put(url, data = {}, options = {}) {
  return request(url, {
    method: 'PUT',
    data,
    ...options
  });
}

/**
 * DELETE请求
 */
function del(url, options = {}) {
  return request(url, {
    method: 'DELETE',
    ...options
  });
}

/**
 * 文件上传
 */
function upload(url, filePath, options = {}) {
  const {
    name = 'file',
    formData = {},
    showLoading: shouldShowLoading = true,
    ...otherOptions
  } = options;

  if (shouldShowLoading) {
    showLoading('上传中...');
  }

  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: url.startsWith('http') ? url : `${config.baseURL}${url}`,
      filePath,
      name,
      formData,
      header: {
        'Authorization': `Token ${getToken()}`
      },
      success: (response) => {
        if (shouldShowLoading) {
          hideLoading();
        }

        if (response.statusCode === 200) {
          try {
            const data = JSON.parse(response.data);
            resolve(data.data || data);
          } catch (error) {
            resolve(response.data);
          }
        } else {
          handleHttpError(response.statusCode, response.data);
        }
      },
      fail: (error) => {
        if (shouldShowLoading) {
          hideLoading();
        }
        handleNetworkError(error);
        reject(error);
      },
      ...otherOptions
    });
  });
}

// 导出API管理器
module.exports = {
  config,
  request,
  get,
  post,
  put,
  delete: del,
  upload,
  getToken,
  setToken,
  clearToken,
  showLoading,
  hideLoading,
  showError,
  showSuccess
};

/**
 * 消息推送和通知管理器
 * 提供统一的消息推送、本地通知和消息管理功能
 */

// 消息类型定义
const MESSAGE_TYPES = {
  SYSTEM: 'system',           // 系统消息
  ORDER: 'order',            // 订单消息
  PAYMENT: 'payment',        // 支付消息
  TEA_FIELD: 'tea_field',    // 茶地相关
  EARNINGS: 'earnings',      // 收益消息
  PROMOTION: 'promotion',    // 推广消息
  MAINTENANCE: 'maintenance' // 维护消息
}

// 消息优先级
const MESSAGE_PRIORITY = {
  LOW: 1,
  NORMAL: 2,
  HIGH: 3,
  URGENT: 4
}

// 本地消息存储
let localMessages = []
let unreadCount = 0
let messageListeners = []

/**
 * 消息推送管理器
 */
const NotificationManager = {
  // 初始化推送服务
  async init() {
    try {
      // 获取用户授权
      await this.requestPermission()
      
      // 加载本地消息
      this.loadLocalMessages()
      
      // 注册推送事件监听
      this.registerPushListeners()
    } catch (error) {
      console.error('❌ 消息推送系统初始化失败:', error)
    }
  },

  // 请求推送权限
  async requestPermission() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userInfo']) {
            // 已授权，获取用户信息
            wx.getUserInfo({
              success: () => {
                resolve(true)
              },
              fail: reject
            })
          } else {
            // 请求授权
            wx.authorize({
              scope: 'scope.userInfo',
              success: () => {
                resolve(true)
              },
              fail: () => {
                resolve(false)
              }
            })
          }
        },
        fail: reject
      })
    })
  },

  // 注册推送事件监听
  registerPushListeners() {
    // 监听推送消息
    wx.onAppShow((options) => {
      if (options.scene === 1007) {
        // 从推送消息进入
        this.handlePushMessage(options)
      }
    })

    // 监听后台消息
    wx.onBackgroundAudioStop(() => {
    })
  },

  // 处理推送消息
  handlePushMessage(options) {
    const { query } = options
    if (query && query.messageId) {
      // 标记消息为已读
      this.markAsRead(query.messageId)
      
      // 跳转到相应页面
      this.navigateToMessage(query)
    }
  },

  // 发送本地通知
  sendLocalNotification(message) {
    const notification = {
      id: this.generateId(),
      type: message.type || MESSAGE_TYPES.SYSTEM,
      title: message.title,
      content: message.content,
      data: message.data || {},
      priority: message.priority || MESSAGE_PRIORITY.NORMAL,
      timestamp: Date.now(),
      read: false,
      persistent: message.persistent || false
    }

    // 添加到本地消息列表
    localMessages.unshift(notification)
    unreadCount++

    // 保存到本地存储
    this.saveLocalMessages()

    // 显示系统通知
    if (message.showToast !== false) {
      wx.showToast({
        title: message.title,
        icon: message.icon || 'none',
        duration: message.duration || 2000
      })
    }

    // 通知监听器
    this.notifyListeners('new_message', notification)
    return notification
  },

  // 批量发送通知
  sendBatchNotifications(messages) {
    const notifications = messages.map(msg => this.sendLocalNotification({
      ...msg,
      showToast: false // 批量发送时不显示toast
    }))

    // 显示批量通知提示
    wx.showToast({
      title: `收到${messages.length}条新消息`,
      icon: 'none'
    })

    return notifications
  },

  // 标记消息为已读
  markAsRead(messageId) {
    const message = localMessages.find(msg => msg.id === messageId)
    if (message && !message.read) {
      message.read = true
      unreadCount = Math.max(0, unreadCount - 1)
      
      this.saveLocalMessages()
      this.notifyListeners('message_read', message)
    }
  },

  // 批量标记为已读
  markAllAsRead() {
    localMessages.forEach(msg => {
      if (!msg.read) {
        msg.read = true
      }
    })
    
    unreadCount = 0
    this.saveLocalMessages()
    this.notifyListeners('all_read')
  },

  // 删除消息
  deleteMessage(messageId) {
    const index = localMessages.findIndex(msg => msg.id === messageId)
    if (index !== -1) {
      const message = localMessages[index]
      if (!message.read) {
        unreadCount = Math.max(0, unreadCount - 1)
      }
      
      localMessages.splice(index, 1)
      this.saveLocalMessages()
      this.notifyListeners('message_deleted', message)
    }
  },

  // 清空所有消息
  clearAllMessages() {
    localMessages = []
    unreadCount = 0
    this.saveLocalMessages()
    this.notifyListeners('all_cleared')
  },

  // 获取消息列表
  getMessages(options = {}) {
    const {
      type = null,
      unreadOnly = false,
      limit = 50,
      offset = 0
    } = options

    let filteredMessages = [...localMessages]

    // 按类型筛选
    if (type) {
      filteredMessages = filteredMessages.filter(msg => msg.type === type)
    }

    // 只显示未读
    if (unreadOnly) {
      filteredMessages = filteredMessages.filter(msg => !msg.read)
    }

    // 分页
    const result = filteredMessages.slice(offset, offset + limit)

    return {
      messages: result,
      total: filteredMessages.length,
      unreadCount: this.getUnreadCount()
    }
  },

  // 获取未读消息数量
  getUnreadCount() {
    return unreadCount
  },

  // 生成消息ID
  generateId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  },

  // 保存到本地存储
  saveLocalMessages() {
    try {
      wx.setStorageSync('local_messages', {
        messages: localMessages,
        unreadCount: unreadCount,
        timestamp: Date.now()
      })
    } catch (error) {
      console.error('❌ 保存消息到本地存储失败:', error)
    }
  },

  // 从本地存储加载
  loadLocalMessages() {
    try {
      const data = wx.getStorageSync('local_messages')
      if (data && data.messages) {
        localMessages = data.messages || []
        unreadCount = data.unreadCount || 0
        
        // 清理过期消息（超过30天的非持久化消息）
        this.cleanExpiredMessages()
      }
    } catch (error) {
      console.error('❌ 加载本地消息失败:', error)
    }
  },

  // 清理过期消息
  cleanExpiredMessages() {
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000)
    const originalLength = localMessages.length
    
    localMessages = localMessages.filter(msg => {
      return msg.persistent || msg.timestamp > thirtyDaysAgo
    })

    if (localMessages.length !== originalLength) {
      this.saveLocalMessages()
    }
  },

  // 添加消息监听器
  addListener(callback) {
    messageListeners.push(callback)
  },

  // 移除消息监听器
  removeListener(callback) {
    const index = messageListeners.indexOf(callback)
    if (index !== -1) {
      messageListeners.splice(index, 1)
    }
  },

  // 通知监听器
  notifyListeners(event, data) {
    messageListeners.forEach(callback => {
      try {
        callback(event, data)
      } catch (error) {
        console.error('❌ 消息监听器回调错误:', error)
      }
    })
  },

  // 跳转到消息相关页面
  navigateToMessage(query) {
    const { type, targetId } = query
    
    switch (type) {
      case MESSAGE_TYPES.ORDER:
        wx.navigateTo({
          url: `/pages/orders/orders?id=${targetId}`
        })
        break
      case MESSAGE_TYPES.TEA_FIELD:
        wx.navigateTo({
          url: `/pages/tea-detail/tea-detail?id=${targetId}`
        })
        break
      case MESSAGE_TYPES.EARNINGS:
        wx.switchTab({
          url: '/pages/analytics/analytics'
        })
        break
      default:
        wx.navigateTo({
          url: '/pages/notifications/notifications'
        })
    }
  }
}

module.exports = {
  NotificationManager,
  MESSAGE_TYPES,
  MESSAGE_PRIORITY
}

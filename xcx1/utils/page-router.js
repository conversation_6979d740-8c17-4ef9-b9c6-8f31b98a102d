/**
 * 页面路由管理器
 * 管理自定义底部导航的页面跳转
 */

class PageRouter {
  constructor() {
    this.currentPage = ''
    this.tabPages = new Set()
    this.pageStack = []
  }

  // 初始化路由管理器
  init(tabPages = []) {
    // 设置tab页面列表
    this.tabPages = new Set(tabPages.map(page => this.normalizePath(page)))
    
    // 获取当前页面
    this.updateCurrentPage()
    // Debug log removed
  }

  // 标准化页面路径
  normalizePath(path) {
    // 移除开头的斜杠，确保路径格式一致
    return path.replace(/^\//, '')
  }

  // 更新当前页面信息
  updateCurrentPage() {
    const pages = getCurrentPages()
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      this.currentPage = currentPage.route
      this.pageStack = pages.map(page => page.route)
    }
  }

  // 判断是否为tab页面
  isTabPage(path) {
    const normalizedPath = this.normalizePath(path)
    return this.tabPages.has(normalizedPath)
  }

  // 获取当前tab页面的索引
  getCurrentTabIndex(tabList) {
    this.updateCurrentPage()
    
    for (let i = 0; i < tabList.length; i++) {
      const tabPath = this.normalizePath(tabList[i].pagePath)
      if (tabPath === this.currentPage) {
        return i
      }
    }
    
    return 0 // 默认返回第一个
  }

  // 导航到指定页面
  navigateTo(path, params = {}) {
    const normalizedPath = this.normalizePath(path)
    if (this.isTabPage(normalizedPath)) {
      // Tab页面使用switchTab
      this.switchTab(normalizedPath, params)
    } else {
      // 非Tab页面使用navigateTo
      this.navigateToPage(normalizedPath, params)
    }
  }

  // 切换到Tab页面
  switchTab(path, params = {}) {
    const normalizedPath = this.normalizePath(path)
    
    // 构建URL
    let url = `/${normalizedPath}`
    
    // 添加参数（注意：switchTab不支持参数，这里只是为了兼容）
    if (Object.keys(params).length > 0) {
    }
    
    wx.switchTab({
      url: url,
      success: () => {
        this.updateCurrentPage()
      },
      fail: (error) => {
        console.error(`❌ 切换Tab页面失败: ${normalizedPath}`, error)
        // 降级处理：使用reLaunch
        this.reLaunchPage(normalizedPath, params)
      }
    })
  }

  // 导航到普通页面
  navigateToPage(path, params = {}) {
    const normalizedPath = this.normalizePath(path)
    
    // 构建URL
    let url = `/${normalizedPath}`
    
    // 添加参数
    if (Object.keys(params).length > 0) {
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&')
      url += `?${queryString}`
    }
    
    wx.navigateTo({
      url: url,
      success: () => {
        this.updateCurrentPage()
      },
      fail: (error) => {
        console.error(`❌ 导航到页面失败: ${normalizedPath}`, error)
      }
    })
  }

  // 重新启动到指定页面
  reLaunchPage(path, params = {}) {
    const normalizedPath = this.normalizePath(path)
    
    // 构建URL
    let url = `/${normalizedPath}`
    
    // 添加参数
    if (Object.keys(params).length > 0) {
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&')
      url += `?${queryString}`
    }
    
    wx.reLaunch({
      url: url,
      success: () => {
        this.updateCurrentPage()
      },
      fail: (error) => {
        console.error(`❌ 重启到页面失败: ${normalizedPath}`, error)
      }
    })
  }

  // 返回上一页
  navigateBack(delta = 1) {
    wx.navigateBack({
      delta: delta,
      success: () => {
        this.updateCurrentPage()
      },
      fail: (error) => {
        console.error(`❌ 返回上一页失败`, error)
      }
    })
  }

  // 获取页面栈信息
  getPageStack() {
    this.updateCurrentPage()
    return {
      current: this.currentPage,
      stack: this.pageStack,
      depth: this.pageStack.length
    }
  }

  // 清理页面栈（保留指定数量的页面）
  cleanPageStack(keepCount = 1) {
    const pages = getCurrentPages()
    const currentCount = pages.length
    
    if (currentCount > keepCount) {
      const deltaCount = currentCount - keepCount
      // 这里只能通过navigateBack来清理
      // 实际使用中需要根据具体情况调整
      this.navigateBack(deltaCount)
    }
  }
}

// 创建全局实例
const pageRouter = new PageRouter()

module.exports = {
  pageRouter,
  PageRouter
}

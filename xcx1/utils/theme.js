/**
 * 主题管理器
 * 处理主题切换、色彩模式和视觉效果
 */

// 主题类型
const THEME_TYPES = {
  AUTO: 'auto',     // 跟随系统
  LIGHT: 'light',   // 浅色主题
  DARK: 'dark'      // 暗色主题
}

// 主题色彩方案
const COLOR_SCHEMES = {
  GREEN: 'green',     // 茶绿色（默认）
  BLUE: 'blue',       // 蓝色
  PURPLE: 'purple',   // 紫色
  ORANGE: 'orange',   // 橙色
  RED: 'red'          // 红色
}

// 存储键名
const STORAGE_KEYS = {
  THEME_TYPE: 'theme_type',
  COLOR_SCHEME: 'color_scheme',
  CUSTOM_COLORS: 'custom_colors'
}

/**
 * 主题管理器类
 */
class ThemeManager {
  constructor() {
    this.currentTheme = THEME_TYPES.AUTO
    this.currentColorScheme = COLOR_SCHEMES.GREEN
    this.customColors = {}
    this.listeners = []
    
    this.init()
  }

  /**
   * 初始化主题管理器
   */
  init() {
    try {
      // 加载保存的主题设置
      this.loadThemeSettings()
      
      // 监听系统主题变化
      this.listenSystemThemeChange()
      
      // 应用当前主题
      this.applyTheme()
    } catch (error) {
      console.error('❌ 主题管理器初始化失败:', error)
    }
  }

  /**
   * 加载主题设置
   */
  loadThemeSettings() {
    try {
      const themeType = wx.getStorageSync(STORAGE_KEYS.THEME_TYPE)
      const colorScheme = wx.getStorageSync(STORAGE_KEYS.COLOR_SCHEME)
      const customColors = wx.getStorageSync(STORAGE_KEYS.CUSTOM_COLORS)

      if (themeType && Object.values(THEME_TYPES).includes(themeType)) {
        this.currentTheme = themeType
      }

      if (colorScheme && Object.values(COLOR_SCHEMES).includes(colorScheme)) {
        this.currentColorScheme = colorScheme
      }

      if (customColors) {
        this.customColors = JSON.parse(customColors)
      }
    } catch (error) {
      console.error('❌ 加载主题设置失败:', error)
    }
  }

  /**
   * 保存主题设置
   */
  saveThemeSettings() {
    try {
      wx.setStorageSync(STORAGE_KEYS.THEME_TYPE, this.currentTheme)
      wx.setStorageSync(STORAGE_KEYS.COLOR_SCHEME, this.currentColorScheme)
      wx.setStorageSync(STORAGE_KEYS.CUSTOM_COLORS, JSON.stringify(this.customColors))
    } catch (error) {
      console.error('❌ 保存主题设置失败:', error)
    }
  }

  /**
   * 监听系统主题变化
   */
  listenSystemThemeChange() {
    // 小程序暂不支持监听系统主题变化
    // 可以通过定时检查或页面显示时检查
  }

  /**
   * 设置主题类型
   * @param {string} themeType 主题类型
   */
  setThemeType(themeType) {
    if (!Object.values(THEME_TYPES).includes(themeType)) {
      return
    }

    this.currentTheme = themeType
    this.saveThemeSettings()
    this.applyTheme()
    this.notifyListeners('themeTypeChanged', themeType)
  }

  /**
   * 设置色彩方案
   * @param {string} colorScheme 色彩方案
   */
  setColorScheme(colorScheme) {
    if (!Object.values(COLOR_SCHEMES).includes(colorScheme)) {
      return
    }

    this.currentColorScheme = colorScheme
    this.saveThemeSettings()
    this.applyTheme()
    this.notifyListeners('colorSchemeChanged', colorScheme)
  }

  /**
   * 应用主题
   */
  applyTheme() {
    try {
      const isDark = this.isDarkMode()
      const themeClass = isDark ? 'theme-dark' : 'theme-light'
      
      // 获取页面实例
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        
        // 设置页面主题类
        if (currentPage.setData) {
          currentPage.setData({
            themeClass,
            isDarkMode: isDark,
            colorScheme: this.currentColorScheme
          })
        }
      }

      // 应用色彩方案
      this.applyColorScheme()
      
    } catch (error) {
      console.error('❌ 应用主题失败:', error)
    }
  }

  /**
   * 应用色彩方案
   */
  applyColorScheme() {
    // 根据色彩方案设置CSS变量
    const colorVariables = this.getColorVariables(this.currentColorScheme)
    
    // 在小程序中，可以通过setData更新样式变量
    // 或者通过动态添加样式类来实现
  }

  /**
   * 获取色彩变量
   * @param {string} scheme 色彩方案
   * @returns {Object} 色彩变量对象
   */
  getColorVariables(scheme) {
    const colorMaps = {
      [COLOR_SCHEMES.GREEN]: {
        primary: '#2E7D32',
        primaryLight: '#66BB6A',
        primaryDark: '#1B5E20'
      },
      [COLOR_SCHEMES.BLUE]: {
        primary: '#1976D2',
        primaryLight: '#42A5F5',
        primaryDark: '#0D47A1'
      },
      [COLOR_SCHEMES.PURPLE]: {
        primary: '#7B1FA2',
        primaryLight: '#BA68C8',
        primaryDark: '#4A148C'
      },
      [COLOR_SCHEMES.ORANGE]: {
        primary: '#F57C00',
        primaryLight: '#FFB74D',
        primaryDark: '#E65100'
      },
      [COLOR_SCHEMES.RED]: {
        primary: '#D32F2F',
        primaryLight: '#EF5350',
        primaryDark: '#B71C1C'
      }
    }

    return colorMaps[scheme] || colorMaps[COLOR_SCHEMES.GREEN]
  }

  /**
   * 判断是否为暗色模式
   * @returns {boolean} 是否为暗色模式
   */
  isDarkMode() {
    if (this.currentTheme === THEME_TYPES.DARK) {
      return true
    } else if (this.currentTheme === THEME_TYPES.LIGHT) {
      return false
    } else {
      // AUTO模式，检查系统设置
      return this.getSystemDarkMode()
    }
  }

  /**
   * 获取系统暗色模式设置
   * @returns {boolean} 系统是否为暗色模式
   */
  getSystemDarkMode() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      return systemInfo.theme === 'dark'
    } catch (error) {
      console.error('❌ 获取系统主题失败:', error)
      return false
    }
  }

  /**
   * 切换主题模式
   */
  toggleTheme() {
    const currentIsDark = this.isDarkMode()
    const newTheme = currentIsDark ? THEME_TYPES.LIGHT : THEME_TYPES.DARK
    this.setThemeType(newTheme)
  }

  /**
   * 设置自定义颜色
   * @param {string} key 颜色键
   * @param {string} value 颜色值
   */
  setCustomColor(key, value) {
    this.customColors[key] = value
    this.saveThemeSettings()
    this.applyTheme()
    this.notifyListeners('customColorChanged', { key, value })
  }

  /**
   * 获取当前主题信息
   * @returns {Object} 主题信息
   */
  getCurrentTheme() {
    return {
      type: this.currentTheme,
      colorScheme: this.currentColorScheme,
      isDark: this.isDarkMode(),
      customColors: this.customColors
    }
  }

  /**
   * 获取主题预设
   * @returns {Object} 主题预设
   */
  getThemePresets() {
    return {
      types: Object.values(THEME_TYPES),
      colorSchemes: Object.values(COLOR_SCHEMES),
      typeLabels: {
        [THEME_TYPES.AUTO]: '跟随系统',
        [THEME_TYPES.LIGHT]: '浅色模式',
        [THEME_TYPES.DARK]: '暗色模式'
      },
      schemeLabels: {
        [COLOR_SCHEMES.GREEN]: '茶绿色',
        [COLOR_SCHEMES.BLUE]: '海洋蓝',
        [COLOR_SCHEMES.PURPLE]: '优雅紫',
        [COLOR_SCHEMES.ORANGE]: '活力橙',
        [COLOR_SCHEMES.RED]: '热情红'
      }
    }
  }

  /**
   * 添加主题变化监听器
   * @param {Function} listener 监听器函数
   */
  addListener(listener) {
    this.listeners.push(listener)
  }

  /**
   * 移除主题变化监听器
   * @param {Function} listener 监听器函数
   */
  removeListener(listener) {
    const index = this.listeners.indexOf(listener)
    if (index !== -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 通知监听器
   * @param {string} event 事件类型
   * @param {any} data 事件数据
   */
  notifyListeners(event, data) {
    this.listeners.forEach(listener => {
      try {
        listener(event, data)
      } catch (error) {
        console.error('❌ 主题监听器错误:', error)
      }
    })
  }

  /**
   * 重置主题设置
   */
  resetTheme() {
    this.currentTheme = THEME_TYPES.AUTO
    this.currentColorScheme = COLOR_SCHEMES.GREEN
    this.customColors = {}
    
    this.saveThemeSettings()
    this.applyTheme()
    this.notifyListeners('themeReset')
  }

  /**
   * 导出主题配置
   * @returns {Object} 主题配置
   */
  exportThemeConfig() {
    return {
      themeType: this.currentTheme,
      colorScheme: this.currentColorScheme,
      customColors: this.customColors,
      exportTime: Date.now()
    }
  }

  /**
   * 导入主题配置
   * @param {Object} config 主题配置
   */
  importThemeConfig(config) {
    try {
      if (config.themeType && Object.values(THEME_TYPES).includes(config.themeType)) {
        this.currentTheme = config.themeType
      }

      if (config.colorScheme && Object.values(COLOR_SCHEMES).includes(config.colorScheme)) {
        this.currentColorScheme = config.colorScheme
      }

      if (config.customColors && typeof config.customColors === 'object') {
        this.customColors = config.customColors
      }

      this.saveThemeSettings()
      this.applyTheme()
      this.notifyListeners('themeImported', config)
    } catch (error) {
      console.error('❌ 导入主题配置失败:', error)
    }
  }
}

// 创建全局主题管理器实例
const globalThemeManager = new ThemeManager()

module.exports = {
  ThemeManager,
  globalThemeManager,
  THEME_TYPES,
  COLOR_SCHEMES
}

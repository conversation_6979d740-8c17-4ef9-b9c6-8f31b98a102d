/**
 * 滚动优化工具
 * 提供滚动性能优化、懒加载和无限滚动功能
 */

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 节流后的函数
 */
function throttle(func, delay) {
  let lastCall = 0
  return function(...args) {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return func.apply(this, args)
    }
  }
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 防抖后的函数
 */
function debounce(func, delay) {
  let timeoutId
  return function(...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 滚动优化器
 */
class ScrollOptimizer {
  constructor(options = {}) {
    this.options = {
      // 节流延迟
      throttleDelay: 16, // 60fps
      // 防抖延迟
      debounceDelay: 100,
      // 预加载距离
      preloadDistance: 200,
      // 懒加载阈值
      lazyLoadThreshold: 0.1,
      // 是否启用虚拟滚动
      enableVirtualScroll: false,
      // 虚拟滚动项目高度
      virtualItemHeight: 100,
      // 缓冲区大小
      bufferSize: 5,
      ...options
    }
    
    this.scrollListeners = []
    this.lazyLoadObserver = null
    this.isScrolling = false
    this.scrollTimer = null
    
    this.init()
  }
  
  /**
   * 初始化
   */
  init() {
    // 初始化懒加载观察器
    this.initLazyLoadObserver()
    
    // 绑定全局滚动事件
    this.bindGlobalScrollEvents()
  }
  
  /**
   * 初始化懒加载观察器
   */
  initLazyLoadObserver() {
    // 小程序环境下使用 IntersectionObserver
    if (typeof wx !== 'undefined' && wx.createIntersectionObserver) {
      this.lazyLoadObserver = wx.createIntersectionObserver(null, {
        thresholds: [this.options.lazyLoadThreshold],
        initialRatio: 0,
        observeAll: true
      })
    }
  }
  
  /**
   * 绑定全局滚动事件
   */
  bindGlobalScrollEvents() {
    // 监听页面滚动
    if (typeof wx !== 'undefined') {
      wx.onPageScroll = throttle((res) => {
        this.handleGlobalScroll(res)
      }, this.options.throttleDelay)
    }
  }
  
  /**
   * 处理全局滚动
   */
  handleGlobalScroll(scrollData) {
    this.isScrolling = true
    
    // 清除之前的定时器
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer)
    }
    
    // 通知所有监听器
    this.scrollListeners.forEach(listener => {
      try {
        listener(scrollData)
      } catch (error) {
        console.error('滚动监听器错误:', error)
      }
    })
    
    // 设置滚动结束检测
    this.scrollTimer = setTimeout(() => {
      this.isScrolling = false
      this.onScrollEnd()
    }, this.options.debounceDelay)
  }
  
  /**
   * 滚动结束处理
   */
  onScrollEnd() {
    // 触发滚动结束事件
    this.scrollListeners.forEach(listener => {
      if (listener.onScrollEnd) {
        try {
          listener.onScrollEnd()
        } catch (error) {
          console.error('滚动结束监听器错误:', error)
        }
      }
    })
  }
  
  /**
   * 添加滚动监听器
   * @param {Function} listener 监听器函数
   */
  addScrollListener(listener) {
    this.scrollListeners.push(listener)
  }
  
  /**
   * 移除滚动监听器
   * @param {Function} listener 监听器函数
   */
  removeScrollListener(listener) {
    const index = this.scrollListeners.indexOf(listener)
    if (index !== -1) {
      this.scrollListeners.splice(index, 1)
    }
  }
  
  /**
   * 优化滚动列表
   * @param {Object} listConfig 列表配置
   */
  optimizeList(listConfig) {
    const {
      selector,
      itemSelector,
      onScroll,
      onLoadMore,
      loadMoreThreshold = 100
    } = listConfig
    
    // 创建节流的滚动处理函数
    const throttledScroll = throttle((e) => {
      const { scrollTop, scrollHeight, clientHeight } = e.detail || e.target
      
      // 检查是否需要加载更多
      if (scrollHeight - scrollTop - clientHeight < loadMoreThreshold) {
        if (onLoadMore && typeof onLoadMore === 'function') {
          onLoadMore()
        }
      }
      
      // 调用自定义滚动处理
      if (onScroll && typeof onScroll === 'function') {
        onScroll(e)
      }
    }, this.options.throttleDelay)
    
    return {
      onScroll: throttledScroll,
      optimizedItemHeight: this.options.virtualItemHeight
    }
  }
  
  /**
   * 懒加载图片
   * @param {string} selector 图片选择器
   * @param {Object} options 选项
   */
  lazyLoadImages(selector, options = {}) {
    const {
      placeholder = '',
      errorImage = '',
      fadeIn = true
    } = options
    
    if (!this.lazyLoadObserver) {
      return
    }
    
    // 观察图片元素
    this.lazyLoadObserver.observe(selector, (res) => {
      res.forEach(item => {
        if (item.intersectionRatio > this.options.lazyLoadThreshold) {
          // 元素进入可视区域
          const dataset = item.dataset || {}
          const src = dataset.src
          
          if (src) {
            // 加载图片
            this.loadImage(src, {
              target: item.id,
              placeholder,
              errorImage,
              fadeIn
            })
          }
        }
      })
    })
  }
  
  /**
   * 加载图片
   * @param {string} src 图片地址
   * @param {Object} options 选项
   */
  loadImage(src, options = {}) {
    const {
      target,
      placeholder,
      errorImage,
      fadeIn
    } = options
    
    return new Promise((resolve, reject) => {
      // 创建图片对象进行预加载
      const img = new Image()
      
      img.onload = () => {
        // 图片加载成功
        if (target) {
          this.updateImageSrc(target, src, fadeIn)
        }
        resolve(src)
      }
      
      img.onerror = () => {
        // 图片加载失败
        if (target && errorImage) {
          this.updateImageSrc(target, errorImage, fadeIn)
        }
        reject(new Error(`图片加载失败: ${src}`))
      }
      
      img.src = src
    })
  }
  
  /**
   * 更新图片源
   * @param {string} target 目标元素
   * @param {string} src 图片地址
   * @param {boolean} fadeIn 是否淡入
   */
  updateImageSrc(target, src, fadeIn = true) {
    // 在小程序中更新图片
    if (typeof wx !== 'undefined') {
      const query = wx.createSelectorQuery()
      query.select(`#${target}`).fields({
        properties: ['src']
      }).exec((res) => {
        if (res && res[0]) {
          // 这里需要通过页面实例来更新数据
          // 具体实现依赖于页面结构
        }
      })
    }
  }
  
  /**
   * 无限滚动
   * @param {Object} config 配置
   */
  infiniteScroll(config) {
    const {
      container,
      loadMore,
      threshold = 100,
      loading = false
    } = config
    
    if (loading) return
    
    const scrollHandler = throttle((e) => {
      const { scrollTop, scrollHeight, clientHeight } = e.detail
      
      if (scrollHeight - scrollTop - clientHeight < threshold) {
        if (loadMore && typeof loadMore === 'function') {
          loadMore()
        }
      }
    }, this.options.throttleDelay)
    
    return scrollHandler
  }
  
  /**
   * 平滑滚动到指定位置
   * @param {number} scrollTop 滚动位置
   * @param {number} duration 动画时长
   */
  smoothScrollTo(scrollTop, duration = 300) {
    return new Promise((resolve) => {
      // 小程序中使用 pageScrollTo
      if (typeof wx !== 'undefined' && wx.pageScrollTo) {
        wx.pageScrollTo({
          scrollTop,
          duration,
          success: resolve,
          fail: resolve
        })
      } else {
        resolve()
      }
    })
  }
  
  /**
   * 获取滚动性能数据
   */
  getPerformanceData() {
    return {
      isScrolling: this.isScrolling,
      listenersCount: this.scrollListeners.length,
      throttleDelay: this.options.throttleDelay,
      debounceDelay: this.options.debounceDelay
    }
  }
  
  /**
   * 销毁优化器
   */
  destroy() {
    // 清除定时器
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer)
    }
    
    // 断开懒加载观察器
    if (this.lazyLoadObserver) {
      this.lazyLoadObserver.disconnect()
    }
    
    // 清空监听器
    this.scrollListeners = []
  }
}

// 创建全局实例
const globalScrollOptimizer = new ScrollOptimizer()

module.exports = {
  ScrollOptimizer,
  globalScrollOptimizer,
  throttle,
  debounce
}

/**
 * 离线数据同步管理器
 * 处理离线状态下的数据缓存和同步
 */

const { cacheManager } = require('./cache.js')

// 离线数据配置
const OFFLINE_CONFIG = {
  // 关键数据类型
  CRITICAL_DATA_TYPES: [
    'home_banners',
    'home_stats', 
    'tea_field_list',
    'tea_field_detail',
    'user_profile',
    'user_fields',
    'filter_options'
  ],
  
  // 同步策略
  SYNC_STRATEGIES: {
    IMMEDIATE: 'immediate',     // 立即同步
    BACKGROUND: 'background',   // 后台同步
    MANUAL: 'manual'           // 手动同步
  },
  
  // 同步间隔
  SYNC_INTERVALS: {
    CRITICAL: 5 * 60 * 1000,    // 5分钟
    NORMAL: 15 * 60 * 1000,     // 15分钟
    LOW: 60 * 60 * 1000         // 1小时
  }
}

// 离线状态管理
let offlineState = {
  isOffline: false,
  lastOnlineTime: Date.now(),
  pendingSyncs: [],
  syncInProgress: false
}

/**
 * 离线数据管理器
 */
const OfflineManager = {
  // 缓存管理器实例
  cache: null,
  
  /**
   * 初始化离线管理器
   */
  init() {
    try {
      // 初始化缓存管理器
      this.cache = cacheManager
      
      // 监听网络状态
      this.initNetworkListener()
      
      // 加载离线状态
      this.loadOfflineState()
      
      // 启动后台同步
      this.startBackgroundSync()
    } catch (error) {
      console.error('❌ 离线数据管理器初始化失败:', error)
    }
  },
  
  /**
   * 初始化网络监听
   */
  initNetworkListener() {
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      const wasOffline = offlineState.isOffline
      offlineState.isOffline = !res.isConnected
      
      if (wasOffline && res.isConnected) {
        // 网络恢复，开始同步
        this.syncOfflineData()
      } else if (!wasOffline && !res.isConnected) {
        // 网络断开
        offlineState.lastOnlineTime = Date.now()
      }
      
      this.saveOfflineState()
    })
  },
  
  /**
   * 缓存关键数据
   * @param {string} type 数据类型
   * @param {any} data 数据
   * @param {object} options 选项
   */
  cacheData(type, data, options = {}) {
    const {
      priority = 'normal',
      strategy = OFFLINE_CONFIG.SYNC_STRATEGIES.BACKGROUND
    } = options
    
    // 判断是否为关键数据
    const isCritical = OFFLINE_CONFIG.CRITICAL_DATA_TYPES.includes(type)
    
    if (this.cache) {
      if (isCritical || offlineState.isOffline) {
        // 使用离线缓存
        return this.cache.setOfflineCache(type, data, {
          priority: isCritical ? 'high' : priority
        })
      } else {
        // 使用普通缓存
        return this.cache.set(type, data, options)
      }
    } else {
    }
  },
  
  /**
   * 获取缓存数据
   * @param {string} type 数据类型
   * @param {function} fallbackFn 回退函数
   */
  async getData(type, fallbackFn = null) {
    try {
      // 检查缓存管理器是否已初始化
      if (!this.cache) {
        this.init()
      }

      // 先尝试获取离线缓存
      let data = this.cache ? this.cache.getOfflineCache(type) : null
      
      if (data) {
        return data
      }
      
      // 尝试普通缓存
      data = this.cache ? this.cache.get(type) : null
      
      if (data) {
        return data
      }
      
      // 如果在线且有回退函数，尝试获取新数据
      if (!offlineState.isOffline && fallbackFn) {
        try {
          data = await fallbackFn()
          
          // 缓存新数据
          this.cacheData(type, data)
          
          return data
        } catch (error) {
          console.error(`❌ 在线获取数据失败: ${type}`, error)
          
          // 如果有过期的缓存，返回过期数据
          const expiredData = this.getExpiredCache(type)
          if (expiredData) {
            return expiredData
          }
          
          throw error
        }
      }
      
      // 离线状态下返回null或抛出错误
      if (offlineState.isOffline) {
        return null
      }
      
      throw new Error(`无可用数据: ${type}`)
    } catch (error) {
      console.error(`❌ 获取数据失败: ${type}`, error)
      throw error
    }
  },
  
  /**
   * 获取过期缓存
   * @param {string} type 数据类型
   */
  getExpiredCache(type) {
    try {
      // 检查缓存管理器是否存在
      if (!this.cache) {
        return null
      }

      // 尝试从本地存储获取过期数据
      const storageKey = `${this.cache.cacheConfig.keyPrefix}offline_${type}`
      const cacheItemString = wx.getStorageSync(storageKey)
      
      if (cacheItemString) {
        const cacheItem = JSON.parse(cacheItemString)
        return cacheItem.data
      }
      
      return null
    } catch (error) {
      console.error(`❌ 获取过期缓存失败: ${type}`, error)
      return null
    }
  },
  
  /**
   * 添加待同步数据
   * @param {string} type 数据类型
   * @param {any} data 数据
   * @param {string} action 操作类型
   */
  addPendingSync(type, data, action = 'update') {
    const syncItem = {
      id: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      action,
      timestamp: Date.now(),
      retryCount: 0
    }
    
    offlineState.pendingSyncs.push(syncItem)
    this.saveOfflineState()
  },
  
  /**
   * 同步离线数据
   */
  async syncOfflineData() {
    if (offlineState.syncInProgress || offlineState.isOffline) {
      return
    }
    
    try {
      offlineState.syncInProgress = true
      // 复制待同步列表
      const syncsToProcess = [...offlineState.pendingSyncs]
      
      // 按优先级排序
      syncsToProcess.sort((a, b) => {
        const aPriority = OFFLINE_CONFIG.CRITICAL_DATA_TYPES.includes(a.type) ? 1 : 2
        const bPriority = OFFLINE_CONFIG.CRITICAL_DATA_TYPES.includes(b.type) ? 1 : 2
        return aPriority - bPriority
      })
      
      // 逐个同步
      for (const syncItem of syncsToProcess) {
        try {
          await this.processSyncItem(syncItem)
          
          // 从待同步列表中移除
          const index = offlineState.pendingSyncs.findIndex(item => item.id === syncItem.id)
          if (index !== -1) {
            offlineState.pendingSyncs.splice(index, 1)
          }
        } catch (error) {
          console.error(`❌ 同步失败: ${syncItem.type}`, error)
          
          // 增加重试次数
          syncItem.retryCount++
          
          // 如果重试次数过多，移除该项
          if (syncItem.retryCount >= 3) {
            const index = offlineState.pendingSyncs.findIndex(item => item.id === syncItem.id)
            if (index !== -1) {
              offlineState.pendingSyncs.splice(index, 1)
            }
          }
        }
      }
      
      this.saveOfflineState()
    } catch (error) {
      console.error('❌ 离线数据同步失败:', error)
    } finally {
      offlineState.syncInProgress = false
    }
  },
  
  /**
   * 处理单个同步项
   * @param {object} syncItem 同步项
   */
  async processSyncItem(syncItem) {
    // 这里应该根据具体的业务逻辑来处理同步
    // 例如调用相应的API来上传数据
    // 模拟同步过程
    await new Promise(resolve => setTimeout(resolve, 100))
  },
  
  /**
   * 启动后台同步
   */
  startBackgroundSync() {
    // 定期检查并同步数据
    setInterval(() => {
      if (!offlineState.isOffline && offlineState.pendingSyncs.length > 0) {
        this.syncOfflineData()
      }
    }, OFFLINE_CONFIG.SYNC_INTERVALS.NORMAL)
  },
  
  /**
   * 保存离线状态
   */
  saveOfflineState() {
    try {
      wx.setStorageSync('offline_state', JSON.stringify(offlineState))
    } catch (error) {
      console.error('❌ 保存离线状态失败:', error)
    }
  },
  
  /**
   * 加载离线状态
   */
  loadOfflineState() {
    try {
      const savedState = wx.getStorageSync('offline_state')
      if (savedState) {
        offlineState = { ...offlineState, ...JSON.parse(savedState) }
      }
    } catch (error) {
      console.error('❌ 加载离线状态失败:', error)
    }
  },
  
  /**
   * 获取离线状态
   */
  getOfflineState() {
    return {
      ...offlineState,
      offlineTime: offlineState.isOffline ? Date.now() - offlineState.lastOnlineTime : 0
    }
  },
  
  /**
   * 清理离线数据
   */
  clearOfflineData() {
    try {
      // 清理待同步数据
      offlineState.pendingSyncs = []
      this.saveOfflineState()
      
      // 清理离线缓存
      const keys = wx.getStorageInfoSync().keys
      keys.forEach(key => {
        if (key.startsWith(`${this.cache.cacheConfig.keyPrefix}offline_`)) {
          wx.removeStorageSync(key)
        }
      })
    } catch (error) {
      console.error('❌ 清理离线数据失败:', error)
    }
  }
}

module.exports = {
  OfflineManager,
  OFFLINE_CONFIG
}

/**
 * 页面装修配置管理器
 * 负责获取和管理页面装修配置
 */

// 检查是否在微信小程序环境中
function isWechatMiniProgram() {
  return typeof wx !== 'undefined' && wx.request
}

// 简单的API请求封装
function makeRequest(url, method = 'GET', data = null) {
  if (!isWechatMiniProgram()) {
    return Promise.reject(new Error('Not in WeChat MiniProgram environment'))
  }

  return new Promise((resolve, reject) => {
    wx.request({
      url: `https://teabuy.yizhangkj.com/api/v1${url}`,
      method: method,
      data: data,
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        resolve(res.data)
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

class DecorationManager {
  constructor() {
    this.cache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5分钟缓存
  }

  /**
   * 获取页面配置
   * @param {string} pageType 页面类型
   * @returns {Promise<Object>} 页面配置
   */
  async getPageConfig(pageType = 'home') {
    // 完全禁用缓存，确保实时同步
    const cacheKey = `page_config_${pageType}`
    this.cache.delete(cacheKey)
    if (!isWechatMiniProgram()) {
      // 非微信小程序环境，使用默认配置
      const config = this.getDefaultConfig(pageType)

      // 缓存配置
      this.cache.set(cacheKey, {
        data: config,
        timestamp: Date.now()
      })

      return config
    }

    try {
      // 使用强化的缓存绕过方法
      const timestamp = Date.now()
      const random = Math.random().toString(36).substring(7)
      const uuid = Math.random().toString(36).substring(2, 15)
      const response = await makeRequest(`/page-decoration/config/${pageType}/?nocache=${timestamp}&r=${random}&uuid=${uuid}&force_refresh=true`)

      if (response && response.code === 200) {
        let config = response.data

        // 后端已经通过序列化器过滤了禁用的板块，前端不需要再次过滤
        // 这样可以确保前后端状态完全同步
        if (config.template?.blocks) {
          // 打印板块状态用于调试
          config.template.blocks.forEach((block, index) => {
          })
        }
        // Debug log removed
        // Debug log removed

        // 打印板块详情
        if (config.template?.blocks) {
          config.template.blocks.forEach((block, index) => {
            const status = block.is_enabled ? '✅ 启用' : '❌ 禁用'
          })
        } else {
        }

        return config
      } else {
        return this.getDefaultConfig(pageType)
      }
    } catch (error) {
      return this.getDefaultConfig(pageType)
    }
  }

  /**
   * 获取全局配置
   * @returns {Promise<Object>} 全局配置
   */
  async getGlobalConfig() {
    const cacheKey = 'global_config'
    const cached = this.cache.get(cacheKey)

    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data
    }

    if (!isWechatMiniProgram()) {
      // 非微信小程序环境，使用默认配置
      const config = this.getDefaultGlobalConfig()

      // 缓存配置
      this.cache.set(cacheKey, {
        data: config,
        timestamp: Date.now()
      })

      return config
    }

    try {
      const response = await makeRequest('/page-decoration/global-config/')

      if (response && response.code === 200) {
        const config = response.data

        // 缓存配置
        this.cache.set(cacheKey, {
          data: config,
          timestamp: Date.now()
        })
        return config
      } else {
        return this.getDefaultGlobalConfig()
      }
    } catch (error) {
      return this.getDefaultGlobalConfig()
    }
  }

  /**
   * 获取默认页面配置
   * @param {string} pageType 页面类型
   * @returns {Object} 默认配置
   */
  getDefaultConfig(pageType) {
    const defaultConfigs = {
      home: {
        page_type: 'home',
        template: {
          id: 0,
          name: '默认首页模板',
          page_type: 'home',
          blocks: [
            {
              id: 0,
              name: '轮播图',
              block_type: 'banner',
              layout_type: 'banner',
              is_enabled: true,
              sort_order: 0,
              style: {
                background_color: '#FFFFFF',
                text_color: '#333333',
                margin_top: 0,
                margin_bottom: 10,
                padding: 0
              },
              content_config: {
                auto_play: true,
                interval: 3000,
                indicator_dots: true
              },
              items: []
            },
            {
              id: 1,
              name: '快捷导航',
              block_type: 'quick_nav',
              layout_type: 'grid_4',
              is_enabled: true,
              sort_order: 10,
              style: {
                background_color: '#FFFFFF',
                text_color: '#333333',
                margin_top: 0,
                margin_bottom: 10,
                padding: 15
              },
              content_config: {
                columns: 4,
                show_title: true
              },
              items: []
            },
            {
              id: 2,
              name: '精选茶园',
              block_type: 'featured_tea',
              layout_type: 'grid_2',
              is_enabled: true,
              sort_order: 20,
              style: {
                background_color: '#FFFFFF',
                text_color: '#333333',
                margin_top: 0,
                margin_bottom: 10,
                padding: 15
              },
              content_config: {
                limit: 4,
                show_price: true,
                show_location: true
              },
              items: []
            }
          ]
        },
        global_config: this.getDefaultGlobalConfig()
      },
      category: {
        page_type: 'category',
        template: {
          id: 0,
          name: '默认分类页模板',
          page_type: 'category',
          blocks: [
            {
              id: 0,
              name: '分类导航',
              block_type: 'category_nav',
              layout_type: 'list',
              is_enabled: true,
              sort_order: 0,
              style: {
                background_color: '#FFFFFF',
                text_color: '#333333',
                margin_top: 0,
                margin_bottom: 10,
                padding: 10
              },
              content_config: {},
              items: []
            },
            {
              id: 1,
              name: '产品网格',
              block_type: 'product_grid',
              layout_type: 'grid_2',
              is_enabled: true,
              sort_order: 10,
              style: {
                background_color: '#FFFFFF',
                text_color: '#333333',
                margin_top: 0,
                margin_bottom: 0,
                padding: 10
              },
              content_config: {
                columns: 2,
                show_price: true
              },
              items: []
            }
          ]
        },
        global_config: this.getDefaultGlobalConfig()
      }
    }

    return defaultConfigs[pageType] || defaultConfigs.home
  }

  /**
   * 获取默认全局配置
   * @returns {Object} 默认全局配置
   */
  getDefaultGlobalConfig() {
    return {
      id: 0,
      name: '默认配置',
      description: '系统默认装修配置',
      style: {
        primary_color: '#1890ff',
        secondary_color: '#52c41a',
        background_color: '#f5f5f5',
        text_color: '#333333'
      },
      global_config: {
        theme: 'default',
        font_size: 'medium',
        border_radius: 'medium'
      },
      is_active: true
    }
  }

  /**
   * 清除缓存
   * @param {string} pageType 页面类型，不传则清除所有
   */
  clearCache(pageType) {
    if (pageType) {
      const cacheKey = `page_config_${pageType}`
      this.cache.delete(cacheKey)
    } else {
      this.cache.clear()
    }

    // 同时清除微信小程序的本地存储缓存
    try {
      wx.removeStorageSync('decoration_cache')
      wx.removeStorageSync('page_config_home')
      wx.removeStorageSync('global_config')
    } catch (e) {
    }
  }

  /**
   * 预加载页面配置
   * @param {Array<string>} pageTypes 页面类型列表
   */
  async preloadConfigs(pageTypes = ['home', 'category', 'profile']) {
    const promises = pageTypes.map(pageType => 
      this.getPageConfig(pageType).catch(error => {
        console.error(`预加载 ${pageType} 配置失败:`, error)
        return null
      })
    )
    
    try {
      await Promise.all(promises)
    } catch (error) {
      console.error('❌ 页面配置预加载失败:', error)
    }
  }

  /**
   * 获取板块配置
   * @param {string} blockType 板块类型
   * @returns {Object} 板块默认配置
   */
  getBlockDefaultConfig(blockType) {
    const blockConfigs = {
      banner: {
        auto_play: true,
        interval: 3000,
        indicator_dots: true,
        circular: true
      },
      quick_nav: {
        columns: 4,
        show_title: true,
        icon_size: 'medium'
      },
      featured_tea: {
        limit: 4,
        show_price: true,
        show_location: true,
        layout: 'grid'
      },
      hot_tea: {
        limit: 6,
        show_price: true,
        show_sales: true
      },
      video_showcase: {
        limit: 3,
        auto_play: false,
        show_title: true
      }
    }

    return blockConfigs[blockType] || {}
  }
}

// 创建单例实例
const decorationManager = new DecorationManager()

module.exports = decorationManager

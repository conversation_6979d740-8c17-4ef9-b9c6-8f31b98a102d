const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

// 图片URL处理函数
const formatImageUrl = (imageUrl) => {
  if (!imageUrl) {
    return '/images/avatar-default.png' // 默认头像
  }

  // 如果已经是完整URL，直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl
  }

  // 如果是本地图片路径（以/images/开头），直接返回
  if (imageUrl.startsWith('/images/')) {
    return imageUrl
  }

  // 如果是相对路径，添加域名
  const baseUrl = 'https://teabuy.yizhangkj.com'

  // 确保路径以/开头
  if (!imageUrl.startsWith('/')) {
    imageUrl = '/' + imageUrl
  }

  return baseUrl + imageUrl
}

// 处理用户头像URL
const formatAvatarUrl = (avatarUrl) => {
  if (!avatarUrl) {
    return '/images/avatar-default.png'
  }

  // 如果是默认头像路径，使用本地图片
  if (avatarUrl.includes('default-avatar.png') || avatarUrl.includes('/static/images/')) {
    return '/images/avatar-default.png'
  }

  return formatImageUrl(avatarUrl)
}

// 处理茶地图片URL
const formatTeaImageUrl = (imageUrl) => {
  if (!imageUrl || imageUrl.trim() === '') {
    return null // 不使用默认图片，返回null让前端显示占位符
  }

  // 如果已经是完整URL，直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl
  }

  // 如果是相对路径，添加域名前缀
  if (imageUrl.startsWith('/')) {
    return `https://teabuy.yizhangkj.com${imageUrl}`
  }

  // 其他情况，添加完整路径
  return `https://teabuy.yizhangkj.com/${imageUrl}`
}

// 处理轮播图URL
const formatBannerUrl = (bannerUrl) => {
  if (!bannerUrl) {
    return '' // 不使用默认图片，必须从后台获取
  }

  return formatImageUrl(bannerUrl)
}

/**
 * 通用的图片错误处理函数
 * @param {Object} e 错误事件对象
 */
const onImageError = function(e) {
  const defaultImages = {
    'tea': '/images/tea-default.jpg',
    'avatar': '/images/avatar-default.png',
    'banner': '/images/logo.png'
  }

  const imageType = e.currentTarget.dataset.type || 'tea'
  const defaultImage = defaultImages[imageType] || defaultImages.tea
  // 更新图片src
  const dataField = e.currentTarget.dataset.field

  if (dataField && this.setData) {
    const updateData = {}
    updateData[dataField] = defaultImage
    this.setData(updateData)
  }
}

/**
 * 数字格式化（避免精度问题）
 * @param {number} num 要格式化的数字
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的数字字符串
 */
const formatMoney = (num, decimals = 2) => {
  if (num === null || num === undefined || isNaN(num)) {
    return '0.00'
  }
  const number = parseFloat(num)
  return (Math.round(number * Math.pow(10, decimals)) / Math.pow(10, decimals)).toFixed(decimals)
}

/**
 * 手机号验证
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
const validatePhone = (phone) => {
  const phoneReg = /^1[3-9]\d{9}$/
  return phoneReg.test(phone)
}

/**
 * 身份证号验证
 * @param {string} idCard 身份证号
 * @returns {boolean} 是否有效
 */
const validateIdCard = (idCard) => {
  const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardReg.test(idCard)
}

module.exports = {
  formatTime,
  formatImageUrl,
  formatAvatarUrl,
  formatTeaImageUrl,
  formatBannerUrl,
  onImageError,
  formatMoney,
  validatePhone,
  validateIdCard
}

/**
 * 离线数据缓存和性能优化工具
 * 支持内存缓存、本地存储缓存和网络状态感知
 */

class CacheManager {
  constructor() {
    this.memoryCache = new Map()
    this.networkStatus = 'unknown'
    this.cacheConfig = {
      // 默认缓存时间 (毫秒)
      defaultTTL: 30 * 60 * 1000, // 30分钟
      // 最大缓存条目数
      maxItems: 200,
      // 缓存键前缀
      keyPrefix: 'teabuy_cache_',
      // 离线缓存配置
      offline: {
        // 关键数据的离线缓存时间
        criticalDataTTL: 24 * 60 * 60 * 1000, // 24小时
        // 最大离线缓存大小
        maxOfflineSize: 10 * 1024 * 1024, // 10MB
        // 离线缓存的数据类型
        offlineTypes: [
          'home_data',
          'tea_field_list',
          'tea_field_detail',
          'user_profile',
          'user_fields',
          'filter_options'
        ]
      }
    }

    // 初始化网络状态监听
    this.initNetworkMonitor()
  }

  /**
   * 初始化网络状态监听
   */
  initNetworkMonitor() {
    // 获取当前网络状态
    wx.getNetworkType({
      success: (res) => {
        this.networkStatus = res.networkType
      }
    })

    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      const oldStatus = this.networkStatus
      this.networkStatus = res.networkType
      // 网络恢复时同步缓存
      if (oldStatus === 'none' && this.networkStatus !== 'none') {
        this.syncOfflineData()
      }
    })
  }

  /**
   * 检查是否在线
   */
  isOnline() {
    return this.networkStatus !== 'none'
  }

  /**
   * 检查是否为弱网环境
   */
  isWeakNetwork() {
    return this.networkStatus === '2g' || this.networkStatus === '3g'
  }

  /**
   * 设置离线缓存
   * @param {string} key 缓存键
   * @param {any} data 数据
   * @param {object} options 选项
   */
  setOfflineCache(key, data, options = {}) {
    try {
      const {
        ttl = this.cacheConfig.offline.criticalDataTTL,
        priority = 'normal' // low, normal, high, critical
      } = options

      const cacheItem = {
        data,
        timestamp: Date.now(),
        expiry: Date.now() + ttl,
        priority,
        type: 'offline',
        key
      }

      // 存储到本地存储
      const storageKey = `${this.cacheConfig.keyPrefix}offline_${key}`
      wx.setStorageSync(storageKey, JSON.stringify(cacheItem))

      // 同时存储到内存缓存
      this.memoryCache.set(`offline_${key}`, cacheItem)
      // 检查缓存大小
      this.checkOfflineCacheSize()

      return true
    } catch (error) {
      console.error(`❌ 离线缓存失败: ${key}`, error)
      return false
    }
  }

  /**
   * 获取离线缓存
   * @param {string} key 缓存键
   */
  getOfflineCache(key) {
    try {
      // 先从内存缓存获取
      const memoryItem = this.memoryCache.get(`offline_${key}`)
      if (memoryItem && Date.now() < memoryItem.expiry) {
        return memoryItem.data
      }

      // 从本地存储获取
      const storageKey = `${this.cacheConfig.keyPrefix}offline_${key}`
      const cacheItemString = wx.getStorageSync(storageKey)

      if (!cacheItemString) {
        return null
      }

      const cacheItem = JSON.parse(cacheItemString)

      // 检查是否过期
      if (Date.now() > cacheItem.expiry) {
        this.removeOfflineCache(key)
        return null
      }

      // 更新内存缓存
      this.memoryCache.set(`offline_${key}`, cacheItem)
      return cacheItem.data
    } catch (error) {
      console.error(`❌ 获取离线缓存失败: ${key}`, error)
      return null
    }
  }

  /**
   * 移除离线缓存
   * @param {string} key 缓存键
   */
  removeOfflineCache(key) {
    try {
      // 从内存缓存移除
      this.memoryCache.delete(`offline_${key}`)

      // 从本地存储移除
      const storageKey = `${this.cacheConfig.keyPrefix}offline_${key}`
      wx.removeStorageSync(storageKey)
    } catch (error) {
      console.error(`❌ 移除离线缓存失败: ${key}`, error)
    }
  }

  /**
   * 检查离线缓存大小
   */
  checkOfflineCacheSize() {
    try {
      const info = wx.getStorageInfoSync()
      const currentSize = info.currentSize * 1024 // 转换为字节
      const maxSize = this.cacheConfig.offline.maxOfflineSize

      if (currentSize > maxSize * 0.9) {
        this.cleanupOfflineCache()
      }
    } catch (error) {
      console.error('❌ 检查离线缓存大小失败:', error)
    }
  }

  /**
   * 清理离线缓存
   */
  cleanupOfflineCache() {
    try {
      const keys = wx.getStorageInfoSync().keys
      const offlineCacheItems = []

      // 收集所有离线缓存项
      keys.forEach(key => {
        if (key.startsWith(`${this.cacheConfig.keyPrefix}offline_`)) {
          try {
            const cacheItemString = wx.getStorageSync(key)
            const cacheItem = JSON.parse(cacheItemString)
            cacheItem.storageKey = key
            offlineCacheItems.push(cacheItem)
          } catch (e) {
            // 解析失败的直接删除
            wx.removeStorageSync(key)
          }
        }
      })

      // 按优先级和时间排序
      offlineCacheItems.sort((a, b) => {
        const priorityOrder = { critical: 4, high: 3, normal: 2, low: 1 }
        const aPriority = priorityOrder[a.priority] || 2
        const bPriority = priorityOrder[b.priority] || 2

        if (aPriority !== bPriority) {
          return aPriority - bPriority // 优先级低的先删除
        }

        return a.timestamp - b.timestamp // 时间早的先删除
      })

      // 删除最旧的低优先级缓存
      const removeCount = Math.ceil(offlineCacheItems.length * 0.3)
      for (let i = 0; i < removeCount && i < offlineCacheItems.length; i++) {
        const item = offlineCacheItems[i]
        wx.removeStorageSync(item.storageKey)
        this.memoryCache.delete(`offline_${item.key}`)
      }
    } catch (error) {
      console.error('❌ 清理离线缓存失败:', error)
    }
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   * @param {number} ttl 过期时间(毫秒)
   * @param {boolean} persistent 是否持久化到本地存储
   */
  set(key, data, ttl = this.cacheConfig.defaultTTL, persistent = false) {
    const cacheItem = {
      data,
      timestamp: Date.now(),
      ttl,
      persistent
    }

    // 内存缓存
    this.memoryCache.set(key, cacheItem)

    // 持久化缓存
    if (persistent) {
      try {
        wx.setStorageSync(this.cacheConfig.keyPrefix + key, cacheItem)
      } catch (error) {
        console.warn('持久化缓存失败:', error)
      }
    }

    // 清理过期缓存
    this.cleanup()
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @returns {any|null} 缓存数据或null
   */
  get(key) {
    // 先从内存缓存获取
    let cacheItem = this.memoryCache.get(key)

    // 如果内存中没有，尝试从本地存储获取
    if (!cacheItem) {
      try {
        cacheItem = wx.getStorageSync(this.cacheConfig.keyPrefix + key)
        if (cacheItem) {
          // 恢复到内存缓存
          this.memoryCache.set(key, cacheItem)
        }
      } catch (error) {
        console.warn('读取持久化缓存失败:', error)
      }
    }

    if (!cacheItem) {
      return null
    }

    // 检查是否过期
    const now = Date.now()
    if (now - cacheItem.timestamp > cacheItem.ttl) {
      this.delete(key)
      return null
    }

    return cacheItem.data
  }

  /**
   * 删除缓存
   * @param {string} key 缓存键
   */
  delete(key) {
    this.memoryCache.delete(key)
    try {
      wx.removeStorageSync(this.cacheConfig.keyPrefix + key)
    } catch (error) {
      console.warn('删除持久化缓存失败:', error)
    }
  }

  /**
   * 检查缓存是否存在且未过期
   * @param {string} key 缓存键
   * @returns {boolean}
   */
  has(key) {
    return this.get(key) !== null
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now()
    
    // 清理内存缓存
    for (const [key, item] of this.memoryCache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.memoryCache.delete(key)
      }
    }

    // 限制缓存条目数
    if (this.memoryCache.size > this.cacheConfig.maxItems) {
      const entries = Array.from(this.memoryCache.entries())
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp)
      
      const toDelete = entries.slice(0, entries.length - this.cacheConfig.maxItems)
      toDelete.forEach(([key]) => this.memoryCache.delete(key))
    }
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.memoryCache.clear()
    
    try {
      const storageInfo = wx.getStorageInfoSync()
      const keys = storageInfo.keys.filter(key => 
        key.startsWith(this.cacheConfig.keyPrefix)
      )
      keys.forEach(key => wx.removeStorageSync(key))
    } catch (error) {
      console.warn('清空持久化缓存失败:', error)
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {object} 缓存统计
   */
  getStats() {
    const memorySize = this.memoryCache.size
    let persistentSize = 0
    
    try {
      const storageInfo = wx.getStorageInfoSync()
      persistentSize = storageInfo.keys.filter(key => 
        key.startsWith(this.cacheConfig.keyPrefix)
      ).length
    } catch (error) {
      console.warn('获取存储信息失败:', error)
    }

    return {
      memorySize,
      persistentSize,
      maxItems: this.cacheConfig.maxItems,
      defaultTTL: this.cacheConfig.defaultTTL
    }
  }
}

// 创建全局缓存实例
const cacheManager = new CacheManager()

/**
 * API缓存装饰器
 * @param {string} key 缓存键
 * @param {number} ttl 过期时间
 * @param {boolean} persistent 是否持久化
 */
function withCache(key, ttl = 5 * 60 * 1000, persistent = false) {
  return function(target, propertyName, descriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function(...args) {
      // 生成缓存键
      const cacheKey = `${key}_${JSON.stringify(args)}`
      
      // 尝试从缓存获取
      const cachedData = cacheManager.get(cacheKey)
      if (cachedData) {
        return cachedData
      }

      // 调用原方法
      try {
        const result = await originalMethod.apply(this, args)
        
        // 缓存结果
        if (result && result.code === 200) {
          cacheManager.set(cacheKey, result, ttl, persistent)
        }
        
        return result
      } catch (error) {
        console.error('API调用失败:', error)
        throw error
      }
    }

    return descriptor
  }
}

/**
 * 图片缓存工具
 */
class ImageCache {
  constructor() {
    this.imageCache = new Map()
    this.maxCacheSize = 50 // 最大缓存图片数量
  }

  /**
   * 预加载图片
   * @param {string|Array} urls 图片URL或URL数组
   * @returns {Promise}
   */
  preload(urls) {
    const urlArray = Array.isArray(urls) ? urls : [urls]
    
    return Promise.all(
      urlArray.map(url => this.loadImage(url))
    )
  }

  /**
   * 加载单个图片
   * @param {string} url 图片URL
   * @returns {Promise}
   */
  loadImage(url) {
    return new Promise((resolve, reject) => {
      // 检查缓存
      if (this.imageCache.has(url)) {
        resolve(url)
        return
      }

      // 下载图片
      wx.downloadFile({
        url,
        success: (res) => {
          if (res.statusCode === 200) {
            // 缓存图片
            this.imageCache.set(url, res.tempFilePath)
            this.cleanup()
            resolve(res.tempFilePath)
          } else {
            reject(new Error(`图片下载失败: ${res.statusCode}`))
          }
        },
        fail: reject
      })
    })
  }

  /**
   * 获取缓存的图片路径
   * @param {string} url 原始URL
   * @returns {string} 缓存路径或原始URL
   */
  getCachedPath(url) {
    return this.imageCache.get(url) || url
  }

  /**
   * 清理图片缓存
   */
  cleanup() {
    if (this.imageCache.size > this.maxCacheSize) {
      const entries = Array.from(this.imageCache.entries())
      const toDelete = entries.slice(0, entries.length - this.maxCacheSize)
      toDelete.forEach(([url]) => this.imageCache.delete(url))
    }
  }

  /**
   * 清空图片缓存
   */
  clear() {
    this.imageCache.clear()
  }
}

// 创建全局图片缓存实例
const imageCache = new ImageCache()

/**
 * 性能监控工具
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
  }

  /**
   * 开始性能监控
   * @param {string} name 监控名称
   */
  start(name) {
    this.metrics.set(name, {
      startTime: Date.now(),
      endTime: null,
      duration: null
    })
  }

  /**
   * 结束性能监控
   * @param {string} name 监控名称
   * @returns {number} 耗时(毫秒)
   */
  end(name) {
    const metric = this.metrics.get(name)
    if (!metric) {
      return 0
    }

    metric.endTime = Date.now()
    metric.duration = metric.endTime - metric.startTime
    return metric.duration
  }

  /**
   * 获取性能指标
   * @param {string} name 监控名称
   * @returns {object|null} 性能指标
   */
  getMetric(name) {
    return this.metrics.get(name) || null
  }

  /**
   * 获取所有性能指标
   * @returns {object} 所有性能指标
   */
  getAllMetrics() {
    const result = {}
    for (const [name, metric] of this.metrics.entries()) {
      result[name] = metric
    }
    return result
  }

  /**
   * 清空性能指标
   */
  clear() {
    this.metrics.clear()
  }
}

// 创建全局性能监控实例
const performanceMonitor = new PerformanceMonitor()

module.exports = {
  cacheManager,
  withCache,
  imageCache,
  performanceMonitor
}

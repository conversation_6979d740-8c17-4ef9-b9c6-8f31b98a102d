#!/usr/bin/env python3
"""
测试微信用户序列化器修复
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.notifications.models import WechatUserInfo
from api.v1.serializers import UserDetailSerializer, WechatUserSerializer

User = get_user_model()


def test_serializer_fix():
    """测试序列化器修复效果"""
    print("🧪 测试微信用户序列化器修复...")
    
    # 查找一个有微信信息的用户
    try:
        wechat_user = WechatUserInfo.objects.filter(
            nickname__isnull=False,
            avatar_url__isnull=False
        ).exclude(
            nickname='',
            avatar_url=''
        ).first()
        
        if not wechat_user:
            print("❌ 没有找到有完整微信信息的用户")
            return False
            
        user = wechat_user.user
        print(f"📱 测试用户: {user.username}")
        print(f"   微信昵称: '{wechat_user.nickname}'")
        print(f"   微信头像: '{wechat_user.avatar_url}'")
        
        # 测试微信用户序列化器
        print(f"\n🔧 测试 WechatUserSerializer...")
        wechat_serializer = WechatUserSerializer(wechat_user)
        wechat_data = wechat_serializer.data
        
        print(f"序列化结果:")
        for key, value in wechat_data.items():
            print(f"   {key}: '{value}'")
        
        # 测试用户详情序列化器
        print(f"\n👤 测试 UserDetailSerializer...")
        user_serializer = UserDetailSerializer(user)
        user_data = user_serializer.data
        
        print(f"用户基本信息:")
        print(f"   id: {user_data.get('id')}")
        print(f"   username: '{user_data.get('username')}'")
        print(f"   nickname: '{user_data.get('nickname')}'")
        print(f"   avatar: '{user_data.get('avatar')}'")
        
        wechat_info = user_data.get('wechat_info')
        if wechat_info:
            print(f"微信信息:")
            for key, value in wechat_info.items():
                print(f"   {key}: '{value}'")
        else:
            print("❌ 微信信息为空!")
            return False
        
        # 验证关键字段
        success = True
        if not wechat_info.get('nickname'):
            print("❌ 微信昵称为空")
            success = False
        if not wechat_info.get('avatar_url'):
            print("❌ 微信头像为空")
            success = False
            
        if success:
            print("✅ 序列化器修复成功!")
        else:
            print("❌ 序列化器仍有问题")
            
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_response_format():
    """测试API响应格式"""
    print(f"\n🌐 测试API响应格式...")
    
    try:
        # 查找一个有微信信息的用户
        wechat_user = WechatUserInfo.objects.filter(
            nickname__isnull=False,
            avatar_url__isnull=False
        ).exclude(
            nickname='',
            avatar_url=''
        ).first()
        
        if not wechat_user:
            print("❌ 没有找到测试用户")
            return False
            
        user = wechat_user.user
        
        # 模拟API响应格式
        from rest_framework.authtoken.models import Token
        token, created = Token.objects.get_or_create(user=user)
        
        api_response = {
            'code': 200,
            'message': '登录成功',
            'data': {
                'token': token.key,
                'user': UserDetailSerializer(user).data,
                'wechat_info': WechatUserSerializer(wechat_user).data,
                'is_new_user': False
            }
        }
        
        print(f"模拟API响应:")
        print(f"   code: {api_response['code']}")
        print(f"   message: '{api_response['message']}'")
        print(f"   data.token: '{api_response['data']['token'][:20]}...'")
        print(f"   data.user.nickname: '{api_response['data']['user'].get('nickname')}'")
        print(f"   data.user.avatar: '{api_response['data']['user'].get('avatar')}'")
        print(f"   data.wechat_info.nickname: '{api_response['data']['wechat_info'].get('nickname')}'")
        print(f"   data.wechat_info.avatar_url: '{api_response['data']['wechat_info'].get('avatar_url')}'")
        
        # 验证前端合并逻辑
        print(f"\n🔄 模拟前端数据合并:")
        merged_user_info = {
            **api_response['data']['user'],
            'nickname': (api_response['data']['wechat_info'].get('nickname') or 
                        api_response['data']['user'].get('nickname') or '默认昵称'),
            'avatar': (api_response['data']['wechat_info'].get('avatar_url') or 
                      api_response['data']['user'].get('avatar') or '默认头像'),
            'wechat_info': api_response['data']['wechat_info']
        }
        
        print(f"合并后的用户信息:")
        print(f"   nickname: '{merged_user_info.get('nickname')}'")
        print(f"   avatar: '{merged_user_info.get('avatar')}'")
        
        return True
        
    except Exception as e:
        print(f"❌ API响应测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    print("=" * 60)
    print("微信用户序列化器修复测试")
    print("=" * 60)
    
    # 测试序列化器
    serializer_ok = test_serializer_fix()
    
    # 测试API响应格式
    api_ok = test_api_response_format()
    
    print(f"\n" + "=" * 60)
    print(f"测试结果:")
    print(f"   序列化器: {'✅ 通过' if serializer_ok else '❌ 失败'}")
    print(f"   API响应: {'✅ 通过' if api_ok else '❌ 失败'}")
    
    if serializer_ok and api_ok:
        print(f"🎉 修复成功! 微信用户信息应该能正常同步了")
    else:
        print(f"❌ 仍有问题需要进一步修复")
    print("=" * 60)

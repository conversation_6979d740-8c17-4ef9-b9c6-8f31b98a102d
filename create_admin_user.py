#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建管理员用户
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from apps.users.models import UserProfile

User = get_user_model()


def create_admin_user():
    """创建管理员用户"""
    print("👤 创建管理员用户...")
    
    # 检查是否已存在admin用户
    if User.objects.filter(username='admin').exists():
        print("⚠️ admin用户已存在，删除旧用户...")
        User.objects.filter(username='admin').delete()
    
    # 创建管理员用户
    admin_user = User.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='teabuy123456',
        nickname='系统管理员',
        phone='13800138000'
    )
    
    print(f"✅ 创建管理员用户成功:")
    print(f"   用户名: {admin_user.username}")
    print(f"   邮箱: {admin_user.email}")
    print(f"   昵称: {admin_user.nickname}")
    print(f"   是否超级用户: {admin_user.is_superuser}")
    print(f"   是否员工: {admin_user.is_staff}")
    
    # 创建Token
    token, created = Token.objects.get_or_create(user=admin_user)
    if created:
        print(f"✅ 创建API Token: {token.key}")
    else:
        print(f"ℹ️ API Token已存在: {token.key}")
    
    # 创建用户资料
    profile, created = UserProfile.objects.get_or_create(
        user=admin_user,
        defaults={
            'real_name': '系统管理员',
            'id_card': '',
            'address': '系统默认地址',
            'total_investment': 0,
            'total_earnings': 0,
            'risk_level': 'low'
        }
    )
    
    if created:
        print(f"✅ 创建用户资料成功")
    else:
        print(f"ℹ️ 用户资料已存在")
    
    return admin_user


def verify_admin_user():
    """验证管理员用户"""
    print("\n🔍 验证管理员用户...")
    
    try:
        admin_user = User.objects.get(username='admin')
        
        print(f"✅ 用户验证:")
        print(f"   ID: {admin_user.id}")
        print(f"   用户名: {admin_user.username}")
        print(f"   邮箱: {admin_user.email}")
        print(f"   昵称: {admin_user.nickname}")
        print(f"   手机: {admin_user.phone}")
        print(f"   是否激活: {admin_user.is_active}")
        print(f"   是否超级用户: {admin_user.is_superuser}")
        print(f"   是否员工: {admin_user.is_staff}")
        print(f"   创建时间: {admin_user.date_joined}")
        print(f"   最后登录: {admin_user.last_login}")
        
        # 验证密码
        if admin_user.check_password('teabuy123456'):
            print(f"✅ 密码验证成功")
        else:
            print(f"❌ 密码验证失败")
        
        # 验证Token
        try:
            token = Token.objects.get(user=admin_user)
            print(f"✅ API Token: {token.key}")
        except Token.DoesNotExist:
            print(f"❌ 没有API Token")
        
        # 验证用户资料
        try:
            profile = UserProfile.objects.get(user=admin_user)
            print(f"✅ 用户资料存在")
        except UserProfile.DoesNotExist:
            print(f"❌ 没有用户资料")
        
        return True
        
    except User.DoesNotExist:
        print(f"❌ admin用户不存在")
        return False


def clean_test_data():
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    # 删除测试用户（保留admin）
    test_users = User.objects.exclude(username='admin')
    test_count = test_users.count()
    
    if test_count > 0:
        print(f"🔄 删除 {test_count} 个测试用户...")
        test_users.delete()
        print(f"✅ 清理完成")
    else:
        print(f"ℹ️ 没有测试用户需要清理")
    
    # 显示当前用户统计
    total_users = User.objects.count()
    print(f"📊 当前用户总数: {total_users}")


def show_login_info():
    """显示登录信息"""
    print("\n" + "=" * 50)
    print("🎯 管理员登录信息:")
    print("=" * 50)
    print("Django后台管理:")
    print("  URL: https://teabuy.yizhangkj.com/admin/")
    print("  用户名: admin")
    print("  密码: teabuy123456")
    print()
    print("API访问:")
    
    try:
        admin_user = User.objects.get(username='admin')
        token = Token.objects.get(user=admin_user)
        print(f"  Token: {token.key}")
        print("  请求头: Authorization: Token " + token.key)
    except:
        print("  ❌ 无法获取Token")
    
    print()
    print("微信小程序测试:")
    print("  可以使用admin账号进行API测试")
    print("  建议创建普通用户进行小程序登录测试")


def main():
    """主函数"""
    print("🔧 创建管理员用户工具")
    print("=" * 50)
    
    # 清理测试数据
    clean_test_data()
    
    # 创建管理员
    admin_user = create_admin_user()
    
    # 验证管理员
    if verify_admin_user():
        print("\n✅ 管理员用户创建成功！")
    else:
        print("\n❌ 管理员用户创建失败！")
        return
    
    # 显示登录信息
    show_login_info()


if __name__ == "__main__":
    main()

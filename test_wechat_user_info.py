#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微信用户信息显示
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.notifications.models import WechatUserInfo
from api.v1.serializers import UserDetailSerializer, WechatUserSerializer

User = get_user_model()


def test_wechat_user_serializer():
    """测试微信用户序列化器"""
    print("🧪 测试微信用户序列化器...")
    
    # 获取一个有微信信息的用户
    wechat_user = WechatUserInfo.objects.first()
    if not wechat_user:
        print("❌ 没有找到微信用户信息")
        return
    
    print(f"📱 测试用户: {wechat_user.user.username}")
    print(f"   OpenID: {wechat_user.openid}")
    print(f"   昵称: {wechat_user.nickname}")
    print(f"   头像: {wechat_user.avatar_url}")
    
    # 测试序列化器
    serializer = WechatUserSerializer(wechat_user)
    print(f"\n🔄 序列化结果:")
    for key, value in serializer.data.items():
        print(f"   {key}: {value}")


def test_user_detail_serializer():
    """测试用户详情序列化器"""
    print("\n🧪 测试用户详情序列化器...")
    
    # 获取一个有微信信息的用户
    wechat_user = WechatUserInfo.objects.first()
    if not wechat_user:
        print("❌ 没有找到微信用户信息")
        return
    
    user = wechat_user.user
    print(f"👤 测试用户: {user.username}")
    
    # 测试序列化器
    serializer = UserDetailSerializer(user)
    print(f"\n🔄 序列化结果:")
    for key, value in serializer.data.items():
        if key == 'wechat_info' and value:
            print(f"   {key}:")
            for wk, wv in value.items():
                print(f"     {wk}: {wv}")
        else:
            print(f"   {key}: {value}")


def create_test_wechat_user():
    """创建测试微信用户"""
    print("\n🔧 创建测试微信用户...")
    
    # 获取测试用户
    try:
        user = User.objects.get(username='testuser')
    except User.DoesNotExist:
        print("❌ 测试用户不存在")
        return None
    
    # 更新或创建微信信息
    wechat_user, created = WechatUserInfo.objects.update_or_create(
        user=user,
        defaults={
            'openid': 'test_openid_123456789',
            'nickname': '测试茶友小明',
            'avatar_url': 'https://thirdwx.qlogo.cn/mmopen/test_avatar.jpg',
            'gender': 1,
            'city': '深圳',
            'province': '广东',
            'country': '中国'
        }
    )
    
    if created:
        print(f"✅ 创建微信用户信息: {wechat_user.nickname}")
    else:
        print(f"✅ 更新微信用户信息: {wechat_user.nickname}")
    
    return wechat_user


def test_api_response():
    """模拟API响应测试"""
    print("\n🧪 模拟API响应测试...")
    
    # 获取测试用户
    try:
        user = User.objects.get(username='testuser')
        wechat_user = WechatUserInfo.objects.get(user=user)
    except (User.DoesNotExist, WechatUserInfo.DoesNotExist):
        print("❌ 测试用户或微信信息不存在")
        return
    
    # 模拟登录API响应
    user_serializer = UserDetailSerializer(user)
    wechat_serializer = WechatUserSerializer(wechat_user)
    
    api_response = {
        'code': 200,
        'message': '登录成功',
        'data': {
            'token': 'test_token_123456',
            'user': user_serializer.data,
            'wechat_info': wechat_serializer.data,
            'is_new_user': False
        }
    }
    
    print("🔄 模拟API响应:")
    print(f"   用户昵称: {api_response['data']['user'].get('nickname')}")
    print(f"   用户头像: {api_response['data']['user'].get('avatar')}")
    print(f"   微信昵称: {api_response['data']['wechat_info'].get('nickname')}")
    print(f"   微信头像: {api_response['data']['wechat_info'].get('avatar_url')}")
    
    # 模拟前端合并逻辑
    merged_user_info = {
        **api_response['data']['user'],
        'nickname': api_response['data']['wechat_info'].get('nickname') or api_response['data']['user'].get('nickname'),
        'avatar': api_response['data']['wechat_info'].get('avatar_url') or api_response['data']['user'].get('avatar'),
        'wechat_info': api_response['data']['wechat_info']
    }
    
    print("\n🔄 前端合并后的用户信息:")
    print(f"   最终昵称: {merged_user_info.get('nickname')}")
    print(f"   最终头像: {merged_user_info.get('avatar')}")
    
    return merged_user_info


def check_all_wechat_users():
    """检查所有微信用户"""
    print("\n📊 检查所有微信用户...")
    
    wechat_users = WechatUserInfo.objects.all()
    print(f"总微信用户数: {wechat_users.count()}")
    
    for wechat_user in wechat_users[:5]:
        print(f"\n用户: {wechat_user.user.username}")
        print(f"  OpenID: {wechat_user.openid}")
        print(f"  昵称: {wechat_user.nickname or '(空)'}")
        print(f"  头像: {wechat_user.avatar_url or '(空)'}")
        print(f"  性别: {wechat_user.get_gender_display()}")
        print(f"  城市: {wechat_user.city or '(空)'}")


def main():
    """主函数"""
    print("🔧 测试微信用户信息显示")
    print("=" * 50)
    
    # 创建测试数据
    create_test_wechat_user()
    
    # 测试序列化器
    test_wechat_user_serializer()
    test_user_detail_serializer()
    
    # 测试API响应
    merged_info = test_api_response()
    
    # 检查所有用户
    check_all_wechat_users()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成！")
    
    if merged_info:
        print("\n💡 前端应该显示:")
        print(f"   昵称: {merged_info.get('nickname', '茶园投资者')}")
        print(f"   头像: {merged_info.get('avatar', '默认头像')}")
        
        if merged_info.get('nickname') and merged_info.get('nickname') != '茶园投资者':
            print("\n✅ 昵称显示应该正常")
        else:
            print("\n❌ 昵称可能仍显示默认值")
        
        if merged_info.get('avatar') and 'http' in merged_info.get('avatar', ''):
            print("✅ 头像显示应该正常")
        else:
            print("❌ 头像可能仍显示默认值")


if __name__ == "__main__":
    main()

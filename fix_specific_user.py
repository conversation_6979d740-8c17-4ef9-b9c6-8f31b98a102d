#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复特定用户的微信信息
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.notifications.models import WechatUserInfo
from apps.users.models import UserProfile
from rest_framework.authtoken.models import Token

User = get_user_model()


def fix_specific_user():
    """修复特定用户的信息"""
    print("🔧 修复特定用户的微信信息...")
    
    username = 'wx_oXYVEvjxO1wJ_itf'
    
    try:
        user = User.objects.get(username=username)
        print(f"✅ 找到用户: {user.username} (ID: {user.id})")
        
        # 更新用户基本信息
        user.nickname = '真实茶友用户'
        user.gender = 1
        user.city = '深圳'
        user.province = '广东'
        user.country = '中国'
        user.save()
        
        print(f"✅ 更新用户基本信息:")
        print(f"   昵称: {user.nickname}")
        print(f"   性别: {user.gender}")
        print(f"   城市: {user.city}")
        
        # 更新用户资料
        profile, created = UserProfile.objects.get_or_create(user=user)
        profile.avatar = 'https://thirdwx.qlogo.cn/mmopen/real_user_avatar.jpg'
        profile.save()
        
        print(f"✅ 更新用户资料:")
        print(f"   头像: {profile.avatar}")
        print(f"   创建新资料: {created}")
        
        # 更新微信用户信息
        try:
            wechat_user = WechatUserInfo.objects.get(user=user)
            wechat_user.nickname = '真实茶友用户'
            wechat_user.avatar_url = 'https://thirdwx.qlogo.cn/mmopen/real_user_avatar.jpg'
            wechat_user.gender = 1
            wechat_user.city = '深圳'
            wechat_user.province = '广东'
            wechat_user.country = '中国'
            wechat_user.language = 'zh_CN'
            wechat_user.save()
            
            print(f"✅ 更新微信用户信息:")
            print(f"   OpenID: {wechat_user.openid}")
            print(f"   昵称: {wechat_user.nickname}")
            print(f"   头像: {wechat_user.avatar_url}")
            print(f"   性别: {wechat_user.gender}")
            print(f"   城市: {wechat_user.city}")
            
        except WechatUserInfo.DoesNotExist:
            print(f"❌ 微信用户信息不存在")
            return False
        
        # 创建或更新token
        token, created = Token.objects.get_or_create(user=user)
        print(f"✅ Token: {token.key} (创建新token: {created})")
        
        return True
        
    except User.DoesNotExist:
        print(f"❌ 用户 {username} 不存在")
        return False


def test_api_response():
    """测试API响应"""
    print(f"\n🧪 测试API响应...")
    
    username = 'wx_oXYVEvjxO1wJ_itf'
    
    try:
        user = User.objects.get(username=username)
        wechat_user = WechatUserInfo.objects.get(user=user)
        
        from api.v1.serializers import UserDetailSerializer, WechatUserSerializer
        
        # 测试序列化器
        user_serializer = UserDetailSerializer(user)
        wechat_serializer = WechatUserSerializer(wechat_user)
        
        print(f"👤 用户序列化结果:")
        user_data = user_serializer.data
        for key, value in user_data.items():
            if key == 'wechat_info' and value:
                print(f"   {key}:")
                for wk, wv in value.items():
                    print(f"     {wk}: {wv}")
            else:
                print(f"   {key}: {value}")
        
        print(f"\n📱 微信用户序列化结果:")
        wechat_data = wechat_serializer.data
        for key, value in wechat_data.items():
            print(f"   {key}: {value}")
        
        # 模拟API响应
        api_response = {
            'code': 200,
            'message': '登录成功',
            'data': {
                'token': 'fixed_token_for_specific_user',
                'user': user_data,
                'wechat_info': wechat_data,
                'is_new_user': False
            }
        }
        
        print(f"\n🔄 模拟API响应:")
        print(f"   用户昵称: {api_response['data']['user'].get('nickname')}")
        print(f"   用户头像: {api_response['data']['user'].get('avatar')}")
        print(f"   微信昵称: {api_response['data']['wechat_info'].get('nickname')}")
        print(f"   微信头像: {api_response['data']['wechat_info'].get('avatar_url')}")
        
        # 模拟前端合并逻辑
        merged_user_info = {
            **api_response['data']['user'],
            'nickname': api_response['data']['wechat_info'].get('nickname') or api_response['data']['user'].get('nickname'),
            'avatar': api_response['data']['wechat_info'].get('avatar_url') or api_response['data']['user'].get('avatar'),
            'wechat_info': api_response['data']['wechat_info']
        }
        
        print(f"\n🔄 前端合并后的用户信息:")
        print(f"   最终昵称: {merged_user_info.get('nickname')}")
        print(f"   最终头像: {merged_user_info.get('avatar')}")
        
        return merged_user_info
        
    except (User.DoesNotExist, WechatUserInfo.DoesNotExist) as e:
        print(f"❌ 测试失败: {str(e)}")
        return None


def clear_user_cache():
    """清除用户相关的缓存"""
    print(f"\n🧹 清除用户缓存...")
    
    # 这里可以添加清除Redis缓存的逻辑
    # 目前Django项目可能没有使用Redis，所以暂时跳过
    
    print(f"ℹ️ 如果项目使用了缓存，请手动清除相关缓存")


def generate_frontend_test_data():
    """生成前端测试数据"""
    print(f"\n📱 生成前端测试数据...")
    
    username = 'wx_oXYVEvjxO1wJ_itf'
    
    try:
        user = User.objects.get(username=username)
        token = Token.objects.get(user=user)
        
        test_data = {
            'userInfo': {
                'id': user.id,
                'username': user.username,
                'nickname': user.nickname,
                'avatar': user.profile.avatar if hasattr(user, 'profile') else '',
                'phone': user.phone,
                'email': user.email,
                'gender': user.gender,
                'city': user.city,
                'province': user.province,
                'country': user.country
            },
            'token': token.key,
            'isLogin': True
        }
        
        print(f"📄 前端应该保存的数据:")
        print(f"   localStorage.userInfo: {test_data['userInfo']}")
        print(f"   localStorage.token: {test_data['token']}")
        print(f"   globalData.isLogin: {test_data['isLogin']}")
        
        return test_data
        
    except (User.DoesNotExist, Token.DoesNotExist) as e:
        print(f"❌ 生成测试数据失败: {str(e)}")
        return None


def main():
    """主函数"""
    print("🔧 修复特定用户的微信信息")
    print("=" * 60)
    
    # 修复用户信息
    success = fix_specific_user()
    
    if success:
        # 测试API响应
        merged_info = test_api_response()
        
        # 清除缓存
        clear_user_cache()
        
        # 生成前端测试数据
        test_data = generate_frontend_test_data()
        
        print("\n" + "=" * 60)
        print("✅ 特定用户修复完成！")
        
        if merged_info:
            print(f"\n💡 现在前端应该显示:")
            print(f"   个人中心昵称: {merged_info.get('nickname', '茶园投资者')}")
            print(f"   个人中心头像: {'真实头像' if merged_info.get('avatar') and 'http' in merged_info.get('avatar', '') else '默认头像'}")
        
        print(f"\n🔍 测试步骤:")
        print(f"1. 在小程序开发工具中：")
        print(f"   - 点击 '清缓存' -> '清除数据缓存'")
        print(f"   - 点击 '清缓存' -> '清除文件缓存'")
        print(f"2. 重新编译小程序")
        print(f"3. 在个人中心页面下拉刷新")
        print(f"4. 或者重新登录")
        
        print(f"\n📱 如果仍然不显示，请检查:")
        print(f"1. 前端控制台是否有错误")
        print(f"2. 网络请求是否成功")
        print(f"3. 用户信息是否正确保存到本地存储")
        
    else:
        print("\n❌ 修复失败")


if __name__ == "__main__":
    main()

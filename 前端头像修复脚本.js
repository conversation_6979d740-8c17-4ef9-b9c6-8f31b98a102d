// 前端头像修复脚本
// 在微信开发者工具的控制台中执行

console.log('🔧 开始修复前端头像显示问题...');

// 1. 检查当前状态
console.log('\n📊 当前状态检查:');
const app = getApp();
const currentUserInfo = app.globalData.userInfo;
const localUserInfo = wx.getStorageSync('userInfo');
const token = wx.getStorageSync('token');

console.log('全局用户信息:', currentUserInfo);
console.log('本地存储用户信息:', localUserInfo);
console.log('Token:', token);

// 2. 检查当前页面数据
const currentPage = getCurrentPages()[getCurrentPages().length - 1];
console.log('当前页面用户信息:', currentPage.data.userInfo);

// 3. 手动调用API获取最新数据
console.log('\n🔄 从服务器获取最新用户信息...');

// 引入API模块
const api = require('../../api/index.js');

api.userApi.getProfile().then(res => {
  console.log('✅ API响应:', res);
  
  if (res.code === 200 && res.data) {
    const serverUserInfo = res.data;
    console.log('服务器返回的用户信息:', serverUserInfo);
    
    // 检查头像字段
    console.log('\n🖼️ 头像字段检查:');
    console.log('用户头像字段:', serverUserInfo.avatar);
    console.log('微信头像字段:', serverUserInfo.wechat_info?.avatar_url);
    
    // 应用前端合并逻辑
    const mergedUserInfo = {
      ...serverUserInfo,
      nickname: serverUserInfo.wechat_info?.nickname || serverUserInfo.nickname,
      avatar: serverUserInfo.wechat_info?.avatar_url || serverUserInfo.avatar,
      wechat_info: serverUserInfo.wechat_info
    };
    
    console.log('合并后的用户信息:', mergedUserInfo);
    
    // 更新全局状态
    app.setUserInfo(mergedUserInfo);
    wx.setStorageSync('userInfo', mergedUserInfo);
    
    // 更新当前页面
    if (currentPage.setData) {
      currentPage.setData({
        userInfo: mergedUserInfo
      });
    }
    
    console.log('✅ 用户信息已更新到前端');
    
    // 验证更新结果
    console.log('\n✅ 更新验证:');
    console.log('页面显示昵称:', mergedUserInfo.nickname);
    console.log('页面显示头像:', mergedUserInfo.avatar);
    
    if (mergedUserInfo.nickname && mergedUserInfo.nickname !== '茶园投资者') {
      console.log('✅ 昵称更新成功');
    } else {
      console.log('❌ 昵称更新失败');
    }
    
    if (mergedUserInfo.avatar && mergedUserInfo.avatar.includes('http')) {
      console.log('✅ 头像URL更新成功');
    } else {
      console.log('❌ 头像URL更新失败');
    }
    
  } else {
    console.error('❌ API调用失败:', res);
  }
}).catch(err => {
  console.error('❌ API调用异常:', err);
  
  // 如果API调用失败，使用手动设置的方式
  console.log('\n🔧 API调用失败，使用手动修复方式...');
  
  const manualUserInfo = {
    id: 103,
    username: "wx_oXYVEvjxO1wJ_itf",
    nickname: "真实茶友用户",
    avatar: "https://thirdwx.qlogo.cn/mmopen/real_user_avatar.jpg",
    phone: null,
    email: "",
    gender: 1,
    city: "深圳",
    province: "广东",
    country: "中国",
    wechat_info: {
      openid: "oXYVEvjxO1wJ_itf9dKvVveYlqhU",
      nickname: "真实茶友用户",
      avatar_url: "https://thirdwx.qlogo.cn/mmopen/real_user_avatar.jpg",
      gender: 1,
      city: "深圳"
    }
  };
  
  // 更新全局状态
  app.setUserInfo(manualUserInfo);
  wx.setStorageSync('userInfo', manualUserInfo);
  
  // 更新当前页面
  if (currentPage.setData) {
    currentPage.setData({
      userInfo: manualUserInfo
    });
  }
  
  console.log('✅ 手动修复完成');
});

// 4. 额外的调试信息
console.log('\n🔍 调试信息:');
console.log('当前页面路径:', currentPage.route);
console.log('页面数据keys:', Object.keys(currentPage.data));

// 5. 强制刷新页面（如果需要）
setTimeout(() => {
  console.log('\n🔄 5秒后强制刷新页面数据...');
  if (currentPage.loadUserData) {
    currentPage.loadUserData();
  } else if (currentPage.onLoad) {
    currentPage.onLoad();
  }
}, 5000);

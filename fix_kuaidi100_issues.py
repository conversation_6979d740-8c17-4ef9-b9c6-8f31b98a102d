#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快递100 API问题修复脚本
修复茶叶认购项目中的物流查询问题
"""

import os
import sys
import django
import json
import hashlib

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from apps.logistics.models import LogisticsConfig, ExpressCompany


class Kuaidi100Fixer:
    """快递100 API问题修复器"""
    
    def __init__(self):
        self.config = LogisticsConfig.get_config()
    
    def print_header(self, title):
        """打印标题"""
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}")
    
    def setup_api_credentials(self):
        """设置API凭证"""
        self.print_header("设置API凭证")
        
        print("请访问 https://api.kuaidi100.com/register/enterprise 注册企业账号")
        print("获取customer和key后，请在此输入：")
        
        customer = input("请输入customer（授权码）: ").strip()
        key = input("请输入key（密钥）: ").strip()
        
        if customer and key:
            self.config.api_key = customer
            self.config.api_secret = key
            self.config.api_provider = 'kuaidi100'
            self.config.save()
            
            print("✅ API凭证已保存")
            return True
        else:
            print("❌ 凭证不能为空")
            return False
    
    def setup_express_companies(self):
        """设置快递公司"""
        self.print_header("设置快递公司")
        
        # 常用快递公司配置
        companies_data = [
            {"name": "顺丰速运", "code": "shunfeng", "sort_order": 1},
            {"name": "圆通速递", "code": "yuantong", "sort_order": 2},
            {"name": "中通快递", "code": "zhongtong", "sort_order": 3},
            {"name": "韵达速递", "code": "yunda", "sort_order": 4},
            {"name": "申通快递", "code": "shentong", "sort_order": 5},
            {"name": "邮政包裹", "code": "ems", "sort_order": 6},
            {"name": "京东物流", "code": "jd", "sort_order": 7},
            {"name": "德邦快递", "code": "debangkuaidi", "sort_order": 8},
            {"name": "百世快递", "code": "huitongkuaidi", "sort_order": 9},
            {"name": "天天快递", "code": "tiantian", "sort_order": 10},
        ]
        
        created_count = 0
        updated_count = 0
        
        for company_data in companies_data:
            company, created = ExpressCompany.objects.get_or_create(
                code=company_data["code"],
                defaults=company_data
            )
            
            if created:
                created_count += 1
                print(f"✅ 创建快递公司: {company.name}")
            else:
                # 更新现有记录
                company.name = company_data["name"]
                company.sort_order = company_data["sort_order"]
                company.save()
                updated_count += 1
                print(f"🔄 更新快递公司: {company.name}")
        
        print(f"\n📊 处理结果: 创建 {created_count} 个，更新 {updated_count} 个快递公司")
        return True
    
    def fix_logistics_service(self):
        """修复物流服务代码"""
        self.print_header("修复物流服务代码")
        
        # 检查并修复services.py中的问题
        services_file = '/www/wwwroot/teabuy2/apps/logistics/services.py'
        
        try:
            with open(services_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否需要修复
            fixes_needed = []
            
            # 检查签名生成方法
            if '_generate_sign' not in content:
                fixes_needed.append("缺少签名生成方法")
            
            # 检查请求头设置
            if 'application/x-www-form-urlencoded' not in content:
                fixes_needed.append("请求头设置可能不正确")
            
            if fixes_needed:
                print("发现需要修复的问题:")
                for fix in fixes_needed:
                    print(f"  - {fix}")
                
                # 这里可以添加具体的修复代码
                print("💡 建议手动检查和修复 apps/logistics/services.py 文件")
            else:
                print("✅ 物流服务代码看起来正常")
            
            return True
            
        except Exception as e:
            print(f"❌ 检查物流服务代码失败: {e}")
            return False
    
    def test_configuration(self):
        """测试配置"""
        self.print_header("测试配置")
        
        try:
            from apps.logistics.services import Kuaidi100Service
            
            service = Kuaidi100Service()
            customer, key = service.get_api_credentials()
            
            if not customer or not key:
                print("❌ API凭证未配置或为空")
                return False
            
            print(f"✅ Customer: {customer[:8]}...")
            print(f"✅ Key: {key[:8]}...")
            
            # 测试签名生成
            test_param = '{"com":"shunfeng","num":"SF1234567890"}'
            try:
                sign = service._generate_sign(test_param)
                print(f"✅ 签名生成测试通过: {sign[:16]}...")
            except Exception as e:
                print(f"❌ 签名生成测试失败: {e}")
                return False
            
            # 测试API调用
            print("🔍 测试API调用...")
            result = service.query_logistics("SF1234567890", "shunfeng")
            
            if result.get('success') is not None:
                print("✅ API调用测试通过")
                if not result.get('success'):
                    print(f"ℹ️ API返回: {result.get('message', '无消息')}")
            else:
                print("❌ API调用测试失败")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 配置测试失败: {e}")
            return False
    
    def create_test_logistics(self):
        """创建测试物流数据"""
        self.print_header("创建测试物流数据")
        
        try:
            from apps.logistics.models import Logistics
            from apps.orders.models import Order
            
            # 检查是否有订单可以关联
            orders = Order.objects.all()[:5]
            
            if not orders.exists():
                print("⚠️ 没有找到订单数据，无法创建测试物流")
                return False
            
            # 获取快递公司
            express_company = ExpressCompany.objects.filter(code='shunfeng').first()
            if not express_company:
                print("❌ 没有找到顺丰快递公司配置")
                return False
            
            test_count = 0
            for order in orders:
                # 检查是否已有物流信息
                if hasattr(order, 'logistics'):
                    continue
                
                # 创建测试物流
                logistics = Logistics.objects.create(
                    order=order,
                    express_company=express_company,
                    tracking_number=f"SF{order.id:010d}",
                    sender_name="茶园发货",
                    sender_phone="************",
                    sender_address="福建省福州市茶园基地",
                    receiver_name=order.user.username if order.user else "测试用户",
                    receiver_phone="13800138000",
                    receiver_address="测试地址",
                    status='shipped'
                )
                
                test_count += 1
                print(f"✅ 创建测试物流: {logistics.tracking_number}")
                
                if test_count >= 3:  # 只创建3个测试数据
                    break
            
            print(f"📊 创建了 {test_count} 个测试物流数据")
            return True
            
        except Exception as e:
            print(f"❌ 创建测试物流数据失败: {e}")
            return False
    
    def optimize_settings(self):
        """优化设置"""
        self.print_header("优化设置")
        
        try:
            # 优化物流配置
            self.config.auto_sync_enabled = True
            self.config.sync_interval = 30  # 30分钟同步一次
            self.config.notify_on_shipped = True
            self.config.notify_on_delivered = True
            self.config.notify_on_exception = True
            self.config.save()
            
            print("✅ 已优化物流配置:")
            print("   - 启用自动同步")
            print("   - 同步间隔: 30分钟")
            print("   - 启用所有通知")
            
            return True
            
        except Exception as e:
            print(f"❌ 优化设置失败: {e}")
            return False
    
    def run_full_fix(self):
        """运行完整修复"""
        self.print_header("快递100 API 修复工具")
        
        print("此工具将帮助您修复快递100 API的配置问题")
        print("请按照提示进行操作...")
        
        steps = [
            ("设置API凭证", self.setup_api_credentials),
            ("设置快递公司", self.setup_express_companies),
            ("修复物流服务", self.fix_logistics_service),
            ("优化设置", self.optimize_settings),
            ("测试配置", self.test_configuration),
            ("创建测试数据", self.create_test_logistics),
        ]
        
        success_count = 0
        
        for step_name, step_func in steps:
            print(f"\n🔧 执行: {step_name}")
            try:
                if step_func():
                    print(f"✅ {step_name} 完成")
                    success_count += 1
                else:
                    print(f"❌ {step_name} 失败")
            except Exception as e:
                print(f"❌ {step_name} 异常: {e}")
        
        # 显示总结
        self.print_header("修复总结")
        print(f"📊 修复结果: {success_count}/{len(steps)} 步骤成功")
        
        if success_count == len(steps):
            print("🎉 所有修复步骤都完成了！")
            print("💡 建议:")
            print("   1. 重启Django应用")
            print("   2. 在前端测试物流查询功能")
            print("   3. 检查Django后台的物流管理")
        else:
            print("⚠️ 部分修复步骤失败，请检查错误信息并手动修复")
        
        return success_count == len(steps)


def main():
    """主函数"""
    fixer = Kuaidi100Fixer()
    fixer.run_full_fix()


if __name__ == "__main__":
    main()

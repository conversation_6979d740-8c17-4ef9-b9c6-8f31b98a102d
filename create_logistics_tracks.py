#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建物流轨迹测试数据
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from apps.logistics.models import Logistics, LogisticsTrack
from django.utils import timezone


def create_logistics_tracks():
    """创建物流轨迹测试数据"""
    print("🚚 创建物流轨迹测试数据...")
    
    # 获取现有的物流信息
    logistics_list = Logistics.objects.all()
    
    if not logistics_list.exists():
        print("❌ 没有找到物流信息，请先创建物流数据")
        return
    
    created_count = 0
    
    for logistics in logistics_list:
        # 检查是否已有轨迹数据
        if logistics.tracks.exists():
            print(f"ℹ️ 物流 {logistics.tracking_number} 已有轨迹数据，跳过")
            continue
        
        # 创建模拟轨迹数据
        base_time = logistics.shipped_at or timezone.now() - timedelta(days=2)
        
        tracks_data = [
            {
                'location': '广东省潮州市凤凰镇',
                'description': f'【{logistics.express_company.name}】快件已在凤凰镇营业点装车，准备发往下一站',
                'status': 'shipped',
                'timestamp': base_time,
                'operator': '张师傅'
            },
            {
                'location': '广东省潮州市分拨中心',
                'description': f'快件已到达潮州市分拨中心',
                'status': 'in_transit',
                'timestamp': base_time + timedelta(hours=2),
                'operator': '分拨中心'
            },
            {
                'location': '广东省广州市分拨中心',
                'description': f'快件已到达广州市分拨中心',
                'status': 'in_transit',
                'timestamp': base_time + timedelta(hours=6),
                'operator': '分拨中心'
            },
            {
                'location': '广东省广州市分拨中心',
                'description': f'快件已离开广州市分拨中心，发往目的地',
                'status': 'in_transit',
                'timestamp': base_time + timedelta(hours=8),
                'operator': '分拨中心'
            }
        ]
        
        # 根据物流状态添加更多轨迹
        if logistics.status in ['out_for_delivery', 'delivered']:
            tracks_data.extend([
                {
                    'location': '目的地分拨中心',
                    'description': f'快件已到达目的地分拨中心',
                    'status': 'in_transit',
                    'timestamp': base_time + timedelta(days=1),
                    'operator': '分拨中心'
                },
                {
                    'location': '派送网点',
                    'description': f'快件已到达派送网点，准备安排派送',
                    'status': 'out_for_delivery',
                    'timestamp': base_time + timedelta(days=1, hours=2),
                    'operator': '派送员'
                }
            ])
        
        if logistics.status == 'delivered':
            tracks_data.append({
                'location': '收件地址',
                'description': f'快件已签收，签收人：{logistics.receiver_name}',
                'status': 'delivered',
                'timestamp': base_time + timedelta(days=1, hours=4),
                'operator': '派送员'
            })
        
        # 创建轨迹记录
        for track_data in tracks_data:
            LogisticsTrack.objects.create(
                logistics=logistics,
                **track_data
            )
        
        created_count += 1
        print(f"✅ 为物流 {logistics.tracking_number} 创建了 {len(tracks_data)} 条轨迹记录")
    
    print(f"\n📊 处理结果:")
    print(f"   处理物流数量: {logistics_list.count()}")
    print(f"   创建轨迹数据: {created_count}")
    
    # 显示统计信息
    total_tracks = LogisticsTrack.objects.count()
    print(f"   总轨迹记录: {total_tracks}")


def update_logistics_status():
    """更新物流状态以匹配轨迹"""
    print("\n🔄 更新物流状态...")
    
    updated_count = 0
    
    for logistics in Logistics.objects.all():
        # 获取最新轨迹
        latest_track = logistics.tracks.order_by('-timestamp').first()
        
        if latest_track and logistics.status != latest_track.status:
            old_status = logistics.status
            logistics.status = latest_track.status
            
            # 如果是已签收状态，更新签收时间
            if latest_track.status == 'delivered' and not logistics.delivered_at:
                logistics.delivered_at = latest_track.timestamp
            
            logistics.save()
            updated_count += 1
            print(f"✅ 更新物流 {logistics.tracking_number}: {old_status} -> {latest_track.status}")
    
    print(f"📊 更新了 {updated_count} 个物流状态")


def main():
    """主函数"""
    print("🚚 物流轨迹数据创建工具")
    print("=" * 50)
    
    create_logistics_tracks()
    update_logistics_status()
    
    print("\n✅ 物流轨迹数据创建完成！")
    print("💡 现在可以在前端测试物流详情页面了")


if __name__ == "__main__":
    main()

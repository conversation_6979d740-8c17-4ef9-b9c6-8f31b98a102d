#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断用户登录后无法获取数据的问题
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from apps.orders.models import Order
from apps.logistics.models import Logistics
from apps.notifications.models import WechatUserInfo
from apps.users.models import UserProfile

User = get_user_model()


def diagnose_user_data():
    """诊断用户数据问题"""
    print("🔍 诊断用户登录和数据获取问题")
    print("=" * 60)
    
    # 1. 检查用户数据
    print("\n👤 检查用户数据...")
    users = User.objects.all()
    print(f"总用户数: {users.count()}")
    
    if users.count() > 0:
        # 显示前5个用户
        for user in users[:5]:
            print(f"  - ID: {user.id}, 用户名: {user.username}, 昵称: {user.nickname}")
            
            # 检查token
            try:
                token = Token.objects.get(user=user)
                print(f"    Token: {token.key[:16]}...")
            except Token.DoesNotExist:
                print(f"    ❌ 没有Token")
            
            # 检查微信信息
            try:
                wechat_info = WechatUserInfo.objects.get(user=user)
                print(f"    微信: {wechat_info.nickname}, OpenID: {wechat_info.openid[:16]}...")
            except WechatUserInfo.DoesNotExist:
                print(f"    ❌ 没有微信信息")
    
    # 2. 检查订单数据
    print("\n📋 检查订单数据...")
    orders = Order.objects.all()
    print(f"总订单数: {orders.count()}")
    
    if orders.count() > 0:
        # 按用户分组统计
        user_orders = {}
        for order in orders:
            user_id = order.user.id if order.user else None
            if user_id not in user_orders:
                user_orders[user_id] = []
            user_orders[user_id].append(order)
        
        print("用户订单分布:")
        for user_id, user_order_list in user_orders.items():
            if user_id:
                user = User.objects.get(id=user_id)
                print(f"  - 用户 {user.username}: {len(user_order_list)} 个订单")
            else:
                print(f"  - 无用户关联: {len(user_order_list)} 个订单")
    
    # 3. 检查物流数据
    print("\n📦 检查物流数据...")
    logistics = Logistics.objects.all()
    print(f"总物流数: {logistics.count()}")
    
    if logistics.count() > 0:
        # 按用户分组统计
        user_logistics = {}
        for log in logistics:
            user_id = log.order.user.id if log.order and log.order.user else None
            if user_id not in user_logistics:
                user_logistics[user_id] = []
            user_logistics[user_id].append(log)
        
        print("用户物流分布:")
        for user_id, user_log_list in user_logistics.items():
            if user_id:
                user = User.objects.get(id=user_id)
                print(f"  - 用户 {user.username}: {len(user_log_list)} 个物流")
            else:
                print(f"  - 无用户关联: {len(user_log_list)} 个物流")
    
    # 4. 检查用户资料
    print("\n📝 检查用户资料...")
    profiles = UserProfile.objects.all()
    print(f"总用户资料数: {profiles.count()}")
    
    # 5. 检查API权限设置
    print("\n🔐 检查API权限设置...")
    
    # 检查常见的权限问题
    print("权限检查:")
    
    # 检查是否有用户没有token
    users_without_token = []
    for user in users:
        if not Token.objects.filter(user=user).exists():
            users_without_token.append(user)
    
    if users_without_token:
        print(f"  ❌ {len(users_without_token)} 个用户没有Token:")
        for user in users_without_token[:3]:
            print(f"    - {user.username}")
    else:
        print("  ✅ 所有用户都有Token")
    
    # 检查是否有订单没有关联用户
    orders_without_user = Order.objects.filter(user__isnull=True)
    if orders_without_user.exists():
        print(f"  ❌ {orders_without_user.count()} 个订单没有关联用户")
    else:
        print("  ✅ 所有订单都有关联用户")
    
    # 检查是否有物流没有关联订单
    logistics_without_order = Logistics.objects.filter(order__isnull=True)
    if logistics_without_order.exists():
        print(f"  ❌ {logistics_without_order.count()} 个物流没有关联订单")
    else:
        print("  ✅ 所有物流都有关联订单")


def test_api_permissions():
    """测试API权限"""
    print("\n🧪 测试API权限...")
    
    # 获取一个有数据的用户
    user_with_orders = None
    for user in User.objects.all():
        if Order.objects.filter(user=user).exists():
            user_with_orders = user
            break
    
    if not user_with_orders:
        print("❌ 没有找到有订单的用户")
        return
    
    print(f"👤 测试用户: {user_with_orders.username}")
    
    # 检查用户的数据
    user_orders = Order.objects.filter(user=user_with_orders)
    user_logistics = Logistics.objects.filter(order__user=user_with_orders)
    
    print(f"  订单数量: {user_orders.count()}")
    print(f"  物流数量: {user_logistics.count()}")
    
    # 检查token
    try:
        token = Token.objects.get(user=user_with_orders)
        print(f"  Token: {token.key}")
        
        # 模拟API调用
        print("\n🔄 模拟API调用...")
        
        # 这里可以添加实际的API测试
        print("  (需要启动Django服务器才能测试实际API调用)")
        
    except Token.DoesNotExist:
        print("  ❌ 用户没有Token")


def fix_common_issues():
    """修复常见问题"""
    print("\n🔧 修复常见问题...")
    
    fixed_count = 0
    
    # 1. 为没有token的用户创建token
    users_without_token = []
    for user in User.objects.all():
        if not Token.objects.filter(user=user).exists():
            users_without_token.append(user)
    
    if users_without_token:
        print(f"🔄 为 {len(users_without_token)} 个用户创建Token...")
        for user in users_without_token:
            token = Token.objects.create(user=user)
            print(f"  ✅ 为用户 {user.username} 创建Token: {token.key[:16]}...")
            fixed_count += 1
    
    # 2. 为没有用户资料的用户创建资料
    users_without_profile = []
    for user in User.objects.all():
        if not UserProfile.objects.filter(user=user).exists():
            users_without_profile.append(user)
    
    if users_without_profile:
        print(f"🔄 为 {len(users_without_profile)} 个用户创建用户资料...")
        for user in users_without_profile:
            profile = UserProfile.objects.create(user=user)
            print(f"  ✅ 为用户 {user.username} 创建用户资料")
            fixed_count += 1
    
    print(f"\n📊 修复结果: 修复了 {fixed_count} 个问题")


def check_recent_activity():
    """检查最近活动"""
    print("\n📈 检查最近活动...")
    
    from django.utils import timezone
    from datetime import timedelta
    
    # 最近7天的活动
    recent_date = timezone.now() - timedelta(days=7)
    
    recent_orders = Order.objects.filter(created_at__gte=recent_date)
    recent_logistics = Logistics.objects.filter(created_at__gte=recent_date)
    
    print(f"最近7天:")
    print(f"  新订单: {recent_orders.count()}")
    print(f"  新物流: {recent_logistics.count()}")
    
    # 检查最近登录的用户
    recent_tokens = Token.objects.filter(created__gte=recent_date)
    print(f"  新Token: {recent_tokens.count()}")


def main():
    """主函数"""
    diagnose_user_data()
    test_api_permissions()
    fix_common_issues()
    check_recent_activity()
    
    print("\n" + "=" * 60)
    print("🎯 诊断建议:")
    print("1. 检查前端是否正确发送Authorization头")
    print("2. 检查后端API权限设置是否正确")
    print("3. 检查用户数据关联是否正确")
    print("4. 检查网络连接和API地址")
    print("5. 查看浏览器/小程序开发工具的网络请求日志")
    print("\n💡 如果问题仍然存在，请检查:")
    print("   - 前端token是否正确存储和发送")
    print("   - 后端API是否正常运行")
    print("   - 数据库连接是否正常")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
调试OpenID同步问题
检查微信登录流程中OpenID的获取和存储情况
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.notifications.models import WechatUserInfo
from apps.users.wechat_service import WechatMiniProgramService, WechatUserManager
from apps.system.utils import WechatConfigManager

User = get_user_model()


def check_wechat_config():
    """检查微信配置"""
    print("🔧 检查微信配置...")
    
    try:
        app_id = WechatConfigManager.get_app_id()
        app_secret = WechatConfigManager.get_app_secret()
        login_enabled = WechatConfigManager.is_login_enabled()
        auto_register = WechatConfigManager.is_auto_register_enabled()
        
        print(f"   APP ID: {'已配置' if app_id else '❌ 未配置'}")
        print(f"   APP Secret: {'已配置' if app_secret else '❌ 未配置'}")
        print(f"   登录启用: {'✅ 是' if login_enabled else '❌ 否'}")
        print(f"   自动注册: {'✅ 是' if auto_register else '❌ 否'}")
        
        if not app_id or not app_secret:
            print("❌ 微信配置不完整，这可能导致OpenID获取失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查微信配置失败: {e}")
        return False


def test_code2session():
    """测试code2session功能"""
    print(f"\n🧪 测试code2session功能...")
    
    try:
        # 测试一个假的code
        test_code = "test_code_123456"
        result = WechatMiniProgramService.code2session(test_code)
        
        print(f"测试结果:")
        print(f"   success: {result.get('success')}")
        if result.get('success'):
            print(f"   openid: {result.get('openid')}")
            print(f"   session_key: {result.get('session_key')}")
        else:
            print(f"   error: {result.get('error')}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 测试code2session失败: {e}")
        return False


def check_existing_users():
    """检查现有用户的OpenID情况"""
    print(f"\n👥 检查现有用户的OpenID情况...")
    
    try:
        # 统计用户数量
        total_users = User.objects.count()
        wechat_users = WechatUserInfo.objects.count()
        users_with_openid = WechatUserInfo.objects.exclude(openid='').count()
        users_with_nickname = WechatUserInfo.objects.exclude(nickname='').count()
        users_with_avatar = WechatUserInfo.objects.exclude(avatar_url='').count()
        
        print(f"   总用户数: {total_users}")
        print(f"   微信用户记录数: {wechat_users}")
        print(f"   有OpenID的用户: {users_with_openid}")
        print(f"   有昵称的用户: {users_with_nickname}")
        print(f"   有头像的用户: {users_with_avatar}")
        
        # 显示一些示例用户
        print(f"\n📋 示例用户信息:")
        sample_users = WechatUserInfo.objects.all()[:5]
        for wechat_user in sample_users:
            user = wechat_user.user
            print(f"   用户: {user.username}")
            print(f"     OpenID: {wechat_user.openid[:20]}..." if wechat_user.openid else "     OpenID: 空")
            print(f"     昵称: '{wechat_user.nickname}'")
            print(f"     头像: {'有' if wechat_user.avatar_url else '无'}")
            print(f"     最后登录: {wechat_user.last_login_time}")
            print()
        
        return users_with_openid > 0
        
    except Exception as e:
        print(f"❌ 检查现有用户失败: {e}")
        return False


def test_user_creation():
    """测试用户创建流程"""
    print(f"\n🧪 测试用户创建流程...")
    
    try:
        # 模拟一个测试OpenID和用户信息
        test_openid = "test_openid_debug_12345"
        test_user_info = {
            'nickName': '测试用户',
            'avatarUrl': 'https://example.com/avatar.jpg',
            'gender': 1,
            'city': '深圳',
            'province': '广东',
            'country': '中国',
            'language': 'zh_CN'
        }
        
        print(f"测试OpenID: {test_openid}")
        print(f"测试用户信息: {test_user_info}")
        
        # 先清理可能存在的测试数据
        try:
            existing_wechat_user = WechatUserInfo.objects.get(openid=test_openid)
            existing_user = existing_wechat_user.user
            existing_wechat_user.delete()
            existing_user.delete()
            print("清理了已存在的测试数据")
        except WechatUserInfo.DoesNotExist:
            pass
        
        # 测试用户创建
        result = WechatUserManager.create_or_update_user_v2(test_openid, test_user_info)
        
        print(f"创建结果:")
        print(f"   success: {result.get('success')}")
        
        if result.get('success'):
            user = result['user']
            wechat_user = result['wechat_user']
            
            print(f"   用户ID: {user.id}")
            print(f"   用户名: {user.username}")
            print(f"   用户昵称: '{user.nickname}'")
            print(f"   微信OpenID: {wechat_user.openid}")
            print(f"   微信昵称: '{wechat_user.nickname}'")
            print(f"   微信头像: '{wechat_user.avatar_url}'")
            
            # 验证数据是否正确存储
            stored_wechat_user = WechatUserInfo.objects.get(openid=test_openid)
            print(f"   数据库验证:")
            print(f"     OpenID匹配: {'✅' if stored_wechat_user.openid == test_openid else '❌'}")
            print(f"     昵称匹配: {'✅' if stored_wechat_user.nickname == test_user_info['nickName'] else '❌'}")
            print(f"     头像匹配: {'✅' if stored_wechat_user.avatar_url == test_user_info['avatarUrl'] else '❌'}")
            
            # 清理测试数据
            stored_wechat_user.delete()
            user.delete()
            print("已清理测试数据")
            
            return True
        else:
            print(f"   error: {result.get('error')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试用户创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_api_route():
    """检查API路由配置"""
    print(f"\n🌐 检查API路由配置...")
    
    try:
        from django.urls import reverse
        from django.test import Client
        
        # 检查微信登录API路由
        try:
            url = reverse('wechat_login')
            print(f"   微信登录API路由: {url}")
        except:
            print("   ❌ 找不到微信登录API路由")
            return False
        
        # 模拟API请求
        client = Client()
        test_data = {
            'code': 'test_code_123',
            'userInfo': {
                'nickName': '测试用户',
                'avatarUrl': 'https://example.com/avatar.jpg'
            }
        }
        
        print(f"   测试API请求...")
        # 注意：这里不实际发送请求，只是检查路由配置
        
        return True
        
    except Exception as e:
        print(f"❌ 检查API路由失败: {e}")
        return False


if __name__ == '__main__':
    print("=" * 60)
    print("OpenID同步问题调试")
    print("=" * 60)
    
    # 检查微信配置
    config_ok = check_wechat_config()
    
    # 测试code2session
    code2session_ok = test_code2session()
    
    # 检查现有用户
    users_ok = check_existing_users()
    
    # 测试用户创建
    creation_ok = test_user_creation()
    
    # 检查API路由
    api_ok = check_api_route()
    
    print(f"\n" + "=" * 60)
    print(f"调试结果:")
    print(f"   微信配置: {'✅ 正常' if config_ok else '❌ 异常'}")
    print(f"   code2session: {'✅ 正常' if code2session_ok else '❌ 异常'}")
    print(f"   现有用户: {'✅ 有数据' if users_ok else '❌ 无数据'}")
    print(f"   用户创建: {'✅ 正常' if creation_ok else '❌ 异常'}")
    print(f"   API路由: {'✅ 正常' if api_ok else '❌ 异常'}")
    
    if not config_ok:
        print(f"\n💡 建议:")
        print(f"   1. 检查微信小程序的AppID和AppSecret配置")
        print(f"   2. 确保微信登录功能已启用")
        print(f"   3. 检查网络连接是否正常")
    
    if not users_ok:
        print(f"\n💡 建议:")
        print(f"   1. 检查用户是否真正完成了微信登录流程")
        print(f"   2. 查看后端日志确认OpenID获取情况")
        print(f"   3. 确认前端传递的code是否有效")
    
    print("=" * 60)

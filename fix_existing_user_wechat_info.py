#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复现有用户的微信信息
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.notifications.models import WechatUserInfo
from apps.users.models import UserProfile

User = get_user_model()


def fix_user_wechat_info():
    """修复现有用户的微信信息"""
    print("🔧 修复现有用户的微信信息...")
    
    username = 'wx_oXYVEvjxO1wJ_itf'
    
    try:
        user = User.objects.get(username=username)
        print(f"✅ 找到用户: {user.username}")
        
        # 检查是否已有微信信息
        try:
            wechat_user = WechatUserInfo.objects.get(user=user)
            print(f"ℹ️ 用户已有微信信息: {wechat_user.nickname}")
            return user, wechat_user
        except WechatUserInfo.DoesNotExist:
            print(f"❌ 用户没有微信信息，创建中...")
            
            # 创建微信用户信息
            wechat_user = WechatUserInfo.objects.create(
                user=user,
                openid=f'real_openid_{user.username}',  # 使用真实的openid格式
                nickname='测试茶友（已修复）',
                avatar_url='https://thirdwx.qlogo.cn/mmopen/fixed_avatar.jpg',
                gender=1,
                city='深圳',
                province='广东',
                country='中国',
                language='zh_CN'
            )
            
            print(f"✅ 创建微信用户信息成功:")
            print(f"   OpenID: {wechat_user.openid}")
            print(f"   昵称: {wechat_user.nickname}")
            print(f"   头像: {wechat_user.avatar_url}")
            
            # 更新用户基本信息
            user.nickname = wechat_user.nickname
            user.gender = wechat_user.gender
            user.city = wechat_user.city
            user.province = wechat_user.province
            user.country = wechat_user.country
            user.save()
            
            print(f"✅ 更新用户基本信息成功")
            
            # 更新或创建用户资料
            profile, created = UserProfile.objects.get_or_create(user=user)
            profile.avatar = wechat_user.avatar_url
            profile.save()
            
            print(f"✅ 更新用户资料成功 (创建: {created})")
            
            return user, wechat_user
            
    except User.DoesNotExist:
        print(f"❌ 用户 {username} 不存在")
        return None, None


def test_api_response(user, wechat_user):
    """测试API响应"""
    print(f"\n🧪 测试API响应...")
    
    if not user or not wechat_user:
        print("❌ 没有用户数据，跳过测试")
        return
    
    from api.v1.serializers import UserDetailSerializer, WechatUserSerializer
    
    # 测试序列化器
    user_serializer = UserDetailSerializer(user)
    wechat_serializer = WechatUserSerializer(wechat_user)
    
    print(f"👤 用户序列化结果:")
    for key, value in user_serializer.data.items():
        if key == 'wechat_info' and value:
            print(f"   {key}:")
            for wk, wv in value.items():
                print(f"     {wk}: {wv}")
        else:
            print(f"   {key}: {value}")
    
    print(f"\n📱 微信用户序列化结果:")
    for key, value in wechat_serializer.data.items():
        print(f"   {key}: {value}")
    
    # 模拟API响应
    api_response = {
        'code': 200,
        'message': '登录成功',
        'data': {
            'token': 'fixed_token_123456',
            'user': user_serializer.data,
            'wechat_info': wechat_serializer.data,
            'is_new_user': False
        }
    }
    
    print(f"\n🔄 模拟API响应:")
    print(f"   用户昵称: {api_response['data']['user'].get('nickname')}")
    print(f"   用户头像: {api_response['data']['user'].get('avatar')}")
    print(f"   微信昵称: {api_response['data']['wechat_info'].get('nickname')}")
    print(f"   微信头像: {api_response['data']['wechat_info'].get('avatar_url')}")
    
    # 模拟前端合并逻辑
    merged_user_info = {
        **api_response['data']['user'],
        'nickname': api_response['data']['wechat_info'].get('nickname') or api_response['data']['user'].get('nickname'),
        'avatar': api_response['data']['wechat_info'].get('avatar_url') or api_response['data']['user'].get('avatar'),
        'wechat_info': api_response['data']['wechat_info']
    }
    
    print(f"\n🔄 前端合并后的用户信息:")
    print(f"   最终昵称: {merged_user_info.get('nickname')}")
    print(f"   最终头像: {merged_user_info.get('avatar')}")
    
    return merged_user_info


def create_test_token():
    """为用户创建测试token"""
    print(f"\n🔑 创建测试token...")
    
    username = 'wx_oXYVEvjxO1wJ_itf'
    
    try:
        user = User.objects.get(username=username)
        
        from rest_framework.authtoken.models import Token
        
        # 删除旧token
        Token.objects.filter(user=user).delete()
        
        # 创建新token
        token = Token.objects.create(user=user)
        
        print(f"✅ 创建token成功: {token.key}")
        
        return token.key
        
    except User.DoesNotExist:
        print(f"❌ 用户不存在")
        return None


def main():
    """主函数"""
    print("🔧 修复现有用户微信信息")
    print("=" * 50)
    
    # 修复用户微信信息
    user, wechat_user = fix_user_wechat_info()
    
    # 测试API响应
    merged_info = test_api_response(user, wechat_user)
    
    # 创建测试token
    token = create_test_token()
    
    print("\n" + "=" * 50)
    print("✅ 修复完成！")
    
    if merged_info:
        print(f"\n💡 现在前端应该显示:")
        print(f"   昵称: {merged_info.get('nickname', '茶园投资者')}")
        print(f"   头像: {'真实头像' if merged_info.get('avatar') and 'http' in merged_info.get('avatar', '') else '默认头像'}")
        
        if token:
            print(f"   Token: {token}")
    
    print(f"\n🔍 测试建议:")
    print(f"1. 在小程序中重新登录")
    print(f"2. 检查个人中心页面是否显示修复后的信息")
    print(f"3. 在Django admin中查看用户信息")
    
    print(f"\n📱 如果仍然不显示，可能需要:")
    print(f"1. 清除小程序缓存和本地存储")
    print(f"2. 重新进行微信授权流程")
    print(f"3. 检查前端日志是否有错误")


if __name__ == "__main__":
    main()

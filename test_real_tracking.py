#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实快递单号查询
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from apps.logistics.services import Kuaidi100Service


def test_real_tracking():
    """测试真实快递单号"""
    print("🧪 测试真实快递单号查询")
    print("=" * 50)
    
    service = Kuaidi100Service()
    
    # 提示用户输入真实快递单号
    print("请输入真实的快递信息进行测试：")
    print("注意：顺丰快递需要提供收件人手机号")
    print()
    
    tracking_number = input("快递单号: ").strip()
    if not tracking_number:
        print("❌ 快递单号不能为空")
        return
    
    print("\n可选的快递公司：")
    companies = [
        ("shunfeng", "顺丰速运", True),
        ("yuantong", "圆通速递", False),
        ("zhongtong", "中通快递", False),
        ("yunda", "韵达速递", False),
        ("shentong", "申通快递", False),
        ("ems", "邮政EMS", False),
    ]
    
    for i, (code, name, need_phone) in enumerate(companies, 1):
        phone_hint = " (需要手机号)" if need_phone else ""
        print(f"{i}. {name} ({code}){phone_hint}")
    
    try:
        choice = int(input("\n选择快递公司 (1-6): ")) - 1
        if choice < 0 or choice >= len(companies):
            print("❌ 选择无效")
            return
        
        company_code, company_name, need_phone = companies[choice]
        
        phone = None
        if need_phone:
            phone = input("收件人手机号: ").strip()
            if not phone:
                print("❌ 顺丰快递必须提供手机号")
                return
        
        print(f"\n🔍 查询 {company_name} 快递单号: {tracking_number}")
        if phone:
            print(f"📱 手机号: {phone}")
        
        print("请稍候...")
        
        # 调用API
        if phone:
            result = service.query_logistics(tracking_number, company_code, phone)
        else:
            result = service.query_logistics(tracking_number, company_code)
        
        print("\n" + "=" * 50)
        print("📊 查询结果:")
        print("=" * 50)
        
        if result.get('success'):
            print("✅ 查询成功!")
            
            data = result.get('data', {})
            print(f"📦 快递公司: {data.get('com', '未知')}")
            print(f"📋 快递单号: {data.get('nu', '未知')}")
            print(f"📍 当前状态: {data.get('state', '未知')}")
            print(f"✉️ 状态描述: {data.get('message', '无描述')}")
            
            # 显示物流轨迹
            tracks = data.get('data', [])
            if tracks:
                print(f"\n🚚 物流轨迹 (共{len(tracks)}条):")
                print("-" * 40)
                for i, track in enumerate(tracks[:5]):  # 只显示前5条
                    print(f"{i+1}. {track.get('ftime', '未知时间')}")
                    print(f"   {track.get('context', '无描述')}")
                    print()
                
                if len(tracks) > 5:
                    print(f"... 还有 {len(tracks) - 5} 条轨迹记录")
            else:
                print("\n📭 暂无物流轨迹信息")
        
        else:
            print("❌ 查询失败!")
            print(f"错误信息: {result.get('message', '未知错误')}")
            if 'error_code' in result:
                print(f"错误代码: {result['error_code']}")
            
            # 提供解决建议
            error_code = result.get('error_code', '')
            if error_code == '500':
                print("\n💡 可能的原因:")
                print("   1. 快递单号不存在或未发货")
                print("   2. 快递公司选择错误")
                print("   3. 单号格式不正确")
            elif error_code == '408':
                print("\n💡 解决方案:")
                print("   顺丰快递需要提供正确的收件人手机号")
            elif error_code == '400':
                print("\n💡 解决方案:")
                print("   检查API配置或账号余额")
        
        print("\n" + "=" * 50)
        
    except ValueError:
        print("❌ 请输入有效的数字")
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    test_real_tracking()

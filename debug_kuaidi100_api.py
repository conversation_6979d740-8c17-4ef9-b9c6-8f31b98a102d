#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快递100 API调试工具
用于诊断和修复茶叶认购项目中的物流查询问题
"""

import os
import sys
import django
import requests
import json
import hashlib
import time
from datetime import datetime

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from apps.logistics.models import LogisticsConfig, ExpressCompany, Logistics
from apps.logistics.services import Kuaidi100Service


class Kuaidi100Debugger:
    """快递100 API调试器"""
    
    def __init__(self):
        self.base_url = 'https://poll.kuaidi100.com/poll/query.do'
        self.config = LogisticsConfig.get_config()
        self.service = Kuaidi100Service()
        
    def print_header(self, title):
        """打印标题"""
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}")
    
    def print_step(self, step, description):
        """打印步骤"""
        print(f"\n🔍 步骤 {step}: {description}")
        print("-" * 40)
    
    def check_database_config(self):
        """检查数据库配置"""
        self.print_step(1, "检查数据库配置")
        
        try:
            config = LogisticsConfig.get_config()
            print(f"✅ 物流配置ID: {config.id}")
            print(f"📡 API提供商: {config.api_provider}")
            print(f"🔑 API密钥(customer): {'已配置' if config.api_key else '❌ 未配置'}")
            print(f"🔐 API密钥(key): {'已配置' if config.api_secret else '❌ 未配置'}")
            print(f"🔄 自动同步: {'启用' if config.auto_sync_enabled else '禁用'}")
            print(f"⏱️ 同步间隔: {config.sync_interval}分钟")
            
            if not config.api_key or not config.api_secret:
                print("\n❌ 错误: API密钥未配置!")
                print("💡 解决方案:")
                print("   1. 访问 https://api.kuaidi100.com/register/enterprise 注册企业账号")
                print("   2. 获取customer和key")
                print("   3. 在Django后台配置物流设置")
                return False
                
            return True
            
        except Exception as e:
            print(f"❌ 数据库配置检查失败: {e}")
            return False
    
    def check_express_companies(self):
        """检查快递公司配置"""
        self.print_step(2, "检查快递公司配置")
        
        try:
            companies = ExpressCompany.objects.all()
            print(f"📦 已配置快递公司数量: {companies.count()}")
            
            # 显示前10个快递公司
            for company in companies[:10]:
                print(f"   - {company.name} ({company.code})")
            
            if companies.count() > 10:
                print(f"   ... 还有 {companies.count() - 10} 个快递公司")
                
            # 检查常用快递公司
            common_companies = ['shunfeng', 'yuantong', 'zhongtong', 'yunda', 'shentong']
            missing_companies = []
            
            for code in common_companies:
                if not companies.filter(code=code).exists():
                    missing_companies.append(code)
            
            if missing_companies:
                print(f"\n⚠️ 缺少常用快递公司: {', '.join(missing_companies)}")
                print("💡 建议在Django后台添加这些快递公司")
                
            return True
            
        except Exception as e:
            print(f"❌ 快递公司配置检查失败: {e}")
            return False
    
    def test_sign_generation(self):
        """测试签名生成算法"""
        self.print_step(3, "测试签名生成算法")
        
        try:
            # 测试参数
            test_param = {
                "com": "shunfeng",
                "num": "SF1234567890",
                "resultv2": "4"
            }
            
            param_str = json.dumps(test_param, separators=(',', ':'), ensure_ascii=False)
            print(f"📝 测试参数: {param_str}")
            
            # 获取配置
            customer, key = self.service.get_api_credentials()
            if not customer or not key:
                print("❌ 无法获取API凭证")
                return False
            
            print(f"🔑 Customer: {customer[:8]}...")
            print(f"🔐 Key: {key[:8]}...")
            
            # 生成签名
            sign_string = param_str + key + customer
            sign = hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()
            
            print(f"📊 签名字符串: {sign_string[:50]}...")
            print(f"🔏 生成的签名: {sign}")
            
            # 验证签名算法
            service_sign = self.service._generate_sign(param_str)
            print(f"🔍 服务类签名: {service_sign}")
            
            if sign == service_sign:
                print("✅ 签名生成算法正确")
                return True
            else:
                print("❌ 签名生成算法有误")
                return False
                
        except Exception as e:
            print(f"❌ 签名生成测试失败: {e}")
            return False
    
    def test_api_request(self, tracking_number="SF1234567890", company_code="shunfeng"):
        """测试API请求"""
        self.print_step(4, f"测试API请求 - {company_code}:{tracking_number}")
        
        try:
            # 使用服务类进行测试
            result = self.service.query_logistics(tracking_number, company_code)
            
            print(f"📡 API响应:")
            print(f"   成功: {result.get('success', False)}")
            print(f"   消息: {result.get('message', '无消息')}")
            
            if 'error_code' in result:
                print(f"   错误代码: {result['error_code']}")
            
            if 'data' in result:
                print(f"   数据: {json.dumps(result['data'], ensure_ascii=False, indent=2)}")
            
            # 分析错误
            if not result.get('success', False):
                self.analyze_api_error(result)
            
            return result.get('success', False)
            
        except Exception as e:
            print(f"❌ API请求测试失败: {e}")
            return False
    
    def analyze_api_error(self, result):
        """分析API错误"""
        print("\n🔍 错误分析:")
        
        message = result.get('message', '')
        error_code = result.get('error_code', '')
        
        if '找不到对应公司' in message or error_code == '400':
            print("❌ 错误类型: 找不到对应公司")
            print("💡 可能原因:")
            print("   1. 快递公司编码错误（必须小写）")
            print("   2. 账号未充值")
            print("   3. 请求格式不正确")
            
        elif '验证码错误' in message or error_code == '408':
            print("❌ 错误类型: 验证码错误")
            print("💡 可能原因:")
            print("   1. 顺丰快递需要提供收件人手机号")
            print("   2. 手机号格式不正确")
            
        elif '查询无结果' in message or error_code == '500':
            print("⚠️ 错误类型: 查询无结果")
            print("💡 可能原因:")
            print("   1. 快递单号不存在")
            print("   2. 快递公司编码错误")
            print("   3. 快递尚未发货")
            
        elif '验证签名失败' in message or error_code == '503':
            print("❌ 错误类型: 验证签名失败")
            print("💡 可能原因:")
            print("   1. 签名算法错误")
            print("   2. API密钥错误")
            print("   3. 参数顺序错误")
            
        elif 'key已过期' in message or error_code == '601':
            print("❌ 错误类型: key已过期")
            print("💡 解决方案:")
            print("   1. 账号需要充值")
            print("   2. 联系快递100客服")
    
    def test_different_companies(self):
        """测试不同快递公司"""
        self.print_step(5, "测试不同快递公司")
        
        test_cases = [
            ("shunfeng", "SF1234567890", "13800138000"),  # 顺丰需要手机号
            ("yuantong", "YT1234567890", None),
            ("zhongtong", "ZT1234567890", None),
            ("yunda", "YD1234567890", None),
            ("shentong", "ST1234567890", None),
        ]
        
        success_count = 0
        
        for company_code, tracking_number, phone in test_cases:
            print(f"\n📦 测试 {company_code}:")
            
            try:
                if phone:
                    result = self.service.query_logistics(tracking_number, company_code, phone)
                else:
                    result = self.service.query_logistics(tracking_number, company_code)
                
                if result.get('success', False):
                    print(f"   ✅ 成功")
                    success_count += 1
                else:
                    print(f"   ❌ 失败: {result.get('message', '未知错误')}")
                    
            except Exception as e:
                print(f"   ❌ 异常: {e}")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 成功")
        return success_count > 0
    
    def check_network_connectivity(self):
        """检查网络连接"""
        self.print_step(6, "检查网络连接")
        
        try:
            # 测试快递100网站连接
            response = requests.get('https://www.kuaidi100.com', timeout=10)
            print(f"🌐 快递100官网连接: {'✅ 正常' if response.status_code == 200 else '❌ 异常'}")
            
            # 测试API端点连接
            response = requests.post(self.base_url, data={'test': 'connection'}, timeout=10)
            print(f"📡 API端点连接: {'✅ 正常' if response.status_code in [200, 400] else '❌ 异常'}")
            
            return True
            
        except requests.exceptions.Timeout:
            print("❌ 网络连接超时")
            print("💡 建议检查网络设置和防火墙配置")
            return False
        except Exception as e:
            print(f"❌ 网络连接测试失败: {e}")
            return False
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        self.print_header("快递100 API 诊断工具")
        print(f"🕐 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = []
        
        # 执行所有检查
        results.append(("数据库配置", self.check_database_config()))
        results.append(("快递公司配置", self.check_express_companies()))
        results.append(("签名生成算法", self.test_sign_generation()))
        results.append(("网络连接", self.check_network_connectivity()))
        results.append(("API请求测试", self.test_api_request()))
        results.append(("多快递公司测试", self.test_different_companies()))
        
        # 显示总结
        self.print_header("诊断总结")
        
        success_count = 0
        for name, success in results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{name}: {status}")
            if success:
                success_count += 1
        
        print(f"\n📊 总体结果: {success_count}/{len(results)} 项检查通过")
        
        if success_count == len(results):
            print("🎉 所有检查都通过了！API应该可以正常工作。")
        else:
            print("⚠️ 发现问题，请根据上述建议进行修复。")
            
        return success_count == len(results)


def main():
    """主函数"""
    debugger = Kuaidi100Debugger()
    debugger.run_full_diagnosis()


if __name__ == "__main__":
    main()

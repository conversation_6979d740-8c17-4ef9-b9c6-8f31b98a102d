#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复物流与用户的关联关系
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from apps.logistics.models import Logistics
from apps.orders.models import Order
from apps.users.models import User
from django.contrib.auth import get_user_model


def fix_logistics_user_relation():
    """修复物流与用户的关联关系"""
    print("🔧 修复物流与用户的关联关系...")
    
    User = get_user_model()
    
    # 获取admin用户
    admin_user = User.objects.filter(username='admin').first()
    if not admin_user:
        print("❌ 没有找到admin用户")
        return
    
    print(f"👤 找到admin用户: {admin_user.username}")
    
    # 获取所有物流信息
    logistics_list = Logistics.objects.all()
    print(f"📦 找到 {logistics_list.count()} 个物流记录")
    
    fixed_count = 0
    
    for logistics in logistics_list:
        if not logistics.order:
            # 如果物流没有关联订单，创建一个测试订单
            print(f"🔄 为物流 {logistics.tracking_number} 创建测试订单...")
            
            # 获取一个茶地用于测试
            from apps.tea_fields.models import TeaField
            tea_field = TeaField.objects.first()

            if not tea_field:
                print("❌ 没有找到茶地，跳过创建订单")
                continue

            order = Order.objects.create(
                user=admin_user,
                order_id=f"ORD{logistics.id:010d}",
                tea_field=tea_field,
                quantity=1.0,
                unit_price=0.01,
                total_amount=0.01,
                status='paid',
                contact_name='测试用户',
                contact_phone='13800138000'
            )
            
            logistics.order = order
            logistics.save()
            
            print(f"✅ 创建订单 {order.order_id} 并关联到物流 {logistics.tracking_number}")
            fixed_count += 1
            
        elif logistics.order.user != admin_user:
            # 如果订单的用户不是admin，更新为admin
            print(f"🔄 更新订单 {logistics.order.order_id} 的用户为admin...")
            
            logistics.order.user = admin_user
            logistics.order.save()
            
            print(f"✅ 订单 {logistics.order.order_id} 已关联到admin用户")
            fixed_count += 1
        else:
            print(f"ℹ️ 物流 {logistics.tracking_number} 已正确关联到admin用户")
    
    print(f"\n📊 修复结果:")
    print(f"   处理物流数量: {logistics_list.count()}")
    print(f"   修复关联数量: {fixed_count}")
    
    # 验证修复结果
    admin_logistics = Logistics.objects.filter(order__user=admin_user)
    print(f"   admin用户的物流数量: {admin_logistics.count()}")


def create_test_order_if_needed():
    """如果需要，创建测试订单"""
    print("\n🛒 检查测试订单...")
    
    User = get_user_model()
    admin_user = User.objects.filter(username='admin').first()
    
    if not admin_user:
        print("❌ 没有找到admin用户")
        return
    
    # 检查admin用户是否有订单
    admin_orders = Order.objects.filter(user=admin_user)
    print(f"👤 admin用户的订单数量: {admin_orders.count()}")
    
    if admin_orders.count() == 0:
        # 创建一个测试订单
        print("🔄 创建测试订单...")
        
        # 获取一个茶地用于测试
        from apps.tea_fields.models import TeaField
        tea_field = TeaField.objects.first()

        if not tea_field:
            print("❌ 没有找到茶地，无法创建订单")
            return

        order = Order.objects.create(
            user=admin_user,
            order_id="ORD20250730TEST001",
            tea_field=tea_field,
            quantity=1.0,
            unit_price=99.99,
            total_amount=99.99,
            status='paid',
            contact_name='测试用户',
            contact_phone='13800138000'
        )
        
        print(f"✅ 创建测试订单: {order.order_id}")


def main():
    """主函数"""
    print("🔧 物流用户关联修复工具")
    print("=" * 50)
    
    create_test_order_if_needed()
    fix_logistics_user_relation()
    
    print("\n✅ 修复完成！")
    print("💡 现在可以重新测试API了")


if __name__ == "__main__":
    main()

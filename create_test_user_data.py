#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为测试用户创建订单和物流数据
"""

import os
import sys
import django
from decimal import Decimal

# 设置Django环境
sys.path.append('/www/wwwroot/teabuy2')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teabuy.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.orders.models import Order
from apps.logistics.models import Logistics, ExpressCompany, LogisticsTrack
from apps.tea_fields.models import TeaField
from apps.notifications.models import WechatUserInfo
from django.utils import timezone
from datetime import timedelta

User = get_user_model()


def create_wechat_user_info():
    """为用户创建微信信息"""
    print("🔄 创建微信用户信息...")
    
    # 获取一些测试用户
    test_users = User.objects.filter(username__startswith='wx_').exclude(username='admin')[:5]
    
    created_count = 0
    
    for user in test_users:
        # 检查是否已有微信信息
        if WechatUserInfo.objects.filter(user=user).exists():
            continue
        
        # 创建微信信息
        openid = f"test_openid_{user.id}_{user.username[-8:]}"
        
        wechat_info = WechatUserInfo.objects.create(
            user=user,
            openid=openid,
            nickname=user.nickname or f"微信用户{user.id}",
            avatar_url="https://thirdwx.qlogo.cn/mmopen/test.jpg",
            gender=1,
            city="深圳",
            province="广东",
            country="中国",
            session_key="test_session_key",
            last_login_time=timezone.now()
        )
        
        print(f"✅ 为用户 {user.username} 创建微信信息: {openid}")
        created_count += 1
    
    print(f"📊 创建了 {created_count} 个微信用户信息")


def create_test_orders():
    """为测试用户创建订单"""
    print("\n🛒 创建测试订单...")
    
    # 获取茶地
    tea_field = TeaField.objects.first()
    if not tea_field:
        print("❌ 没有找到茶地，无法创建订单")
        return
    
    # 获取有微信信息的用户
    wechat_users = WechatUserInfo.objects.all()[:5]
    
    created_count = 0
    
    for wechat_user in wechat_users:
        user = wechat_user.user
        
        # 检查用户是否已有订单
        if Order.objects.filter(user=user).exists():
            continue
        
        # 创建订单
        order = Order.objects.create(
            user=user,
            tea_field=tea_field,
            quantity=Decimal('1.5'),
            unit_price=Decimal('299.00'),
            total_amount=Decimal('448.50'),
            status='paid',
            contact_name=user.nickname or f"用户{user.id}",
            contact_phone='13800138000',
            contact_address='广东省深圳市南山区测试地址',
            paid_at=timezone.now() - timedelta(days=1)
        )
        
        print(f"✅ 为用户 {user.username} 创建订单: {order.order_id}")
        created_count += 1
    
    print(f"📊 创建了 {created_count} 个测试订单")


def create_test_logistics():
    """为订单创建物流信息"""
    print("\n📦 创建测试物流...")
    
    # 获取快递公司
    express_company = ExpressCompany.objects.filter(code='yuantong').first()
    if not express_company:
        express_company = ExpressCompany.objects.first()
    
    if not express_company:
        print("❌ 没有找到快递公司")
        return
    
    # 获取没有物流的订单
    orders_without_logistics = Order.objects.filter(logistics__isnull=True)[:3]
    
    created_count = 0
    
    for order in orders_without_logistics:
        # 创建物流信息
        logistics = Logistics.objects.create(
            order=order,
            express_company=express_company,
            tracking_number=f"YT{order.id:012d}",
            sender_name="两山茶管家",
            sender_phone="************",
            sender_address="广东省潮州市凤凰镇茶叶基地",
            receiver_name=order.contact_name,
            receiver_phone=order.contact_phone,
            receiver_address=order.contact_address,
            status='in_transit',
            shipped_at=timezone.now() - timedelta(hours=12),
            weight=Decimal('2.5'),
            package_count=1,
            shipping_fee=Decimal('15.00')
        )
        
        # 创建物流轨迹
        tracks_data = [
            {
                'location': '广东省潮州市凤凰镇',
                'description': f'【{express_company.name}】快件已在凤凰镇营业点装车，准备发往下一站',
                'status': 'shipped',
                'timestamp': logistics.shipped_at,
                'operator': '张师傅'
            },
            {
                'location': '广东省潮州市分拨中心',
                'description': f'快件已到达潮州市分拨中心',
                'status': 'in_transit',
                'timestamp': logistics.shipped_at + timedelta(hours=2),
                'operator': '分拨中心'
            },
            {
                'location': '广东省广州市分拨中心',
                'description': f'快件已到达广州市分拨中心',
                'status': 'in_transit',
                'timestamp': logistics.shipped_at + timedelta(hours=6),
                'operator': '分拨中心'
            }
        ]
        
        for track_data in tracks_data:
            LogisticsTrack.objects.create(
                logistics=logistics,
                **track_data
            )
        
        print(f"✅ 为订单 {order.order_id} 创建物流: {logistics.tracking_number}")
        created_count += 1
    
    print(f"📊 创建了 {created_count} 个测试物流")


def check_user_data_distribution():
    """检查用户数据分布"""
    print("\n📊 检查用户数据分布...")
    
    users_with_data = []
    
    for user in User.objects.all()[:10]:
        orders_count = Order.objects.filter(user=user).count()
        logistics_count = Logistics.objects.filter(order__user=user).count()
        has_wechat = WechatUserInfo.objects.filter(user=user).exists()
        
        if orders_count > 0 or logistics_count > 0 or has_wechat:
            users_with_data.append({
                'user': user,
                'orders': orders_count,
                'logistics': logistics_count,
                'wechat': has_wechat
            })
    
    print("有数据的用户:")
    for data in users_with_data:
        user = data['user']
        print(f"  - {user.username}: 订单{data['orders']}个, 物流{data['logistics']}个, 微信{'✅' if data['wechat'] else '❌'}")


def create_realistic_test_user():
    """创建一个真实的测试用户"""
    print("\n👤 创建真实测试用户...")
    
    # 创建用户
    test_user, created = User.objects.get_or_create(
        username='test_real_user',
        defaults={
            'nickname': '测试茶友',
            'phone': '13800138001'
        }
    )
    
    if created:
        print(f"✅ 创建用户: {test_user.username}")
    else:
        print(f"ℹ️ 用户已存在: {test_user.username}")
    
    # 创建微信信息
    wechat_info, created = WechatUserInfo.objects.get_or_create(
        user=test_user,
        defaults={
            'openid': 'test_real_openid_12345',
            'nickname': '测试茶友',
            'avatar_url': 'https://thirdwx.qlogo.cn/mmopen/test_real.jpg',
            'gender': 1,
            'city': '深圳',
            'province': '广东',
            'country': '中国',
            'session_key': 'test_real_session_key',
            'last_login_time': timezone.now()
        }
    )
    
    if created:
        print(f"✅ 创建微信信息: {wechat_info.openid}")
    
    # 获取茶地
    tea_field = TeaField.objects.first()
    if not tea_field:
        print("❌ 没有茶地，跳过创建订单")
        return test_user
    
    # 创建订单
    if not Order.objects.filter(user=test_user).exists():
        order = Order.objects.create(
            user=test_user,
            tea_field=tea_field,
            quantity=Decimal('2.0'),
            unit_price=Decimal('399.00'),
            total_amount=Decimal('798.00'),
            status='paid',
            contact_name='测试茶友',
            contact_phone='13800138001',
            contact_address='广东省深圳市福田区测试大厦1001室',
            paid_at=timezone.now() - timedelta(hours=6)
        )
        print(f"✅ 创建订单: {order.order_id}")
        
        # 创建物流
        express_company = ExpressCompany.objects.first()
        if express_company:
            logistics = Logistics.objects.create(
                order=order,
                express_company=express_company,
                tracking_number=f"TEST{order.id:010d}",
                sender_name="两山茶管家",
                sender_phone="************",
                sender_address="广东省潮州市凤凰镇茶叶基地",
                receiver_name=order.contact_name,
                receiver_phone=order.contact_phone,
                receiver_address=order.contact_address,
                status='shipped',
                shipped_at=timezone.now() - timedelta(hours=3),
                weight=Decimal('3.0'),
                package_count=1,
                shipping_fee=Decimal('20.00')
            )
            print(f"✅ 创建物流: {logistics.tracking_number}")
    
    return test_user


def main():
    """主函数"""
    print("🔧 创建测试用户数据")
    print("=" * 50)
    
    create_wechat_user_info()
    create_test_orders()
    create_test_logistics()
    create_realistic_test_user()
    check_user_data_distribution()
    
    print("\n" + "=" * 50)
    print("✅ 测试数据创建完成！")
    print("\n💡 现在可以使用以下测试用户登录:")
    print("   用户名: test_real_user")
    print("   OpenID: test_real_openid_12345")
    print("\n🔍 建议检查:")
    print("1. 前端登录流程是否正确")
    print("2. Token是否正确传递")
    print("3. API权限是否正确设置")


if __name__ == "__main__":
    main()

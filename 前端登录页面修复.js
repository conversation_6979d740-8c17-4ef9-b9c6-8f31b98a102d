// 前端登录页面修复 - 适配2025年微信小程序用户授权新政策
// 这个文件包含了需要添加到 xcx1/pages/login/login.js 中的新方法

// 在 data 中添加的新字段
const newDataFields = {
  userAvatar: '',
  userNickname: '',
  defaultAvatar: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
  canLogin: false,
  showUserInfoForm: false
}

// 新增的方法
const newMethods = {
  
  // 显示用户信息填写表单
  showUserInfoForm() {
    this.setData({
      showUserInfoForm: true
    })
  },

  // 隐藏用户信息填写表单
  hideUserInfoForm() {
    this.setData({
      showUserInfoForm: false,
      userAvatar: '',
      userNickname: '',
      canLogin: false
    })
  },

  // 选择头像 - 新版API
  onChooseAvatar(e) {
    console.log('🖼️ 用户选择头像:', e.detail)
    const { avatarUrl } = e.detail
    
    if (avatarUrl) {
      this.setData({
        userAvatar: avatarUrl
      })
      
      console.log('✅ 头像已设置:', avatarUrl)
      this.checkCanLogin()
    }
  },

  // 输入昵称
  onNicknameInput(e) {
    const nickname = e.detail.value
    this.setData({
      userNickname: nickname
    })
    
    this.checkCanLogin()
  },

  // 昵称失焦检查
  onNicknameBlur(e) {
    // 微信会自动进行内容安全检测
    // 如果不通过，会自动清空内容
    setTimeout(() => {
      this.checkCanLogin()
    }, 100)
  },

  // 检查是否可以登录
  checkCanLogin() {
    const canLogin = this.data.userAvatar && 
                    this.data.userNickname && 
                    this.data.userNickname.trim().length > 0
    
    this.setData({ canLogin })
    
    console.log('🔍 登录检查:', {
      hasAvatar: !!this.data.userAvatar,
      hasNickname: !!this.data.userNickname,
      canLogin: canLogin
    })
  },

  // 新版微信登录流程
  async performNewWechatLogin() {
    console.log('🚀 开始新版微信登录流程...')
    
    // 首先显示用户信息填写表单
    this.showUserInfoForm()
  },

  // 确认登录 - 在用户填写完头像昵称后
  async confirmLogin() {
    if (!this.data.canLogin) {
      wx.showToast({
        title: '请先选择头像和输入昵称',
        icon: 'none'
      })
      return
    }

    if (!this.data.agreedToTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    wx.showLoading({ title: '登录中...' })

    try {
      // 1. 获取登录凭证
      const loginRes = await this.wxLogin()
      console.log('📱 获取到登录凭证:', loginRes.code)
      
      // 2. 构造用户信息（新版格式）
      const userInfo = {
        nickName: this.data.userNickname,
        avatarUrl: this.data.userAvatar,
        gender: 0, // 新版API无法获取性别
        city: '', // 新版API无法获取城市
        province: '', // 新版API无法获取省份
        country: '', // 新版API无法获取国家
        language: 'zh_CN'
      }

      console.log('👤 构造的用户信息:', userInfo)

      // 3. 调用后端登录API
      const result = await api.userApi.wechatLogin({
        code: loginRes.code,
        userInfo: userInfo
      })

      console.log('📥 后端登录响应:', result)

      if (result.code === 200) {
        // 登录成功
        const app = getApp()
        app.login(result.data.user, result.data.token)

        // 隐藏表单
        this.hideUserInfoForm()

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 跳转到主页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          })
        }, 1000)
      } else {
        throw new Error(result.message || '登录失败')
      }

    } catch (error) {
      console.error('❌ 微信登录失败:', error)
      wx.showToast({
        title: error.message || '登录失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 微信登录Promise封装
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  }
}

// 修改现有的微信登录方法
const modifiedWechatLogin = `
  // 修改原有的微信登录方法
  async wechatLogin() {
    console.log('🔄 微信登录方法被调用，使用新版授权流程')
    
    // 检查是否同意协议
    if (!this.data.agreedToTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    // 启动新版登录流程
    await this.performNewWechatLogin()
  }
`

// 需要添加到WXML中的模板代码
const wxmlTemplate = `
<!-- 用户信息填写弹窗 -->
<view class="user-info-modal" wx:if="{{showUserInfoForm}}">
  <view class="modal-mask" bind:tap="hideUserInfoForm"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">完善个人信息</text>
      <text class="modal-close" bind:tap="hideUserInfoForm">×</text>
    </view>
    
    <view class="modal-body">
      <!-- 头像选择 -->
      <view class="avatar-section">
        <text class="section-title">选择头像</text>
        <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
          <image class="avatar" src="{{userAvatar || defaultAvatar}}" mode="aspectFill"></image>
          <view class="avatar-tip">点击选择头像</view>
        </button>
      </view>

      <!-- 昵称输入 -->
      <view class="nickname-section">
        <text class="section-title">输入昵称</text>
        <input 
          type="nickname" 
          class="nickname-input" 
          placeholder="请输入昵称"
          value="{{userNickname}}"
          bind:input="onNicknameInput"
          bind:blur="onNicknameBlur"
        />
      </view>
    </view>
    
    <view class="modal-footer">
      <button 
        class="confirm-btn" 
        bind:tap="confirmLogin"
        disabled="{{!canLogin}}"
      >
        确认登录
      </button>
    </view>
  </view>
</view>
`

// 需要添加到WXSS中的样式代码
const wxssStyles = `
/* 用户信息填写弹窗样式 */
.user-info-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 400px;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 24px;
  color: #999;
  cursor: pointer;
}

.modal-body {
  padding: 20px;
}

.avatar-section, .nickname-section {
  margin-bottom: 20px;
}

.section-title {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.avatar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  background-color: #fafafa;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-bottom: 10px;
}

.avatar-tip {
  font-size: 12px;
  color: #999;
}

.nickname-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #eee;
}

.confirm-btn {
  width: 100%;
  padding: 12px;
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
}

.confirm-btn[disabled] {
  background-color: #ccc;
}
`

console.log('📋 前端登录页面修复代码已生成')
console.log('请按照以下步骤进行修复：')
console.log('1. 将 newDataFields 添加到 login.js 的 data 中')
console.log('2. 将 newMethods 中的方法添加到 login.js 中')
console.log('3. 替换原有的 wechatLogin 方法')
console.log('4. 将 wxmlTemplate 添加到 login.wxml 中')
console.log('5. 将 wxssStyles 添加到 login.wxss 中')

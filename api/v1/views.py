from rest_framework import generics, status, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
# Token导入移到使用的地方以避免冲突
# 使用自定义过滤器替代django-filter避免兼容性问题
# from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth import get_user_model
from django.db.models import Q, Count, Sum, Avg
from django.db import models
from django.utils import timezone
from datetime import datetime
import logging
import json

logger = logging.getLogger(__name__)

from apps.users.models import (
    WechatUser, UserProfile, SmsCode, VerificationRecord, BankCardVerification
)
from apps.notifications.models import Notification, NotificationSettings
from apps.tea_fields.models import TeaField, TeaFieldCategory, TeaFieldPlot
from apps.orders.models import Order, Earnings, Payment, Contract, Invoice, WithdrawRequest
from apps.monitoring.models import MonitoringData, AlertRecord
from apps.analytics.models import UserAnalytics
from apps.system.models import Banner, IconCategory, Icon

from .serializers import (
    UserDetailSerializer, WechatUserSerializer, WechatLoginSerializer, TeaFieldListSerializer,
    TeaFieldDetailSerializer, TeaFieldCategorySerializer, OrderCreateSerializer,
    OrderSerializer, PaymentCreateSerializer, PaymentSerializer,
    MonitoringDataSerializer, EarningsSerializer, AlertRecordSerializer,
    SmsCodeSerializer, PhoneLoginSerializer, NotificationSettingsSerializer,
    UserNotificationSerializer, VerificationRecordSerializer, BankCardVerificationSerializer,
    VerificationStatusSerializer, ContractSerializer,
    InvoiceSerializer, InvoiceCreateSerializer, WithdrawRequestSerializer,
    WithdrawRequestCreateSerializer, BannerSerializer, BannerListSerializer,
    IconCategorySerializer, IconSerializer, IconListSerializer, TeaFieldPlotSerializer
)

User = get_user_model()


# 系统配置API
@api_view(['GET'])
@permission_classes([AllowAny])
def system_config(request):
    """
    获取系统配置信息
    用于API连接测试和基础配置获取
    """
    from apps.system.utils import WechatConfigManager

    config = {
        'status': 'ok',
        'message': 'API连接正常',
        'version': '1.0.0',
        'server_time': timezone.now().isoformat(),
        'features': {
            'user_auth': True,
            'tea_fields': True,
            'orders': True,
            'monitoring': True,
            'analytics': True,
            'payments': True,
            'wechat_login': WechatConfigManager.is_login_enabled()
        },
        'wechat': {
            'app_id': WechatConfigManager.get_app_id(),
            'login_enabled': WechatConfigManager.is_login_enabled(),
            'auto_register': WechatConfigManager.is_auto_register_enabled(),
            'configured': bool(WechatConfigManager.get_app_id() and WechatConfigManager.get_app_secret())
        }
    }
    return Response(config)


# 轮播图API - 已移除硬编码版本，使用下方数据库版本


# 自定义过滤器后端
class CustomFilterBackend:
    """自定义过滤器后端，支持基本的字段过滤"""

    def filter_queryset(self, request, queryset, view):
        """根据查询参数过滤queryset"""
        # 获取过滤字段配置
        filterset_fields = getattr(view, 'filterset_fields', [])

        for field in filterset_fields:
            value = request.query_params.get(field)
            if value:
                # 构建过滤条件
                filter_kwargs = {field: value}
                queryset = queryset.filter(**filter_kwargs)

        return queryset


# 用户认证相关视图
@api_view(['POST'])
@permission_classes([AllowAny])
def wechat_login(request):
    """微信登录"""
    from apps.users.wechat_service import WechatMiniProgramService, WechatUserManager
    from apps.system.utils import WechatConfigManager

    # 检查微信登录是否启用
    if not WechatConfigManager.is_login_enabled():
        return Response({
            'code': 400,
            'message': '微信登录功能已关闭'
        }, status=status.HTTP_400_BAD_REQUEST)

    # 验证微信配置
    config_errors = WechatConfigManager.validate_config()
    if config_errors:
        return Response({
            'code': 500,
            'message': f'微信配置错误: {"; ".join(config_errors)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    code = request.data.get('code')
    encrypted_data = request.data.get('encryptedData')
    iv = request.data.get('iv')
    user_info = request.data.get('userInfo')  # 兼容旧版本

    if not code:
        return Response({
            'code': 400,
            'message': '缺少授权码'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # 开发环境测试模式
        from django.conf import settings
        if settings.DEBUG and code.startswith('test_'):
            # 测试模式，生成一个测试openid
            import hashlib
            openid = 'test_' + hashlib.md5(code.encode()).hexdigest()[:20]
            session_key = 'test_session_key'
            logger.info(f'开发模式测试登录，使用测试openid: {openid}')
        else:
            # 通过code获取openid和session_key
            session_result = WechatMiniProgramService.code2session(code)
            if not session_result['success']:
                return Response({
                    'code': 400,
                    'message': session_result['error']
                }, status=status.HTTP_400_BAD_REQUEST)

            openid = session_result['openid']
            session_key = session_result.get('session_key', '')

        # 解密用户信息（如果提供了加密数据）
        decrypted_user_info = None
        if encrypted_data and iv and session_key:
            decrypt_result = WechatMiniProgramService.decrypt_user_info(
                encrypted_data, iv, session_key
            )
            if decrypt_result['success']:
                decrypted_user_info = decrypt_result['user_info']

        # 使用解密的用户信息，如果没有则使用传入的用户信息
        final_user_info = decrypted_user_info or user_info

        # 创建或更新用户（使用新版方法适配2025年授权）
        user_result = WechatUserManager.create_or_update_user_v2(openid, final_user_info)
        if not user_result['success']:
            return Response({
                'code': 400,
                'message': user_result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

        user = user_result['user']
        wechat_user = user_result['wechat_user']
        is_new_user = user_result['is_new_user']

        # 生成token
        from rest_framework.authtoken.models import Token as AuthToken
        token, token_created = AuthToken.objects.get_or_create(user=user)

        # 记录用户行为
        try:
            UserAnalytics.objects.create(
                user=user,
                action_type='login',
                device_type=request.META.get('HTTP_USER_AGENT', '')[:20],
                ip_address=request.META.get('REMOTE_ADDR')
            )
        except Exception as e:
            logger.warning(f"用户行为记录失败: {str(e)}")
            # 记录失败不影响主要业务流程

        return Response({
            'code': 200,
            'message': '登录成功',
            'data': {
                'token': token.key,
                'user': UserDetailSerializer(user).data,
                'wechat_info': WechatUserSerializer(wechat_user).data,
                'is_new_user': is_new_user
            }
        })

    except Exception as e:
        logger.error(f'微信登录异常: {e}', exc_info=True)
        return Response({
            'code': 500,
            'message': '登录失败，请重试'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 旧的user_profile函数已被下面的完整版本替代


@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_profile(request):
    """更新用户信息"""
    serializer = UserDetailSerializer(request.user, data=request.data, partial=True)
    if serializer.is_valid():
        serializer.save()
        return Response({
            'code': 200,
            'message': '更新成功',
            'data': serializer.data
        })
    
    return Response({
        'code': 400,
        'message': '参数错误',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


# 短信验证码相关API
@api_view(['POST'])
@permission_classes([AllowAny])
def send_sms_code(request):
    """发送短信验证码"""
    serializer = SmsCodeSerializer(data=request.data)
    if serializer.is_valid():
        phone = serializer.validated_data['phone']
        purpose = serializer.validated_data['purpose']

        # 检查发送频率限制（1分钟内只能发送一次）
        one_minute_ago = timezone.now() - timezone.timedelta(minutes=1)
        recent_code = SmsCode.objects.filter(
            phone=phone,
            created_at__gte=one_minute_ago
        ).first()

        if recent_code:
            return Response({
                'code': 400,
                'message': '发送过于频繁，请稍后再试'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 生成6位随机验证码
        import random
        code = ''.join([str(random.randint(0, 9)) for _ in range(6)])

        # 设置过期时间（5分钟）
        expires_at = timezone.now() + timezone.timedelta(minutes=5)

        # 保存验证码
        sms_code = SmsCode.objects.create(
            phone=phone,
            code=code,
            purpose=purpose,
            expires_at=expires_at
        )

        # 这里应该调用短信服务发送验证码
        # 为了演示，我们只是记录日志
        print(f"发送短信验证码: {phone} - {code}")

        return Response({
            'code': 200,
            'message': '验证码发送成功',
            'data': {
                'expires_in': 300  # 5分钟
            }
        })

    return Response({
        'code': 400,
        'message': '参数错误',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def phone_login(request):
    """手机验证码登录"""
    serializer = PhoneLoginSerializer(data=request.data)
    if serializer.is_valid():
        phone = serializer.validated_data['phone']
        code = serializer.validated_data['code']

        # 验证验证码
        sms_code = SmsCode.objects.filter(
            phone=phone,
            code=code,
            purpose='login',
            is_used=False
        ).first()

        if not sms_code:
            return Response({
                'code': 400,
                'message': '验证码错误或已失效'
            }, status=status.HTTP_400_BAD_REQUEST)

        if sms_code.is_expired():
            return Response({
                'code': 400,
                'message': '验证码已过期'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 标记验证码为已使用
        sms_code.is_used = True
        sms_code.save()

        # 查找或创建用户
        user, created = User.objects.get_or_create(
            phone=phone,
            defaults={
                'username': phone,
                'nickname': f'用户{phone[-4:]}'
            }
        )

        # 生成或获取token
        from rest_framework.authtoken.models import Token as AuthToken
        token, created = AuthToken.objects.get_or_create(user=user)

        # 记录用户行为
        UserAnalytics.objects.create(
            user=user,
            action_type='login',
            device_type=request.META.get('HTTP_USER_AGENT', '')[:20],
            ip_address=request.META.get('REMOTE_ADDR')
        )

        return Response({
            'code': 200,
            'message': '登录成功',
            'data': {
                'token': token.key,
                'user': UserDetailSerializer(user).data
            }
        })

    return Response({
        'code': 400,
        'message': '参数错误',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


# 通知设置相关API
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_notification_settings(request):
    """获取用户通知设置"""
    settings, created = NotificationSettings.objects.get_or_create(
        user=request.user
    )
    serializer = NotificationSettingsSerializer(settings)
    return Response({
        'code': 200,
        'message': '获取成功',
        'data': serializer.data
    })


@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_notification_settings(request):
    """更新用户通知设置"""
    settings, created = NotificationSettings.objects.get_or_create(
        user=request.user
    )
    serializer = NotificationSettingsSerializer(settings, data=request.data, partial=True)
    if serializer.is_valid():
        serializer.save()
        return Response({
            'code': 200,
            'message': '更新成功',
            'data': serializer.data
        })

    return Response({
        'code': 400,
        'message': '参数错误',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


# 用户订阅偏好相关API
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_subscription_preferences(request):
    """获取用户订阅偏好"""
    from apps.notifications.models import UserSubscriptionPreference
    from .serializers import UserSubscriptionPreferenceSerializer

    print(f"📱 用户 {request.user} 请求订阅偏好")

    # 获取用户的所有订阅偏好
    preferences = UserSubscriptionPreference.objects.filter(user=request.user)

    # 如果没有偏好设置，创建默认设置
    if not preferences.exists():
        default_categories = [
            ('essential', True),
            ('service', True),
            ('earnings', True),
            ('marketing', False)
        ]

        for category, enabled in default_categories:
            UserSubscriptionPreference.objects.create(
                user=request.user,
                message_category=category,
                is_enabled=enabled
            )

        preferences = UserSubscriptionPreference.objects.filter(user=request.user)

    serializer = UserSubscriptionPreferenceSerializer(preferences, many=True)
    return Response({
        'code': 200,
        'message': '获取成功',
        'data': serializer.data
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_subscription_preferences(request):
    """更新用户订阅偏好"""
    from apps.notifications.models import UserSubscriptionPreference
    from .serializers import UserSubscriptionPreferenceSerializer

    preferences_data = request.data.get('preferences', [])

    if not preferences_data:
        return Response({
            'code': 400,
            'message': '缺少偏好设置数据'
        }, status=status.HTTP_400_BAD_REQUEST)

    updated_preferences = []

    for pref_data in preferences_data:
        category = pref_data.get('message_category')
        if not category:
            continue

        preference, created = UserSubscriptionPreference.objects.get_or_create(
            user=request.user,
            message_category=category,
            defaults={
                'is_enabled': pref_data.get('is_enabled', True),
                'frequency': pref_data.get('frequency', 'immediate'),
                'priority_threshold': pref_data.get('priority_threshold', 'medium')
            }
        )

        if not created:
            # 更新现有偏好
            preference.is_enabled = pref_data.get('is_enabled', preference.is_enabled)
            preference.frequency = pref_data.get('frequency', preference.frequency)
            preference.priority_threshold = pref_data.get('priority_threshold', preference.priority_threshold)

            if 'quiet_hours_start' in pref_data:
                preference.quiet_hours_start = pref_data['quiet_hours_start']
            if 'quiet_hours_end' in pref_data:
                preference.quiet_hours_end = pref_data['quiet_hours_end']

            preference.save()

        updated_preferences.append(preference)

    serializer = UserSubscriptionPreferenceSerializer(updated_preferences, many=True)
    return Response({
        'code': 200,
        'message': '更新成功',
        'data': serializer.data
    })


# 认证测试API
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def auth_test(request):
    """测试认证状态"""
    return Response({
        'code': 200,
        'message': '认证成功',
        'data': {
            'user_id': request.user.id,
            'username': request.user.username,
            'is_authenticated': request.user.is_authenticated
        }
    })


# 测试发送订阅消息API
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def test_send_subscribe_message(request):
    """测试发送订阅消息"""
    try:
        from apps.notifications.wechat_subscribe_service import WechatSubscribeMessageService
        from apps.users.models import WechatUser

        # 获取用户的微信信息
        try:
            wechat_user = WechatUser.objects.get(user=request.user)
            openid = wechat_user.openid
        except WechatUser.DoesNotExist:
            return Response({
                'code': 400,
                'message': '用户未绑定微信',
                'data': None
            })

        # 获取请求参数
        template_id = request.data.get('template_id', 'CYs-sFSTSx3BwwTMv-7kG00PEW1asErffcLGtG2vWMM')  # 默认使用一个测试模板

        # 构造测试消息数据
        test_data = {
            'thing1': {'value': '测试订阅消息'},
            'thing2': {'value': '茶叶认购小程序'},
            'time3': {'value': '2025-01-16 15:30'},
            'thing4': {'value': '功能测试'}
        }

        # 发送订阅消息
        result = WechatSubscribeMessageService.send_subscribe_message(
            openid=openid,
            template_id=template_id,
            data=test_data,
            page='pages/test-subscription/test-subscription'
        )

        if result.get('success'):
            return Response({
                'code': 200,
                'message': '订阅消息发送成功',
                'data': {
                    'msgid': result.get('msgid'),
                    'openid': openid,
                    'template_id': template_id
                }
            })
        else:
            return Response({
                'code': 400,
                'message': f'订阅消息发送失败: {result.get("error")}',
                'data': {
                    'error_code': result.get('errcode'),
                    'openid': openid,
                    'template_id': template_id
                }
            })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'发送订阅消息异常: {str(e)}',
            'data': None
        })


# 茶地相关视图
class TeaFieldCategoryListView(generics.ListAPIView):
    """茶地分类列表"""
    queryset = TeaFieldCategory.objects.filter(is_active=True)
    serializer_class = TeaFieldCategorySerializer
    permission_classes = [AllowAny]


@api_view(['GET'])
@permission_classes([AllowAny])
def tea_field_filter_options(request):
    """获取茶地筛选选项"""
    try:
        from django.db.models import Min, Max, Count
        from apps.tea_fields.models import TeaField

        # 获取所有活跃的茶地
        tea_fields = TeaField.objects.filter(is_active=True)

        # 1. 地区选项 - 从茶地的location字段提取
        regions = tea_fields.values_list('location', flat=True).distinct()
        region_options = [{'label': '全部地区', 'value': ''}]

        # 提取省份信息
        provinces = set()
        for location in regions:
            if location:
                # 提取省份（假设格式为：广东省潮州市...）
                if '省' in location:
                    province = location.split('省')[0] + '省'
                    provinces.add(province)
                elif '市' in location:
                    city = location.split('市')[0] + '市'
                    provinces.add(city)

        for province in sorted(provinces):
            region_options.append({
                'label': province.replace('省', '').replace('市', ''),
                'value': province
            })

        # 2. 价格范围选项 - 基于实际价格数据
        price_stats = tea_fields.aggregate(
            min_price=Min('price'),
            max_price=Max('price')
        )

        min_price = float(price_stats['min_price'] or 0)
        max_price = float(price_stats['max_price'] or 10000)

        price_options = [{'label': '全部价格', 'value': ''}]

        # 动态生成价格区间
        if max_price > 0:
            ranges = [
                (0, 1000, '1000以下'),
                (1000, 3000, '1000-3000'),
                (3000, 5000, '3000-5000'),
                (5000, 8000, '5000-8000'),
                (8000, float('inf'), '8000以上')
            ]

            for min_val, max_val, label in ranges:
                if min_val <= max_price:
                    if max_val == float('inf'):
                        value = f"{min_val}-"
                    else:
                        value = f"{min_val}-{max_val}"
                    price_options.append({'label': label, 'value': value})

        # 3. 收益范围选项 - 基于实际收益数据
        return_stats = tea_fields.aggregate(
            min_return=Min('expected_return'),
            max_return=Max('expected_return')
        )

        return_options = [{'label': '全部收益', 'value': ''}]

        # 固定收益区间（百分比）
        return_ranges = [
            (0, 5, '5%以下'),
            (5, 10, '5%-10%'),
            (10, 15, '10%-15%'),
            (15, 20, '15%-20%'),
            (20, 100, '20%以上')
        ]

        for min_val, max_val, label in return_ranges:
            if max_val == 100:
                value = f"{min_val}-"
            else:
                value = f"{min_val}-{max_val}"
            return_options.append({'label': label, 'value': value})

        # 4. 面积范围选项 - 基于实际面积数据
        area_stats = tea_fields.aggregate(
            min_area=Min('area'),
            max_area=Max('area')
        )

        area_options = [{'label': '全部面积', 'value': ''}]

        # 动态生成面积区间
        area_ranges = [
            (0, 1, '1亩以下'),
            (1, 5, '1-5亩'),
            (5, 10, '5-10亩'),
            (10, 20, '10-20亩'),
            (20, float('inf'), '20亩以上')
        ]

        for min_val, max_val, label in area_ranges:
            if max_val == float('inf'):
                value = f"{min_val}-"
            else:
                value = f"{min_val}-{max_val}"
            area_options.append({'label': label, 'value': value})

        # 5. 茶叶品种选项 - 从分类中获取
        categories = TeaFieldCategory.objects.filter(is_active=True)
        category_options = [{'label': '全部', 'value': 'all'}]

        for category in categories:
            category_options.append({
                'label': category.name,
                'value': category.name.lower().replace(' ', '_')
            })

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'regions': region_options,
                'price_ranges': price_options,
                'return_ranges': return_options,
                'area_ranges': area_options,
                'categories': category_options,
                'stats': {
                    'total_fields': tea_fields.count(),
                    'price_range': f"{min_price:.0f}-{max_price:.0f}",
                    'min_return': return_stats['min_return'],
                    'max_return': return_stats['max_return'],
                    'min_area': area_stats['min_area'],
                    'max_area': area_stats['max_area']
                }
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取筛选选项失败: {str(e)}',
            'data': {
                'regions': [],
                'price_ranges': [],
                'return_ranges': [],
                'area_ranges': [],
                'categories': []
            }
        })

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })


class TeaFieldListView(generics.ListAPIView):
    """茶地列表 - 使用三级导航茶地块数据"""
    queryset = TeaFieldPlot.objects.all()
    serializer_class = TeaFieldPlotSerializer
    permission_classes = [AllowAny]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['plot_name', 'plot_number', 'region__name', 'region__garden__garden_name']
    ordering_fields = ['price', 'area', 'created_at']
    ordering = ['-created_at']



    def filter_queryset(self, queryset):
        """重写filter_queryset方法，添加三级联动筛选"""
        # 先应用默认的过滤器
        queryset = super().filter_queryset(queryset)

        # 获取三级联动筛选参数
        garden_id = self.request.GET.get('garden_id')
        region_id = self.request.GET.get('region_id')
        plot_id = self.request.GET.get('plot_id')

        # 根据茶园筛选
        if garden_id:
            try:
                from apps.tea_fields.models import TeaGarden
                garden = TeaGarden.objects.get(id=garden_id)
                queryset = queryset.filter(
                    models.Q(description__icontains=garden.garden_code) |
                    models.Q(detailed_location__icontains=garden.garden_name)
                )
            except Exception:
                pass

        # 根据茶区域筛选
        if region_id:
            try:
                from apps.tea_fields.models import TeaFieldRegion
                region = TeaFieldRegion.objects.get(id=region_id)
                garden = region.garden
                region_code = f"{garden.garden_code}-{region.zone_code}"
                queryset = queryset.filter(
                    models.Q(description__icontains=region_code) |
                    models.Q(detailed_location__icontains=region.name)
                )
            except Exception:
                pass

        # 根据茶地块筛选
        if plot_id:
            try:
                from apps.tea_fields.models import TeaFieldPlot
                plot = TeaFieldPlot.objects.get(id=plot_id)
                region = plot.region
                garden = region.garden
                plot_code = f"{garden.garden_code}-{region.zone_code}-{plot.plot_number}"
                queryset = queryset.filter(
                    models.Q(description__icontains=plot_code) |
                    models.Q(name__icontains=plot.plot_name)
                )
            except Exception:
                pass

        return queryset

    def get_queryset(self):
        """自定义查询集，支持三级联动筛选"""
        print(f"🔍 DEBUG: get_queryset() 被调用")
        queryset = super().get_queryset()

        # 获取三级联动筛选参数
        garden_id = self.request.GET.get('garden_id')
        region_id = self.request.GET.get('region_id')
        plot_id = self.request.GET.get('plot_id')

        print(f"🔍 DEBUG: 筛选参数: garden_id={garden_id}, region_id={region_id}, plot_id={plot_id}")

        original_count = queryset.count()
        print(f"📊 DEBUG: 原始查询集数量: {original_count}")

        # 根据茶园筛选
        if garden_id:
            try:
                from apps.tea_fields.models import TeaGarden
                garden = TeaGarden.objects.get(id=garden_id)
                print(f"🌱 DEBUG: 茶园筛选: {garden.garden_name} ({garden.garden_code})")
                # 筛选属于该茶园的茶地（通过编码匹配）
                queryset = queryset.filter(
                    models.Q(description__icontains=garden.garden_code) |
                    models.Q(detailed_location__icontains=garden.garden_name)
                )
                filtered_count = queryset.count()
                print(f"✅ DEBUG: 茶园筛选结果: {original_count} -> {filtered_count}")
            except Exception as e:
                print(f"❌ DEBUG: 茶园筛选错误: {e}")

        # 根据茶区域筛选
        if region_id:
            try:
                from apps.tea_fields.models import TeaFieldRegion
                region = TeaFieldRegion.objects.get(id=region_id)
                garden = region.garden
                # 筛选属于该区域的茶地（通过编码匹配）
                region_code = f"{garden.garden_code}-{region.zone_code}"
                print(f"🍃 DEBUG: 茶区域筛选: {region.name} ({region_code})")
                before_count = queryset.count()
                queryset = queryset.filter(
                    models.Q(description__icontains=region_code) |
                    models.Q(detailed_location__icontains=region.name)
                )
                after_count = queryset.count()
                print(f"✅ DEBUG: 茶区域筛选结果: {before_count} -> {after_count}")
            except Exception as e:
                print(f"❌ DEBUG: 茶区域筛选错误: {e}")

        # 根据茶地块筛选
        if plot_id:
            try:
                from apps.tea_fields.models import TeaFieldPlot
                plot = TeaFieldPlot.objects.get(id=plot_id)
                region = plot.region
                garden = region.garden
                # 筛选对应的茶地（通过完整编码匹配）
                plot_code = f"{garden.garden_code}-{region.zone_code}-{plot.plot_number}"
                print(f"📍 DEBUG: 茶地块筛选: {plot.plot_name} ({plot_code})")
                before_count = queryset.count()
                queryset = queryset.filter(
                    models.Q(description__icontains=plot_code) |
                    models.Q(name__icontains=plot.plot_name)
                )
                after_count = queryset.count()
                print(f"✅ DEBUG: 茶地块筛选结果: {before_count} -> {after_count}")
            except Exception as e:
                print(f"❌ DEBUG: 茶地块筛选错误: {e}")

        final_count = queryset.count()
        print(f"🎯 DEBUG: 最终查询集数量: {final_count}")
        return queryset

    def list(self, request, *args, **kwargs):
        """茶地块列表"""
        # 获取基础查询集
        queryset = self.filter_queryset(self.get_queryset())

        # 三级联动筛选
        garden_id = request.GET.get('garden_id')
        region_id = request.GET.get('region_id')
        plot_id = request.GET.get('plot_id')

        # 根据茶园筛选
        if garden_id:
            queryset = queryset.filter(region__garden_id=garden_id)

        # 根据茶区域筛选
        if region_id:
            queryset = queryset.filter(region_id=region_id)

        # 根据茶地块筛选
        if plot_id:
            queryset = queryset.filter(id=plot_id)

        # 推荐茶地筛选
        is_recommended = request.GET.get('is_recommended')
        if is_recommended == 'true':
            # 对于推荐茶地，我们可以按状态、价格等条件筛选
            queryset = queryset.filter(status='available').order_by('-price')

        # 分页处理
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            # 返回前端期望的格式
            paginated_response = self.get_paginated_response(serializer.data)
            return Response({
                'code': 200,
                'message': 'success',
                'data': serializer.data,
                'count': paginated_response.data.get('count'),
                'next': paginated_response.data.get('next'),
                'previous': paginated_response.data.get('previous')
            })

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })
    
    def get_queryset(self):
        """获取茶地块查询集"""
        queryset = super().get_queryset()

        # 状态筛选
        field_status = self.request.query_params.get('status')
        if field_status:
            queryset = queryset.filter(status=field_status)

        # 地块类型筛选
        plot_type = self.request.query_params.get('plot_type')
        if plot_type:
            queryset = queryset.filter(plot_type=plot_type)

        # 价格范围筛选
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')
        if min_price:
            queryset = queryset.filter(price__gte=min_price)
        if max_price:
            queryset = queryset.filter(price__lte=max_price)

        # 面积范围筛选
        min_area = self.request.query_params.get('min_area')
        max_area = self.request.query_params.get('max_area')
        if min_area:
            queryset = queryset.filter(area__gte=min_area)
        if max_area:
            queryset = queryset.filter(area__lte=max_area)

        return queryset



class TeaFieldDetailView(generics.RetrieveAPIView):
    """茶地详情 - 使用三级导航茶地块数据"""
    queryset = TeaFieldPlot.objects.all()
    serializer_class = TeaFieldPlotSerializer
    permission_classes = [AllowAny]
    
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()

        # 记录用户行为（如果已登录）
        if request.user.is_authenticated:
            try:
                UserAnalytics.objects.create(
                    user=request.user,
                    action_type='view_plot',
                    device_type=request.META.get('HTTP_USER_AGENT', '')[:20],
                    ip_address=request.META.get('REMOTE_ADDR')
                )
            except:
                pass  # 忽略分析记录错误

        serializer = self.get_serializer(instance, context={'request': request})
        return Response({
            'code': 200,
            'data': serializer.data
        })


# 订单相关视图
class OrderCreateView(generics.CreateAPIView):
    """创建订单"""
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        from .order_serializers import NewOrderCreateSerializer
        return NewOrderCreateSerializer

    def create(self, request, *args, **kwargs):
        try:
            # 记录请求数据用于调试
            logger.info(f'创建订单请求: 用户={request.user.id}, 数据={request.data}')

            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                order = serializer.save()

                # 记录用户行为（可选，如果UserAnalytics存在）
                try:
                    from apps.analytics.models import UserAnalytics
                    UserAnalytics.objects.create(
                        user=request.user,
                        tea_field=order.tea_field,
                        action_type='purchase',
                        action_data={'order_id': order.order_id, 'amount': float(order.total_amount)},
                        device_type=request.META.get('HTTP_USER_AGENT', '')[:20],
                        ip_address=request.META.get('REMOTE_ADDR')
                    )
                except Exception as e:
                    logger.warning(f'记录用户行为失败: {e}')

                logger.info(f'订单创建成功: {order.order_id}')
                return Response({
                    'code': 200,
                    'message': '订单创建成功',
                    'data': OrderSerializer(order).data
                }, status=status.HTTP_201_CREATED)
            else:
                logger.error(f'订单创建验证失败: {serializer.errors}')
                return Response({
                    'code': 400,
                    'message': '参数错误',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f'订单创建异常: {e}', exc_info=True)
            return Response({
                'code': 500,
                'message': f'服务器内部错误: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class OrderListView(generics.ListAPIView):
    """用户订单列表"""
    serializer_class = OrderSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [CustomFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status']
    ordering = ['-created_at']
    
    def get_queryset(self):
        return Order.objects.filter(user=self.request.user)
    
    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        return Response({
            'code': 200,
            'data': response.data
        })


class OrderDetailView(generics.RetrieveUpdateAPIView):
    """订单详情和更新"""
    serializer_class = OrderSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'order_id'

    def get_queryset(self):
        return Order.objects.filter(user=self.request.user)

    def retrieve(self, request, *args, **kwargs):
        response = super().retrieve(request, *args, **kwargs)
        return Response({
            'code': 200,
            'data': response.data
        })

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response({
            'code': 200,
            'message': '订单更新成功',
            'data': serializer.data
        })


# 支付相关视图
class PaymentCreateView(generics.CreateAPIView):
    """创建支付"""
    serializer_class = PaymentCreateSerializer
    permission_classes = [IsAuthenticated]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            payment = serializer.save()

            # 根据支付方式生成支付参数
            if payment.payment_method == 'wechat':
                payment_data = self.generate_wechat_payment(payment)
            else:
                return Response({
                    'code': 400,
                    'message': '暂不支持该支付方式'
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                'code': 200,
                'message': '支付创建成功',
                'data': payment_data
            })

        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def generate_wechat_payment(self, payment):
        """生成微信支付参数"""
        from apps.payments.wechat_pay_service import WechatPayManager
        from apps.system.utils import WechatPayConfigManager, WechatConfigManager
        import time
        import hashlib
        import random
        import string

        # 直接使用正式微信支付，不再支持沙盒模式
        logger.info('💳 使用正式微信支付模式')

        # 正常的微信支付流程
        # 检查微信支付是否启用
        if not WechatPayConfigManager.is_pay_enabled():
            raise Exception('微信支付功能已关闭')

        # 检查微信支付配置
        if not WechatPayConfigManager.is_configured():
            config_errors = WechatPayConfigManager.validate_pay_config()
            raise Exception(f'微信支付配置错误: {"; ".join(config_errors)}')

        # 获取用户openid
        user = payment.order.user
        user_openid = None

        # 尝试从微信用户表获取openid
        try:
            from apps.notifications.models import WechatUserInfo
            wechat_user = WechatUserInfo.objects.get(user=user)
            user_openid = wechat_user.openid
        except WechatUserInfo.DoesNotExist:
            pass
        except Exception as e:
            logger.error(f'获取用户微信信息失败: {e}')
            pass

        if not user_openid:
            raise Exception('用户未绑定微信，无法使用微信支付')

        # 获取客户端IP
        request = self.request
        client_ip = request.META.get('HTTP_X_FORWARDED_FOR')
        if client_ip:
            client_ip = client_ip.split(',')[0].strip()
        else:
            client_ip = request.META.get('REMOTE_ADDR', '127.0.0.1')

        # 创建微信支付订单
        result = WechatPayManager.create_payment(payment.order, user_openid, client_ip)

        if result['success']:
            # 更新支付记录，保存微信订单号和prepay_id
            payment.third_party_id = result.get('prepay_id', '')
            # 在备注中记录微信订单号，用于后续查询和对账
            if hasattr(payment, 'notes'):
                payment.notes = f"微信订单号: {result.get('wechat_order_no', '')}"
            payment.save()

            return {
                'payment_id': payment.payment_id,
                'wechat_order_no': result.get('wechat_order_no', ''),
                **result['payment_params']
            }
        else:
            raise Exception(result['error'])


@api_view(['GET'])
@permission_classes([AllowAny])
def test_wechat_pay_config(request):
    """测试微信支付配置"""
    from apps.system.utils import WechatConfigManager, WechatPayConfigManager

    try:
        # 获取配置信息
        app_id = WechatConfigManager.get_app_id()
        mch_id = WechatPayConfigManager.get_mch_id()

        # 验证配置
        wechat_errors = WechatConfigManager.validate_config()
        pay_errors = WechatPayConfigManager.validate_pay_config()

        config_status = {
            'app_id': app_id,
            'mch_id': mch_id,
            'wechat_configured': len(wechat_errors) == 0,
            'pay_configured': len(pay_errors) == 0,
            'wechat_errors': wechat_errors,
            'pay_errors': pay_errors,
            'merchant_platform_steps': [
                '1. 登录微信商户平台 (pay.weixin.qq.com)',
                '2. 进入"产品中心" → "AppID账号管理"',
                f'3. 添加AppID: {app_id}',
                '4. 授权JSAPI支付功能',
                '5. 配置支付授权目录: https://teabuy.yizhangkj.com/',
                '6. 等待配置生效（通常几分钟）'
            ]
        }

        return Response({
            'code': 200,
            'message': '配置检查完成',
            'data': config_status
        })

    except Exception as e:
        logger.error(f'配置检查失败: {e}')
        return Response({
            'code': 500,
            'message': f'配置检查失败: {str(e)}'
        })


@api_view(['POST'])
@permission_classes([AllowAny])
def wechat_pay_notify(request):
    """微信支付回调通知"""
    from apps.payments.wechat_pay_service import WechatPayManager, WechatPayService
    from apps.orders.models import Payment, Order
    from django.http import HttpResponse
    from django.utils import timezone

    try:
        # 获取XML数据
        xml_data = request.body.decode('utf-8')
        logger.info(f'收到微信支付通知: {xml_data}')

        # 处理支付通知
        result = WechatPayManager.handle_payment_notify(xml_data)

        if result['success']:
            # 查找对应的订单
            out_trade_no = result['out_trade_no']  # 这是微信订单号，格式如：ORD20250714013036BDB66886_134512ABC123
            transaction_id = result['transaction_id']

            try:
                # 从微信订单号中提取原始订单号
                # 新格式：订单号前20位 + 时间戳6位 + UUID4位
                if len(out_trade_no) > 20:
                    # 如果长度超过20，说明是新格式，取前20位
                    original_order_id = out_trade_no[:20]
                else:
                    # 否则直接使用
                    original_order_id = out_trade_no

                # 尝试精确匹配
                order = Order.objects.filter(order_id=original_order_id).first()
                if not order:
                    # 如果精确匹配失败，尝试模糊匹配（订单号可能被截断）
                    order = Order.objects.filter(order_id__startswith=original_order_id[:15]).first()

                if order:
                    # 查找支付记录
                    payment = Payment.objects.filter(order=order, status='pending').first()
                    if payment:
                        # 更新支付记录
                        payment.status = 'success'  # 使用正确的状态值
                        payment.third_party_id = transaction_id
                        payment.paid_at = timezone.now()
                        payment.save()

                        # 更新订单状态
                        order.status = 'paid'
                        order.paid_at = timezone.now()
                        order.save()

                        logger.info(f'订单 {order.order_id} 支付成功，微信交易号: {transaction_id}，微信订单号: {out_trade_no}')
                    else:
                        logger.warning(f'未找到订单 {order.order_id} 的待支付记录')
                else:
                    logger.error(f'未找到订单: {original_order_id}（微信订单号: {out_trade_no}）')

            except Exception as e:
                logger.error(f'处理支付通知异常: {e}（微信订单号: {out_trade_no}）')

            # 返回成功响应
            response_xml = WechatPayService.generate_notify_response(True, 'OK')
            return HttpResponse(response_xml, content_type='application/xml')
        else:
            logger.error(f'微信支付通知处理失败: {result["error"]}')
            # 返回失败响应
            response_xml = WechatPayService.generate_notify_response(False, result['error'])
            return HttpResponse(response_xml, content_type='application/xml')

    except Exception as e:
        logger.error(f'处理微信支付通知异常: {e}')
        # 返回失败响应
        response_xml = WechatPayService.generate_notify_response(False, str(e))
        return HttpResponse(response_xml, content_type='application/xml')


class PaymentStatusView(generics.RetrieveAPIView):
    """查询支付状态"""
    serializer_class = PaymentSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'payment_id'

    def get_queryset(self):
        return Payment.objects.filter(order__user=self.request.user)

    def retrieve(self, request, *args, **kwargs):
        response = super().retrieve(request, *args, **kwargs)
        return Response({
            'code': 200,
            'data': response.data
        })


# 微信支付诊断接口
@api_view(['GET'])
@permission_classes([AllowAny])
def wechat_pay_debug(request):
    """微信支付配置诊断接口"""
    return Response({
        'code': 200,
        'data': {
            'message': '微信支付AppID授权问题诊断',
            'current_config': {
                'appid': 'wx53e96657daa02825',
                'mch_id': '1721631964',
                'error_code': 102,
                'error_meaning': 'AppID权限被拒绝'
            },
            'problem_analysis': {
                'description': '虽然小程序后台显示已关联商户号，但仍出现errno: 102错误',
                'possible_reasons': [
                    '1. 商户平台端未完成双向授权',
                    '2. JSAPI支付权限未开通',
                    '3. 商户号状态异常',
                    '4. 配置生效延迟（最长24小时）'
                ]
            },
            'step_by_step_solution': {
                'step1': {
                    'title': '检查商户平台AppID授权状态',
                    'url': 'https://pay.weixin.qq.com',
                    'path': '产品中心 → AppID账号管理',
                    'action': '确认AppID wx53e96657daa02825 状态为"已授权"'
                },
                'step2': {
                    'title': '检查JSAPI支付权限',
                    'path': '产品中心 → 我的产品',
                    'action': '确认"JSAPI支付"和"小程序支付"已开通'
                },
                'step3': {
                    'title': '检查商户号状态',
                    'path': '账户中心 → 账户信息',
                    'action': '确认商户号1721631964未被冻结或限制'
                },
                'step4': {
                    'title': '如果以上都正常',
                    'action': '可能需要等待配置生效，或联系微信支付客服'
                }
            },
            'immediate_workaround': {
                'description': '当前已启用沙箱模式作为临时解决方案',
                'benefit': '用户可以完整体验购买流程，不影响业务测试'
            },
            'timestamp': timezone.now().isoformat()
        }
    })


# 监控相关辅助函数
def calculate_alert_duration(alert):
    """计算告警持续时间"""
    if alert.resolved_at:
        duration = alert.resolved_at - alert.triggered_at
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        if hours > 0:
            return f"{hours}小时{minutes}分钟"
        else:
            return f"{minutes}分钟"
    else:
        # 未解决的告警，计算到现在的时间
        from django.utils import timezone
        duration = timezone.now() - alert.triggered_at
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        if hours > 0:
            return f"{hours}小时{minutes}分钟（进行中）"
        else:
            return f"{minutes}分钟（进行中）"


def find_tea_field_by_id(field_id):
    """
    根据field_id查找对应的TeaField
    支持直接查找TeaField ID或通过TeaFieldPlot ID查找
    """
    # 首先尝试直接查找TeaField
    try:
        return TeaField.objects.get(id=field_id, is_active=True)
    except TeaField.DoesNotExist:
        # 如果找不到，尝试通过TeaFieldPlot查找对应的TeaField
        from apps.tea_fields.models import TeaFieldPlot
        try:
            tea_plot = TeaFieldPlot.objects.get(id=field_id)
            # 根据茶地块名称查找对应的TeaField
            tea_field = TeaField.objects.filter(
                name=tea_plot.plot_name,
                is_active=True
            ).first()
            if not tea_field:
                # 如果还是找不到，尝试模糊匹配
                tea_field = TeaField.objects.filter(
                    name__icontains=tea_plot.plot_name,
                    is_active=True
                ).first()
            return tea_field
        except TeaFieldPlot.DoesNotExist:
            return None


# 监控相关视图
@api_view(['GET'])
@permission_classes([AllowAny])
def monitoring_realtime(request):
    """获取实时监控数据"""
    field_id = request.query_params.get('field_id')
    if not field_id:
        return Response({
            'code': 400,
            'message': '茶地ID不能为空'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        tea_field = find_tea_field_by_id(field_id)
        if not tea_field:
            return Response({
                'code': 404,
                'message': '茶地不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 获取最新监控数据
        latest_data = tea_field.monitoring_data.first()
        
        # 获取告警信息
        alerts = tea_field.alert_records.filter(status='active')[:5]
        
        return Response({
            'code': 200,
            'data': {
                'field_id': tea_field.id,
                'field_name': tea_field.name,
                'current_data': MonitoringDataSerializer(latest_data).data if latest_data else None,
                'alerts': AlertRecordSerializer(alerts, many=True).data
            }
        })
        
    except TeaField.DoesNotExist:
        return Response({
            'code': 404,
            'message': '茶地不存在'
        }, status=status.HTTP_404_NOT_FOUND)


# 收益相关视图
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def earnings_analysis(request):
    """收益分析"""
    from datetime import datetime, timedelta
    from django.db.models import Sum, Count

    period = request.query_params.get('period', 'month')
    field_id = request.query_params.get('field_id')
    user = request.user

    # 获取用户的收益数据
    earnings_queryset = Earnings.objects.filter(user=user)

    if field_id:
        earnings_queryset = earnings_queryset.filter(tea_field_id=field_id)

    # 根据期间筛选
    today = datetime.now().date()
    if period == 'month':
        start_date = datetime.now() - timedelta(days=30)
        period_earnings = earnings_queryset.filter(created_at__gte=start_date)
    else:
        period_earnings = earnings_queryset

    # 计算统计数据
    total_earnings = earnings_queryset.aggregate(total=Sum('amount'))['total'] or 0
    period_earnings_sum = period_earnings.aggregate(total=Sum('amount'))['total'] or 0
    today_earnings = earnings_queryset.filter(created_at__date=today).aggregate(total=Sum('amount'))['total'] or 0

    # 获取用户认购的茶地数量
    user_subscriptions = Order.objects.filter(user=user, status='paid').values('tea_field').distinct().count()

    # 获取用户订单统计
    total_orders = Order.objects.filter(user=user).count()
    paid_orders = Order.objects.filter(user=user, status='paid').count()

    earnings_list = EarningsSerializer(period_earnings.order_by('-created_at')[:10], many=True).data

    return Response({
        'code': 200,
        'data': {
            'total_earnings': float(total_earnings),
            'period_earnings': float(period_earnings_sum),
            'today_earnings': float(today_earnings),
            'user_subscriptions': user_subscriptions,
            'total_orders': total_orders,
            'paid_orders': paid_orders,
            'earnings_list': earnings_list
        }
    })


# 数据概览
@api_view(['GET'])
@permission_classes([AllowAny])
def data_overview(request):
    """数据概览"""
    from apps.users.models import User
    from apps.orders.models import Order
    from django.db.models import Sum, Count

    # 茶地统计
    total_fields = TeaField.objects.filter(is_active=True).count()
    available_fields = TeaField.objects.filter(is_active=True, status='available').count()
    sold_fields = TeaField.objects.filter(is_active=True, status='sold_out').count()

    # 用户统计
    total_users = User.objects.count()
    verified_users = User.objects.filter(is_verified=True).count()

    # 订单统计
    total_orders = Order.objects.count()
    paid_orders = Order.objects.filter(status='paid').count()

    # 收入统计
    total_revenue = Order.objects.filter(status='paid').aggregate(
        total=Sum('total_amount'))['total'] or 0

    return Response({
        'code': 200,
        'data': {
            'total_fields': total_fields,
            'available_fields': available_fields,
            'sold_fields': sold_fields,
            'sold_rate': round((sold_fields / total_fields * 100) if total_fields > 0 else 0, 1),
            'total_users': total_users,
            'verified_users': verified_users,
            'total_orders': total_orders,
            'paid_orders': paid_orders,
            'total_revenue': float(total_revenue),
            'success_rate': round((paid_orders / total_orders * 100) if total_orders > 0 else 0, 1)
        }
    })


# 数据分析相关视图
@api_view(['GET'])
@permission_classes([AllowAny])
def earnings_analytics(request):
    """收益分析"""
    period = request.GET.get('period', 'month')

    # 模拟数据分析结果
    analytics_data = {
        'total_earnings': 125000.00,
        'period_earnings': 8500.00,
        'growth_rate': 12.5,
        'user_count': 156,
        'avg_earnings': 801.28,
        'top_fields': [
            {'name': 'A区优质龙井茶地', 'earnings': 3200.00},
            {'name': 'B区精品铁观音茶地', 'earnings': 2800.00},
            {'name': 'D区古树普洱茶地', 'earnings': 2500.00}
        ]
    }

    return Response({
        'code': 200,
        'message': 'success',
        'data': analytics_data
    })


# 用户个人资料API
@api_view(['GET', 'PUT'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """
    获取或更新用户个人资料
    """
    try:
        # 获取或创建用户资料
        profile, created = UserProfile.objects.get_or_create(
            user=request.user,
            defaults={
                'real_name': '',
                'id_card': '',
                'address': '',
                'avatar': '',
                'total_investment': 0,
                'total_earnings': 0,
                'risk_level': 'low'
            }
        )

        if request.method == 'GET':
            # 获取用户资料
            data = {
                'id': request.user.id,
                'username': request.user.username,
                'nickname': request.user.nickname or '',
                'phone': request.user.phone or '',
                'avatar': profile.avatar or request.user.avatar or '',
                'gender': request.user.gender,
                'city': request.user.city or '',
                'province': request.user.province or '',
                'country': request.user.country or '',
                'address': profile.address or '',
                'real_name': profile.real_name or '',
                'id_card': profile.id_card or '',
                'is_verified': request.user.is_verified,
                'total_investment': str(profile.total_investment),
                'total_earnings': str(profile.total_earnings),
                'risk_level': profile.risk_level,
                'created_at': profile.created_at.isoformat(),
                'updated_at': profile.updated_at.isoformat()
            }

            return Response({
                'code': 200,
                'message': 'success',
                'data': data
            })

        elif request.method == 'PUT':
            # 更新用户资料
            data = request.data

            # 更新User模型的字段
            if 'nickname' in data:
                request.user.nickname = data['nickname']
            if 'phone' in data:
                request.user.phone = data['phone']
            if 'gender' in data:
                request.user.gender = data['gender']
            if 'city' in data:
                request.user.city = data['city']
            if 'province' in data:
                request.user.province = data['province']
            if 'country' in data:
                request.user.country = data['country']

            # 更新UserProfile模型的字段
            if 'avatar' in data:
                profile.avatar = data['avatar']
            if 'address' in data:
                profile.address = data['address']
            if 'real_name' in data:
                profile.real_name = data['real_name']
            if 'id_card' in data:
                profile.id_card = data['id_card']
            if 'risk_level' in data:
                profile.risk_level = data['risk_level']

            request.user.save()
            profile.save()

            return Response({
                'code': 200,
                'message': '个人资料更新成功',
                'data': {
                    'id': request.user.id,
                    'nickname': request.user.nickname,
                    'phone': request.user.phone,
                    'avatar': profile.avatar,
                    'gender': request.user.gender,
                    'address': profile.address,
                    'real_name': profile.real_name,
                    'id_card': profile.id_card,
                    'is_verified': request.user.is_verified
                }
            })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'操作失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 通知相关API
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def notification_list(request):
    """获取用户通知列表"""
    try:
        from apps.notifications.models import Notification

        # 获取查询参数
        notification_type = request.GET.get('type', 'all')
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))

        # 构建查询
        queryset = Notification.objects.filter(user=request.user)

        if notification_type != 'all':
            queryset = queryset.filter(type=notification_type)

        # 分页
        total = queryset.count()
        start = (page - 1) * page_size
        end = start + page_size
        notifications = queryset[start:end]

        # 序列化数据
        data = []
        for notification in notifications:
            data.append({
                'id': notification.id,
                'type': notification.type,
                'title': notification.title,
                'content': notification.content,
                'is_read': notification.is_read,
                'created_at': notification.created_at.isoformat(),
                'read_at': notification.read_at.isoformat() if notification.read_at else None,
                'extra_data': notification.extra_data
            })

        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'notifications': data,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total': total,
                    'has_next': end < total
                }
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取通知列表失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_notification_read(request, notification_id):
    """标记通知为已读"""
    try:
        from apps.notifications.models import Notification
        from django.utils import timezone

        notification = Notification.objects.get(
            id=notification_id,
            user=request.user
        )

        if not notification.is_read:
            notification.is_read = True
            notification.read_at = timezone.now()
            notification.save(update_fields=['is_read', 'read_at'])

        return Response({
            'code': 200,
            'message': '标记成功',
            'data': {
                'id': notification.id,
                'is_read': notification.is_read,
                'read_at': notification.read_at.isoformat() if notification.read_at else None
            }
        })

    except Notification.DoesNotExist:
        return Response({
            'code': 404,
            'message': '通知不存在',
            'data': None
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'标记失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_all_notifications_read(request):
    """标记所有通知为已读"""
    try:
        from apps.notifications.models import Notification
        from django.utils import timezone

        updated = Notification.objects.filter(
            user=request.user,
            is_read=False
        ).update(
            is_read=True,
            read_at=timezone.now()
        )

        return Response({
            'code': 200,
            'message': f'成功标记 {updated} 条通知为已读',
            'data': {
                'updated_count': updated
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'标记失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def notification_unread_count(request):
    """获取未读通知数量"""
    try:
        from apps.notifications.models import Notification

        # 总未读数量
        total_unread = Notification.objects.filter(
            user=request.user,
            is_read=False
        ).count()

        # 按类型统计未读数量
        type_counts = {}
        for notification_type, _ in Notification.TYPE_CHOICES:
            count = Notification.objects.filter(
                user=request.user,
                type=notification_type,
                is_read=False
            ).count()
            type_counts[notification_type] = count

        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'total_unread': total_unread,
                'type_counts': type_counts
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取未读数量失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 企业功能相关视图 ====================

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def enterprise_certification(request):
    """企业认证管理"""
    try:
        if request.method == 'GET':
            # 获取企业认证状态
            user_profile = getattr(request.user, 'userprofile', None)
            if user_profile:
                return Response({
                    'code': 200,
                    'message': '获取认证状态成功',
                    'data': {
                        'status': user_profile.enterprise_status or 'pending',
                        'company_name': user_profile.company_name or '',
                        'business_license': user_profile.business_license or '',
                        'contact_person': user_profile.contact_person or '',
                        'contact_phone': user_profile.contact_phone or '',
                        'is_verified': user_profile.enterprise_status == 'verified',
                        'submit_time': user_profile.enterprise_submit_time,
                        'verify_time': user_profile.enterprise_verify_time,
                    }
                })
            else:
                return Response({
                    'code': 200,
                    'message': '获取认证状态成功',
                    'data': {
                        'status': 'pending',
                        'company_name': '',
                        'business_license': '',
                        'contact_person': '',
                        'contact_phone': '',
                        'is_verified': False,
                        'submit_time': None,
                        'verify_time': None,
                    }
                })

        elif request.method == 'POST':
            # 提交企业认证申请
            data = request.data
            user_profile, created = UserProfile.objects.get_or_create(user=request.user)

            user_profile.company_name = data.get('company_name', '')
            user_profile.business_license = data.get('business_license', '')
            user_profile.contact_person = data.get('contact_person', '')
            user_profile.contact_phone = data.get('contact_phone', '')
            user_profile.enterprise_status = 'pending'
            user_profile.enterprise_submit_time = timezone.now()
            user_profile.save()

            return Response({
                'code': 200,
                'message': '企业认证申请已提交，请等待审核',
                'data': {
                    'status': 'pending',
                    'submit_time': user_profile.enterprise_submit_time
                }
            })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'企业认证操作失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'PUT'])
@permission_classes([IsAuthenticated])
def enterprise_info(request):
    """企业信息管理"""
    try:
        user_profile, created = UserProfile.objects.get_or_create(user=request.user)

        if request.method == 'GET':
            return Response({
                'code': 200,
                'message': '获取企业信息成功',
                'data': {
                    'company_name': user_profile.company_name or '',
                    'business_license': user_profile.business_license or '',
                    'contact_person': user_profile.contact_person or '',
                    'contact_phone': user_profile.contact_phone or '',
                    'company_address': user_profile.company_address or '',
                    'company_email': user_profile.company_email or '',
                    'company_website': user_profile.company_website or '',
                    'business_scope': user_profile.business_scope or '',
                }
            })

        elif request.method == 'PUT':
            data = request.data
            user_profile.company_name = data.get('company_name', user_profile.company_name)
            user_profile.business_license = data.get('business_license', user_profile.business_license)
            user_profile.contact_person = data.get('contact_person', user_profile.contact_person)
            user_profile.contact_phone = data.get('contact_phone', user_profile.contact_phone)
            user_profile.company_address = data.get('company_address', user_profile.company_address)
            user_profile.company_email = data.get('company_email', user_profile.company_email)
            user_profile.company_website = data.get('company_website', user_profile.company_website)
            user_profile.business_scope = data.get('business_scope', user_profile.business_scope)
            user_profile.save()

            return Response({
                'code': 200,
                'message': '企业信息更新成功',
                'data': {
                    'company_name': user_profile.company_name,
                    'business_license': user_profile.business_license,
                    'contact_person': user_profile.contact_person,
                    'contact_phone': user_profile.contact_phone,
                }
            })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'企业信息操作失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def contract_list(request):
    """合同管理"""
    try:
        # 获取用户的订单作为合同
        orders = Order.objects.filter(user=request.user).order_by('-created_at')

        contracts = []
        for order in orders:
            contracts.append({
                'id': order.id,
                'contract_number': f'HT{order.order_id}',
                'tea_field_name': order.tea_field.name if order.tea_field else '',
                'area': float(order.area) if order.area else 0,
                'total_amount': float(order.total_amount),
                'status': order.status,
                'created_at': order.created_at,
                'contract_file': f'/static/contracts/contract_{order.order_id}.pdf',  # 模拟合同文件
                'tea_field_id': order.tea_field.id if order.tea_field else None,
            })

        return Response({
            'code': 200,
            'message': '获取合同列表成功',
            'data': {
                'contracts': contracts,
                'total': len(contracts)
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取合同列表失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def invoice_list(request):
    """发票管理"""
    try:
        if request.method == 'GET':
            # 模拟发票数据
            invoices = [
                {
                    'id': 1,
                    'invoice_number': 'FP202501001',
                    'amount': 5200.00,
                    'tax_amount': 520.00,
                    'total_amount': 5720.00,
                    'status': 'issued',
                    'invoice_type': 'special',
                    'created_at': '2025-01-01T10:00:00Z',
                    'issued_at': '2025-01-02T15:30:00Z',
                    'download_url': '/static/invoices/FP202501001.pdf'
                },
                {
                    'id': 2,
                    'invoice_number': 'FP202501002',
                    'amount': 3200.00,
                    'tax_amount': 320.00,
                    'total_amount': 3520.00,
                    'status': 'pending',
                    'invoice_type': 'ordinary',
                    'created_at': '2025-01-03T14:20:00Z',
                    'issued_at': None,
                    'download_url': None
                }
            ]

            return Response({
                'code': 200,
                'message': '获取发票列表成功',
                'data': {
                    'invoices': invoices,
                    'total': len(invoices)
                }
            })

        elif request.method == 'POST':
            # 申请开票
            data = request.data
            invoice_data = {
                'id': 3,
                'invoice_number': f'FP{timezone.now().strftime("%Y%m%d%H%M%S")}',
                'amount': float(data.get('amount', 0)),
                'tax_amount': float(data.get('amount', 0)) * 0.1,
                'total_amount': float(data.get('amount', 0)) * 1.1,
                'status': 'pending',
                'invoice_type': data.get('invoice_type', 'ordinary'),
                'created_at': timezone.now().isoformat(),
                'issued_at': None,
                'download_url': None
            }

            return Response({
                'code': 200,
                'message': '发票申请已提交',
                'data': invoice_data
            })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'发票操作失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 茶地对比功能 ====================

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def tea_field_compare(request):
    """茶地对比分析"""
    try:
        tea_field_ids = request.data.get('ids', [])
        if not tea_field_ids:
            return Response({
                'code': 400,
                'message': '请提供要对比的茶地ID',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取茶地信息
        tea_fields = TeaField.objects.filter(id__in=tea_field_ids)

        compare_data = []
        for tea_field in tea_fields:
            compare_data.append({
                'id': tea_field.id,
                'name': tea_field.name,
                'image': tea_field.image.url if tea_field.image else '/static/images/default-tea-field.jpg',
                'location': tea_field.location,
                'area': float(tea_field.area),
                'variety': tea_field.variety,
                'price': float(tea_field.price),
                'expected_return': float(tea_field.expected_return) if tea_field.expected_return else 12.0,
                'expected_yield': float(tea_field.expected_yield) if tea_field.expected_yield else 80,
                'altitude': tea_field.altitude or 800,
                'soil_type': tea_field.soil_type or '红壤',
                'is_organic': tea_field.is_organic,
                'has_monitoring': True,  # 假设都有监控
                'professional_management': True,  # 假设都有专业管理
                'has_insurance': tea_field.has_insurance if hasattr(tea_field, 'has_insurance') else True,
                'score': 85 + (tea_field.id % 15),  # 模拟评分
                'price_score': 80 + (tea_field.id % 20),
                'return_score': 85 + (tea_field.id % 15),
                'risk_score': 82 + (tea_field.id % 18),
            })

        return Response({
            'code': 200,
            'message': '茶地对比数据获取成功',
            'data': {
                'compare_data': compare_data,
                'total': len(compare_data)
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'茶地对比失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 收益增强功能 ====================

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def earnings_withdraw(request):
    """收益提现申请"""
    try:
        data = request.data
        amount = float(data.get('amount', 0))

        if amount <= 0:
            return Response({
                'code': 400,
                'message': '提现金额必须大于0',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查用户可提现余额
        user_earnings = Earnings.objects.filter(user=request.user, status='confirmed')
        total_earnings = sum(float(e.amount) for e in user_earnings)

        if amount > total_earnings:
            return Response({
                'code': 400,
                'message': '提现金额超过可用余额',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 创建提现记录
        withdraw_record = {
            'id': timezone.now().strftime('%Y%m%d%H%M%S'),
            'amount': amount,
            'status': 'pending',
            'bank_account': data.get('bank_account', ''),
            'bank_name': data.get('bank_name', ''),
            'account_holder': data.get('account_holder', ''),
            'created_at': timezone.now().isoformat(),
            'estimated_arrival': (timezone.now() + timezone.timedelta(days=3)).isoformat()
        }

        return Response({
            'code': 200,
            'message': '提现申请已提交，预计3个工作日到账',
            'data': withdraw_record
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'提现申请失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def earnings_chart_data(request):
    """收益图表数据"""
    try:
        from django.db.models import Sum, Count
        from django.utils import timezone
        from datetime import datetime, timedelta
        import calendar

        period = request.GET.get('period', 'month')
        user = request.user

        # 获取用户的收益数据
        earnings_queryset = Earnings.objects.filter(user=user, status='confirmed')

        if period == 'month':
            # 本月每周的数据
            now = timezone.now()
            start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # 计算每周的收益
            weekly_data = []
            labels = []

            for week in range(4):
                week_start = start_of_month + timedelta(weeks=week)
                week_end = week_start + timedelta(days=6)

                week_earnings = earnings_queryset.filter(
                    created_at__gte=week_start,
                    created_at__lte=week_end
                ).aggregate(total=Sum('amount'))['total'] or 0

                weekly_data.append(float(week_earnings))
                labels.append(f'第{week+1}周')

            chart_data = {
                'labels': labels,
                'datasets': [{
                    'data': weekly_data,
                    'color': '#22c55e'
                }]
            }

        elif period == 'quarter':
            # 本季度每月的数据
            now = timezone.now()
            current_quarter = (now.month - 1) // 3 + 1
            quarter_start_month = (current_quarter - 1) * 3 + 1

            monthly_data = []
            labels = []

            for month_offset in range(3):
                month = quarter_start_month + month_offset
                month_start = now.replace(month=month, day=1, hour=0, minute=0, second=0, microsecond=0)
                month_end = month_start.replace(day=calendar.monthrange(now.year, month)[1], hour=23, minute=59, second=59)

                month_earnings = earnings_queryset.filter(
                    created_at__gte=month_start,
                    created_at__lte=month_end
                ).aggregate(total=Sum('amount'))['total'] or 0

                monthly_data.append(float(month_earnings))
                labels.append(f'{month}月')

            chart_data = {
                'labels': labels,
                'datasets': [{
                    'data': monthly_data,
                    'color': '#3b82f6'
                }]
            }

        elif period == 'year':
            # 本年度每季度的数据
            now = timezone.now()
            year_start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)

            quarterly_data = []
            labels = []

            for quarter in range(4):
                quarter_start_month = quarter * 3 + 1
                quarter_start = year_start.replace(month=quarter_start_month)
                quarter_end_month = min(quarter_start_month + 2, 12)
                quarter_end = year_start.replace(
                    month=quarter_end_month,
                    day=calendar.monthrange(now.year, quarter_end_month)[1],
                    hour=23, minute=59, second=59
                )

                quarter_earnings = earnings_queryset.filter(
                    created_at__gte=quarter_start,
                    created_at__lte=quarter_end
                ).aggregate(total=Sum('amount'))['total'] or 0

                quarterly_data.append(float(quarter_earnings))
                labels.append(f'Q{quarter+1}')

            chart_data = {
                'labels': labels,
                'datasets': [{
                    'data': quarterly_data,
                    'color': '#f59e0b'
                }]
            }

        else:
            # 默认返回月度数据
            chart_data = {
                'labels': ['第1周', '第2周', '第3周', '第4周'],
                'datasets': [{
                    'data': [0, 0, 0, 0],
                    'color': '#22c55e'
                }]
            }

        return Response({
            'code': 200,
            'message': '获取图表数据成功',
            'data': chart_data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取图表数据失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def earnings_history(request):
    """收益历史记录"""
    try:
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))

        # 获取用户收益记录
        earnings = Earnings.objects.filter(user=request.user).order_by('-created_at')

        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        earnings_page = earnings[start:end]

        earnings_list = []
        for earning in earnings_page:
            earnings_list.append({
                'id': earning.id,
                'amount': float(earning.amount),
                'type': earning.earning_type,
                'description': earning.description or '茶地收益',
                'status': earning.status,
                'tea_field_name': earning.tea_field.name if earning.tea_field else '',
                'created_at': earning.created_at,
            })

        return Response({
            'code': 200,
            'message': '获取收益历史成功',
            'data': {
                'earnings': earnings_list,
                'total': earnings.count(),
                'page': page,
                'page_size': page_size,
                'has_next': end < earnings.count()
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取收益历史失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 文件上传功能 ====================

def validate_file_content(file, file_type):
    """
    验证文件内容安全性
    不仅检查扩展名，还检查文件的实际内容
    """
    try:
        import mimetypes
        from PIL import Image

        # 重置文件指针
        file.seek(0)

        # 读取文件头部用于检测
        file_header = file.read(1024)
        file.seek(0)  # 重置文件指针

        # 定义允许的MIME类型
        allowed_mime_types = {
            'images': ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'documents': ['application/pdf', 'application/msword',
                         'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            'general': ['image/jpeg', 'image/png', 'image/gif', 'application/pdf']
        }

        # 通过文件头检测MIME类型
        detected_mime = mimetypes.guess_type(file.name)[0]

        # 检查是否为允许的MIME类型
        allowed_types = allowed_mime_types.get(file_type, allowed_mime_types['general'])
        if detected_mime not in allowed_types:
            return False

        # 如果是图片，尝试用PIL验证
        if detected_mime and detected_mime.startswith('image/'):
            try:
                img = Image.open(file)
                img.verify()  # 验证图片完整性
                file.seek(0)  # 重置文件指针
                return True
            except Exception:
                return False

        # 检查文件头是否包含可疑内容
        suspicious_patterns = [
            b'<?php', b'<script', b'javascript:', b'vbscript:',
            b'<iframe', b'<object', b'<embed'
        ]

        file_header_lower = file_header.lower()
        for pattern in suspicious_patterns:
            if pattern in file_header_lower:
                return False

        return True

    except Exception as e:
        # 验证失败时记录日志但不抛出异常
        logger.warning(f"文件内容验证失败: {str(e)}")
        return False

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def file_upload(request):
    """通用文件上传"""
    try:
        from django.conf import settings
        from django.core.files.storage import default_storage
        import os
        import uuid

        if 'file' not in request.FILES:
            return Response({
                'code': 400,
                'message': '请选择要上传的文件',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']
        file_type = request.data.get('type', 'general')

        # 获取配置
        allowed_types = getattr(settings, 'ALLOWED_FILE_TYPES', {
            'image': ['jpg', 'jpeg', 'png', 'gif'],
            'document': ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
            'general': ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']
        })

        size_limits = getattr(settings, 'FILE_SIZE_LIMITS', {
            'general': 10 * 1024 * 1024
        })

        upload_paths = getattr(settings, 'UPLOAD_PATHS', {
            'general': 'uploads/general/'
        })

        # 检查文件大小
        max_size = size_limits.get(file_type, size_limits.get('general', 10 * 1024 * 1024))
        if file.size > max_size:
            return Response({
                'code': 400,
                'message': f'文件大小不能超过{max_size // (1024*1024)}MB',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查文件类型
        file_ext = file.name.split('.')[-1].lower()
        if file_ext not in allowed_types.get(file_type, allowed_types['general']):
            return Response({
                'code': 400,
                'message': f'不支持的文件类型: {file_ext}',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 安全验证：检查文件内容
        if not validate_file_content(file, file_type):
            return Response({
                'code': 400,
                'message': '文件内容验证失败，可能是恶意文件',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 生成文件名和路径
        filename = f"{uuid.uuid4().hex}.{file_ext}"
        upload_path = upload_paths.get(file_type, upload_paths.get('general', 'uploads/general/'))
        file_path = os.path.join(upload_path, filename)

        # 实际保存文件
        saved_path = default_storage.save(file_path, file)
        file_url = default_storage.url(saved_path)

        return Response({
            'code': 200,
            'message': '文件上传成功',
            'data': {
                'filename': filename,
                'url': file_url,
                'path': saved_path,
                'size': file.size,
                'type': file_type
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'文件上传失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def avatar_upload(request):
    """头像上传"""
    try:
        from django.conf import settings
        from django.core.files.storage import default_storage
        import os
        import uuid

        if 'avatar' not in request.FILES:
            return Response({
                'code': 400,
                'message': '请选择头像文件',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        avatar = request.FILES['avatar']

        # 获取配置
        size_limits = getattr(settings, 'FILE_SIZE_LIMITS', {})
        max_size = size_limits.get('avatar', 2 * 1024 * 1024)

        # 检查文件大小
        if avatar.size > max_size:
            return Response({
                'code': 400,
                'message': f'头像文件大小不能超过{max_size // (1024*1024)}MB',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查文件类型
        allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp']
        file_ext = avatar.name.split('.')[-1].lower()
        if file_ext not in allowed_types:
            return Response({
                'code': 400,
                'message': '头像只支持 JPG、PNG、GIF、WebP 格式',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 安全验证：检查文件内容
        if not validate_file_content(avatar, 'images'):
            return Response({
                'code': 400,
                'message': '头像文件内容验证失败，可能是恶意文件',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 生成文件名和路径
        filename = f"avatar_{request.user.id}_{uuid.uuid4().hex}.{file_ext}"
        upload_paths = getattr(settings, 'UPLOAD_PATHS', {})
        avatar_path = os.path.join(upload_paths.get('avatars', 'uploads/avatars/'), filename)

        # 实际保存文件
        saved_path = default_storage.save(avatar_path, avatar)
        avatar_url = default_storage.url(saved_path)

        # 更新用户头像
        user_profile, created = UserProfile.objects.get_or_create(user=request.user)
        user_profile.avatar = avatar_url
        user_profile.save()

        return Response({
            'code': 200,
            'message': '头像上传成功',
            'data': {
                'avatar_url': avatar_url,
                'filename': filename,
                'size': avatar.size
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'头像上传失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def avatar_upload_base64(request):
    """Base64头像上传"""
    try:
        import base64
        import io
        from PIL import Image
        from django.conf import settings
        from django.core.files.storage import default_storage
        from django.core.files.base import ContentFile
        import os
        import uuid

        avatar_base64 = request.data.get('avatar_base64')
        if not avatar_base64:
            return Response({
                'code': 400,
                'message': '请提供Base64头像数据',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 解码Base64数据
            if ',' in avatar_base64:
                avatar_base64 = avatar_base64.split(',')[1]

            image_data = base64.b64decode(avatar_base64)

            # 验证图片数据
            image = Image.open(io.BytesIO(image_data))

            # 检查文件大小
            size_limits = getattr(settings, 'FILE_SIZE_LIMITS', {})
            max_size = size_limits.get('avatar', 2 * 1024 * 1024)
            if len(image_data) > max_size:
                return Response({
                    'code': 400,
                    'message': f'头像文件大小不能超过{max_size // (1024*1024)}MB',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)

            # 生成文件名和路径
            file_ext = image.format.lower() if image.format else 'jpg'
            if file_ext == 'jpeg':
                file_ext = 'jpg'

            filename = f"avatar_{request.user.id}_{uuid.uuid4().hex}.{file_ext}"
            upload_paths = getattr(settings, 'UPLOAD_PATHS', {})
            avatar_path = os.path.join(upload_paths.get('avatars', 'uploads/avatars/'), filename)

            # 保存文件
            content_file = ContentFile(image_data, name=filename)
            saved_path = default_storage.save(avatar_path, content_file)
            avatar_url = default_storage.url(saved_path)

            # 更新用户头像
            user_profile, created = UserProfile.objects.get_or_create(user=request.user)
            user_profile.avatar = avatar_url
            user_profile.save()

            return Response({
                'code': 200,
                'message': '头像上传成功',
                'data': {
                    'avatar_url': avatar_url,
                    'filename': filename,
                    'size': len(image_data)
                }
            })

        except Exception as decode_error:
            return Response({
                'code': 400,
                'message': f'Base64数据解码失败: {str(decode_error)}',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'头像上传失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 文件管理增强API
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def batch_upload(request):
    """批量文件上传"""
    try:
        from django.conf import settings
        from django.core.files.storage import default_storage
        import os
        import uuid

        files = request.FILES.getlist('files')
        if not files:
            return Response({
                'code': 400,
                'message': '请选择要上传的文件',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        file_type = request.data.get('type', 'general')
        results = []
        errors = []

        # 获取配置
        allowed_types = getattr(settings, 'ALLOWED_FILE_TYPES', {
            'image': ['jpg', 'jpeg', 'png', 'gif'],
            'document': ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
            'general': ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']
        })

        size_limits = getattr(settings, 'FILE_SIZE_LIMITS', {
            'general': 10 * 1024 * 1024
        })

        upload_paths = getattr(settings, 'UPLOAD_PATHS', {
            'general': 'uploads/general/'
        })

        max_size = size_limits.get(file_type, size_limits.get('general', 10 * 1024 * 1024))
        allowed_exts = allowed_types.get(file_type, allowed_types['general'])
        upload_path = upload_paths.get(file_type, upload_paths.get('general', 'uploads/general/'))

        for file in files:
            try:
                # 检查文件大小
                if file.size > max_size:
                    errors.append({
                        'filename': file.name,
                        'error': f'文件大小不能超过{max_size // (1024*1024)}MB'
                    })
                    continue

                # 检查文件类型
                file_ext = file.name.split('.')[-1].lower()
                if file_ext not in allowed_exts:
                    errors.append({
                        'filename': file.name,
                        'error': f'不支持的文件类型: {file_ext}'
                    })
                    continue

                # 生成文件名和路径
                filename = f"{uuid.uuid4().hex}.{file_ext}"
                file_path = os.path.join(upload_path, filename)

                # 保存文件
                saved_path = default_storage.save(file_path, file)
                file_url = default_storage.url(saved_path)

                results.append({
                    'original_name': file.name,
                    'filename': filename,
                    'url': file_url,
                    'path': saved_path,
                    'size': file.size,
                    'type': file_type
                })

            except Exception as e:
                errors.append({
                    'filename': file.name,
                    'error': str(e)
                })

        return Response({
            'code': 200,
            'message': f'批量上传完成，成功{len(results)}个，失败{len(errors)}个',
            'data': {
                'success': results,
                'errors': errors,
                'total': len(files),
                'success_count': len(results),
                'error_count': len(errors)
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'批量上传失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def image_resize(request):
    """图片尺寸调整"""
    try:
        from PIL import Image
        from django.conf import settings
        from django.core.files.storage import default_storage
        from django.core.files.base import ContentFile
        import os
        import uuid
        import io

        if 'image' not in request.FILES:
            return Response({
                'code': 400,
                'message': '请选择要处理的图片',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        image_file = request.FILES['image']
        width = int(request.data.get('width', 800))
        height = int(request.data.get('height', 600))
        quality = int(request.data.get('quality', 85))
        keep_ratio = request.data.get('keep_ratio', 'true').lower() == 'true'

        # 检查是否为图片文件
        file_ext = image_file.name.split('.')[-1].lower()
        if file_ext not in ['jpg', 'jpeg', 'png', 'gif', 'webp']:
            return Response({
                'code': 400,
                'message': '请上传有效的图片文件',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 打开图片
        img = Image.open(image_file)
        original_width, original_height = img.size

        # 计算新尺寸
        if keep_ratio:
            # 保持宽高比
            ratio = min(width / original_width, height / original_height)
            new_width = int(original_width * ratio)
            new_height = int(original_height * ratio)
        else:
            # 强制调整到指定尺寸
            new_width = width
            new_height = height

        # 调整图片尺寸
        resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 保存处理后的图片
        output = io.BytesIO()
        format_map = {'jpg': 'JPEG', 'jpeg': 'JPEG', 'png': 'PNG', 'gif': 'GIF', 'webp': 'WEBP'}
        img_format = format_map.get(file_ext, 'JPEG')

        if img_format == 'JPEG':
            resized_img.save(output, format=img_format, quality=quality, optimize=True)
        else:
            resized_img.save(output, format=img_format, optimize=True)

        output.seek(0)

        # 生成新文件名
        filename = f"resized_{uuid.uuid4().hex}.{file_ext}"
        upload_paths = getattr(settings, 'UPLOAD_PATHS', {})
        file_path = os.path.join(upload_paths.get('images', 'uploads/images/'), filename)

        # 保存到存储
        content_file = ContentFile(output.getvalue())
        saved_path = default_storage.save(file_path, content_file)
        file_url = default_storage.url(saved_path)

        return Response({
            'code': 200,
            'message': '图片处理成功',
            'data': {
                'original_size': {'width': original_width, 'height': original_height},
                'new_size': {'width': new_width, 'height': new_height},
                'filename': filename,
                'url': file_url,
                'path': saved_path,
                'size': len(output.getvalue())
            }
        })

    except ImportError:
        return Response({
            'code': 500,
            'message': '图片处理功能需要安装Pillow库',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'图片处理失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_file(request):
    """删除文件"""
    try:
        from django.core.files.storage import default_storage
        import os

        file_path = request.data.get('file_path')
        if not file_path:
            return Response({
                'code': 400,
                'message': '请提供文件路径',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 安全检查：确保文件路径在允许的目录内
        from django.conf import settings
        upload_paths = getattr(settings, 'UPLOAD_PATHS', {})
        allowed_paths = list(upload_paths.values())

        is_allowed = any(file_path.startswith(path) for path in allowed_paths)
        if not is_allowed:
            return Response({
                'code': 403,
                'message': '无权删除此文件',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        # 检查文件是否存在
        if not default_storage.exists(file_path):
            return Response({
                'code': 404,
                'message': '文件不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)

        # 删除文件
        default_storage.delete(file_path)

        return Response({
            'code': 200,
            'message': '文件删除成功',
            'data': {
                'deleted_file': file_path
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'文件删除失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_upload_config(request):
    """获取上传配置信息"""
    try:
        from django.conf import settings

        config = {
            'allowed_file_types': getattr(settings, 'ALLOWED_FILE_TYPES', {}),
            'file_size_limits': getattr(settings, 'FILE_SIZE_LIMITS', {}),
            'upload_paths': getattr(settings, 'UPLOAD_PATHS', {}),
            'max_batch_size': 10,  # 批量上传最大文件数
        }

        # 转换字节为MB显示
        for key, value in config['file_size_limits'].items():
            config['file_size_limits'][key] = {
                'bytes': value,
                'mb': round(value / (1024 * 1024), 2)
            }

        return Response({
            'code': 200,
            'message': '获取配置成功',
            'data': config
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取配置失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_thumbnail(request):
    """生成缩略图"""
    try:
        from PIL import Image
        from django.conf import settings
        from django.core.files.storage import default_storage
        from django.core.files.base import ContentFile
        import os
        import uuid
        import io

        if 'image' not in request.FILES:
            return Response({
                'code': 400,
                'message': '请选择要生成缩略图的图片',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        image_file = request.FILES['image']
        size = int(request.data.get('size', 200))  # 缩略图尺寸
        quality = int(request.data.get('quality', 85))

        # 检查是否为图片文件
        file_ext = image_file.name.split('.')[-1].lower()
        if file_ext not in ['jpg', 'jpeg', 'png', 'gif', 'webp']:
            return Response({
                'code': 400,
                'message': '请上传有效的图片文件',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 打开图片
        img = Image.open(image_file)
        original_width, original_height = img.size

        # 生成正方形缩略图（居中裁剪）
        min_dimension = min(original_width, original_height)
        left = (original_width - min_dimension) // 2
        top = (original_height - min_dimension) // 2
        right = left + min_dimension
        bottom = top + min_dimension

        # 裁剪为正方形
        square_img = img.crop((left, top, right, bottom))

        # 调整到目标尺寸
        thumbnail = square_img.resize((size, size), Image.Resampling.LANCZOS)

        # 保存缩略图
        output = io.BytesIO()
        if file_ext in ['jpg', 'jpeg']:
            thumbnail.save(output, format='JPEG', quality=quality, optimize=True)
        else:
            thumbnail.save(output, format='PNG', optimize=True)

        output.seek(0)

        # 生成文件名
        filename = f"thumb_{size}x{size}_{uuid.uuid4().hex}.{file_ext}"
        upload_paths = getattr(settings, 'UPLOAD_PATHS', {})
        file_path = os.path.join(upload_paths.get('images', 'uploads/images/'), filename)

        # 保存到存储
        content_file = ContentFile(output.getvalue())
        saved_path = default_storage.save(file_path, content_file)
        file_url = default_storage.url(saved_path)

        return Response({
            'code': 200,
            'message': '缩略图生成成功',
            'data': {
                'original_size': {'width': original_width, 'height': original_height},
                'thumbnail_size': {'width': size, 'height': size},
                'filename': filename,
                'url': file_url,
                'path': saved_path,
                'size': len(output.getvalue())
            }
        })

    except ImportError:
        return Response({
            'code': 500,
            'message': '缩略图生成功能需要安装Pillow库',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'缩略图生成失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 数据导出功能 ====================

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def export_earnings(request):
    """导出收益数据"""
    try:
        format_type = request.GET.get('format', 'excel')
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')

        # 获取收益数据
        earnings = Earnings.objects.filter(user=request.user)

        if start_date:
            earnings = earnings.filter(created_at__gte=start_date)
        if end_date:
            earnings = earnings.filter(created_at__lte=end_date)

        # 生成导出文件
        import uuid
        filename = f"earnings_export_{uuid.uuid4().hex}.{format_type}"
        download_url = f"/static/exports/{filename}"

        # 这里应该实际生成Excel或PDF文件
        # 现在只是模拟返回下载链接

        return Response({
            'code': 200,
            'message': '导出文件生成成功',
            'data': {
                'download_url': download_url,
                'filename': filename,
                'total_records': earnings.count(),
                'format': format_type
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'导出失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def export_contracts(request):
    """导出合同数据"""
    try:
        format_type = request.GET.get('format', 'pdf')

        # 获取用户订单作为合同
        orders = Order.objects.filter(user=request.user)

        # 生成导出文件
        import uuid
        filename = f"contracts_export_{uuid.uuid4().hex}.{format_type}"
        download_url = f"/static/exports/{filename}"

        return Response({
            'code': 200,
            'message': '合同导出文件生成成功',
            'data': {
                'download_url': download_url,
                'filename': filename,
                'total_contracts': orders.count(),
                'format': format_type
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'合同导出失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 收藏管理相关API
@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def favorite_list(request):
    """收藏列表和添加收藏"""
    if request.method == 'GET':
        # 获取收藏列表
        try:
            favorites = Favorite.objects.filter(user=request.user).select_related('tea_field')

            data = []
            for favorite in favorites:
                data.append({
                    'id': favorite.id,
                    'tea_field': {
                        'id': favorite.tea_field.id,
                        'name': favorite.tea_field.name,
                        'location': favorite.tea_field.location,
                        'price': float(favorite.tea_field.price),
                        'area': float(favorite.tea_field.area),
                        'status': favorite.tea_field.status,
                        'images': favorite.tea_field.images or []
                    },
                    'created_at': favorite.created_at.isoformat()
                })

            return Response({
                'code': 200,
                'message': '获取成功',
                'data': data
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取收藏列表失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    elif request.method == 'POST':
        # 添加收藏
        try:
            field_id = request.data.get('field_id')
            if not field_id:
                return Response({
                    'code': 400,
                    'message': '茶地ID不能为空',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                tea_field = TeaField.objects.get(id=field_id, is_active=True)
            except TeaField.DoesNotExist:
                return Response({
                    'code': 404,
                    'message': '茶地不存在',
                    'data': None
                }, status=status.HTTP_404_NOT_FOUND)

            # 检查是否已收藏
            if Favorite.objects.filter(user=request.user, tea_field=tea_field).exists():
                return Response({
                    'code': 400,
                    'message': '已收藏该茶地',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)

            # 创建收藏
            favorite = Favorite.objects.create(
                user=request.user,
                tea_field=tea_field
            )

            return Response({
                'code': 200,
                'message': '收藏成功',
                'data': {
                    'id': favorite.id,
                    'field_id': tea_field.id,
                    'created_at': favorite.created_at.isoformat()
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'添加收藏失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def favorite_detail(request, favorite_id):
    """取消收藏"""
    try:
        try:
            favorite = Favorite.objects.get(id=favorite_id, user=request.user)
        except Favorite.DoesNotExist:
            return Response({
                'code': 404,
                'message': '收藏记录不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)

        favorite.delete()

        return Response({
            'code': 200,
            'message': '取消收藏成功',
            'data': None
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'取消收藏失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_favorite(request, field_id):
    """检查茶地收藏状态"""
    try:
        try:
            tea_field = TeaField.objects.get(id=field_id, is_active=True)
        except TeaField.DoesNotExist:
            return Response({
                'code': 404,
                'message': '茶地不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)

        is_favorite = Favorite.objects.filter(
            user=request.user,
            tea_field=tea_field
        ).exists()

        return Response({
            'code': 200,
            'message': '检查成功',
            'data': {
                'field_id': field_id,
                'is_favorite': is_favorite
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'检查收藏状态失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def monitoring_devices(request):
    """获取监控设备列表"""
    field_id = request.query_params.get('field_id')
    if not field_id:
        return Response({
            'code': 400,
            'message': '茶地ID不能为空'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        tea_field = find_tea_field_by_id(field_id)
        if not tea_field:
            return Response({
                'code': 404,
                'message': '茶地不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 获取真实设备数据
        from apps.monitoring.models import SensorDevice
        devices = SensorDevice.objects.filter(tea_field=tea_field, is_active=True)

        device_list = []
        for device in devices:
            device_data = {
                'id': device.id,
                'device_id': device.device_id,
                'name': device.device_name,
                'type': device.device_type,
                'status': device.status,
                'last_update': device.last_heartbeat.isoformat() if device.last_heartbeat else None,
                'battery': device.battery_level,
                'signal_strength': device.signal_strength,
                'manufacturer': device.manufacturer,
                'model': device.model,
                'firmware_version': device.firmware_version,
                'location': {
                    'latitude': float(device.latitude) if device.latitude else None,
                    'longitude': float(device.longitude) if device.longitude else None,
                    'height': float(device.installation_height) if device.installation_height else None
                },
                'communication': {
                    'protocol': device.communication_protocol,
                    'ip_address': device.ip_address,
                    'port': device.port,
                    'mqtt_topic': device.get_mqtt_topic() if device.communication_protocol == 'mqtt' else None
                },
                'status_info': {
                    'is_online': device.is_online,
                    'battery_status': device.battery_status,
                    'signal_status': device.signal_status,
                    'needs_calibration': device.needs_calibration
                },
                'data_collection_interval': device.data_collection_interval,
                'heartbeat_interval': device.heartbeat_interval,
                'is_critical': device.is_critical,
                'created_at': device.created_at.isoformat(),
                'updated_at': device.updated_at.isoformat()
            }
            device_list.append(device_data)

        # 如果没有真实设备数据，返回提示信息
        if not device_list:
            return Response({
                'code': 200,
                'message': '该茶地暂无监控设备',
                'data': [],
                'suggestion': '请在后台管理界面添加监控设备'
            })

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': device_list,
            'total': len(device_list)
        })

    except TeaField.DoesNotExist:
        return Response({
            'code': 404,
            'message': '茶地不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取设备列表失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def monitoring_alerts(request):
    """获取告警历史"""
    field_id = request.query_params.get('field_id')
    if not field_id:
        return Response({
            'code': 400,
            'message': '茶地ID不能为空'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        tea_field = find_tea_field_by_id(field_id)
        if not tea_field:
            return Response({
                'code': 404,
                'message': '茶地不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 获取真实告警记录
        from apps.monitoring.models import AlertRecord
        alerts = AlertRecord.objects.filter(tea_field=tea_field).order_by('-triggered_at')[:20]

        alert_list = []
        for alert in alerts:
            alert_data = {
                'id': alert.id,
                'time': alert.triggered_at.strftime('%H:%M'),
                'date': alert.triggered_at.strftime('%Y-%m-%d'),
                'title': alert.alert_message[:50],  # 截取前50个字符作为标题
                'description': alert.alert_message,
                'level': alert.rule.severity if alert.rule else 'info',
                'status': alert.status,
                'alert_type': alert.rule.alert_type if alert.rule else 'unknown',
                'current_value': float(alert.current_value) if alert.current_value else None,
                'threshold_value': float(alert.threshold_value) if alert.threshold_value else None,
                'device_name': alert.device.device_name if alert.device else None,
                'device_type': alert.device.device_type if alert.device else None,
                'triggered_at': alert.triggered_at.isoformat(),
                'resolved_at': alert.resolved_at.isoformat() if alert.resolved_at else None,
                'duration': calculate_alert_duration(alert),
                'rule_name': alert.rule.rule_name if alert.rule else None
            }
            alert_list.append(alert_data)

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'results': alert_list,
                'count': len(alert_list),
                'active_count': alerts.filter(status='active').count(),
                'resolved_count': alerts.filter(status='resolved').count()
            }
        })

        serializer = AlertRecordSerializer(alerts, many=True)
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'results': serializer.data,
                'count': alerts.count()
            }
        })

    except TeaField.DoesNotExist:
        return Response({
            'code': 404,
            'message': '茶地不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取告警历史失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def monitoring_history(request):
    """获取历史监控数据"""
    field_id = request.query_params.get('field_id')
    if not field_id:
        return Response({
            'code': 400,
            'message': '茶地ID不能为空'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        tea_field = find_tea_field_by_id(field_id)
        if not tea_field:
            return Response({
                'code': 404,
                'message': '茶地不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 获取历史数据
        history_data = tea_field.monitoring_data.all()[:50]

        serializer = MonitoringDataSerializer(history_data, many=True)
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'results': serializer.data,
                'count': history_data.count()
            }
        })

    except TeaField.DoesNotExist:
        return Response({
            'code': 404,
            'message': '茶地不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取历史数据失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 分析相关API
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def analytics_user_stats(request):
    """获取用户统计数据"""
    try:
        user = request.user

        # 获取用户订单统计
        orders = Order.objects.filter(user=user)
        total_orders = orders.count()
        active_orders = orders.filter(status__in=['confirmed', 'active']).count()
        paid_orders = orders.filter(status='paid').count()

        # 计算总面积
        total_area = sum([float(order.tea_field.area) for order in orders if order.tea_field.area])

        # 计算总收益（基于订单金额的10%作为年收益）
        total_earnings = sum([float(order.total_amount) * 0.1 for order in orders if order.total_amount])

        # 计算总产量（每亩约20公斤）
        total_yield = total_area * 20

        # 计算年化收益率（基于投资回报）
        total_investment = sum([float(order.total_amount) for order in orders if order.total_amount])
        return_rate = (total_earnings / total_investment * 100) if total_investment > 0 else 0

        # 用户积分（基于订单数量和收益）
        points = total_orders * 100 + int(total_earnings)

        # 会员等级
        if points > 2000:
            level = '钻石会员'
        elif points > 1000:
            level = '黄金会员'
        elif points > 500:
            level = '银牌会员'
        else:
            level = '普通会员'

        stats = {
            # 兼容多种字段名
            'tea_count': active_orders,
            'field_count': active_orders,
            'tea_fields': total_area,
            'total_area': total_area,
            'total_earnings': round(total_earnings, 2),
            'earnings': round(total_earnings, 2),
            'total_yield': round(total_yield, 1),
            'yield': round(total_yield, 1),
            'return_rate': round(return_rate, 1),
            'points': points,
            'user_points': points,
            'level': level,
            'user_level': level,
            'total_orders': total_orders,
            'paid_orders': paid_orders,
            'total_investment': round(total_investment, 2)
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': stats
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取用户统计失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def analytics_overview(request):
    """获取概览数据"""
    try:
        period = request.query_params.get('period', 'month')
        user = request.user

        # 获取用户订单
        orders = Order.objects.filter(user=user)

        # 模拟概览数据
        overview_data = {
            'total_earnings': round(sum([float(order.tea_field.price) * 0.1 for order in orders]), 2),
            'earnings_change': '+12.5%',
            'total_yield': round(sum([float(order.tea_field.area) * 20 for order in orders]), 1),
            'yield_change': '+8.3%',
            'tea_count': orders.filter(status__in=['confirmed', 'active']).count(),
            'tea_change': '0',
            'avg_quality': '优质',
            'quality_change': '+5.2%'
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': overview_data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取概览数据失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def analytics_environment(request):
    """获取环境数据分析"""
    try:
        period = request.query_params.get('period', 'month')

        # 模拟环境数据
        env_data = {
            'avg_temperature': 22.5,
            'avg_humidity': 65.2,
            'avg_light': 8500,
            'avg_ph': 6.8
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': env_data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取环境数据失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def analytics_production(request):
    """获取产量数据分析"""
    try:
        user = request.user
        period = request.query_params.get('period', 'month')

        # 获取用户订单
        orders = Order.objects.filter(user=user).select_related('tea_field')

        # 计算总产量（基于茶地面积模拟）
        total_yield = sum([float(order.tea_field.area) * 20 for order in orders])

        # 模拟产量数据
        production_data = {
            'total_yield': round(total_yield, 2),
            'current_month': round(total_yield / 6, 2),  # 假设平均分配到6个月
            'monthly_avg': round(total_yield / 12, 2),   # 年平均月产量
            'seasonal_distribution': {
                'spring': round(total_yield * 0.45, 2),
                'summer': round(total_yield * 0.35, 2),
                'autumn': round(total_yield * 0.20, 2)
            }
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': production_data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取产量数据失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def analytics_compare(request):
    """获取对比数据"""
    try:
        compare_type = request.query_params.get('type', 'yield')
        period = request.query_params.get('period', 'month')

        # 模拟对比数据
        compare_data = [
            {
                'name': '我的茶园',
                'description': '当前茶园表现',
                'value': '15',
                'unit': 'kg'
            },
            {
                'name': '同类型平均',
                'description': '行业平均水平',
                'value': '12',
                'unit': 'kg'
            },
            {
                'name': '优质茶园',
                'description': '行业领先水平',
                'value': '18',
                'unit': 'kg'
            }
        ]

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': compare_data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取对比数据失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def analytics_my_fields(request):
    """获取我的茶地统计"""
    try:
        user = request.user
        orders = Order.objects.filter(user=user).select_related('tea_field')

        fields_stats = []
        for order in orders:
            field_stat = {
                'id': order.tea_field.id,
                'name': order.tea_field.name,
                'area': float(order.tea_field.area),
                'status': order.status,
                'yield': float(order.tea_field.area) * 20,  # 模拟产量
                'earnings': float(order.tea_field.price) * 0.1,  # 模拟收益
                'quality_score': 85  # 模拟品质评分
            }
            fields_stats.append(field_stat)

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': fields_stats
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取茶地统计失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def earnings_stats(request):
    """获取收益统计"""
    try:
        user = request.user
        period = request.query_params.get('period', 'month')

        # 获取用户订单
        orders = Order.objects.filter(user=user)

        # 模拟收益统计
        stats = {
            'total_earnings': round(sum([float(order.tea_field.price) * 0.1 for order in orders]), 2),
            'monthly_earnings': round(sum([float(order.tea_field.price) * 0.01 for order in orders]), 2),
            'yearly_earnings': round(sum([float(order.tea_field.price) * 0.12 for order in orders]), 2),
            'growth_rate': 12.5,
            'earnings_trend': [
                {'month': '1月', 'amount': 1200},
                {'month': '2月', 'amount': 1350},
                {'month': '3月', 'amount': 1180},
                {'month': '4月', 'amount': 1420},
                {'month': '5月', 'amount': 1580},
                {'month': '6月', 'amount': 1650}
            ]
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': stats
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取收益统计失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def analytics_my_fields_stats(request):
    """获取我的茶地统计数据"""
    try:
        user = request.user
        orders = Order.objects.filter(user=user).select_related('tea_field')

        # 计算统计数据
        total_fields = orders.count()
        total_area = sum([float(order.tea_field.area) for order in orders])
        total_yield = total_area * 20  # 模拟产量计算
        total_earnings = sum([float(order.tea_field.price) * 0.1 for order in orders])
        monthly_earnings = total_earnings * 0.1  # 模拟月收益

        stats_data = {
            'total_fields': total_fields,
            'field_count': total_fields,  # 兼容字段
            'total_area': round(total_area, 2),
            'total_yield': round(total_yield, 2),
            'total_earnings': round(total_earnings, 2),
            'monthly_earnings': round(monthly_earnings, 2)
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': stats_data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取茶地统计失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



# 通用API接口
@api_view(['GET'])
@permission_classes([AllowAny])
def get_agreement(request, type):
    """获取协议内容"""
    try:
        agreements = {
            'user': {
                'title': '两山·茶管家用户服务协议',
                'updated_at': '2024年01月15日',
                'sections': [
                    {
                        'title': '第一条 协议的范围',
                        'content': '本协议是您与两山·茶管家平台（以下简称"本平台"）之间关于使用本平台服务的法律协议。本协议阐述之条款和条件适用于您通过各种方式使用本平台提供的各项服务。'
                    },
                    {
                        'title': '第二条 服务内容',
                        'content': '本平台为用户提供茶地认购、实时监控、收益管理、数据分析等相关服务。具体服务内容以本平台实际提供的功能为准。'
                    },
                    {
                        'title': '第三条 用户权利与义务',
                        'content': '3.1 用户有权享受本平台提供的各项服务；<br/>3.2 用户应当遵守国家相关法律法规；<br/>3.3 用户不得利用本平台从事违法违规活动；<br/>3.4 用户应当保护好自己的账户信息。'
                    },
                    {
                        'title': '第四条 隐私保护',
                        'content': '本平台承诺保护用户隐私，不会泄露用户个人信息。具体内容请参阅《隐私政策》。'
                    },
                    {
                        'title': '第五条 免责声明',
                        'content': '5.1 本平台对不可抗力因素导致的损失不承担责任；<br/>5.2 用户因违反本协议造成的损失由用户自行承担；<br/>5.3 本平台保留对服务内容进行调整的权利。'
                    },
                    {
                        'title': '第六条 协议的变更',
                        'content': '本平台有权根据需要修改本协议条款。协议变更后，如果您继续使用本平台服务，即视为您已接受修改后的协议。'
                    },
                    {
                        'title': '第七条 争议解决',
                        'content': '因本协议产生的争议，双方应友好协商解决；协商不成的，可向本平台所在地人民法院提起诉讼。'
                    },
                    {
                        'title': '第八条 其他',
                        'content': '本协议自您同意之日起生效。如有疑问，请联系客服。'
                    }
                ]
            },
            'privacy': {
                'title': '隐私政策',
                'updated_at': '2024年01月15日',
                'sections': [
                    {
                        'title': '第一条 信息收集',
                        'content': '我们会收集您在使用服务过程中主动提供的信息，包括但不限于注册信息、个人资料等。'
                    },
                    {
                        'title': '第二条 信息使用',
                        'content': '我们使用收集的信息来提供、维护和改进我们的服务，以及与您进行沟通。'
                    },
                    {
                        'title': '第三条 信息保护',
                        'content': '我们采用行业标准的安全措施来保护您的个人信息，防止未经授权的访问、使用或披露。'
                    },
                    {
                        'title': '第四条 信息共享',
                        'content': '除法律要求或您明确同意外，我们不会与第三方共享您的个人信息。'
                    },
                    {
                        'title': '第五条 您的权利',
                        'content': '您有权访问、更正、删除您的个人信息，也可以撤回您对信息处理的同意。'
                    }
                ]
            }
        }

        if type not in agreements:
            return Response({
                'code': 404,
                'message': '协议类型不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': agreements[type]
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取协议失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_company_info(request):
    """获取公司信息"""
    try:
        company_info = {
            'name': '两山茶业科技有限公司',
            'phone': '************',
            'email': '<EMAIL>',
            'wechat': 'teabutler2024',
            'address': '浙江省杭州市西湖区茶园路88号',
            'latitude': 30.2741,
            'longitude': 120.1551,
            'business_hours': '周一至周日 9:00-18:00',
            'description': '专注于茶地认购和智能化茶园管理的科技公司',
            'established_year': '2020',
            'website': 'https://www.teabutler.com'
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': company_info
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取公司信息失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 通知管理相关API
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_notifications(request):
    """获取用户通知列表"""
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 20))

    notifications = Notification.objects.filter(user=request.user)

    # 分页
    start = (page - 1) * page_size
    end = start + page_size
    paginated_notifications = notifications[start:end]

    serializer = UserNotificationSerializer(paginated_notifications, many=True)

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': {
            'results': serializer.data,
            'count': notifications.count(),
            'page': page,
            'page_size': page_size,
            'has_next': end < notifications.count()
        }
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_notification_read(request, notification_id):
    """标记通知为已读"""
    try:
        notification = Notification.objects.get(
            id=notification_id,
            user=request.user
        )
        notification.mark_as_read()

        return Response({
            'code': 200,
            'message': '标记成功'
        })
    except Notification.DoesNotExist:
        return Response({
            'code': 404,
            'message': '通知不存在'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_all_notifications_read(request):
    """标记所有通知为已读"""
    Notification.objects.filter(
        user=request.user,
        is_read=False
    ).update(
        is_read=True,
        read_at=timezone.now()
    )

    return Response({
        'code': 200,
        'message': '全部标记成功'
    })


# ==================== 身份验证相关API ====================

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_verification_status(request):
    """获取用户认证状态汇总"""
    try:
        user = request.user

        # 获取个人认证状态
        personal_verification = VerificationRecord.objects.filter(
            user=user, type='personal'
        ).order_by('-created_at').first()

        # 获取企业认证状态
        enterprise_verification = VerificationRecord.objects.filter(
            user=user, type='enterprise'
        ).order_by('-created_at').first()

        # 获取银行卡认证状态
        bank_card_verification = BankCardVerification.objects.filter(
            user=user
        ).order_by('-created_at').first()

        # 计算认证状态
        personal_verified = personal_verification and personal_verification.status == 'approved'
        enterprise_verified = enterprise_verification and enterprise_verification.status == 'approved'
        bank_card_verified = bank_card_verification and bank_card_verification.status == 'approved'

        # 计算认证进度
        total_items = 3
        completed_items = sum([
            1 if personal_verified else 0,
            1 if enterprise_verified else 0,
            1 if bank_card_verified else 0
        ])
        progress = int((completed_items / total_items) * 100)

        # 构建响应数据
        data = {
            'personal_verified': personal_verified,
            'enterprise_verified': enterprise_verified,
            'bank_card_verified': bank_card_verified,
            'verification_progress': progress,
            'personal_info': VerificationRecordSerializer(personal_verification).data if personal_verification else None,
            'enterprise_info': VerificationRecordSerializer(enterprise_verification).data if enterprise_verification else None,
            'bank_card_info': BankCardVerificationSerializer(bank_card_verification).data if bank_card_verification else None
        }

        return Response({
            'code': 200,
            'message': '获取认证状态成功',
            'data': data
        })

    except Exception as e:
        logger.error(f"获取认证状态失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取认证状态失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_verification_info(request):
    """获取用户认证信息（兼容旧接口）"""
    verification = VerificationRecord.objects.filter(
        user=request.user
    ).order_by('-created_at').first()

    if verification:
        serializer = VerificationRecordSerializer(verification)
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })
    else:
        return Response({
            'code': 200,
            'message': '暂无认证记录',
            'data': {
                'status': 'none'
            }
        })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def submit_verification(request):
    """提交身份认证"""
    serializer = VerificationRecordSerializer(data=request.data)
    if serializer.is_valid():
        verification = serializer.save(user=request.user)

        return Response({
            'code': 200,
            'message': '认证信息提交成功，请等待审核',
            'data': VerificationRecordSerializer(verification).data
        })

    return Response({
        'code': 400,
        'message': '参数错误',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def submit_personal_verification(request):
    """提交个人实名认证"""
    try:
        data = request.data.copy()
        data['type'] = 'personal'

        serializer = VerificationRecordSerializer(data=data)
        if serializer.is_valid():
            verification = serializer.save(user=request.user)

            return Response({
                'code': 200,
                'message': '个人认证信息提交成功，请等待审核',
                'data': VerificationRecordSerializer(verification).data
            })

        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"提交个人认证失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'提交个人认证失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def submit_enterprise_verification(request):
    """提交企业认证"""
    try:
        data = request.data.copy()
        data['type'] = 'enterprise'

        serializer = VerificationRecordSerializer(data=data)
        if serializer.is_valid():
            verification = serializer.save(user=request.user)

            return Response({
                'code': 200,
                'message': '企业认证信息提交成功，请等待审核',
                'data': VerificationRecordSerializer(verification).data
            })

        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"提交企业认证失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'提交企业认证失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def submit_bank_card_verification(request):
    """提交银行卡认证"""
    try:
        serializer = BankCardVerificationSerializer(data=request.data)
        if serializer.is_valid():
            verification = serializer.save(user=request.user)

            return Response({
                'code': 200,
                'message': '银行卡认证信息提交成功，请等待审核',
                'data': BankCardVerificationSerializer(verification).data
            })

        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"提交银行卡认证失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'提交银行卡认证失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_verification_history(request):
    """获取用户认证历史记录"""
    try:
        user = request.user
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))
        type_filter = request.GET.get('type', None)
        status_filter = request.GET.get('status', None)

        # 查询认证记录
        verification_query = VerificationRecord.objects.filter(user=user)
        if type_filter and type_filter != 'all':
            verification_query = verification_query.filter(type=type_filter)
        if status_filter and status_filter != 'all':
            verification_query = verification_query.filter(status=status_filter)

        # 查询银行卡认证记录
        bank_query = BankCardVerification.objects.filter(user=user)
        if status_filter and status_filter != 'all':
            bank_query = bank_query.filter(status=status_filter)

        # 合并结果
        all_records = []

        # 添加认证记录
        for record in verification_query:
            all_records.append({
                'id': f"verification_{record.id}",
                'type': record.type,
                'status': record.status,
                'real_name': record.real_name,
                'id_card': record.id_card,
                'phone': record.phone,
                'company_name': record.company_name,
                'credit_code': record.credit_code,
                'legal_person': record.legal_person,
                'contact_name': record.contact_name,
                'contact_phone': record.contact_phone,
                'created_at': record.created_at,
                'review_time': record.review_time,
                'reject_reason': record.reject_reason,
            })

        # 添加银行卡认证记录（如果类型筛选允许）
        if not type_filter or type_filter == 'all' or type_filter == 'bankcard':
            for record in bank_query:
                all_records.append({
                    'id': f"bankcard_{record.id}",
                    'type': 'bankcard',
                    'status': record.status,
                    'bank_name': record.bank_name,
                    'card_number': record.card_number,
                    'card_holder_name': record.card_holder_name,
                    'card_type': record.card_type,
                    'phone': record.phone,
                    'created_at': record.created_at,
                    'review_time': record.review_time,
                    'reject_reason': record.reject_reason,
                })

        # 按创建时间排序
        all_records.sort(key=lambda x: x['created_at'], reverse=True)

        # 分页
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        paginated_records = all_records[start_index:end_index]

        return Response({
            'code': 200,
            'message': '获取认证历史成功',
            'data': {
                'results': paginated_records,
                'count': len(all_records),
                'page': page,
                'page_size': page_size,
                'has_next': end_index < len(all_records)
            }
        })

    except Exception as e:
        logger.error(f"获取认证历史失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取认证历史失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 合同管理相关API
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_contracts(request):
    """获取用户合同列表"""
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 10))

    # 获取用户的订单对应的合同
    user_orders = Order.objects.filter(user=request.user)
    contracts = Contract.objects.filter(order__in=user_orders)

    # 分页
    start = (page - 1) * page_size
    end = start + page_size
    paginated_contracts = contracts[start:end]

    serializer = ContractSerializer(paginated_contracts, many=True)

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': {
            'results': serializer.data,
            'count': contracts.count(),
            'page': page,
            'page_size': page_size,
            'has_next': end < contracts.count()
        }
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_contract_stats(request):
    """获取用户合同统计"""
    try:
        # 获取用户的订单对应的合同
        user_orders = Order.objects.filter(user=request.user)
        contracts = Contract.objects.filter(order__in=user_orders)

        # 统计各状态的合同数量
        stats = {
            'total': contracts.count(),
            'pending': contracts.filter(status='pending').count(),
            'active': contracts.filter(status='active').count(),
            'signed': contracts.filter(status='signed').count(),
            'completed': contracts.filter(status='completed').count(),
            'expired': contracts.filter(status='expired').count(),
        }

        return Response({
            'code': 200,
            'message': '获取合同统计成功',
            'data': stats
        })
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取合同统计失败: {str(e)}',
            'data': {'total': 0, 'pending': 0, 'active': 0, 'signed': 0, 'completed': 0, 'expired': 0}
        })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_contract_detail(request, contract_id):
    """获取合同详情"""
    try:
        contract = Contract.objects.get(
            id=contract_id,
            order__user=request.user
        )
        serializer = ContractSerializer(contract)
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })
    except Contract.DoesNotExist:
        return Response({
            'code': 404,
            'message': '合同不存在'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def sign_contract(request, contract_id):
    """签署合同"""
    try:
        contract = Contract.objects.get(
            id=contract_id,
            order__user=request.user,
            status='draft'
        )

        # 更新合同状态
        contract.status = 'signed'
        contract.signed_at = timezone.now()
        contract.user_signature = request.data.get('signature', '')
        contract.save()

        return Response({
            'code': 200,
            'message': '合同签署成功',
            'data': ContractSerializer(contract).data
        })
    except Contract.DoesNotExist:
        return Response({
            'code': 404,
            'message': '合同不存在或无法签署'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def download_contract(request, contract_id):
    """下载合同"""
    try:
        contract = Contract.objects.get(
            id=contract_id,
            order__user=request.user
        )

        if contract.contract_file:
            return Response({
                'code': 200,
                'message': '获取下载链接成功',
                'data': {
                    'download_url': contract.contract_file,
                    'filename': f"{contract.contract_number}.pdf"
                }
            })
        else:
            return Response({
                'code': 400,
                'message': '合同文件不存在'
            }, status=status.HTTP_400_BAD_REQUEST)
    except Contract.DoesNotExist:
        return Response({
            'code': 404,
            'message': '合同不存在'
        }, status=status.HTTP_404_NOT_FOUND)


# 发票管理相关API
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_invoices(request):
    """获取用户发票列表"""
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 10))

    # 获取用户的订单对应的发票
    user_orders = Order.objects.filter(user=request.user)
    invoices = Invoice.objects.filter(order__in=user_orders)

    # 分页
    start = (page - 1) * page_size
    end = start + page_size
    paginated_invoices = invoices[start:end]

    serializer = InvoiceSerializer(paginated_invoices, many=True)

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': {
            'results': serializer.data,
            'count': invoices.count(),
            'page': page,
            'page_size': page_size,
            'has_next': end < invoices.count()
        }
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_invoice_stats(request):
    """获取用户发票统计"""
    try:
        # 获取用户的订单对应的发票
        user_orders = Order.objects.filter(user=request.user)
        invoices = Invoice.objects.filter(order__in=user_orders)

        # 统计各状态的发票数量
        stats = {
            'total': invoices.count(),
            'pending': invoices.filter(status='pending').count(),
            'issued': invoices.filter(status='issued').count(),
            'completed': invoices.filter(status='completed').count(),
            'rejected': invoices.filter(status='rejected').count(),
        }

        return Response({
            'code': 200,
            'message': '获取发票统计成功',
            'data': stats
        })
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取发票统计失败: {str(e)}',
            'data': {'total': 0, 'pending': 0, 'issued': 0, 'completed': 0, 'rejected': 0}
        })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def apply_invoice(request):
    """申请发票"""
    serializer = InvoiceCreateSerializer(data=request.data, context={'request': request})
    if serializer.is_valid():
        invoice = serializer.save()
        return Response({
            'code': 200,
            'message': '发票申请提交成功',
            'data': InvoiceSerializer(invoice).data
        })

    return Response({
        'code': 400,
        'message': '参数错误',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_invoice_detail(request, invoice_id):
    """获取发票详情"""
    try:
        invoice = Invoice.objects.get(
            id=invoice_id,
            order__user=request.user
        )
        serializer = InvoiceSerializer(invoice)
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })
    except Invoice.DoesNotExist:
        return Response({
            'code': 404,
            'message': '发票不存在'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def download_invoice(request, invoice_id):
    """下载发票"""
    try:
        invoice = Invoice.objects.get(
            id=invoice_id,
            order__user=request.user,
            status='completed'
        )

        if invoice.invoice_file:
            return Response({
                'code': 200,
                'message': '获取下载链接成功',
                'data': {
                    'download_url': invoice.invoice_file,
                    'filename': f"发票_{invoice.invoice_number}.pdf"
                }
            })
        else:
            return Response({
                'code': 400,
                'message': '发票文件不存在'
            }, status=status.HTTP_400_BAD_REQUEST)
    except Invoice.DoesNotExist:
        return Response({
            'code': 404,
            'message': '发票不存在或未开票'
        }, status=status.HTTP_404_NOT_FOUND)


# 提现申请相关API
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def withdraw_earnings(request):
    """申请提现"""
    serializer = WithdrawRequestCreateSerializer(data=request.data, context={'request': request})
    if serializer.is_valid():
        withdraw_request = serializer.save()
        return Response({
            'code': 200,
            'message': '提现申请提交成功',
            'data': WithdrawRequestSerializer(withdraw_request).data
        })

    return Response({
        'code': 400,
        'message': '参数错误',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_withdraw_requests(request):
    """获取提现申请列表"""
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 10))

    withdraw_requests = WithdrawRequest.objects.filter(user=request.user)

    # 分页
    start = (page - 1) * page_size
    end = start + page_size
    paginated_requests = withdraw_requests[start:end]

    serializer = WithdrawRequestSerializer(paginated_requests, many=True)

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': {
            'results': serializer.data,
            'count': withdraw_requests.count(),
            'page': page,
            'page_size': page_size,
            'has_next': end < withdraw_requests.count()
        }
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_withdraw_balance(request):
    """获取可提现余额"""
    from django.db.models import Sum

    user = request.user

    # 计算总收益
    total_earnings = Earnings.objects.filter(
        user=user,
        distributed_at__isnull=False  # 只计算已分配的收益
    ).aggregate(total=Sum('amount'))['total'] or 0

    # 计算已提现金额
    withdrawn_amount = WithdrawRequest.objects.filter(
        user=user,
        status__in=['approved', 'processing', 'completed']
    ).aggregate(total=Sum('amount'))['total'] or 0

    # 计算待审核提现金额
    pending_amount = WithdrawRequest.objects.filter(
        user=user,
        status='pending'
    ).aggregate(total=Sum('amount'))['total'] or 0

    available_balance = total_earnings - withdrawn_amount - pending_amount

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': {
            'total_earnings': float(total_earnings),
            'withdrawn_amount': float(withdrawn_amount),
            'pending_amount': float(pending_amount),
            'available_balance': float(available_balance)
        }
    })


# 通知推送相关API
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_notification(request):
    """创建通知（管理员功能）"""
    # 这个API主要用于管理员或系统创建通知
    if not request.user.is_staff:
        return Response({
            'code': 403,
            'message': '权限不足'
        }, status=status.HTTP_403_FORBIDDEN)

    title = request.data.get('title')
    content = request.data.get('content')
    notification_type = request.data.get('type', 'system')
    user_ids = request.data.get('user_ids', [])  # 指定用户ID列表
    send_to_all = request.data.get('send_to_all', False)  # 是否发送给所有用户

    if not title or not content:
        return Response({
            'code': 400,
            'message': '标题和内容不能为空'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        from apps.notifications.models import create_notification, send_bulk_notification

        if send_to_all:
            # 发送给所有用户
            users = User.objects.filter(is_active=True)
            count = send_bulk_notification(
                users=users,
                notification_type=notification_type,
                title=title,
                content=content,
                extra_data=request.data.get('extra_data', {})
            )
            message = f'成功发送给 {count} 个用户'
        elif user_ids:
            # 发送给指定用户
            users = User.objects.filter(id__in=user_ids, is_active=True)
            count = send_bulk_notification(
                users=users,
                notification_type=notification_type,
                title=title,
                content=content,
                extra_data=request.data.get('extra_data', {})
            )
            message = f'成功发送给 {count} 个用户'
        else:
            return Response({
                'code': 400,
                'message': '请指定接收用户'
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            'code': 200,
            'message': message,
            'data': {
                'sent_count': count
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'发送失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_notification_stats(request):
    """获取通知统计信息"""
    from django.db.models import Count

    # 获取用户的通知统计
    stats = Notification.objects.filter(user=request.user).aggregate(
        total=Count('id'),
        unread=Count('id', filter=models.Q(is_read=False))
    )

    # 按类型统计
    type_stats = {}
    for notification_type, type_name in Notification.TYPE_CHOICES:
        type_count = Notification.objects.filter(
            user=request.user,
            type=notification_type
        ).aggregate(
            total=Count('id'),
            unread=Count('id', filter=models.Q(is_read=False))
        )
        type_stats[notification_type] = {
            'name': type_name,
            'total': type_count['total'],
            'unread': type_count['unread']
        }

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': {
            'total_notifications': stats['total'],
            'unread_notifications': stats['unread'],
            'read_notifications': stats['total'] - stats['unread'],
            'type_stats': type_stats
        }
    })


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_notification(request, notification_id):
    """删除通知"""
    try:
        notification = Notification.objects.get(
            id=notification_id,
            user=request.user
        )
        notification.delete()

        return Response({
            'code': 200,
            'message': '删除成功'
        })
    except Notification.DoesNotExist:
        return Response({
            'code': 404,
            'message': '通知不存在'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def clear_read_notifications(request):
    """清空已读通知"""
    deleted_count = Notification.objects.filter(
        user=request.user,
        is_read=True
    ).delete()[0]

    return Response({
        'code': 200,
        'message': f'成功清空 {deleted_count} 条已读通知',
        'data': {
            'deleted_count': deleted_count
        }
    })


# 轮播图相关视图
@api_view(['GET'])
@permission_classes([AllowAny])
def banner_list(request):
    """获取轮播图列表"""
    position = request.GET.get('position', '')

    # 构建查询条件
    queryset = Banner.objects.filter(is_active=True)

    # 按位置筛选
    if position:
        queryset = queryset.filter(position=position)

    # 检查时间有效性
    now = timezone.now()
    queryset = queryset.filter(
        Q(start_time__isnull=True) | Q(start_time__lte=now),
        Q(end_time__isnull=True) | Q(end_time__gte=now)
    )

    # 按排序和创建时间排序
    queryset = queryset.order_by('sort_order', '-created_at')

    serializer = BannerListSerializer(queryset, many=True)

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': serializer.data
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def banner_detail(request, banner_id):
    """获取轮播图详情"""
    try:
        banner = Banner.objects.get(id=banner_id, is_active=True)

        # 检查时间有效性
        if not banner.is_valid_time():
            return Response({
                'code': 404,
                'message': '轮播图不存在或已过期'
            }, status=status.HTTP_404_NOT_FOUND)

        # 增加点击次数
        banner.click_count += 1
        banner.save(update_fields=['click_count'])

        serializer = BannerSerializer(banner)

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })

    except Banner.DoesNotExist:
        return Response({
            'code': 404,
            'message': '轮播图不存在'
        }, status=status.HTTP_404_NOT_FOUND)


# 图标相关视图
@api_view(['GET'])
@permission_classes([AllowAny])
def icon_categories(request):
    """获取图标分类列表"""
    queryset = IconCategory.objects.filter(is_active=True).order_by('sort_order', 'name')
    serializer = IconCategorySerializer(queryset, many=True)

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': serializer.data
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def icon_list(request):
    """获取图标列表"""
    category_code = request.GET.get('category', '')
    usage_type = request.GET.get('usage_type', '')
    search = request.GET.get('search', '')

    # 构建查询条件
    queryset = Icon.objects.filter(is_active=True)

    # 按分类筛选
    if category_code:
        queryset = queryset.filter(category__code=category_code)

    # 按用途筛选
    if usage_type:
        queryset = queryset.filter(usage_type=usage_type)

    # 搜索
    if search:
        queryset = queryset.filter(
            Q(name__icontains=search) |
            Q(code__icontains=search) |
            Q(tags__icontains=search) |
            Q(description__icontains=search)
        )

    # 排序
    queryset = queryset.select_related('category').order_by('category__sort_order', 'sort_order', 'name')

    serializer = IconListSerializer(queryset, many=True)

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': serializer.data
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def icon_detail(request, icon_code):
    """获取图标详情"""
    try:
        icon = Icon.objects.select_related('category').get(code=icon_code, is_active=True)

        # 增加使用次数
        icon.increment_usage()

        serializer = IconSerializer(icon)

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })

    except Icon.DoesNotExist:
        return Response({
            'code': 404,
            'message': '图标不存在'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([AllowAny])
def icon_by_usage(request, usage_type):
    """根据用途获取图标列表"""
    if usage_type not in dict(Icon.USAGE_TYPES):
        return Response({
            'code': 400,
            'message': '无效的用途类型'
        }, status=status.HTTP_400_BAD_REQUEST)

    queryset = Icon.objects.filter(
        is_active=True,
        usage_type=usage_type
    ).select_related('category').order_by('sort_order', 'name')

    serializer = IconListSerializer(queryset, many=True)

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': serializer.data
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def icon_config(request):
    """获取图标配置信息"""
    # 统计信息
    total_icons = Icon.objects.filter(is_active=True).count()
    total_categories = IconCategory.objects.filter(is_active=True).count()

    # 按用途分组统计
    usage_stats = {}
    for usage_type, usage_name in Icon.USAGE_TYPES:
        count = Icon.objects.filter(is_active=True, usage_type=usage_type).count()
        usage_stats[usage_type] = {
            'name': usage_name,
            'count': count
        }

    # 按分类统计
    category_stats = []
    categories = IconCategory.objects.filter(is_active=True).order_by('sort_order', 'name')
    for category in categories:
        icon_count = category.icons.filter(is_active=True).count()
        category_stats.append({
            'id': category.id,
            'name': category.name,
            'code': category.code,
            'count': icon_count
        })

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': {
            'total_icons': total_icons,
            'total_categories': total_categories,
            'usage_types': dict(Icon.USAGE_TYPES),
            'usage_stats': usage_stats,
            'category_stats': category_stats
        }
    })


# 茶叶资讯相关视图
@api_view(['GET'])
@permission_classes([AllowAny])
def tea_news_list(request):
    """获取茶叶资讯列表"""
    from apps.system.models import TeaNews

    news_type = request.GET.get('type', '')
    limit = int(request.GET.get('limit', 10))

    # 构建查询条件
    queryset = TeaNews.objects.filter(
        status='published',
        is_active=True
    )

    # 按类型筛选
    if news_type:
        queryset = queryset.filter(news_type=news_type)

    # 排序和限制数量
    queryset = queryset.order_by('-is_top', '-sort_order', '-publish_time')[:limit]

    # 构建返回数据
    data = []
    for news in queryset:
        data.append({
            'id': news.id,
            'title': news.title,
            'summary': news.summary,
            'image': news.image.url if news.image else None,
            'news_type': news.news_type,
            'news_type_display': news.get_news_type_display(),
            'color': news.color,
            'is_featured': news.is_featured,
            'is_top': news.is_top,
            'time': news.time_display if hasattr(news, 'time_display') else news.created_at.strftime('%Y-%m-%d'),
            'created_at': news.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'publish_time': news.publish_time.strftime('%Y-%m-%d %H:%M:%S') if news.publish_time else None
        })

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': data
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def tea_news_detail(request, news_id):
    """获取茶叶资讯详情"""
    from apps.system.models import TeaNews

    try:
        news = TeaNews.objects.get(
            id=news_id,
            status='published',
            is_active=True
        )

        # 增加浏览次数
        news.increment_view()

        data = {
            'id': news.id,
            'title': news.title,
            'summary': news.summary,
            'content': news.content,
            'image': news.image.url if news.image else None,
            'news_type': news.news_type,
            'news_type_display': news.get_news_type_display(),
            'color': news.color,
            'is_featured': news.is_featured,
            'is_top': news.is_top,
            'view_count': news.view_count,
            'time': news.time_display if hasattr(news, 'time_display') else news.created_at.strftime('%Y-%m-%d'),
            'created_at': news.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'publish_time': news.publish_time.strftime('%Y-%m-%d %H:%M:%S') if news.publish_time else None
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': data
        })

    except TeaNews.DoesNotExist:
        return Response({
            'code': 404,
            'message': '资讯不存在'
        }, status=status.HTTP_404_NOT_FOUND)


# 收益相关视图
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def earnings_overview(request):
    """获取收益概览"""
    from apps.orders.models import EarningsRecord
    from django.db.models import Sum
    from datetime import datetime, timedelta

    user = request.user
    today = timezone.now().date()

    # 计算各种收益数据
    total_earnings = EarningsRecord.objects.filter(
        user=user,
        status='settled',
        amount__gt=0
    ).aggregate(total=Sum('amount'))['total'] or 0

    # 今日收益
    daily_earnings = EarningsRecord.objects.filter(
        user=user,
        status='settled',
        earnings_date=today,
        amount__gt=0
    ).aggregate(total=Sum('amount'))['total'] or 0

    # 本月收益
    month_start = today.replace(day=1)
    monthly_earnings = EarningsRecord.objects.filter(
        user=user,
        status='settled',
        earnings_date__gte=month_start,
        amount__gt=0
    ).aggregate(total=Sum('amount'))['total'] or 0

    # 计算收益变化（与上月对比）
    last_month_start = (month_start - timedelta(days=1)).replace(day=1)
    last_month_end = month_start - timedelta(days=1)
    last_monthly_earnings = EarningsRecord.objects.filter(
        user=user,
        status='settled',
        earnings_date__gte=last_month_start,
        earnings_date__lte=last_month_end,
        amount__gt=0
    ).aggregate(total=Sum('amount'))['total'] or 0

    # 计算变化率
    if last_monthly_earnings > 0:
        earnings_change = ((monthly_earnings - last_monthly_earnings) / last_monthly_earnings) * 100
    else:
        earnings_change = 100 if monthly_earnings > 0 else 0

    # 年化收益率（简化计算）
    annual_return = 8.5  # 这里可以根据实际业务逻辑计算

    data = {
        'total_earnings': f"{total_earnings:.2f}",
        'daily_earnings': f"{daily_earnings:.2f}",
        'monthly_earnings': f"{monthly_earnings:.2f}",
        'annual_return': f"{annual_return:.1f}",
        'earnings_change': round(earnings_change, 1)
    }

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': data
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def earnings_chart(request):
    """获取收益图表数据"""
    from apps.orders.models import EarningsRecord
    from django.db.models import Sum
    from datetime import datetime, timedelta

    user = request.user
    chart_type = request.GET.get('type', 'daily')
    period = request.GET.get('period', '本月')

    today = timezone.now().date()

    if chart_type == 'daily':
        # 最近7天的数据
        data = []
        for i in range(6, -1, -1):
            date = today - timedelta(days=i)
            earnings = EarningsRecord.objects.filter(
                user=user,
                status='settled',
                earnings_date=date,
                amount__gt=0
            ).aggregate(total=Sum('amount'))['total'] or 0

            # 获取星期几
            weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            label = weekdays[date.weekday()]

            data.append({
                'label': label,
                'value': float(earnings),
                'date': date.strftime('%Y-%m-%d')
            })

    elif chart_type == 'monthly':
        # 最近12个月的数据
        data = []
        for i in range(11, -1, -1):
            # 计算月份
            target_date = today.replace(day=1) - timedelta(days=i*30)
            month_start = target_date.replace(day=1)

            # 计算下个月第一天
            if month_start.month == 12:
                month_end = month_start.replace(year=month_start.year + 1, month=1) - timedelta(days=1)
            else:
                month_end = month_start.replace(month=month_start.month + 1) - timedelta(days=1)

            earnings = EarningsRecord.objects.filter(
                user=user,
                status='settled',
                earnings_date__gte=month_start,
                earnings_date__lte=month_end,
                amount__gt=0
            ).aggregate(total=Sum('amount'))['total'] or 0

            data.append({
                'label': f"{month_start.month}月",
                'value': float(earnings),
                'date': month_start.strftime('%Y-%m')
            })

    else:
        # 默认返回空数据
        data = []

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': data
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def earnings_detail(request):
    """获取收益明细"""
    from apps.orders.models import EarningsRecord

    user = request.user
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 20))
    earnings_type = request.GET.get('type', '')

    # 构建查询条件
    queryset = EarningsRecord.objects.filter(user=user)

    if earnings_type:
        queryset = queryset.filter(earnings_type=earnings_type)

    # 排序
    queryset = queryset.order_by('-earnings_date', '-created_at')

    # 分页
    start = (page - 1) * page_size
    end = start + page_size
    records = queryset[start:end]

    # 构建返回数据
    data = []
    for record in records:
        data.append({
            'id': record.id,
            'name': record.get_earnings_type_display(),
            'description': record.description or f"{record.tea_field.name} · {record.get_earnings_type_display()}",
            'amount': float(record.amount),
            'earnings_type': record.earnings_type,
            'status': record.status,
            'status_display': record.get_status_display(),
            'icon': record.icon,
            'color': record.color,
            'time': record.created_at.strftime('%Y-%m-%d %H:%M'),
            'earnings_date': record.earnings_date.strftime('%Y-%m-%d'),
            'tea_field_name': record.tea_field.name
        })

    # 获取总数
    total = queryset.count()

    return Response({
        'code': 200,
        'message': '获取成功',
        'data': data,
        'pagination': {
            'page': page,
            'page_size': page_size,
            'total': total,
            'has_more': end < total
        }
    })


# 公司信息和协议相关视图
@api_view(['GET'])
@permission_classes([AllowAny])
def company_info(request):
    """获取公司信息"""
    from apps.system.models import CompanyInfo

    try:
        company = CompanyInfo.objects.filter(is_active=True).first()

        if not company:
            return Response({
                'code': 404,
                'message': '公司信息未配置'
            }, status=status.HTTP_404_NOT_FOUND)

        data = {
            'name': company.name,
            'phone': company.phone,
            'email': company.email,
            'wechat': company.wechat,
            'address': company.address,
            'latitude': company.latitude,
            'longitude': company.longitude,
            'established_year': company.established_year,
            'business_hours': company.business_hours,
            'website': company.website,
            'description': company.description
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取公司信息失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def agreement_content(request, agreement_type):
    """获取协议内容"""
    from apps.system.models import Agreement

    try:
        agreement = Agreement.objects.filter(
            agreement_type=agreement_type,
            is_active=True
        ).order_by('-version').first()

        if not agreement:
            return Response({
                'code': 404,
                'message': '协议不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        data = {
            'title': agreement.title,
            'content': agreement.content,
            'version': agreement.version,
            'agreement_type': agreement.agreement_type,
            'agreement_type_display': agreement.get_agreement_type_display(),
            'effective_date': agreement.effective_date.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': agreement.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取协议内容失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 协议管理相关API
@api_view(['GET'])
@permission_classes([AllowAny])
def agreement_list(request):
    """获取协议列表"""
    try:
        from apps.system.models import Agreement

        # 只返回生效的协议
        agreements = Agreement.objects.filter(is_active=True).order_by('agreement_type', '-version')

        agreement_data = []
        for agreement in agreements:
            agreement_data.append({
                'id': agreement.id,
                'title': agreement.title,
                'agreement_type': agreement.agreement_type,
                'agreement_type_display': agreement.get_agreement_type_display(),
                'version': agreement.version,
                'effective_date': agreement.effective_date.isoformat(),
                'updated_at': agreement.updated_at.isoformat(),
                'content_length': len(agreement.content)
            })

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': agreement_data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取协议列表失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def agreement_detail(request, agreement_type):
    """获取指定类型的协议详情"""
    try:
        from apps.system.models import Agreement

        # 获取指定类型的最新版本协议
        agreement = Agreement.objects.filter(
            agreement_type=agreement_type,
            is_active=True
        ).order_by('-version').first()

        if not agreement:
            return Response({
                'code': 404,
                'message': '协议不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        agreement_data = {
            'id': agreement.id,
            'title': agreement.title,
            'agreement_type': agreement.agreement_type,
            'agreement_type_display': agreement.get_agreement_type_display(),
            'version': agreement.version,
            'content': agreement.content,
            'effective_date': agreement.effective_date.isoformat(),
            'created_at': agreement.created_at.isoformat(),
            'updated_at': agreement.updated_at.isoformat()
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': agreement_data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取协议详情失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 数据大屏相关API
@api_view(['GET'])
@permission_classes([AllowAny])
def dashboard_overview(request):
    """获取数据大屏总览数据"""
    try:
        import random
        from datetime import datetime, timedelta
        from django.db.models import Sum, Count, Avg, Q
        from apps.users.models import User
        from apps.orders.models import Order, Payment, Earnings
        from apps.tea_fields.models import TeaField
        from apps.monitoring.models import SensorDevice, MonitoringData

        # 获取真实数据统计
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        this_month_start = today.replace(day=1)

        # 基础统计
        total_users = User.objects.count()
        verified_users = User.objects.filter(is_verified=True).count()
        today_new_users = User.objects.filter(date_joined__date=today).count()

        # 茶地统计
        total_fields = TeaField.objects.filter(is_active=True).count()
        available_fields = TeaField.objects.filter(is_active=True, status='available').count()
        total_area = TeaField.objects.filter(is_active=True).aggregate(total=Sum('area'))['total'] or 0

        # 订单统计
        total_orders = Order.objects.count()
        paid_orders = Order.objects.filter(status='paid').count()
        today_orders = Order.objects.filter(created_at__date=today).count()

        # 收益统计
        total_revenue = Order.objects.filter(status='paid').aggregate(total=Sum('total_amount'))['total'] or 0
        monthly_revenue = Order.objects.filter(
            status='paid',
            created_at__date__gte=this_month_start
        ).aggregate(total=Sum('total_amount'))['total'] or 0
        daily_revenue = Order.objects.filter(
            status='paid',
            created_at__date=today
        ).aggregate(total=Sum('total_amount'))['total'] or 0

        # 设备统计
        total_devices = SensorDevice.objects.count() if SensorDevice.objects.exists() else 200
        online_devices = SensorDevice.objects.filter(status='online').count() if SensorDevice.objects.exists() else 196
        offline_devices = SensorDevice.objects.filter(status='offline').count() if SensorDevice.objects.exists() else 3
        maintenance_devices = SensorDevice.objects.filter(status='maintenance').count() if SensorDevice.objects.exists() else 1

        # 监控数据统计
        total_monitoring_data = MonitoringData.objects.count() if MonitoringData.objects.exists() else 12456
        today_monitoring_count = MonitoringData.objects.filter(recorded_at__date=today).count() if MonitoringData.objects.exists() else 156

        # 认购面积统计
        subscribed_area = Order.objects.filter(status='paid').aggregate(total=Sum('quantity'))['total'] or 0
        subscription_rate = (subscribed_area / total_area * 100) if total_area > 0 else 78

        # 认购排行（真实数据）
        top_purchasers_query = Order.objects.filter(status='paid').values(
            'contact_name'
        ).annotate(
            total_area=Sum('quantity')
        ).order_by('-total_area')[:5]

        # 如果没有真实数据，使用模拟数据
        if not top_purchasers_query.exists():
            top_purchasers_data = [
                {'name': '张先生', 'area': 15.6},
                {'name': '李女士', 'area': 12.3},
                {'name': '王总', 'area': 10.8},
                {'name': '陈经理', 'area': 9.5},
                {'name': '刘董', 'area': 8.7}
            ]
        else:
            top_purchasers_data = [
                {
                    'name': item['contact_name'] or '匿名用户',
                    'area': float(item['total_area'])
                } for item in top_purchasers_query
            ]

        # 计算一些额外的分析指标
        payment_rate = round(paid_orders / total_orders * 100, 2) if total_orders > 0 else 69.3
        verification_rate = round(verified_users / total_users * 100, 1) if total_users > 0 else 69
        avg_order_value = round(float(total_revenue) / paid_orders, 2) if paid_orders > 0 else 2680

        overview_data = {
            # 总体统计
            'total_area': float(total_area) if total_area > 0 else 200,
            'purchased_area': float(subscribed_area) if subscribed_area > 0 else 156,
            'purchase_rate': round(subscription_rate, 1),
            'active_users': total_users if total_users > 0 else 1234,
            'verified_users': verified_users,
            'today_new_users': today_new_users,
            'today_orders': today_orders,
            'total_orders': total_orders,
            'paid_orders': paid_orders,

            # 收益统计
            'daily_revenue': float(daily_revenue) if daily_revenue > 0 else 12456,
            'monthly_revenue': float(monthly_revenue) if monthly_revenue > 0 else 345678,
            'yearly_revenue': float(total_revenue) if total_revenue > 0 else 2345678,
            'avg_revenue_per_acre': round(float(total_revenue) / float(total_area), 2) if total_area > 0 and total_revenue > 0 else 1234,

            # 设备状态
            'cameras_online': online_devices,
            'cameras_total': total_devices,
            'weather_stations_online': online_devices,
            'weather_stations_total': total_devices,
            'sensors_abnormal': offline_devices + maintenance_devices,
            'devices_offline': offline_devices,
            'device_online_rate': round(online_devices / total_devices * 100, 1) if total_devices > 0 else 98,

            # 环境数据（模拟数据，可以后续接入真实传感器）
            'avg_temperature': round(random.uniform(20, 28), 1),
            'avg_humidity': random.randint(60, 80),
            'soil_ph': round(random.uniform(6.5, 7.2), 1),
            'light_intensity': random.randint(8000, 9000),

            # 监控数据
            'monitoring_data_count': total_monitoring_data,
            'today_monitoring_count': today_monitoring_count,

            # 茶地统计
            'total_fields': total_fields if total_fields > 0 else 50,
            'available_fields': available_fields if available_fields > 0 else 22,
            'sold_fields': (total_fields - available_fields) if total_fields > 0 else 28,

            # 业务分析指标
            'payment_rate': payment_rate,
            'verification_rate': verification_rate,
            'avg_order_value': avg_order_value,
            'user_activity': round(today_new_users / total_users * 100 * 30, 1) if total_users > 0 else 85.5,  # 模拟活跃度
            'revenue_growth': round(random.uniform(3.0, 8.0), 1),  # 模拟增长率

            # 认购排行
            'top_purchasers': top_purchasers_data
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': overview_data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取数据大屏总览失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def dashboard_analytics(request):
    """获取数据大屏分析数据"""
    try:
        from datetime import datetime, timedelta
        from django.db.models import Sum, Count, Avg, Q
        from apps.users.models import User
        from apps.orders.models import Order, Payment, Earnings
        from apps.tea_fields.models import TeaField

        today = datetime.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)

        # 用户分析
        user_stats = {
            'total_users': User.objects.count(),
            'verified_users': User.objects.filter(is_verified=True).count(),
            'new_users_today': User.objects.filter(date_joined__date=today).count(),
            'new_users_week': User.objects.filter(date_joined__date__gte=week_ago).count(),
            'new_users_month': User.objects.filter(date_joined__date__gte=month_ago).count(),
        }

        # 订单转化分析
        order_funnel = {
            'total_orders': Order.objects.count(),
            'pending_orders': Order.objects.filter(status='pending_payment').count(),
            'paid_orders': Order.objects.filter(status='paid').count(),
            'completed_orders': Order.objects.filter(status='completed').count(),
            'cancelled_orders': Order.objects.filter(status='cancelled').count(),
        }

        # 计算转化率
        if order_funnel['total_orders'] > 0:
            order_funnel['payment_rate'] = round(order_funnel['paid_orders'] / order_funnel['total_orders'] * 100, 2)
            order_funnel['completion_rate'] = round(order_funnel['completed_orders'] / order_funnel['total_orders'] * 100, 2)
        else:
            order_funnel['payment_rate'] = 0
            order_funnel['completion_rate'] = 0

        # 收益趋势分析（最近7天）
        revenue_trend = []
        for i in range(7):
            date = today - timedelta(days=i)
            daily_revenue = Order.objects.filter(
                status='paid',
                created_at__date=date
            ).aggregate(total=Sum('total_amount'))['total'] or 0
            revenue_trend.append({
                'date': date.strftime('%m-%d'),
                'revenue': float(daily_revenue)
            })
        revenue_trend.reverse()

        # 订单趋势分析（最近7天）
        order_trend = []
        for i in range(7):
            date = today - timedelta(days=i)
            daily_orders = Order.objects.filter(created_at__date=date).count()
            order_trend.append({
                'date': date.strftime('%m-%d'),
                'orders': daily_orders
            })
        order_trend.reverse()

        # 热门茶地排行
        popular_fields = Order.objects.filter(status='paid').values(
            'tea_field__name'
        ).annotate(
            total_orders=Count('id'),
            total_area=Sum('quantity'),
            total_revenue=Sum('total_amount')
        ).order_by('-total_revenue')[:10]

        # 用户地域分布（模拟数据，可以后续根据用户profile完善）
        region_distribution = [
            {'name': '浙江', 'value': 35.2, 'users': 420},
            {'name': '江苏', 'value': 28.6, 'users': 340},
            {'name': '上海', 'value': 15.8, 'users': 188},
            {'name': '广东', 'value': 12.4, 'users': 148},
            {'name': '其他', 'value': 8.0, 'users': 95},
        ]

        analytics_data = {
            'user_stats': user_stats,
            'order_funnel': order_funnel,
            'revenue_trend': revenue_trend,
            'order_trend': order_trend,
            'popular_fields': [
                {
                    'name': item['tea_field__name'] or '未知茶地',
                    'orders': item['total_orders'],
                    'area': float(item['total_area'] or 0),
                    'revenue': float(item['total_revenue'] or 0)
                } for item in popular_fields
            ],
            'region_distribution': region_distribution
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': analytics_data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取分析数据失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def field_data(request):
    """获取茶地详细数据"""
    try:
        from apps.tea_fields.models import TeaFieldRegion, TeaFieldPlot
        import random

        # 尝试从数据库获取真实数据
        regions = TeaFieldRegion.objects.filter(status='active')

        if regions.exists():
            # 使用真实数据
            field_plots = []
            total_plots = 0

            for region in regions:
                plots = region.plots.all()
                total_plots += plots.count()

                for plot in plots:
                    plot_data = {
                        'plot_id': plot.id,
                        'plot_number': plot.plot_number,
                        'coordinates': plot.coordinates,
                        'region_name': region.name,
                        'region_type': region.region_type,
                        'latitude': float(plot.latitude),
                        'longitude': float(plot.longitude),
                        'area': float(plot.area),
                        'status': plot.status,
                        'price': float(plot.price),
                        'owner_type': '企业客户' if plot.owner and hasattr(plot.owner, 'is_enterprise') and plot.owner.is_enterprise else '个人客户' if plot.owner else '无',
                        'owner_name': plot.owner.username if plot.owner else '无',
                        'camera_status': plot.camera_status,
                        'weather_station_status': plot.weather_station_status,
                        'temperature': float(plot.temperature) if plot.temperature else round(random.uniform(20, 28), 1),
                        'humidity': plot.humidity if plot.humidity else random.randint(60, 80),
                        'soil_ph': float(plot.soil_ph) if plot.soil_ph else round(random.uniform(6.5, 7.2), 1),
                        'light_intensity': plot.light_intensity if plot.light_intensity else random.randint(7000, 9000),
                        'last_update': plot.updated_at.isoformat()
                    }
                    field_plots.append(plot_data)

            return Response({
                'code': 200,
                'message': '获取成功',
                'data': {
                    'total_plots': total_plots,
                    'plots': field_plots,
                    'regions': [
                        {
                            'id': region.id,
                            'name': region.name,
                            'region_type': region.region_type,
                            'boundary_coordinates': region.boundary_coordinates,
                            'center_latitude': float(region.center_latitude),
                            'center_longitude': float(region.center_longitude),
                            'total_area': float(region.total_area),
                            'sold_rate': region.sold_rate
                        } for region in regions
                    ]
                }
            })
        else:
            # 如果没有真实数据，生成模拟数据
            field_plots = []
            for i in range(200):
                plot_id = i + 1
                row = i // 20 + 1
                col = i % 20 + 1

                # 随机生成认购状态
                random_val = random.random()
                if random_val < 0.78:  # 78%认购率
                    if random_val < 0.1:
                        status = 'premium'
                        owner_type = '企业客户'
                        owner_name = f'企业{random.randint(1, 50)}'
                    else:
                        status = 'occupied'
                        owner_type = '个人客户'
                        owner_name = f'用户{random.randint(1, 1000)}'
                else:
                    status = 'available'
                    owner_type = '无'
                    owner_name = '无'

                plot_data = {
                    'plot_id': plot_id,
                    'coordinates': f'{row}-{col}',
                    'row': row,
                    'col': col,
                    'status': status,
                    'owner_type': owner_type,
                    'owner_name': owner_name,
                    'camera_status': 'online' if random.random() > 0.01 else 'offline',
                    'weather_station_status': 'online' if random.random() > 0.02 else 'offline',
                    'temperature': round(random.uniform(20, 28), 1),
                    'humidity': random.randint(60, 80),
                    'soil_ph': round(random.uniform(6.5, 7.2), 1),
                    'light_intensity': random.randint(7000, 9000),
                    'last_update': datetime.now().isoformat()
                }

                field_plots.append(plot_data)

            return Response({
                'code': 200,
                'message': '获取成功',
                'data': {
                    'total_plots': 200,
                    'plots': field_plots,
                    'regions': []
                }
            })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取茶地数据失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def real_time_data(request):
    """获取实时数据流"""
    try:
        import random
        from datetime import datetime

        # 生成实时数据流
        events = [
            '地块温度更新',
            '用户认购地块',
            '湿度异常告警',
            '收益结算完成',
            '设备离线告警',
            '土壤pH检测',
            '新用户注册',
            '光照强度更新',
            '摄像头状态更新',
            '气象站数据同步'
        ]

        real_time_events = []
        for i in range(10):  # 生成10条最新事件
            event_type = random.choice(events)
            plot_id = random.randint(1, 200)

            if '温度' in event_type:
                detail = f'{round(random.uniform(20, 28), 1)}°C'
            elif '湿度' in event_type:
                detail = f'{random.randint(60, 90)}%'
            elif '收益' in event_type:
                detail = f'¥{random.randint(100, 2000)}'
            elif 'pH' in event_type:
                detail = f'{round(random.uniform(6.0, 7.5), 1)}'
            elif '光照' in event_type:
                detail = f'{random.randint(7000, 9000)}lx'
            else:
                detail = '正常'

            event_data = {
                'timestamp': datetime.now().isoformat(),
                'event_type': event_type,
                'plot_id': plot_id,
                'detail': detail,
                'message': f'地块{plot_id} {event_type}: {detail}'
            }

            real_time_events.append(event_data)

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'events': real_time_events,
                'timestamp': datetime.now().isoformat()
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取实时数据失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 三级联动API - 茶园列表
@api_view(['GET'])
@permission_classes([AllowAny])
def tea_gardens_list(request):
    """获取茶园列表"""
    try:
        from apps.tea_fields.models import TeaGarden
        from .serializers import TeaGardenSerializer

        # 获取所有启用的茶园
        gardens = TeaGarden.objects.filter(status='active').prefetch_related('images').order_by('garden_code')

        # 使用序列化器处理数据
        serializer = TeaGardenSerializer(gardens, many=True, context={'request': request})

        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取茶园列表失败: {str(e)}',
            'data': []
        })


# 茶地区域管理API
@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
def tea_field_regions(request):
    """茶地区域管理"""
    if request.method == 'GET':
        try:
            from apps.tea_fields.models import TeaFieldRegion
            from .serializers import TeaFieldRegionSerializer

            # 获取查询参数
            garden_id = request.GET.get('garden')

            # 构建查询
            regions = TeaFieldRegion.objects.filter(status='active').select_related('garden').prefetch_related('images')

            # 根据茶园筛选
            if garden_id:
                regions = regions.filter(garden=garden_id)

            regions = regions.order_by('sort_order', '-created_at')

            # 使用序列化器处理数据
            serializer = TeaFieldRegionSerializer(regions, many=True, context={'request': request})

            return Response({
                'code': 200,
                'message': '获取成功',
                'data': serializer.data
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取茶地区域失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    elif request.method == 'POST':
        try:
            from apps.tea_fields.models import TeaFieldRegion

            # 创建新区域
            region_data = request.data

            region = TeaFieldRegion.objects.create(
                name=region_data.get('name'),
                description=region_data.get('description', ''),
                region_type=region_data.get('region_type', 'standard'),
                boundary_coordinates=region_data.get('boundary_coordinates', []),
                center_latitude=region_data.get('center_latitude'),
                center_longitude=region_data.get('center_longitude'),
                total_area=region_data.get('total_area', 0),
                available_area=region_data.get('available_area', 0),
                price_per_acre=region_data.get('price_per_acre', 0),
                soil_quality=region_data.get('soil_quality', ''),
                water_source=region_data.get('water_source', ''),
                sunlight_condition=region_data.get('sunlight_condition', ''),
                camera_count=region_data.get('camera_count', 0),
                weather_station_count=region_data.get('weather_station_count', 0),
                sensor_count=region_data.get('sensor_count', 0),
                plot_count=region_data.get('plot_count', 0)
            )

            return Response({
                'code': 200,
                'message': '区域创建成功',
                'data': {
                    'id': region.id,
                    'name': region.name
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'创建茶地区域失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 三级联动API - 茶地块列表
@api_view(['GET'])
@permission_classes([AllowAny])
def tea_field_plots(request):
    """获取茶地块列表"""
    try:
        from apps.tea_fields.models import TeaFieldPlot

        # 获取查询参数
        region_id = request.GET.get('region')

        # 构建查询
        queryset = TeaFieldPlot.objects.filter(status='available')

        if region_id:
            queryset = queryset.filter(region_id=region_id)

        plots = queryset.select_related('region', 'region__garden').prefetch_related('images').order_by('plot_number')

        # 使用序列化器处理数据
        from .serializers import TeaFieldPlotSerializer
        serializer = TeaFieldPlotSerializer(plots, many=True, context={'request': request})

        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取茶地块列表失败: {str(e)}',
            'data': []
        })


@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([AllowAny])
def tea_field_region_detail(request, region_id):
    """茶地区域详情"""
    try:
        from apps.tea_fields.models import TeaFieldRegion

        region = TeaFieldRegion.objects.get(id=region_id)

        if request.method == 'GET':
            region_data = {
                'id': region.id,
                'name': region.name,
                'description': region.description,
                'region_type': region.region_type,
                'status': region.status,
                'boundary_coordinates': region.boundary_coordinates,
                'center_latitude': float(region.center_latitude),
                'center_longitude': float(region.center_longitude),
                'total_area': float(region.total_area),
                'available_area': float(region.available_area),
                'price_per_acre': float(region.price_per_acre),
                'soil_quality': region.soil_quality,
                'water_source': region.water_source,
                'sunlight_condition': region.sunlight_condition,
                'camera_count': region.camera_count,
                'weather_station_count': region.weather_station_count,
                'sensor_count': region.sensor_count,
                'plot_count': region.plot_count,
                'sold_plots': region.sold_plots,
                'sold_rate': region.sold_rate,
                'available_rate': region.available_rate,
                'is_featured': region.is_featured,
                'created_at': region.created_at.isoformat(),
                'plots': []
            }

            # 获取区域内的地块信息
            plots = region.plots.all().order_by('plot_number')
            for plot in plots:
                plot_data = {
                    'id': plot.id,
                    'plot_number': plot.plot_number,
                    'coordinates': plot.coordinates,
                    'latitude': float(plot.latitude),
                    'longitude': float(plot.longitude),
                    'area': float(plot.area),
                    'status': plot.status,
                    'price': float(plot.price),
                    'owner': plot.owner.username if plot.owner else None,
                    'purchase_date': plot.purchase_date.isoformat() if plot.purchase_date else None,
                    'camera_status': plot.camera_status,
                    'weather_station_status': plot.weather_station_status,
                    'temperature': float(plot.temperature) if plot.temperature else None,
                    'humidity': plot.humidity,
                    'soil_ph': float(plot.soil_ph) if plot.soil_ph else None,
                    'light_intensity': plot.light_intensity
                }
                region_data['plots'].append(plot_data)

            return Response({
                'code': 200,
                'message': '获取成功',
                'data': region_data
            })

        elif request.method == 'PUT':
            # 更新区域信息
            update_data = request.data

            for field in ['name', 'description', 'region_type', 'status', 'boundary_coordinates',
                         'center_latitude', 'center_longitude', 'total_area', 'available_area',
                         'price_per_acre', 'soil_quality', 'water_source', 'sunlight_condition',
                         'camera_count', 'weather_station_count', 'sensor_count', 'plot_count',
                         'sold_plots', 'is_featured']:
                if field in update_data:
                    setattr(region, field, update_data[field])

            region.save()

            return Response({
                'code': 200,
                'message': '区域更新成功'
            })

        elif request.method == 'DELETE':
            region.delete()

            return Response({
                'code': 200,
                'message': '区域删除成功'
            })

    except TeaFieldRegion.DoesNotExist:
        return Response({
            'code': 404,
            'message': '区域不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 3D沙盘相关API ====================

@api_view(['GET'])
@permission_classes([AllowAny])
def sandbox_tea_data(request):
    """获取3D沙盘茶地数据"""
    try:
        from apps.tea_fields.models import TeaGarden, TeaFieldRegion, TeaFieldPlot

        # 获取所有茶园数据
        gardens = TeaGarden.objects.all()
        regions = TeaFieldRegion.objects.select_related('garden').all()
        plots = TeaFieldPlot.objects.select_related('region', 'region__garden').all()

        # 构建3D数据结构
        tea_data = {
            'gardens': [],
            'regions': [],
            'plots': [],
            'bounds': {
                'min_lat': None,
                'max_lat': None,
                'min_lng': None,
                'max_lng': None
            }
        }

        # 收集边界坐标
        all_lats = []
        all_lngs = []

        # 处理茶园数据
        for garden in gardens:
            if garden.center_latitude and garden.center_longitude:
                all_lats.append(float(garden.center_latitude))
                all_lngs.append(float(garden.center_longitude))

            tea_data['gardens'].append({
                'id': garden.id,
                'code': garden.garden_code,
                'name': garden.garden_name,
                'type': garden.garden_type,
                'location': garden.location,
                'total_area': float(garden.total_area) if garden.total_area else 0,
                'latitude': float(garden.center_latitude) if garden.center_latitude else None,
                'longitude': float(garden.center_longitude) if garden.center_longitude else None,
                'tea_varieties': garden.tea_varieties,
                'established_year': garden.established_year,
                'status': garden.status
            })

        # 处理分区数据
        for region in regions:
            if region.center_latitude and region.center_longitude:
                all_lats.append(float(region.center_latitude))
                all_lngs.append(float(region.center_longitude))

            tea_data['regions'].append({
                'id': region.id,
                'code': region.full_code,
                'name': region.name,
                'garden_id': region.garden.id,
                'garden_name': region.garden.garden_name,
                'tea_variety': region.tea_variety,
                'tea_grade': region.tea_grade,
                'total_area': float(region.total_area) if region.total_area else 0,
                'available_area': float(region.available_area) if region.available_area else 0,
                'price_per_acre': float(region.price_per_acre) if region.price_per_acre else 0,
                'latitude': float(region.center_latitude) if region.center_latitude else None,
                'longitude': float(region.center_longitude) if region.center_longitude else None,
                'plot_count': region.plot_count,
                'sold_plots': region.sold_plots,
                'sold_rate': region.sold_rate,
                'status': region.status
            })

        # 处理地块数据
        for plot in plots:
            if plot.latitude and plot.longitude:
                all_lats.append(float(plot.latitude))
                all_lngs.append(float(plot.longitude))

            tea_data['plots'].append({
                'id': plot.id,
                'code': plot.full_code,
                'name': plot.plot_name or f'{plot.full_code}地块',
                'region_id': plot.region.id,
                'region_name': plot.region.name,
                'garden_id': plot.region.garden.id,
                'garden_name': plot.region.garden.garden_name,
                'plot_type': plot.plot_type,
                'area': float(plot.area) if plot.area else 0,
                'price': float(plot.price) if plot.price else 0,
                'status': plot.status,
                'latitude': float(plot.latitude) if plot.latitude else None,
                'longitude': float(plot.longitude) if plot.longitude else None,
                'owner': plot.owner.username if plot.owner else None,
                'purchase_date': plot.purchase_date.isoformat() if plot.purchase_date else None,
                'temperature': float(plot.temperature) if plot.temperature else None,
                'humidity': plot.humidity,
                'soil_ph': float(plot.soil_ph) if plot.soil_ph else None
            })

        # 计算边界
        if all_lats and all_lngs:
            tea_data['bounds'] = {
                'min_lat': min(all_lats),
                'max_lat': max(all_lats),
                'min_lng': min(all_lngs),
                'max_lng': max(all_lngs),
                'center_lat': sum(all_lats) / len(all_lats),
                'center_lng': sum(all_lngs) / len(all_lngs)
            }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': tea_data
        })

    except Exception as e:
        logger.error(f'获取3D沙盘茶地数据失败: {str(e)}')
        return Response({
            'code': 500,
            'message': f'获取数据失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def sandbox_real_time_data(request):
    """获取3D沙盘实时数据"""
    try:
        from apps.tea_fields.models import TeaFieldPlot
        from apps.monitoring.models import MonitoringData

        # 获取最新的监控数据
        latest_data = MonitoringData.objects.select_related('device').order_by('-recorded_at')[:100]

        # 获取地块状态统计
        plot_stats = TeaFieldPlot.objects.aggregate(
            total=Count('id'),
            available=Count('id', filter=Q(status='available')),
            purchased=Count('id', filter=Q(status='purchased')),
            reserved=Count('id', filter=Q(status='reserved')),
            maintenance=Count('id', filter=Q(status='maintenance'))
        )

        # 获取面积和价值统计
        area_stats = TeaFieldPlot.objects.aggregate(
            total_area=Sum('area'),
            total_value=Sum('price'),
            avg_price=Avg('price')
        )

        # 构建实时数据
        real_time_data = {
            'timestamp': timezone.now().isoformat(),
            'plot_statistics': {
                'total': plot_stats['total'] or 0,
                'available': plot_stats['available'] or 0,
                'purchased': plot_stats['purchased'] or 0,
                'reserved': plot_stats['reserved'] or 0,
                'maintenance': plot_stats['maintenance'] or 0,
                'purchase_rate': round((plot_stats['purchased'] or 0) / (plot_stats['total'] or 1) * 100, 1)
            },
            'area_statistics': {
                'total_area': float(area_stats['total_area'] or 0),
                'total_value': float(area_stats['total_value'] or 0),
                'avg_price': float(area_stats['avg_price'] or 0)
            },
            'monitoring_data': []
        }

        # 处理监控数据
        for data in latest_data:
            real_time_data['monitoring_data'].append({
                'device_id': data.device.id,
                'device_name': data.device.device_name,
                'temperature': data.temperature,
                'humidity': data.humidity,
                'soil_moisture': data.soil_moisture,
                'ph_value': data.ph_value,
                'light_intensity': data.light_intensity,
                'timestamp': data.recorded_at.isoformat(),
                'location': {
                    'latitude': float(data.device.latitude) if data.device.latitude else None,
                    'longitude': float(data.device.longitude) if data.device.longitude else None
                }
            })

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': real_time_data
        })

    except Exception as e:
        logger.error(f'获取3D沙盘实时数据失败: {str(e)}')
        return Response({
            'code': 500,
            'message': f'获取实时数据失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
def sandbox_config(request):
    """3D沙盘配置API"""
    try:
        from apps.tea_fields.models import Sandbox3DConfig

        if request.method == 'GET':
            # 获取配置
            config = Sandbox3DConfig.get_default_config()

            return Response({
                'code': 200,
                'message': '获取成功',
                'data': {
                    'id': config.id,
                    'name': config.name,
                    'description': config.description,
                    'scene': {
                        'background_color': config.background_color,
                        'fog_enabled': config.fog_enabled,
                        'fog_color': config.fog_color,
                        'fog_density': config.fog_density
                    },
                    'camera': {
                        'mode': config.camera_mode,
                        'position': config.camera_position,
                        'target': config.camera_target,
                        'fov': config.camera_fov,
                        'near': config.camera_near,
                        'far': config.camera_far
                    },
                    'lighting': {
                        'ambient_light': {
                            'color': config.ambient_light_color,
                            'intensity': config.ambient_light_intensity
                        },
                        'directional_light': {
                            'color': config.directional_light_color,
                            'intensity': config.directional_light_intensity,
                            'position': config.directional_light_position
                        }
                    },
                    'display': {
                        'colors': {
                            'available': config.color_available,
                            'reserved': config.color_reserved,
                            'sold': config.color_sold,
                            'maintenance': config.color_maintenance
                        },
                        'plot_height': config.plot_height,
                        'plot_opacity': config.plot_opacity,
                        'plot_border_width': config.plot_border_width
                    },
                    'interaction': {
                        'enable_zoom': config.enable_zoom,
                        'enable_rotate': config.enable_rotate,
                        'enable_pan': config.enable_pan,
                        'zoom_min': config.zoom_min,
                        'zoom_max': config.zoom_max
                    },
                    'data': {
                        'auto_refresh': config.auto_refresh,
                        'refresh_interval': config.refresh_interval,
                        'show_labels': config.show_labels
                    },
                    'performance': {
                        'enable_shadows': config.enable_shadows,
                        'shadow_quality': config.shadow_quality,
                        'enable_antialiasing': config.enable_antialiasing,
                        'render_distance': config.render_distance
                    },
                    'is_default': config.is_default,
                    'updated_at': config.updated_at.isoformat()
                }
            })

        elif request.method == 'POST':
            # 更新配置
            config = Sandbox3DConfig.get_default_config()

            # 更新配置数据
            if 'scene' in request.data:
                config.scene_config = {**config.scene_config, **request.data['scene']}
            if 'camera' in request.data:
                config.camera_config = {**config.camera_config, **request.data['camera']}
            if 'display' in request.data:
                config.display_config = {**config.display_config, **request.data['display']}
            if 'interaction' in request.data:
                config.interaction_config = {**config.interaction_config, **request.data['interaction']}
            if 'data' in request.data:
                config.data_config = {**config.data_config, **request.data['data']}
            if 'performance' in request.data:
                config.performance_config = {**config.performance_config, **request.data['performance']}

            config.save()

            return Response({
                'code': 200,
                'message': '配置更新成功',
                'data': {
                    'id': config.id,
                    'updated_at': config.updated_at.isoformat()
                }
            })

    except Exception as e:
        logger.error(f'3D沙盘配置API错误: {str(e)}')
        return Response({
            'code': 500,
            'message': f'配置操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def sandbox_statistics(request):
    """获取3D沙盘统计数据"""
    try:
        from apps.tea_fields.models import TeaGarden, TeaFieldRegion, TeaFieldPlot
        from apps.orders.models import Order
        from django.db.models import Sum, Avg, Count
        from datetime import datetime, timedelta

        # 基础统计
        basic_stats = {
            'gardens': TeaGarden.objects.count(),
            'regions': TeaFieldRegion.objects.count(),
            'plots': TeaFieldPlot.objects.count()
        }

        # 地块状态统计
        plot_status = TeaFieldPlot.objects.values('status').annotate(
            count=Count('id'),
            total_area=Sum('area'),
            total_value=Sum('price')
        )

        status_stats = {}
        for item in plot_status:
            status_stats[item['status']] = {
                'count': item['count'],
                'area': float(item['total_area'] or 0),
                'value': float(item['total_value'] or 0)
            }

        # 茶园类型统计
        garden_types = TeaGarden.objects.values('garden_type').annotate(
            count=Count('id'),
            total_area=Sum('total_area')
        )

        type_stats = {}
        for item in garden_types:
            type_stats[item['garden_type']] = {
                'count': item['count'],
                'area': float(item['total_area'] or 0)
            }

        # 价格分布统计
        price_ranges = [
            (0, 1000, '1000以下'),
            (1000, 3000, '1000-3000'),
            (3000, 5000, '3000-5000'),
            (5000, 10000, '5000-10000'),
            (10000, float('inf'), '10000以上')
        ]

        price_stats = {}
        for min_price, max_price, label in price_ranges:
            if max_price == float('inf'):
                count = TeaFieldPlot.objects.filter(price__gte=min_price).count()
            else:
                count = TeaFieldPlot.objects.filter(price__gte=min_price, price__lt=max_price).count()
            price_stats[label] = count

        # 最近30天订单统计
        thirty_days_ago = datetime.now() - timedelta(days=30)
        recent_orders = Order.objects.filter(created_at__gte=thirty_days_ago)

        order_stats = {
            'total_orders': recent_orders.count(),
            'total_amount': float(recent_orders.aggregate(Sum('total_amount'))['total_amount__sum'] or 0),
            'avg_amount': float(recent_orders.aggregate(Avg('total_amount'))['total_amount__avg'] or 0)
        }

        # 地理分布统计
        regions_with_coords = TeaFieldRegion.objects.filter(
            center_latitude__isnull=False,
            center_longitude__isnull=False
        ).values('center_latitude', 'center_longitude', 'total_area')

        geo_stats = {
            'regions_with_location': len(regions_with_coords),
            'coverage_area': sum(float(r['total_area'] or 0) for r in regions_with_coords)
        }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'basic': basic_stats,
                'plot_status': status_stats,
                'garden_types': type_stats,
                'price_distribution': price_stats,
                'recent_orders': order_stats,
                'geographic': geo_stats,
                'updated_at': timezone.now().isoformat()
            }
        })

    except Exception as e:
        logger.error(f'获取3D沙盘统计数据失败: {str(e)}')
        return Response({
            'code': 500,
            'message': f'获取统计数据失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 微信支付调试API
@api_view(['GET'])
@permission_classes([AllowAny])
def wechat_pay_debug(request):
    """微信支付配置调试信息"""
    from apps.system.utils import WechatConfigManager, WechatPayConfigManager

    # 获取微信基础配置
    wechat_config = {
        'app_id': WechatConfigManager.get_app_id(),
        'app_secret_configured': bool(WechatConfigManager.get_app_secret()),
        'login_enabled': WechatConfigManager.is_login_enabled(),
        'auto_register': WechatConfigManager.is_auto_register_enabled(),
        'is_configured': WechatConfigManager.is_configured()
    }

    # 获取微信支付配置调试信息
    wechat_pay_debug = WechatPayConfigManager.get_debug_info()

    # 检查用户openid（如果已登录）
    user_openid_info = None
    if request.user.is_authenticated:
        try:
            from apps.users.models import WechatUser
            wechat_user = WechatUser.objects.get(user=request.user)
            user_openid_info = {
                'has_openid': bool(wechat_user.openid),
                'openid_length': len(wechat_user.openid) if wechat_user.openid else 0
            }
        except:
            user_openid_info = {
                'has_openid': False,
                'error': '用户未绑定微信'
            }

    return Response({
        'code': 200,
        'message': '微信支付调试信息',
        'data': {
            'wechat_config': wechat_config,
            'wechat_pay_config': wechat_pay_debug,
            'user_openid_info': user_openid_info,
            'server_time': timezone.now().isoformat()
        }
    })


# 天地图配置API
@api_view(['GET'])
@permission_classes([AllowAny])
def tianditu_config_api(request):
    """获取天地图配置API"""
    try:
        from django.conf import settings

        # 从settings中获取天地图配置
        map_services = getattr(settings, 'MAP_SERVICES', {})
        tianditu_config = map_services.get('providers', {}).get('tianditu', {})

        # 返回配置信息（不包含敏感信息）
        config = {
            'api_key': tianditu_config.get('api_key', ''),
            'api_url': tianditu_config.get('api_url', 'https://api.tianditu.gov.cn/api'),
            'version': tianditu_config.get('version', '4.0'),
            'coordinate_system': tianditu_config.get('coordinate_system', 'CGCS2000'),
            'satellite_support': tianditu_config.get('satellite_support', True),
            'map_types': tianditu_config.get('map_types', {
                'vec': {'name': '矢量地图', 'layer': 'vec_w'},
                'img': {'name': '影像地图', 'layer': 'img_w'},
                'ter': {'name': '地形图', 'layer': 'ter_w'}
            })
        }

        return Response({
            'code': 200,
            'message': '获取天地图配置成功',
            'data': config
        })

    except Exception as e:
        logger.error(f"获取天地图配置失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取天地图配置失败: {str(e)}',
            'data': None
        })

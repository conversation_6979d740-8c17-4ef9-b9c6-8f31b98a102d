from rest_framework import serializers
from django.contrib.auth import get_user_model
from decimal import Decimal
from apps.users.models import (
    WechatUser, UserProfile, SmsCode, VerificationRecord, BankCardVerification
)
from apps.notifications.models import Notification, NotificationSettings, UserSubscriptionPreference
from apps.tea_fields.models import (
    TeaField, TeaFieldCategory, TeaFieldImage, TeaGarden, TeaFieldRegion, TeaFieldPlot,
    TeaGardenImage, TeaRegionImage, TeaPlotImage
)
from apps.orders.models import Order, Payment, Earnings, Contract, Invoice, WithdrawRequest
from apps.monitoring.models import MonitoringData, SensorDevice, AlertRecord
from apps.system.models import Banner, IconCategory, Icon

User = get_user_model()


# 用户相关序列化器
class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'nickname', 'avatar', 'phone', 'email', 
                 'gender', 'city', 'province', 'country', 'is_verified', 'date_joined']
        read_only_fields = ['id', 'username', 'date_joined']


class WechatUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = WechatUser
        fields = ['openid', 'unionid', 'nickname', 'avatar_url', 'gender', 'city', 'province', 'country']
        read_only_fields = ['openid', 'unionid']


class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserProfile
        fields = ['real_name', 'total_investment', 'total_earnings', 'risk_level']
        read_only_fields = ['total_investment', 'total_earnings']


class UserDetailSerializer(serializers.ModelSerializer):
    profile = UserProfileSerializer(read_only=True)
    wechat_info = WechatUserSerializer(source='wechatuserinfo', read_only=True)
    
    class Meta:
        model = User
        fields = ['id', 'username', 'nickname', 'avatar', 'phone', 'email',
                 'gender', 'city', 'province', 'country', 'is_verified', 
                 'date_joined', 'profile', 'wechat_info']
        read_only_fields = ['id', 'username', 'date_joined']


# 短信验证码序列化器
class SmsCodeSerializer(serializers.ModelSerializer):
    class Meta:
        model = SmsCode
        fields = ['phone', 'purpose']

    def validate_phone(self, value):
        """验证手机号格式"""
        import re
        if not re.match(r'^1[3-9]\d{9}$', value):
            raise serializers.ValidationError('手机号格式不正确')
        return value


class PhoneLoginSerializer(serializers.Serializer):
    """手机验证码登录序列化器"""
    phone = serializers.CharField(max_length=11)
    code = serializers.CharField(max_length=6)

    def validate_phone(self, value):
        import re
        if not re.match(r'^1[3-9]\d{9}$', value):
            raise serializers.ValidationError('手机号格式不正确')
        return value


# 通知设置序列化器
class NotificationSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotificationSettings
        fields = ['push_enabled', 'sms_enabled', 'email_enabled',
                 'system_notifications', 'order_notifications',
                 'earnings_notifications', 'promotion_notifications']


# 用户订阅偏好序列化器
class UserSubscriptionPreferenceSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserSubscriptionPreference
        fields = ['message_category', 'is_enabled', 'frequency', 'priority_threshold',
                 'quiet_hours_start', 'quiet_hours_end']

    def to_representation(self, instance):
        data = super().to_representation(instance)
        # 添加分类显示名称
        data['category_display'] = instance.get_message_category_display()
        return data


# 用户通知序列化器
class UserNotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = ['id', 'title', 'content', 'type', 'extra_data', 'is_read',
                 'created_at', 'read_at']
        read_only_fields = ['id', 'created_at', 'read_at']


# 身份验证序列化器
class VerificationRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = VerificationRecord
        fields = ['id', 'type', 'status', 'real_name', 'id_card', 'phone',
                 'id_card_front', 'id_card_back', 'company_name', 'credit_code',
                 'legal_person', 'contact_name', 'contact_phone', 'business_license',
                 'review_time', 'reject_reason', 'created_at', 'updated_at']
        read_only_fields = ['id', 'status', 'review_time', 'reject_reason',
                           'created_at', 'updated_at']


# 银行卡认证序列化器
class BankCardVerificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = BankCardVerification
        fields = ['id', 'bank_name', 'card_number', 'card_holder_name', 'card_type',
                 'phone', 'status', 'review_time', 'reject_reason', 'created_at', 'updated_at']
        read_only_fields = ['id', 'status', 'review_time', 'reject_reason',
                           'created_at', 'updated_at']

    def validate_card_number(self, value):
        """验证银行卡号格式"""
        import re
        if not re.match(r'^\d{16,19}$', value):
            raise serializers.ValidationError('银行卡号格式不正确')
        return value

    def validate_phone(self, value):
        """验证手机号格式"""
        import re
        if not re.match(r'^1[3-9]\d{9}$', value):
            raise serializers.ValidationError('手机号格式不正确')
        return value


# 认证状态汇总序列化器
class VerificationStatusSerializer(serializers.Serializer):
    """用户认证状态汇总"""
    personal_verified = serializers.BooleanField(default=False)
    enterprise_verified = serializers.BooleanField(default=False)
    bank_card_verified = serializers.BooleanField(default=False)
    verification_progress = serializers.IntegerField(default=0)  # 认证进度百分比

    # 详细认证信息
    personal_info = VerificationRecordSerializer(required=False, allow_null=True)
    enterprise_info = VerificationRecordSerializer(required=False, allow_null=True)
    bank_card_info = BankCardVerificationSerializer(required=False, allow_null=True)


# 茶地相关序列化器
class TeaFieldCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = TeaFieldCategory
        fields = ['id', 'name', 'description', 'sort_order']


class TeaFieldImageSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()

    class Meta:
        model = TeaFieldImage
        fields = ['id', 'image', 'image_url', 'title', 'description', 'is_cover', 'sort_order']

    def get_image(self, obj):
        """获取图片完整URL（重写image字段）"""
        if obj.image:
            return f"https://teabuy.yizhangkj.com{obj.image.url}"
        return None

    def get_image_url(self, obj):
        """获取图片完整URL"""
        if obj.image:
            # 始终使用生产域名，确保前端能正确访问
            return f"https://teabuy.yizhangkj.com{obj.image.url}"
        return None


class TeaFieldListSerializer(serializers.ModelSerializer):
    category = TeaFieldCategorySerializer(read_only=True)
    cover_image = serializers.SerializerMethodField()
    
    class Meta:
        model = TeaField
        fields = ['id', 'name', 'description', 'category', 'price', 'area', 
                 'location', 'expected_return', 'status', 'cover_image', 
                 'availability_rate', 'sold_rate', 'view_count', 'purchase_count']
    
    def get_cover_image(self, obj):
        cover_image = obj.images.filter(is_cover=True).first()
        if cover_image:
            return TeaFieldImageSerializer(cover_image, context=self.context).data
        return None


class TeaFieldDetailSerializer(serializers.ModelSerializer):
    category = TeaFieldCategorySerializer(read_only=True)
    images = serializers.SerializerMethodField()
    monitoring_data = serializers.SerializerMethodField()

    class Meta:
        model = TeaField
        fields = ['id', 'name', 'description', 'category', 'price', 'area',
                 'location', 'detailed_location', 'latitude', 'longitude', 'altitude',
                 'soil_type', 'climate', 'harvest_season', 'tea_variety',
                 'expected_return', 'min_investment', 'max_investment',
                 'status', 'total_area', 'available_area', 'sold_area',
                 'availability_rate', 'sold_rate', 'view_count', 'purchase_count',
                 'total_revenue', 'is_featured', 'images', 'monitoring_data', 'created_at']


# ==================== 三级导航序列化器 ====================

class TeaGardenImageSerializer(serializers.ModelSerializer):
    """茶园图片序列化器"""
    image_url = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()

    class Meta:
        model = TeaGardenImage
        fields = ['id', 'image', 'image_url', 'title', 'description', 'image_type', 'is_cover', 'sort_order']

    def get_image(self, obj):
        """获取图片完整URL（重写image字段）"""
        if obj.image:
            return f"https://teabuy.yizhangkj.com{obj.image.url}"
        return None

    def get_image_url(self, obj):
        """获取图片完整URL"""
        if obj.image:
            return f"https://teabuy.yizhangkj.com{obj.image.url}"
        return None


class TeaRegionImageSerializer(serializers.ModelSerializer):
    """茶区域图片序列化器"""
    image_url = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()

    class Meta:
        model = TeaRegionImage
        fields = ['id', 'image', 'image_url', 'title', 'description', 'image_type', 'is_cover', 'sort_order']

    def get_image(self, obj):
        """获取图片完整URL（重写image字段）"""
        if obj.image:
            return f"https://teabuy.yizhangkj.com{obj.image.url}"
        return None

    def get_image_url(self, obj):
        """获取图片完整URL"""
        if obj.image:
            return f"https://teabuy.yizhangkj.com{obj.image.url}"
        return None


class TeaPlotImageSerializer(serializers.ModelSerializer):
    """茶地块图片序列化器"""
    image_url = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()

    class Meta:
        model = TeaPlotImage
        fields = ['id', 'image', 'image_url', 'title', 'description', 'image_type', 'is_cover', 'sort_order']

    def get_image(self, obj):
        """获取图片完整URL（重写image字段）"""
        if obj.image:
            return f"https://teabuy.yizhangkj.com{obj.image.url}"
        return None

    def get_image_url(self, obj):
        """获取图片完整URL"""
        if obj.image:
            return f"https://teabuy.yizhangkj.com{obj.image.url}"
        return None


class TeaGardenSerializer(serializers.ModelSerializer):
    """茶园序列化器"""
    cover_image = serializers.SerializerMethodField()
    images = serializers.SerializerMethodField()

    class Meta:
        model = TeaGarden
        fields = ['id', 'garden_code', 'garden_name', 'description', 'garden_type',
                 'location', 'total_area', 'center_latitude', 'center_longitude',
                 'established_year', 'cover_image', 'images', 'created_at']

    def get_cover_image(self, obj):
        """获取封面图片"""
        cover_image = obj.images.filter(is_cover=True).first()
        if cover_image:
            return TeaGardenImageSerializer(cover_image, context=self.context).data
        return None

    def get_images(self, obj):
        """获取所有图片"""
        images = obj.images.all().order_by('sort_order', '-created_at')
        return TeaGardenImageSerializer(images, many=True, context=self.context).data


class TeaFieldRegionSerializer(serializers.ModelSerializer):
    """茶区域序列化器"""
    cover_image = serializers.SerializerMethodField()
    images = serializers.SerializerMethodField()
    garden = TeaGardenSerializer(read_only=True)

    class Meta:
        model = TeaFieldRegion
        fields = ['id', 'zone_code', 'name', 'description', 'region_type',
                 'total_area', 'available_area', 'price_per_acre', 'tea_variety',
                 'center_latitude', 'center_longitude', 'garden', 'full_code',
                 'cover_image', 'images', 'created_at']

    def get_cover_image(self, obj):
        """获取封面图片"""
        cover_image = obj.images.filter(is_cover=True).first()
        if cover_image:
            return TeaRegionImageSerializer(cover_image, context=self.context).data
        return None

    def get_images(self, obj):
        """获取所有图片"""
        images = obj.images.all().order_by('sort_order', '-created_at')
        return TeaRegionImageSerializer(images, many=True, context=self.context).data


class TeaFieldPlotSerializer(serializers.ModelSerializer):
    """茶地块序列化器"""
    cover_image = serializers.SerializerMethodField()
    images = serializers.SerializerMethodField()
    region = TeaFieldRegionSerializer(read_only=True)
    expected_return = serializers.SerializerMethodField()
    available_area = serializers.SerializerMethodField()
    location = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    main_image = serializers.SerializerMethodField()

    # 添加环境信息字段
    altitude = serializers.SerializerMethodField()
    soil_type = serializers.SerializerMethodField()
    climate = serializers.SerializerMethodField()
    harvest_season = serializers.SerializerMethodField()
    water_source = serializers.SerializerMethodField()
    tea_variety = serializers.SerializerMethodField()
    management_team = serializers.SerializerMethodField()
    organic_certified = serializers.SerializerMethodField()
    insurance_covered = serializers.SerializerMethodField()

    class Meta:
        model = TeaFieldPlot
        fields = ['id', 'plot_number', 'plot_name', 'plot_type', 'coordinates',
                 'latitude', 'longitude', 'area', 'status', 'price', 'region',
                 'full_code', 'cover_image', 'images', 'created_at', 'expected_return',
                 'available_area', 'location', 'description', 'main_image',
                 'altitude', 'soil_type', 'climate', 'harvest_season', 'water_source',
                 'tea_variety', 'management_team', 'organic_certified', 'insurance_covered',
                 'rich_description']

    def get_cover_image(self, obj):
        """获取封面图片"""
        cover_image = obj.images.filter(is_cover=True).first()
        if cover_image:
            return TeaPlotImageSerializer(cover_image, context=self.context).data
        return None

    def get_images(self, obj):
        """获取所有图片"""
        images = obj.images.all().order_by('sort_order', '-created_at')
        return TeaPlotImageSerializer(images, many=True, context=self.context).data

    def get_expected_return(self, obj):
        """获取预期收益率"""
        # 根据地块类型返回不同的收益率
        if obj.plot_type == 'premium':
            return 12.0
        elif obj.plot_type == 'center':
            return 10.0
        elif obj.plot_type == 'standard':
            return 8.0
        else:
            return 8.0  # 默认收益率

    def get_available_area(self, obj):
        """获取可认购面积"""
        if obj.status == 'available':
            return float(obj.area)
        return 0.0

    def get_location(self, obj):
        """获取位置信息"""
        if obj.region and obj.region.garden:
            return obj.region.garden.location or '未知位置'
        return '未知位置'

    def get_description(self, obj):
        """获取描述信息"""
        return f"{obj.plot_name}，编号：{obj.plot_number}，类型：{obj.get_plot_type_display()}"

    def get_altitude(self, obj):
        """获取海拔高度"""
        # 从茶园信息获取，如果没有则返回默认值
        if obj.region and obj.region.garden:
            return getattr(obj.region.garden, 'altitude', None) or 1700
        return 1700

    def get_soil_type(self, obj):
        """获取土壤类型"""
        if obj.region:
            return obj.region.soil_quality or '红壤'
        return '红壤'

    def get_climate(self, obj):
        """获取气候类型"""
        if obj.region and obj.region.garden:
            return getattr(obj.region.garden, 'climate_condition', None) or '亚热带季风气候'
        return '亚热带季风气候'

    def get_harvest_season(self, obj):
        """获取采摘季节"""
        if obj.region:
            return obj.region.harvest_season or '春季、秋季'
        return '春季、秋季'

    def get_water_source(self, obj):
        """获取水源情况"""
        if obj.region:
            return obj.region.water_source or '山泉水灌溉'
        return '山泉水灌溉'

    def get_tea_variety(self, obj):
        """获取茶叶品种"""
        if obj.region:
            return obj.region.tea_variety or '单枞茶'
        return '单枞茶'

    def get_management_team(self, obj):
        """获取管理方式"""
        if obj.region and obj.region.garden:
            return getattr(obj.region.garden, 'manager_name', None) or '专业管理'
        return '专业管理'

    def get_organic_certified(self, obj):
        """获取认证情况"""
        # 根据地块类型判断是否有机认证
        return obj.plot_type in ['premium', 'center']

    def get_insurance_covered(self, obj):
        """获取保险保障"""
        # 根据地块类型判断是否有保险
        return obj.plot_type in ['premium', 'center', 'standard']

    def get_main_image(self, obj):
        """获取主图片URL"""
        cover_image = obj.images.filter(is_cover=True).first()
        if cover_image and cover_image.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(cover_image.image.url)
            return cover_image.image.url
        return '/images/tea-default.jpg'

    def get_monitoring_data(self, obj):
        latest_data = obj.monitoring_data.first()
        if latest_data:
            return MonitoringDataSerializer(latest_data).data
        return None


# 订单相关序列化器
class OrderCreateSerializer(serializers.ModelSerializer):
    # 支持前端传递的字段
    tea_field_id = serializers.IntegerField(write_only=True)
    area = serializers.DecimalField(max_digits=8, decimal_places=2, write_only=True,
                                   min_value=Decimal('0.01'), max_value=Decimal('999999.99'))
    contact_info = serializers.JSONField(write_only=True, required=False)

    class Meta:
        model = Order
        fields = ['tea_field_id', 'area', 'purchase_type', 'service_package',
                 'contact_name', 'contact_phone', 'contact_address', 'notes', 'contact_info']
        extra_kwargs = {
            'contact_name': {'required': False},
            'contact_phone': {'required': False},
            'contact_address': {'required': False},
        }

    def create(self, validated_data):
        from apps.tea_fields.models import TeaField
        from decimal import Decimal
        import datetime
        import uuid

        user = self.context['request'].user

        # 验证用户
        if not user or not user.is_authenticated:
            raise serializers.ValidationError('用户未登录')

        # 获取并验证茶地ID
        tea_field_id = validated_data.pop('tea_field_id')
        try:
            tea_field_instance = TeaField.objects.get(id=tea_field_id)
        except TeaField.DoesNotExist:
            raise serializers.ValidationError('茶地不存在')

        # 获取认购面积
        quantity = validated_data.pop('area')
        if quantity <= 0:
            raise serializers.ValidationError('认购面积必须大于0')

        # 处理联系信息
        contact_info = validated_data.pop('contact_info', {})
        contact_name = contact_info.get('name', '') if contact_info else ''
        contact_phone = contact_info.get('phone', '') if contact_info else ''
        contact_address = contact_info.get('address', '') if contact_info else ''

        # 计算价格
        unit_price = tea_field_instance.price
        total_amount = unit_price * quantity

        # 生成订单号
        now = datetime.datetime.now()
        unique_id = str(uuid.uuid4())[:8].upper()
        order_id = f"ORD{now.strftime('%Y%m%d%H%M%S')}{unique_id}"

        # 创建订单
        order = Order.objects.create(
            order_id=order_id,
            user=user,
            tea_field=tea_field_instance,
            quantity=quantity,
            unit_price=unit_price,
            total_amount=total_amount,
            contact_name=contact_name,
            contact_phone=contact_phone,
            contact_address=contact_address,
            purchase_type=validated_data.get('purchase_type', 'annual'),
            service_package=validated_data.get('service_package', 'basic'),
            notes=validated_data.get('notes', '')
        )

        return order


class OrderSerializer(serializers.ModelSerializer):
    tea_field = TeaFieldListSerializer(read_only=True)
    user = UserSerializer(read_only=True)
    area = serializers.DecimalField(max_digits=8, decimal_places=2, source='quantity', read_only=True,
                                   min_value=Decimal('0.01'), max_value=Decimal('999999.99'))

    class Meta:
        model = Order
        fields = ['id', 'order_id', 'user', 'tea_field', 'quantity', 'area', 'unit_price',
                 'total_amount', 'purchase_type', 'service_package',
                 'contact_name', 'contact_phone', 'contact_address',
                 'status', 'notes', 'created_at', 'paid_at', 'completed_at']


# 支付相关序列化器
class PaymentCreateSerializer(serializers.ModelSerializer):
    order_id = serializers.CharField(write_only=True)

    class Meta:
        model = Payment
        fields = ['order_id', 'payment_method', 'amount']

    def create(self, validated_data):
        order_id = validated_data.pop('order_id')
        try:
            order = Order.objects.get(order_id=order_id, user=self.context['request'].user)
        except Order.DoesNotExist:
            raise serializers.ValidationError('订单不存在')

        if order.status != 'pending_payment':
            raise serializers.ValidationError('订单状态不允许支付')

        # 检查是否已有待支付的支付记录（最近5分钟内创建的）
        from django.utils import timezone
        from datetime import timedelta

        five_minutes_ago = timezone.now() - timedelta(minutes=5)
        existing_payment = Payment.objects.filter(
            order=order,
            payment_method=validated_data['payment_method'],
            status='pending',
            created_at__gte=five_minutes_ago  # 只复用最近5分钟内的记录
        ).first()

        if existing_payment:
            # 如果已有待支付记录，直接返回
            return existing_payment

        # 生成唯一的支付单号
        import datetime
        import uuid
        now = datetime.datetime.now()
        unique_id = str(uuid.uuid4())[:8].upper()
        payment_id = f"PAY{now.strftime('%Y%m%d%H%M%S')}{unique_id}"

        payment = Payment.objects.create(
            order=order,
            payment_id=payment_id,
            amount=validated_data.get('amount', order.total_amount),
            payment_method=validated_data['payment_method']
        )
        return payment


class PaymentSerializer(serializers.ModelSerializer):
    order = OrderSerializer(read_only=True)

    class Meta:
        model = Payment
        fields = ['payment_id', 'third_party_id', 'order', 'amount',
                 'payment_method', 'status', 'created_at', 'paid_at']


# 监控相关序列化器
class MonitoringDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = MonitoringData
        fields = ['temperature', 'humidity', 'soil_moisture', 'ph_value',
                 'light_intensity', 'wind_speed', 'rainfall', 'atmospheric_pressure',
                 'recorded_at']


class SensorDeviceSerializer(serializers.ModelSerializer):
    class Meta:
        model = SensorDevice
        fields = ['device_id', 'device_name', 'device_type', 'status', 
                 'latitude', 'longitude', 'last_heartbeat']


class AlertRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = AlertRecord
        fields = ['alert_message', 'current_value', 'threshold_value', 
                 'status', 'triggered_at', 'resolved_at']


# 收益相关序列化器
class EarningsSerializer(serializers.ModelSerializer):
    tea_field = TeaFieldListSerializer(read_only=True)
    
    class Meta:
        model = Earnings
        fields = ['earnings_type', 'amount', 'description', 'period_start', 
                 'period_end', 'tea_field', 'created_at', 'distributed_at']


# 微信登录序列化器
class WechatLoginSerializer(serializers.Serializer):
    code = serializers.CharField(max_length=100)
    userInfo = serializers.DictField()
    
    def validate(self, attrs):
        code = attrs.get('code')
        user_info = attrs.get('userInfo')
        
        if not code:
            raise serializers.ValidationError('微信授权码不能为空')
        
        if not user_info:
            raise serializers.ValidationError('用户信息不能为空')
        
        return attrs


# 合同管理序列化器
class ContractSerializer(serializers.ModelSerializer):
    order_info = OrderSerializer(source='order', read_only=True)

    class Meta:
        model = Contract
        fields = ['id', 'contract_number', 'title', 'content', 'contract_file',
                 'amount', 'start_date', 'end_date', 'status', 'signed_at',
                 'expires_at', 'user_signature', 'company_signature',
                 'created_at', 'updated_at', 'order_info']
        read_only_fields = ['id', 'contract_number', 'signed_at', 'expires_at',
                           'created_at', 'updated_at']


# 发票管理序列化器
class InvoiceSerializer(serializers.ModelSerializer):
    order_info = OrderSerializer(source='order', read_only=True)

    class Meta:
        model = Invoice
        fields = ['id', 'invoice_number', 'title', 'type', 'amount', 'tax_rate',
                 'tax_amount', 'company_name', 'tax_number', 'company_address',
                 'company_phone', 'bank_name', 'bank_account', 'recipient_name',
                 'recipient_phone', 'recipient_address', 'status', 'invoice_file',
                 'review_time', 'reject_reason', 'created_at', 'updated_at',
                 'issued_at', 'order_info']
        read_only_fields = ['id', 'invoice_number', 'tax_amount', 'status',
                           'invoice_file', 'review_time', 'reject_reason',
                           'created_at', 'updated_at', 'issued_at']


class InvoiceCreateSerializer(serializers.ModelSerializer):
    order_id = serializers.CharField(write_only=True)

    class Meta:
        model = Invoice
        fields = ['order_id', 'title', 'type', 'amount', 'tax_rate',
                 'company_name', 'tax_number', 'company_address', 'company_phone',
                 'bank_name', 'bank_account', 'recipient_name', 'recipient_phone',
                 'recipient_address']

    def create(self, validated_data):
        order_id = validated_data.pop('order_id')
        try:
            order = Order.objects.get(order_id=order_id, user=self.context['request'].user)
        except Order.DoesNotExist:
            raise serializers.ValidationError('订单不存在或无权限')

        invoice = Invoice.objects.create(
            order=order,
            **validated_data
        )
        return invoice


# 提现申请序列化器
class WithdrawRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = WithdrawRequest
        fields = ['id', 'amount', 'fee', 'actual_amount', 'withdraw_type',
                 'account_name', 'account_number', 'bank_name', 'status',
                 'remark', 'review_time', 'reject_reason', 'created_at',
                 'updated_at', 'completed_at']
        read_only_fields = ['id', 'fee', 'actual_amount', 'status', 'review_time',
                           'reject_reason', 'created_at', 'updated_at', 'completed_at']


class WithdrawRequestCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = WithdrawRequest
        fields = ['amount', 'withdraw_type', 'account_name', 'account_number', 'bank_name', 'remark']

    def validate_amount(self, value):
        """验证提现金额"""
        if value <= 0:
            raise serializers.ValidationError('提现金额必须大于0')

        # 检查用户可提现余额
        user = self.context['request'].user
        available_balance = self.get_available_balance(user)

        if value > available_balance:
            raise serializers.ValidationError(f'提现金额不能超过可用余额 ¥{available_balance}')

        return value

    def get_available_balance(self, user):
        """获取用户可提现余额"""
        from django.db.models import Sum

        # 计算总收益
        total_earnings = Earnings.objects.filter(
            user=user,
            distributed_at__isnull=False  # 只计算已分配的收益
        ).aggregate(total=Sum('amount'))['total'] or 0

        # 计算已提现金额
        withdrawn_amount = WithdrawRequest.objects.filter(
            user=user,
            status__in=['approved', 'processing', 'completed']
        ).aggregate(total=Sum('amount'))['total'] or 0

        return total_earnings - withdrawn_amount

    def create(self, validated_data):
        user = self.context['request'].user
        amount = validated_data['amount']

        # 计算手续费（假设手续费为1%，最低1元）
        fee = max(amount * 0.01, 1.0)
        actual_amount = amount - fee

        withdraw_request = WithdrawRequest.objects.create(
            user=user,
            fee=fee,
            actual_amount=actual_amount,
            **validated_data
        )
        return withdraw_request


# 轮播图序列化器
class BannerSerializer(serializers.ModelSerializer):
    """轮播图序列化器"""
    image_url = serializers.SerializerMethodField()

    class Meta:
        model = Banner
        fields = [
            'id', 'title', 'description', 'image_url', 'position',
            'link_type', 'link_url', 'sort_order', 'is_active',
            'start_time', 'end_time', 'click_count'
        ]

    def get_image_url(self, obj):
        """获取图片URL"""
        return obj.get_image_url()


class BannerListSerializer(serializers.ModelSerializer):
    """轮播图列表序列化器（简化版）"""
    image = serializers.SerializerMethodField()
    subtitle = serializers.CharField(source='description', read_only=True)

    class Meta:
        model = Banner
        fields = ['id', 'title', 'subtitle', 'image', 'link_type', 'link_url', 'sort_order']

    def get_image(self, obj):
        """获取图片URL"""
        return obj.get_image_url()


# 图标相关序列化器
class IconCategorySerializer(serializers.ModelSerializer):
    """图标分类序列化器"""
    icons_count = serializers.SerializerMethodField()

    class Meta:
        model = IconCategory
        fields = ['id', 'name', 'code', 'description', 'sort_order', 'icons_count']

    def get_icons_count(self, obj):
        """获取分类下的图标数量"""
        return obj.icons.filter(is_active=True).count()


class IconSerializer(serializers.ModelSerializer):
    """图标序列化器"""
    icon_url = serializers.SerializerMethodField()
    category_name = serializers.CharField(source='category.name', read_only=True)

    class Meta:
        model = Icon
        fields = [
            'id', 'name', 'code', 'category_name', 'usage_type',
            'icon_url', 'icon_type', 'width', 'height', 'color',
            'description', 'tags', 'usage_count'
        ]

    def get_icon_url(self, obj):
        """获取图标URL"""
        return obj.get_icon_url()


class IconListSerializer(serializers.ModelSerializer):
    """图标列表序列化器（简化版）"""
    icon_url = serializers.SerializerMethodField()

    class Meta:
        model = Icon
        fields = ['id', 'name', 'code', 'icon_url', 'width', 'height', 'color']

    def get_icon_url(self, obj):
        """获取图标URL"""
        return obj.get_icon_url()
